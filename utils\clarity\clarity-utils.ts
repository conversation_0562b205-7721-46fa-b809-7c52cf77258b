let Clarity: any;
if (!__DEV__) {
  Clarity = require('@microsoft/react-native-clarity');
}

import { getEnvironmentVariable } from '../getEnvironmentVariable';
import { useCallback, useMemo } from 'react';
import logger from '@/utils/logger/logger';

const mockClarity = {
  LogLevel: { Info: 'info' },
  initialize: (projectId: string, options?: any) => {
    // logger.info('[DEV] Clarity mock initialized with project ID:', projectId);
  },
  setOnSessionStartedCallback: (callback: (sessionId: string) => void) => {
    // logger.info('[DEV] Clarity mock session callback set');
  },
  sendCustomEvent: (eventName: string) => {
    // logger.info('[DEV] Clarity mock event:', eventName);
  },
  setCustomUserId: (userId: string) => {
    // logger.info('[DEV] Clarity mock user ID set:', userId);
  },
  setCustomTag: (key: string, value: string) => {
    // logger.info('[DEV] Clarity mock tag set:', key, value);
  },
  setCurrentScreenName: (screenName: string) => {
    // logger.info('[DEV] Clarity mock screen name set:', screenName);
  }
};

const clarityInstance = __DEV__ ? mockClarity : Clarity;

export const initializeClarity = async () => {
  try {
    const clarityProjectId = getEnvironmentVariable("CLARITY_PROJECT_ID");
    
    if (__DEV__) {
      return; 
    }
    
    clarityInstance.initialize(clarityProjectId, {
      logLevel: Clarity.LogLevel.Info
    });
    
    clarityInstance.setOnSessionStartedCallback((sessionId: string) => {
      logger.info('Clarity session started with ID:', sessionId);
    });
    
    logger.info('Clarity initialized successfully');
  } catch (error) {
    logger.info('Failed to initialize Clarity:', error);
  }
};

/**
 * Track a custom event in Clarity
 * @param eventName Name of the event
 * @param properties Additional properties to track with the event (not directly supported by Clarity)
 */
export const trackClarityEvent = (eventName: string, properties?: Record<string, any>) => {
  if (__DEV__) {
    return;
  }
  
  try {
    if (properties && Object.keys(properties).length > 0) {
      const propertiesStr = JSON.stringify(properties);
      clarityInstance.sendCustomEvent(`${eventName}: ${propertiesStr}`);
    } else {
      clarityInstance.sendCustomEvent(eventName);
    }
  } catch (error) {
    logger.info('Failed to track Clarity event:', error);
  }
};

/**
 * Set user information in Clarity
 * @param userId User identifier
 * @param userProperties Additional user properties
 */
export const identifyClarityUser = (userId: string, userProperties?: Record<string, any>) => {
  if (__DEV__) {
    return;
  }
  
  try {
    clarityInstance.setCustomUserId(userId);
    
    if (userProperties) {
      Object.entries(userProperties).forEach(([key, value]) => {
        if (typeof value === 'string') {
          clarityInstance.setCustomTag(key, value);
        } else {
          clarityInstance.setCustomTag(key, JSON.stringify(value));
        }
      });
    }
  } catch (error) {
    logger.info('Failed to identify user in Clarity:', error);
  }
};

/**
 * Track screen view in Clarity
 * @param screenName Name of the screen
 */
export const trackClarityScreen = (screenName: string) => {
  if (__DEV__) {
    return;
  }
  
  try {
    clarityInstance.setCurrentScreenName(screenName);
    clarityInstance.sendCustomEvent(`screen_view: ${screenName}`);
  } catch (error) {
    logger.info('Failed to track screen view in Clarity:', error);
  }
};

/**
 * Custom hook for using Clarity tracking in React components
 * This hook provides memoized versions of the Clarity tracking functions
 * to prevent unnecessary re-renders and function recreations
 */
export const useClarityTracking = () => {
  // Memoized version of trackClarityEvent
  const trackEvent = useCallback((eventName: string, properties?: Record<string, any>) => {
    trackClarityEvent(eventName, properties);
  }, []);
  
  // Memoized version of identifyClarityUser
  const identifyUser = useCallback((userId: string, userProperties?: Record<string, any>) => {
    identifyClarityUser(userId, userProperties);
  }, []);
  
  // Memoized version of trackClarityScreen
  const trackScreen = useCallback((screenName: string) => {
    trackClarityScreen(screenName);
  }, []);
  
  // Memoized version of initializeClarity
  const initialize = useCallback(async () => {
    await initializeClarity();
  }, []);
  
  // Return memoized API
  return useMemo(() => ({
    trackEvent,
    identifyUser,
    trackScreen,
    initialize
  }), [trackEvent, identifyUser, trackScreen, initialize]);
};
