import appsFlyer from 'react-native-appsflyer';
import { Platform } from 'react-native';
import logger from '@/utils/logger/logger';

interface AppsFlyerInitOptions {
  devKey: string;
  appId?: string;
  isDebug: boolean;
  onInstallConversionDataListener: boolean;
  onDeepLinkListener: boolean;
}

interface ConversionData {
  type: string;
  data: {
    is_first_launch?: string;
    af_status?: string;
    media_source?: string;
    campaign?: string;
    [key: string]: any;
  };
}

interface DeepLinkData {
  type: string;
  data: {
    [key: string]: any;
  };
}

class AppsFlyerService {
  private isInitialized: boolean;
  private initializationPromise: Promise<void> | null;

  constructor() {
    this.isInitialized = false;
    this.initializationPromise = null;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    // If initialization is already in progress, wait for it
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._initialize();
    return this.initializationPromise;
  }

  private async _initialize(): Promise<void> {
    const initOptions: AppsFlyerInitOptions = {
      devKey: 'uVpTeB7n2rHoxLwPKxvoEW',
      appId: Platform.OS === 'ios' ? '6746088428' : undefined,
      isDebug: __DEV__,
      onInstallConversionDataListener: true,
      onDeepLinkListener: true,
    };

    try {
      await appsFlyer.initSdk(initOptions);
      this.isInitialized = true;
      
      this.setupConversionDataListener();
      this.setupDeepLinkListener();
      
    } catch (error) {
      logger.error('AppsFlyer initialization failed:', error);
      this.initializationPromise = null;
      throw error;
    }
  }

  setupConversionDataListener(): void {
    appsFlyer.onInstallConversionData((data: ConversionData) => {
      //logger.info('Install Conversion Data:', data);
      
      if (data.type === 'onInstallConversionDataLoaded') {
        const conversionData = data.data;
        
        // Check if this is a first install
        if (conversionData.is_first_launch === 'true') {
          logger.info('First install detected');
          
          // Check if install was attributed to a campaign
          if (conversionData.af_status === 'Organic') {
            logger.info('Organic install');
          } else if (conversionData.af_status === 'Non-organic') {
            logger.info('Non-organic install from:', conversionData.media_source);
            logger.info('Campaign:', conversionData.campaign);
          }
        }
      }
    });
  }

  setupDeepLinkListener(): void {
    appsFlyer.onDeepLink((data: DeepLinkData) => {
      logger.info('Deep Link Data:', data);
      
      if (data.type === 'onDeepLinking') {
        const deepLinkData = data.data;
        logger.info('Deep link received:', deepLinkData);
        
        // Handle deep link navigation here
        // You can use React Navigation or your preferred routing solution
      }
    });
  }

  // Ensure AppsFlyer is initialized before tracking
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  // Track custom events with better error handling
  async trackEvent(eventName: string, eventValues: Record<string, any> = {}): Promise<void> {
    try {
      // Ensure AppsFlyer is initialized first
      await this.ensureInitialized();

      // Validate event name
      if (!eventName || typeof eventName !== 'string') {
        throw new Error('Event name must be a non-empty string');
      }

      // Clean up event values - remove null/undefined values
      const cleanEventValues = Object.entries(eventValues).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      logger.info(`Tracking event: ${eventName}`, cleanEventValues);
      await appsFlyer.logEvent(eventName, cleanEventValues);
      logger.info(`Event tracked successfully: ${eventName}`);
    } catch (error) {
      logger.error('Error tracking event:', eventName, error);
      // Don't throw the error to prevent app crashes
      // throw error; 
    }
  }

  // Track revenue events
  async trackPurchase(revenue: number, currency: string = 'USD', additionalParams: Record<string, any> = {}): Promise<void> {
    const purchaseData = {
      af_revenue: revenue,
      af_currency: currency,
      ...additionalParams
    };

    await this.trackEvent('af_purchase', purchaseData);
  }

  // Get AppsFlyer ID
  async getAppsFlyerId(): Promise<string | null> {
    try {
      await this.ensureInitialized();
      
      // Different possible method names depending on SDK version
      if ('getAppsFlyerUID' in appsFlyer && typeof appsFlyer.getAppsFlyerUID === 'function') {
        //@ts-ignore
        const appsFlyerId = await appsFlyer.getAppsFlyerUID();
        logger.info('AppsFlyer ID:', appsFlyerId);
        //@ts-ignore
        return appsFlyerId;
      } else if ('getUID' in appsFlyer && typeof (appsFlyer as any).getUID === 'function') {
        const appsFlyerId = await (appsFlyer as any).getUID();
        logger.info('AppsFlyer ID:', appsFlyerId);
        return appsFlyerId;
      } else {
        logger.info('getAppsFlyerUID method not available in this SDK version');
        return null;
      }
    } catch (error) {
      logger.error('Error getting AppsFlyer ID:', error);
      return null;
    }
  }

  // Check if AppsFlyer is properly initialized
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }
}

export default new AppsFlyerService();