"use client"

import type React from "react"
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, Switch, Platform } from "react-native"
import { Heart, Plus, Minus } from "lucide-react-native"
import { useTranslation } from "react-i18next";
import { colors } from "@/constants/colors"
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils"
import Slider from "@react-native-community/slider"
import { useFormDataStore } from "@/store/formDataStore"
import { useState, useCallback, useMemo } from "react"
import { moderateScale, verticalScale, scale } from "react-native-size-matters"
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface ReproductiveStepProps {
  stepIndex: number
  isActive?: boolean
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    content: { padding: scale(16) },
    iconContainer: {
      width: moderateScale(64),
      height: moderateScale(64),
      borderRadius: moderateScale(theme.radii["4xl"]),
      backgroundColor: `${theme.colors.primary.main}20`,
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    formField: { marginBottom: verticalScale(24) },
    label: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      marginBottom: verticalScale(12),
      color: theme.colors.gray[800],
      textAlign: "center",
    },
    fieldLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      marginBottom: verticalScale(8),
      color: theme.colors.gray[700],
    },
    switchContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      gap: moderateScale(16),
    },
    switchLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[700],
    },
    sectionCard: {
      backgroundColor: theme.colors.gray[50],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      marginBottom: verticalScale(24),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: verticalScale(14),
    },
    sectionTitle: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: theme.colors.gray[800],
    },
    clearButton: {
      width: moderateScale(36),
      height: moderateScale(36),
      borderRadius: moderateScale(theme.radii.lg),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
    },
    textArea: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
      height: verticalScale(100),
      textAlignVertical: "top",
    },
    buttonGroup: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: moderateScale(theme.radii.md),
      overflow: "hidden",
    },
    button: {
      flex: 1,
      padding: moderateScale(14),
      alignItems: "center",
      justifyContent: "center",
    },
    activeButton: { backgroundColor: theme.colors.primary.main },
    buttonText: {
      color: theme.colors.primary.main,
      fontSize: moderateScale(theme.fontSize.sm),
    },
    activeButtonText: { color: theme.colors.secondary[50] },
    slider: { width: "100%", height: verticalScale(40) },
    sliderLabels: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: verticalScale(-8),
    },
    sliderMinLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[600],
    },
    sliderMaxLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[600],
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.gray[200],
      marginVertical: verticalScale(16),
    },
    counterContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginTop: verticalScale(8),
    },
    counterButton: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.xl),
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.secondary[50],
    },
    counterText: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      marginHorizontal: moderateScale(24),
      color: theme.colors.gray[800],
    },
  });


export const ReproductiveStep: React.FC<ReproductiveStepProps> = ({ stepIndex, isActive = false }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { formData, updateReproHealth } = useFormDataStore()
  const [flowDays, setFlowDays] = useState(formData.basicInfo.sleep || 5)
  const [padsPerDay, setPadsPerDay] = useState(formData.basicInfo.hydration || 4)

  const updateMenstruation = useCallback(
    (field: string, value: any) => {
      updateReproHealth({
        menstruation: {
          ...formData.reproHealth.menstruation,
          [field]: value,
        },
      })
      trackUserInteraction("Form Field Update", {
        section: "reproductive",
        subsection: "menstruation",
        field,
        value: typeof value === "number" ? value.toString() : value,
      })
    },
    [formData.reproHealth.menstruation, updateReproHealth],
  )

  const updateChildbirth = useCallback(
    (field: string, value: any) => {
      updateReproHealth({
        childbirth: {
          ...formData.reproHealth.childbirth,
          [field]: value,
        },
      })
      trackUserInteraction("Form Field Update", {
        section: "reproductive",
        subsection: "childbirth",
        field,
        value: typeof value === "number" ? value.toString() : value,
      })
    },
    [formData.reproHealth.childbirth, updateReproHealth],
  )

  const clearMenstruationForm = useCallback(() => {
    // Clear all menstruation fields and set hasMenstruated to false
    updateReproHealth({
      hasMenstruated: false,
      menstruation: {
        regularity: "",
        cycleLength: "",
        flowDays: 5,
        padsPerDay: 4,
        symptoms: "",
      },
    })
    setFlowDays(5)
    setPadsPerDay(4)
    trackUserInteraction("Form Cleared", {
      section: "reproductive",
      subsection: "menstruation",
    })
  }, [updateReproHealth])

  const clearChildbirthForm = useCallback(() => {
    // Clear all childbirth fields and set hasChildbirth to false
    updateReproHealth({
      hasChildbirth: false,
      childbirth: {
        children: 0,
        pregnancies: 0,
        complications: "",
      },
    })
    trackUserInteraction("Form Cleared", {
      section: "reproductive",
      subsection: "childbirth",
    })
  }, [updateReproHealth])

  const menstruationSection = useMemo(() => {
    if (!formData.reproHealth.hasMenstruated) return null

    return (
      <View style={styles.sectionCard}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{t('reproductive.menstruation.detailsTitle')}</Text>
          <TouchableOpacity style={styles.clearButton} onPress={clearMenstruationForm}>
            <Minus size={16} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.menstruation.regularity.question')}</Text>
          <View style={styles.buttonGroup}>
            {[t('reproductive.menstruation.regularity.regular'), t('reproductive.menstruation.regularity.irregular'), t('reproductive.menstruation.regularity.notSure')].map((option) => (
              <TouchableOpacity
                key={option}
                style={[styles.button, formData.reproHealth.menstruation.regularity === option && styles.activeButton]}
                onPress={() => updateMenstruation("regularity", option)}
              >
                <Text
                  style={[
                    styles.buttonText,
                    formData.reproHealth.menstruation.regularity === option && styles.activeButtonText,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.menstruation.cycleLength.label')}</Text>
          <TextInput
            style={styles.input}
            placeholder={t('reproductive.menstruation.cycleLength.placeholder')}
            keyboardType="numeric"
            value={formData.reproHealth.menstruation.cycleLength}
            onChangeText={(text) => updateMenstruation("cycleLength", text)}
          />
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.menstruation.flowDays.label', { flowDays })}</Text>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={15}
            step={1}
            value={formData.reproHealth.menstruation.flowDays}
            onValueChange={setFlowDays}
            onSlidingComplete={(value) => updateMenstruation("flowDays", value)}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.gray[300]}
            thumbTintColor={colors.primary}
          />
          <View style={styles.sliderLabels}>
            <Text style={styles.sliderMinLabel}>{t('reproductive.menstruation.flowDays.min')}</Text>
            <Text style={styles.sliderMaxLabel}>{t('reproductive.menstruation.flowDays.max')}</Text>
          </View>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.menstruation.padsPerDay.label', { padsPerDay })}</Text>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={15}
            step={1}
            value={formData.reproHealth.menstruation.padsPerDay}
            onValueChange={setPadsPerDay}
            onSlidingComplete={(value) => updateMenstruation("padsPerDay", value)}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.gray[300]}
            thumbTintColor={colors.primary}
          />
          <View style={styles.sliderLabels}>
            <Text style={styles.sliderMinLabel}>{t('reproductive.menstruation.padsPerDay.min')}</Text>
            <Text style={styles.sliderMaxLabel}>{t('reproductive.menstruation.padsPerDay.max')}</Text>
          </View>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.menstruation.symptoms.question')}</Text>
          <TextInput
            style={styles.textArea}
            placeholder={t('reproductive.menstruation.symptoms.placeholder')}
            multiline={true}
            numberOfLines={3}
            value={formData.reproHealth.menstruation.symptoms}
            onChangeText={(text) => updateMenstruation("symptoms", text)}
          />
        </View>
      </View>
    )
  }, [
    formData.reproHealth.hasMenstruated,
    formData.reproHealth.menstruation,
    flowDays,
    padsPerDay,
    updateMenstruation,
    clearMenstruationForm,
  ])

  const childbirthSection = useMemo(() => {
    if (!formData.reproHealth.hasChildbirth) return null

    return (
      <View style={styles.sectionCard}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{t('reproductive.childbirth.detailsTitle')}</Text>
          <TouchableOpacity style={styles.clearButton} onPress={clearChildbirthForm}>
            <Minus size={16} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.childbirth.children.label')}</Text>
          <View style={styles.counterContainer}>
            <TouchableOpacity
              style={styles.counterButton}
              onPress={() => updateChildbirth("children", Math.max(0, formData.reproHealth.childbirth.children - 1))}
            >
              <Minus size={16} color={colors.gray[600]} />
            </TouchableOpacity>
            <Text style={styles.counterText}>{formData.reproHealth.childbirth.children}</Text>
            <TouchableOpacity
              style={styles.counterButton}
              onPress={() => updateChildbirth("children", formData.reproHealth.childbirth.children + 1)}
            >
              <Plus size={16} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.childbirth.pregnancies.label')}</Text>
          <View style={styles.counterContainer}>
            <TouchableOpacity
              style={styles.counterButton}
              onPress={() =>
                updateChildbirth("pregnancies", Math.max(0, formData.reproHealth.childbirth.pregnancies - 1))
              }
            >
              <Minus size={16} color={colors.gray[600]} />
            </TouchableOpacity>
            <Text style={styles.counterText}>{formData.reproHealth.childbirth.pregnancies}</Text>
            <TouchableOpacity
              style={styles.counterButton}
              onPress={() => updateChildbirth("pregnancies", formData.reproHealth.childbirth.pregnancies + 1)}
            >
              <Plus size={16} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>{t('reproductive.childbirth.complications.question')}</Text>
          <TextInput
            style={styles.textArea}
            placeholder={t('reproductive.childbirth.complications.placeholder')}
            multiline={true}
            numberOfLines={3}
            value={formData.reproHealth.childbirth.complications}
            onChangeText={(text) => updateChildbirth("complications", text)}
          />
        </View>
      </View>
    )
  }, [formData.reproHealth.hasChildbirth, formData.reproHealth.childbirth, updateChildbirth, clearChildbirthForm])

  if (!isActive) return null

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <Heart size={32} color={colors.primary} />
      </View>

      <Text style={styles.title}>{t('reproductive.title')}</Text>
      <Text style={styles.subtitle}>{t('reproductive.subtitle')}</Text>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('reproductive.menstruation.question')}</Text>
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>{t('common.no')}</Text>
          <Switch
            value={formData.reproHealth.hasMenstruated}
            onValueChange={(value) => {
              updateReproHealth({ hasMenstruated: value })
              trackUserInteraction("Form Field Update", {
                section: "reproductive",
                field: "hasMenstruated",
                value: value.toString(),
              })
            }}
            trackColor={{ false: theme.colors.gray[300], true: theme.colors.primary.main }}
            thumbColor={Platform.OS === "android" ? theme.colors.secondary[50] : ""}
          />
          <Text style={styles.switchLabel}>{t('common.yes')}</Text>
        </View>
      </View>

      {menstruationSection}

      <View style={styles.divider} />

      <View style={styles.formField}>
        <Text style={styles.label}>{t('reproductive.childbirth.question')}</Text>
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>{t('common.no')}</Text>
          <Switch
            value={formData.reproHealth.hasChildbirth}
            onValueChange={(value) => {
              updateReproHealth({ hasChildbirth: value })
              trackUserInteraction("Form Field Update", {
                section: "reproductive",
                field: "hasChildbirth",
                value: value.toString(),
              })
            }}
            trackColor={{ false: theme.colors.gray[300], true: theme.colors.primary.main }}
            thumbColor={Platform.OS === "android" ? theme.colors.secondary[50] : ""}
          />
          <Text style={styles.switchLabel}>{t('common.yes')}</Text>
        </View>
      </View>

      {childbirthSection}
    </ScrollView>
  )
}

// const styles = StyleSheet.create({
//   container: { flex: 1 },
//   content: { padding: scale(16) },
//   iconContainer: {
//     width: moderateScale(64),
//     height: moderateScale(64),
//     borderRadius: moderateScale(32),
//     backgroundColor: `${colors.primary}20`,
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: moderateScale(22),
//     fontWeight: "700",
//     color: theme.colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: moderateScale(theme.fontSize.sm),
//     color: colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   formField: { marginBottom: verticalScale(24) },
//   label: {
//     fontSize: moderateScale(theme.fontSize.lg),
//     fontWeight: "600",
//     marginBottom: verticalScale(12),
//     color: colors.gray[800],
//   },
//   fieldLabel: {
//     fontSize: moderateScale(theme.fontSize.sm),
//     fontWeight: "500",
//     marginBottom: verticalScale(8),
//     color: colors.gray[700],
//   },
//   switchContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     gap: moderateScale(16),
//   },
//   switchLabel: { fontSize: moderateScale(14), color: colors.gray[700] },
//   sectionCard: {
//     backgroundColor: colors.gray[50],
//     borderRadius: moderateScale(theme.radii.md),
//     padding: moderateScale(14),
//     marginBottom: verticalScale(24),
//     borderWidth: 1,
//     borderColor: colors.gray[200],
//   },
//   sectionHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginBottom: verticalScale(14),
//   },
//   sectionTitle: {
//     fontSize: moderateScale(18),
//     fontWeight: "600",
//     color: colors.gray[800],
//   },
//   clearButton: {
//     width: moderateScale(36),
//     height: moderateScale(36),
//     borderRadius: moderateScale(18),
//     backgroundColor: colors.gray[100],
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   input: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     backgroundColor: theme.colors.secondary[50],
//     fontSize: moderateScale(14),
//   },
//   textArea: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     backgroundColor: theme.colors.secondary[50],
//     fontSize: moderateScale(14),
//     height: verticalScale(100),
//     textAlignVertical: "top",
//   },
//   buttonGroup: {
//     flexDirection: "row",
//     borderWidth: 1,
//     borderColor: theme.colors.primary.main,
//     borderRadius: moderateScale(12),
//     overflow: "hidden",
//   },
//   button: { flex: 1, padding: moderateScale(14), alignItems: "center", justifyContent: "center" },
//   activeButton: { backgroundColor: colors.primary },
//   buttonText: { color: colors.primary, fontSize: moderateScale(14) },
//   activeButtonText: { color: theme.colors.secondary[50] },
//   slider: { width: "100%", height: verticalScale(40) },
//   sliderLabels: { flexDirection: "row", justifyContent: "space-between", marginTop: verticalScale(-8) },
//   sliderMinLabel: { fontSize: moderateScale(12), color: colors.gray[600] },
//   sliderMaxLabel: { fontSize: moderateScale(12), color: colors.gray[600] },
//   divider: { height: 1, backgroundColor: colors.gray[200], marginVertical: verticalScale(16) },
//   counterContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     marginTop: verticalScale(8),
//   },
//   counterButton: {
//     width: moderateScale(40),
//     height: moderateScale(40),
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(20),
//     alignItems: "center",
//     justifyContent: "center",
//     backgroundColor: theme.colors.secondary[50],
//   },
//   counterText: {
//     fontSize: moderateScale(18),
//     fontWeight: "600",
//     marginHorizontal: moderateScale(24),
//     color: colors.gray[800],
//   },
// })
