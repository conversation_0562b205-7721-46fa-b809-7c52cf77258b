import React, { useMemo, useCallback } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Image,
  Platform,
} from "react-native";
import { useRouter, usePathname } from "expo-router";
import { ChevronLeft, User, Share2, X } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { SafeAreaView } from "react-native-safe-area-context";
import { SvgUri } from "react-native-svg";
import { Share } from "react-native";
import {
  moderateScale,
  moderateVerticalScale,
} from "react-native-size-matters";
import { useChatStore } from "@/store/chatStore";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import logger from '@/utils/logger/logger';
import { useTranslation } from 'react-i18next';

const SafeAreaHeight =
  Platform.OS === "ios" ? moderateVerticalScale(64) : "10%";

type TopNavbarProps = {
  title?: string;
  showProfile?: boolean;
  showBackButton?: boolean;
  showLogo?: boolean;
  onBackPress?: () => void;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    safeArea: {
      zIndex: 1,
      backgroundColor: theme.colors.secondary[50],
      //@ts-ignore
      height: moderateVerticalScale(SafeAreaHeight),
      paddingLeft: moderateScale(8),
    },
    container: {
      paddingBottom: moderateVerticalScale(8),
      paddingTop: moderateVerticalScale(8),
      paddingHorizontal: moderateScale(16),
      backgroundColor: colors.white,
    },
    content: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    leftSection: {
      flexDirection: "row",
      alignItems: "center",
    },
    logoContainer: {
      width: moderateScale(18),
      height: moderateScale(18),
      backgroundColor: "transparent",
      alignItems: "center",
      justifyContent: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: theme.colors.gray[800],
      marginLeft: moderateScale(8),
      alignItems: "center",
    },
    profileButton: {
      width: moderateScale(32),
      height: moderateScale(32),
      borderRadius: moderateScale(theme.radii.xl),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
    },
    backButton: {
      width: moderateScale(36),
      height: moderateVerticalScale(44),
      justifyContent: "center",
      alignItems: "center",
    },
    backButtonText: {
      color: theme.colors.primary.main,
      fontSize: moderateScale(theme.fontSize.lg),
    },
    selectionHeader: {
      zIndex: 1,
      backgroundColor: theme.colors.secondary[50],
      height: moderateVerticalScale(64),
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: moderateScale(16),
    },
    selectedMessagesText: {
      fontSize: moderateScale(theme.fontSize.md),
      alignItems: "center",
      justifyContent: "center",
      color: theme.colors.gray[800],
      alignSelf: "center",
    },
    shareButton: {
      width: moderateScale(32),
      height: moderateScale(32),
      borderRadius: moderateScale(theme.radii.xl),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
    },
  });
 
// Memoize the static images
const MemoizedSvgLogo = React.memo(() => (
  <Image
    source={require("@/assets/images/august-green-a-icon.png")} // adjust path accordingly
    style={{
      width: 100,
      height: 32,
      resizeMode: "contain",
    }}
  />
));

const MemoizedImage = React.memo(() => (
  <Image
    source={require("@/assets/images/august-navbar.png")} // adjust path accordingly
    style={{
      width: 100,
      height: 24,
      resizeMode: "contain",
    }}
  />
));

const TopNavbar = ({
  title,
  showProfile = true,
  showBackButton = false,
  showLogo = true,
  onBackPress,
}: TopNavbarProps) => {
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const { t } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();
  const { selectedMessages, clearSelectedMessages, messages } = useChatStore();
  const isOnChatPage = pathname === "/chat" || pathname === "/(tabs)/chat";
  const isSelectionMode = selectedMessages.length > 0 && isOnChatPage;
  const displayTitle = title || t('navbar.defaultTitle');

  // Memoize date helper functions
  const dateHelpers = useMemo(() => ({
    isToday: (date: Date) => {
      const today = new Date();
      return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
      );
    },

    isYesterday: (date: Date) => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return (
        date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()
      );
    },

    isThisWeek: (date: Date) => {
      const today = new Date();
      const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
      return date >= weekStart;
    },
  }), []);

  // Memoize the date formatting function
  const formatMessageDate = useCallback((messageDate: Date) => {
    const now = new Date();
    let dateString = "";

    if (dateHelpers.isToday(messageDate)) {
      dateString = messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } else if (dateHelpers.isYesterday(messageDate)) {
      dateString = `${t('dateTime.yesterday')} ${messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    } else if (dateHelpers.isThisWeek(messageDate)) {
      dateString = `${messageDate.toLocaleDateString([], {
        weekday: "long",
      })}, ${messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    } else {
      dateString = `${messageDate.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        year: messageDate.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      })}, ${messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })}`;
    }
    return dateString;
  }, [dateHelpers]);

  // Memoize the message formatting function
  const formatMessage = useCallback((msg: any) => {
    const messageDate = new Date(msg.timestamp);
    const dateString = formatMessageDate(messageDate);
    
    let content = msg.text || "";
    
    if (msg.attachments?.length) {
      msg.attachments.forEach((att: any) => {
        if (att.type === "image") {
          content += content ? ` ${t('attachments.image')}` : t('attachments.image');
        } else if (att.type === "pdf") {
          content += content ? ` ${t('attachments.pdf')}` : t('attachments.pdf'); 
        } else if (att.type === "voice") {
          content += content ? ` ${t('attachments.voice')}` : t('attachments.voice');
        }
      });
    }
    
    return `[${dateString}] ${msg.sender === "user" ? t('chat.me') : t('chat.augustName')}: ${content}`;
  }, [formatMessageDate]);

  // Memoize the share handler
  const messagesToShare = useMemo(() => 
    messages.filter(msg => selectedMessages.includes(msg.id)),
    [messages, selectedMessages]
  );

  const formattedSharedText = useMemo(() => 
    messagesToShare.map(formatMessage).join("\n\n"),
    [messagesToShare, formatMessage]
  );

  const handleShare = useCallback(async () => {
    try {
      const appStoreLink = "https://app.meetaugust.ai/redirect/app";
      const shareText = `${t('chat.share.introText')}\n\n${formattedSharedText}\n\n${t('chat.share.downloadText')}\n${appStoreLink}`;

      await Share.share({ message: shareText });
      clearSelectedMessages();
    } catch (error) {
      logger.error("Error sharing messages:", error);
    }
  }, [formattedSharedText, clearSelectedMessages]);

  const handleProfilePress = useCallback(() => {
    router.push("/profile");
  }, [router]);

  const handleLogoPress = useCallback(() => {
    router.push("/(tabs)/chat");
  }, [router]);

  const handleBackPress = useCallback(() => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  }, [onBackPress, router]);

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.safeArea, showBackButton && { paddingLeft: 0 }]}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.leftSection}>
            {!isSelectionMode ? (
              showBackButton ? (
                <TouchableOpacity
                  onPress={handleBackPress}
                  style={styles.backButton}
                >
                  <ChevronLeft size={24} color={colors.gray[600]} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={styles.logoContainer}
                  onPress={handleLogoPress}
                >
                  <MemoizedSvgLogo />
                </TouchableOpacity>
              )
            ) : (
              <TouchableOpacity
                style={styles.logoContainer}
                onPress={clearSelectedMessages}
              >
                <X size={25} color={colors.gray[800]} />
              </TouchableOpacity>
            )}
          </View>

          {!isSelectionMode && showLogo && <MemoizedImage />}

          {isSelectionMode && (
            <Text style={styles.selectedMessagesText}>
              {selectedMessages.length} {t('navbar.selectedCount')}
            </Text>
          )}

          {isSelectionMode ? (
            <>
              <TouchableOpacity
                style={styles.profileButton}
                onPress={handleShare}
              >
                <Share2 size={22} color={colors.primary} />
              </TouchableOpacity>
            </>
          ) : (
            showProfile && (
              <TouchableOpacity
                style={styles.profileButton}
                onPress={handleProfilePress}
              >
                <User size={24} color={colors.primary} />
              </TouchableOpacity>
            )
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

// const styles = StyleSheet.create({
//   safeArea: {
//     zIndex: 1,
//     backgroundColor: colors.white,
//     //@ts-ignore
//     height: moderateVerticalScale(SafeAreaHeight),
//     paddingLeft: moderateScale(8),
//   },
//   container: {
//     paddingBottom: moderateVerticalScale(8),
//     paddingTop: moderateVerticalScale(8),
//     paddingHorizontal: moderateScale(16),
//     backgroundColor: colors.white,
//   },
//   content: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "space-between",
//   },
//   leftSection: {
//     flexDirection: "row",
//     alignItems: "center",
//   },
//   logoContainer: {
//     width: moderateScale(18),
//     height: moderateScale(18),
//     backgroundColor: "transparent",
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   title: {
//     fontSize: moderateScale(18),
//     fontWeight: "600",
//     color: colors.gray[800],
//     marginLeft: moderateScale(8),
//     alignItems: "center",
//   },
//   profileButton: {
//     width: moderateScale(32),
//     height: moderateScale(32),
//     borderRadius: moderateScale(20),
//     backgroundColor: colors.gray[100],
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   backButton: {
//     width: moderateScale(36),
//     height: moderateVerticalScale(44),
//     justifyContent: "center",
//     alignItems: "center",
//   },
//   backButtonText: {
//     color: colors.primary,
//     fontSize: moderateScale(18),
//   },
//   selectionHeader: {
//     zIndex: 1,
//     backgroundColor: colors.white,
//     height: moderateVerticalScale(64),
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "space-between",
//     paddingHorizontal: moderateScale(16),
//   },
//   selectedMessagesText: {
//     fontSize: moderateScale(16),
//     alignItems: "center",
//     justifyContent: "center",
//     color: colors.gray[800],
//     alignSelf: "center",
//   },
//   shareButton: {
//     width: moderateScale(32),
//     height: moderateScale(32),
//     borderRadius: moderateScale(20),
//     backgroundColor: colors.gray[100],
//     alignItems: "center",
//     justifyContent: "center",
//   },
// });

export default TopNavbar;
