import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import TopNavbar from '@/components/navigation/TopNavbar';
import { Stack } from 'expo-router';
import { WebView } from 'react-native-webview';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';

function LibraryScreen() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('library.title')} showProfile={false} showBackButton={true} showLogo={false}/>
      
      <View style={styles.webviewContainer}>
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        )}
        <WebView
          source={{ uri: 'https://www.meetaugust.ai/en/library' }}
          style={styles.webview}
          onLoadStart={() => setLoading(true)}
          onLoadEnd={() => setLoading(false)}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  webviewContainer: {
    flex: 1,
    marginTop: moderateVerticalScale(20),
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 1,
  },
});

export default React.memo(LibraryScreen);
