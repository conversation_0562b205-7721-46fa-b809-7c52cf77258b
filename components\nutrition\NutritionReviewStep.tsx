import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  ScrollView,
  Dimensions,
  Animated
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNutritionStore } from '@/store/nutritionStore';
import { 
  Edit2, 
  Flame, 
  Wheat, 
  Beef, 
  Droplet,
  Award,
  BarChart3,
  Utensils,
  CalendarClock,
  Scale
} from 'lucide-react-native';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import EditValueModal from './EditValueModal';
import getMacroPerDay from '@/utils/nutrition/getMacroPerDay';
import { LinearGradient } from 'expo-linear-gradient';
import logger from '@/utils/logger/logger';
import { trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';
import { AppEventsLogger } from 'react-native-fbsdk-next';

interface NutritionReviewStepProps {
  nutritionData: any;
  onSubmit: (data: any) => Promise<void>;
  isLoading?: boolean;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    content: {
      padding: moderateScale(24),
      paddingBottom: moderateVerticalScale(100), 
    },
    header: {
      alignItems: 'center',
      marginBottom: moderateVerticalScale(32),
    },
    celebrationContainer: {
      alignItems: 'center',
      marginBottom: moderateVerticalScale(24),
    },
    awardCircle: {
      width: moderateScale(80),
      height: moderateScale(80),
      borderRadius: moderateScale(40),
      backgroundColor: theme.colors.primary.main + '20',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: moderateVerticalScale(16),
    },
    title: {
      fontSize: moderateScale(theme.fontSize['xl']),
      fontWeight: '700',
      color: theme.colors.text.primary,
      marginBottom: moderateVerticalScale(8),
      textAlign: 'center',
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray[600],
      textAlign: 'center',
      marginBottom: moderateVerticalScale(8),
    },
    sectionTitle: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: moderateVerticalScale(16),
    },
    sectionSubtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: moderateVerticalScale(16),
    },
    macroGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: moderateVerticalScale(32),
    },
    macroCard: {
      width: '48%',
      borderRadius: moderateScale(theme.radii.lg),
      padding: moderateScale(16),
      marginBottom: moderateScale(16),
      alignItems: 'center',
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: moderateScale(4),
      elevation: 2,
      position: 'relative',
      overflow: 'hidden',
    },
    macroCardContent: {
      alignItems: 'center',
      width: '100%',
      zIndex: 1,
    },
    macroIconContainer: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderRadius: moderateScale(20),
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: moderateVerticalScale(8),
    },
    macroLabel: {
      fontSize: moderateScale(theme.fontSize.md),
      color: '#fff',
      marginBottom: moderateVerticalScale(8),
      fontWeight: '500',
    },
    macroValue: {
      fontSize: moderateScale(theme.fontSize['xl']),
      fontWeight: '700',
      color: '#fff',
    },
    macroUnit: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: 'rgba(255, 255, 255, 0.8)',
      marginTop: moderateVerticalScale(4),
    },
    editButton: {
      position: 'absolute',
      top: moderateScale(8),
      right: moderateScale(8),
      width: moderateScale(28),
      height: moderateScale(28),
      borderRadius: moderateScale(16),
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 2,
    },
    recommendationSection: {
      marginTop: moderateVerticalScale(16),
    },
    recommendationCard: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.lg),
      padding: moderateScale(16),
      marginBottom: moderateScale(16),
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: moderateScale(4),
      elevation: 2,
      borderLeftWidth: 4,
      borderLeftColor: theme.colors.primary.main,
    },
    recommendationIcon: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderRadius: moderateScale(20),
      backgroundColor: theme.colors.primary.main + '15',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: moderateScale(16),
    },
    recommendationText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.text.primary,
      flex: 1,
      fontWeight: '500',
    },
    submitButtonContainer: {
      marginBottom: moderateVerticalScale(24),
      overflow: 'hidden',
      borderRadius: moderateScale(theme.radii.full),
    },
    submitButton: {
      paddingVertical: moderateVerticalScale(16),
      alignItems: 'center',
    },
    submitButtonText: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: '600',
      color: theme.colors.secondary[50],
    },
    confettiContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: moderateScale(150),
      overflow: 'hidden',
      zIndex: -1,
    },
    confetti: {
      position: 'absolute',
      width: moderateScale(10),
      height: moderateScale(10),
      borderRadius: moderateScale(5),
    },
    progressContainer: {
      marginBottom: moderateVerticalScale(32),
      alignItems: 'center',
    },
    progressText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.primary.main,
      fontWeight: '600',
      marginTop: moderateVerticalScale(8),
    },
    progressBar: {
      height: moderateScale(8),
      width: '100%',
      backgroundColor: theme.colors.gray[200],
      borderRadius: moderateScale(4),
      marginTop: moderateVerticalScale(8),
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      width: '100%',
      borderRadius: moderateScale(4),
    },
  });
interface MacroCardProps {
  label: string;
  value: number;
  unit: string;
  icon: React.ReactNode;
  colors: [string, string]; 
  onEdit: () => void;
  animationValue: Animated.Value;
}

const MacroCard = ({ 
  label, 
  value, 
  unit, 
  icon, 
  colors, 
  onEdit,
  animationValue
}: MacroCardProps) => {
  const { theme } = useTheme();
  const styles = React.useMemo(() => createStyles(theme), [theme]);
  
  const scaleAnim = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.9, 1]
  });
  
  return (
    <Animated.View style={[
      styles.macroCard,
      { transform: [{ scale: scaleAnim }] }
    ]}>
      <LinearGradient
        colors={colors}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      <View style={styles.macroCardContent}>
        <View style={styles.macroIconContainer}>
          {icon}
        </View>
        <Text style={styles.macroLabel}>{label}</Text>
        <Text style={styles.macroValue}>{value}</Text>
        <Text style={styles.macroUnit}>{unit}</Text>
      </View>
      <TouchableOpacity 
        style={styles.editButton}
        onPress={onEdit}
      >
        <Edit2 size={16} color="#fff" />
      </TouchableOpacity>
    </Animated.View>
  );
};

const NutritionReviewStep: React.FC<NutritionReviewStepProps> = ({ 
  nutritionData, 
  onSubmit,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = React.useMemo(() => createStyles(theme), [theme]);
  
  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0)).current;
  const progressAnim = React.useRef(new Animated.Value(0)).current;
  const cardAnimations = React.useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0)
  ]).current;
  
  // Calculate target macros based on nutrition data
  const targetMacros = React.useMemo(() => 
    nutritionData ? getMacroPerDay(nutritionData) : { 
      calories: 0, 
      macros: { protein: 0, carbs: 0, fat: 0 } 
    }
  , [nutritionData]);
  
  // State for editable macro values
  const [macroValues, setMacroValues] = useState({
    calories: targetMacros.calories,
    protein: targetMacros.macros.protein,
    carbs: targetMacros.macros.carbs,
    fat: targetMacros.macros.fat
  });
  useEffect(()=> {
    trackUserInteraction('Nutrition Onboarding completed')
    AppEventsLogger.logEvent('completed_nutrition_form');
  })
  // Update macro values when target macros change
  useEffect(() => {
    setMacroValues({
      calories: targetMacros.calories,
      protein: targetMacros.macros.protein,
      carbs: targetMacros.macros.carbs,
      fat: targetMacros.macros.fat
    });
  }, [targetMacros]);
  
  // State for edit modal
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<{
    label: string;
    value: number;
    key: 'calories' | 'protein' | 'carbs' | 'fat';
    unit: string;
  } | null>(null);
  
  // Run animations on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
      }),
      ...cardAnimations.map((anim, index) => 
        Animated.timing(anim, {
          toValue: 1,
          duration: 600,
          delay: 300 + (index * 150),
          useNativeDriver: true,
        })
      )
    ]).start();
  }, []);
  
  const openEditModal = (label: string, value: number, key: 'calories' | 'protein' | 'carbs' | 'fat', unit: string) => {
    setCurrentEditItem({ label, value, key, unit });
    setEditModalVisible(true);
  };
  
  // Handle saving edited value
  const handleSaveEditedValue = (newValue: string) => {
    if (!currentEditItem) return;
    
    const numericMatch = newValue.match(/(\d+(\.\d+)?)/);
    const numericValue = numericMatch ? Math.round(parseFloat(numericMatch[0])) : 0;
    
    setMacroValues(prev => ({
      ...prev,
      [currentEditItem.key]: numericValue
    }));
    
    setEditModalVisible(false);
  };
  
  const setDailyGoals = useNutritionStore(state => state.setDailyGoals);

  const handleSubmit = async () => {
    setDailyGoals({
      calories: macroValues.calories,
      protein: macroValues.protein,
      carbs: macroValues.carbs,
      fat: macroValues.fat
    });
    
    const submissionData = {
      ...nutritionData,
      dailyCalories: macroValues.calories.toString(),
      dailyProtein: macroValues.protein.toString(),
      dailyCarbohydrates: macroValues.carbs.toString(),
      dailyFats: macroValues.fat.toString()
    };
    await onSubmit(submissionData);
  };
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Animated.View 
        style={[
          styles.celebrationContainer, 
          { opacity: fadeAnim, transform: [{ scale: scaleAnim }] }
        ]}
      >
        <View style={styles.awardCircle}>
          <Award size={40} color={theme.colors.primary.main} />
        </View>
        <Text style={styles.title}>{t('nutritionReview.congratulations')}</Text>
        <Text style={styles.subtitle}>{t('nutritionReview.subtitle')}</Text>
      </Animated.View>
      
      <Animated.View 
        style={[
          styles.submitButtonContainer,
          { opacity: fadeAnim, transform: [{ translateY: fadeAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0]
          })}] }
        ]}
      >
        <LinearGradient
          colors={[theme.colors.primary.main, theme.colors.primary.main]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={StyleSheet.absoluteFill}
        />
        <TouchableOpacity 
          style={styles.submitButton} 
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>{t('nutritionReview.submitButton')}</Text>
          )}
        </TouchableOpacity>
      </Animated.View>
      <Text style={styles.sectionTitle}>{t('nutritionReview.dailyTargetsTitle')}</Text>
      
      <View style={styles.macroGrid}>
        {/* Calories Card */}
        <MacroCard
          label={t('nutritionReview.macroLabels.calories')}
          value={macroValues.calories}
          unit="kcal"
          icon={<Flame size={20} color="#fff" />}
          colors={['#FF6B6B', '#FF8E8E']}
          onEdit={() => openEditModal(t('nutritionReview.calories'), macroValues.calories, 'calories', 'kcal')}
          animationValue={cardAnimations[0]}
        />
        
        {/* Carbs Card */}
        <MacroCard
          label={t('nutritionReview.macroLabels.carbs')}
          value={macroValues.carbs}
          unit="g"
          icon={<Wheat size={20} color="#fff" />}
          colors={['#4ECDC4', '#26A69A']}
          onEdit={() => openEditModal(t('nutritionReview.carbs'), macroValues.carbs, 'carbs', 'g')}
          animationValue={cardAnimations[1]}
        />
        
        {/* Protein Card */}
        <MacroCard
          label={t('nutritionReview.macroLabels.protein')}
          value={macroValues.protein}
          unit="g"
          icon={<Beef size={20} color="#fff" />}
          colors={['#6C63FF', '#8F87FF']}
          onEdit={() => openEditModal(t('nutritionReview.protein'), macroValues.protein, 'protein', 'g')}
          animationValue={cardAnimations[2]}
        />
        
        {/* Fat Card */}
        <MacroCard
          label={t('nutritionReview.macroLabels.fats')}
          value={macroValues.fat}
          unit="g"
          icon={<Droplet size={20} color="#fff" />}
          colors={['#FFD166', '#FFC233']}
          onEdit={() => openEditModal(t('nutritionReview.fats'), macroValues.fat, 'fat', 'g')}
          animationValue={cardAnimations[3]}
        />
      </View>
      
      <Animated.View 
        style={[
          styles.recommendationSection, 
          { opacity: fadeAnim, transform: [{ translateY: fadeAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0]
          })}] }
        ]}
      >
        <Text style={styles.sectionTitle}>{t('nutritionReview.recommendations.title')}</Text>
        
        {/* Recommendation Cards */}
        <View style={styles.recommendationCard}>
          <View style={styles.recommendationIcon}>
            <BarChart3 size={20} color={theme.colors.primary.main} />
          </View>
          <Text style={styles.recommendationText}>
            {t('nutritionReview.recommendations.healthScores')}
          </Text>
        </View>
        
        <View style={styles.recommendationCard}>
          <View style={styles.recommendationIcon}>
            <Utensils size={20} color={theme.colors.primary.main} />
          </View>
          <Text style={styles.recommendationText}>
            {t('nutritionReview.recommendations.trackFood')}
          </Text>
        </View>
        
        <View style={styles.recommendationCard}>
          <View style={styles.recommendationIcon}>
            <CalendarClock size={20} color={theme.colors.primary.main} />
          </View>
          <Text style={styles.recommendationText}>
            {t('nutritionReview.recommendations.followCalories')}
          </Text>
        </View>
        
        <View style={styles.recommendationCard}>
          <View style={styles.recommendationIcon}>
            <Scale size={20} color={theme.colors.primary.main} />
          </View>
          <Text style={styles.recommendationText}>
            {t('nutritionReview.recommendations.balanceMacros')}
          </Text>
        </View>
      </Animated.View>
      
      
      
      {/* Edit Value Modal */}
      <EditValueModal
        visible={editModalVisible}
        onClose={() => setEditModalVisible(false)}
        onSave={handleSaveEditedValue}
        initialValue={currentEditItem?.value.toString() || ""}
        label={currentEditItem?.label || ""}
        unit={currentEditItem?.unit}
      />
    </ScrollView>
  );
};

export default NutritionReviewStep;