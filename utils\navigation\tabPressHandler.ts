import { Platform } from 'react-native';
import { useChatStore } from '@/store/chatStore';
import { useScrollStore } from '@/store/scrollStore';
import logger from '@/utils/logger/logger';

export const tabPressHandler = (tabName: string) => {
  try {
    const currentScreen = global.currentScreen;
    logger.info(`Tab press handled for ${tabName}, current screen: ${currentScreen}`);

    if (currentScreen?.includes(tabName)) {
      switch (tabName) {
        case 'chat':
          const store = useChatStore.getState();
          if (store.chatListRef?.current) {
            store.chatListRef.current.scrollToOffset({ offset: 0, animated: true });
          }
          break;

        case 'discover':
          const discoverRef = useScrollStore.getState().discoverRef;
          if (discoverRef?.current) {
            discoverRef.current.scrollTo({ y: 0, animated: true });
          }
          break;

        case 'nutrition':
          const nutritionRef = useScrollStore.getState().nutritionRef;
          if (nutritionRef?.current) {
            nutritionRef.current.scrollTo({ y: 0, animated: true });
          }
          break;

        case 'personalize':
          const personalizeRef = useScrollStore.getState().personalizeRef;
          if (personalizeRef?.current) {
            personalizeRef.current.scrollTo({ y: 0, animated: true });
          }
          break;
      }
    }
  } catch (error) {
    logger.error('Error in tabPressHandler:', error);
  }
};





