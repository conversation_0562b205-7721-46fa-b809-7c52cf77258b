import React, { createContext, useContext, useState, useMemo } from 'react';
import { Theme } from './types';
import { themes } from "./defaultTheme";

type ThemeContextType = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const ThemeContext = createContext<ThemeContextType>({
  theme: themes.default,
  setTheme: () => {},
});


export const useTheme = () => useContext(ThemeContext);

type ThemeProviderProps = {
  children: React.ReactNode;
  initialTheme?: Theme;
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = themes.default,
}) => {
  const [theme, setTheme] = useState<Theme>(initialTheme);

  const themeValue = useMemo(() => ({
    theme,
    setTheme,
  }), [theme]);

  return (
    <ThemeContext.Provider value={themeValue}>
      {children}
    </ThemeContext.Provider>
  );
};


