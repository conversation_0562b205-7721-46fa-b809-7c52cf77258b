import React from "react";
import { Text, TextStyle } from "react-native";
// import { colors } from "@/constants/colors";

interface StyledTextProps {
  text: string;
  style: TextStyle | TextStyle[];
  highlightColor?: string;
  highlightBackground?: boolean;
  highlightHeight?: number;
  highlightOpacity?: number;
}

export const StyledText: React.FC<StyledTextProps> = ({
  text,
  style,
  highlightColor = "#b6ffbb",
  highlightBackground = false,
  highlightHeight = 2,
  highlightOpacity = 1,
}) => {
  // Process the text to handle both bold and highlight tags
  const processText = () => {
    // First split by bold tags
    const parts = text.split(/(<b>.*?<\/b>|<highlight>.*?<\/highlight>)/g);

    return parts.map((part, index) => {
      // Check if this is a bold tag
      if (part.startsWith("<b>") && part.endsWith("</b>")) {
        const content = part.substring(3, part.length - 4);
        return (
          <Text key={`bold-${index}`} style={[style, { fontWeight: "bold" }]}>
            {content}
          </Text>
        );
      }

      // Check if this is a highlight tag
      if (part.startsWith("<highlight>") && part.endsWith("</highlight>")) {
        const content = part.substring(11, part.length - 12);

        if (highlightBackground) {
          // Create the highlight effect with background
          const bgColor = highlightColor
            ? `rgba(${parseInt(highlightColor.slice(1, 3), 16)}, 
                    ${parseInt(highlightColor.slice(3, 5), 16)}, 
                    ${parseInt(highlightColor.slice(5, 7), 16)}, 
                    ${highlightOpacity})`
            : highlightColor;

          return (
            <Text
              key={`highlight-${index}`}
              style={[
                style,
                {
                  backgroundColor: bgColor,
                  borderRadius: 80, // Increased border radius for more rounded corners
                  paddingHorizontal: 40,
                  paddingVertical: 20, // Added vertical padding for better appearance
                  overflow: "hidden", // Ensures content respects the border radius
                  marginHorizontal: 10, // Small margin to prevent touching adjacent text
                },
              ]}
            >
              {content}
            </Text>
          );
        } else {
          // Just change the text color
          return (
            <Text
              key={`highlight-${index}`}
              style={[style, { color: highlightColor }]}
            >
              {content}
            </Text>
          );
        }
      }

      // Regular text
      return part ? (
        <Text key={`regular-${index}`} style={style}>
          {part}
        </Text>
      ) : null;
    });
  };

  return <Text style={style}>{processText()}</Text>;
};








