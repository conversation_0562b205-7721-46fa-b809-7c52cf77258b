import type React from "react"
import { Children, cloneElement, isValidElement, useMemo } from "react"
import { View, StyleSheet } from "react-native"
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";


interface FormStepperProps {
  children: React.ReactNode
  currentStep: number
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
  });

export const FormStepper: React.FC<FormStepperProps> = ({ children, currentStep }) => {
  const childrenArray = Children.toArray(children)
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.container}>
      {Children.map(childrenArray, (child, index) => {
         if (isValidElement(child) && index === currentStep) {
          return cloneElement(child, {
            ...child.props,
            isActive: true,
          })
        }
        return null
      })}
    </View>
  )
}

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
// })

