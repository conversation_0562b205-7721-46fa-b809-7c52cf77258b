import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  Image,
  ActivityIndicator,
  PanResponder,
  Animated,
  Dimensions,
  GestureResponderEvent,
  TouchableOpacity,
  Text, 
  Linking
} from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';
import { WebView } from 'react-native-webview';
import { Download, X } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import logger from '@/utils/logger/logger';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';

interface MediaViewerModalProps {
  visible: boolean;
  uri: string;
  type: 'image' | 'pdf';
  onClose: () => void;
  signedUrl?: string; // Optional signed URL for PDFs
  serverUrl?: string; // Optional server URL for PDFs as fallback
}

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get('window');
const DISMISS_THRESHOLD = SCREEN_HEIGHT * 0.15; // 15% of screen height
const VELOCITY_THRESHOLD = 0.7; // Velocity threshold for quick flick

// X button dimensions and position
const X_BUTTON_SIZE = moderateScale(48);
const X_BUTTON_PADDING = moderateScale(12);
const X_BUTTON_TOP = moderateVerticalScale(50);
const X_BUTTON_RIGHT = moderateScale(20);


const MediaViewerModal: React.FC<MediaViewerModalProps> = ({ visible, uri, type, onClose, signedUrl, serverUrl }) => {
  // Use signed URL if available, otherwise try serverUrl, then fall back to regular URI
  const displayUrl = (type === 'pdf' && signedUrl) ? signedUrl :
    (type === 'pdf' && serverUrl) ? serverUrl :
      uri;

  // Log the URLs for debugging
  if (type === 'pdf') {
    logger.info('MediaViewerModal PDF URLs:', { uri, signedUrl, displayUrl });
  }
  const [isLoading, setIsLoading] = useState(true);

  // For PDF viewer
  const [touchStartY, setTouchStartY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const pan = useRef(new Animated.ValueXY()).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const scale = scaleValue;

  // Reset animations when modal becomes visible (for PDF viewer)
  useEffect(() => {
    if (visible && type === 'pdf') {
      // Reset all animated values when modal opens
      pan.setValue({ x: 0, y: 0 });
      scaleValue.setValue(1);
      opacityValue.setValue(1);
    }
  }, [visible, pan, scaleValue, opacityValue, type]);

  // Check if a touch is within the X button area (for PDF viewer)
  const isInXButtonArea = (x: number, y: number) => {
    const buttonArea = {
      left: SCREEN_WIDTH - X_BUTTON_RIGHT - X_BUTTON_SIZE,
      right: SCREEN_WIDTH - X_BUTTON_RIGHT,
      top: X_BUTTON_TOP,
      bottom: X_BUTTON_TOP + X_BUTTON_SIZE,
    };

    // Add a large buffer (40px) for easier tapping
    return (
      x >= buttonArea.left - 20 &&
      x <= buttonArea.right + 20 &&
      y >= buttonArea.top - 20 &&
      y <= buttonArea.bottom + 20
    );
  };

  // PanResponder for PDF viewer
  const panResponder = useRef(
    PanResponder.create({
      // Always capture touch events to ensure we get priority
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to significant vertical movement
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      onMoveShouldSetPanResponderCapture: (_, gestureState) => {
        // Capture vertical movements with higher priority
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },

      // Handle the gesture movement
      onPanResponderGrant: (e, gestureState) => {
        setTouchStartY(e.nativeEvent.pageY);
        setIsDragging(true);
        pan.flattenOffset();
        pan.extractOffset();
      },

      onPanResponderMove: (_, gestureState) => {
        pan.setValue({ x: gestureState.dx, y: gestureState.dy });

        // Calculate progress for scale and opacity
        const progress = Math.min(Math.abs(gestureState.dy) / (SCREEN_HEIGHT * 0.5), 1);
        scaleValue.setValue(1 - (0.2 * progress));
        opacityValue.setValue(1 - (0.7 * progress));
      },

      onPanResponderRelease: (e, gestureState) => {
        pan.flattenOffset();
        setIsDragging(false);

        // Check if this was a tap (minimal movement) or a swipe
        const isATap = Math.abs(gestureState.dx) < 5 && Math.abs(gestureState.dy) < 5;

        if (isATap) {
          // Get the touch coordinates
          const { locationX, locationY } = e.nativeEvent;
          logger.info('Tap detected at', locationX, locationY);

          // Check if tap is in X button area
          if (isInXButtonArea(locationX, locationY)) {
            logger.info('MediaViewerModal X button area tapped');
            onClose();
            return;
          }
        }

        // Determine if we should dismiss based on distance or velocity
        const shouldDismiss =
          Math.abs(gestureState.dy) > DISMISS_THRESHOLD ||
          Math.abs(gestureState.vy) > VELOCITY_THRESHOLD;

        if (shouldDismiss) {
          Animated.timing(pan, {
            toValue: {
              x: 0,
              y: gestureState.dy > 0 ? SCREEN_HEIGHT : -SCREEN_HEIGHT
            },
            duration: 200,
            useNativeDriver: false, // Set to false to allow opacity animation
          }).start(() => {
            onClose();
          });
        } else {
          // Animate back to original position with spring physics
          Animated.spring(pan, {
            toValue: { x: 0, y: 0 },
            tension: 40,
            friction: 7,
            useNativeDriver: false, // Set to false to allow opacity animation
          }).start();
        }
      },

      onPanResponderTerminate: (_, gestureState) => {
        pan.flattenOffset();
        setIsDragging(false);
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          tension: 40,
          friction: 7,
          useNativeDriver: false, // Set to false to allow opacity animation
        }).start();
      },
    })
  ).current;

  // Create interpolated background color for PDF viewer
  const animatedBackgroundStyle = {
    backgroundColor: opacityValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(0,0,0,0)', 'rgba(0,0,0,0.9)'],
    }),
  };

  if (type === 'image') {
    return (
      <Modal
        visible={visible}
        transparent={true}
        animationType="fade"
        onRequestClose={onClose}
      >
        <View style={{ flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.9)' }}>
          <ImageViewer
            imageUrls={[{ url: displayUrl }]}
            index={0}
            enableSwipeDown
            onSwipeDown={onClose}
            enableImageZoom
            onCancel={onClose}
            backgroundColor="transparent"
            style={{ width: '100%', height: '100%' }} // Ensure it takes full size
            renderIndicator={() => <Text>{''}</Text>}
            renderHeader={() => (
              <View style={{
                position: 'absolute',
                top: 40,
                left: 20,
                right: 20,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                zIndex: 100,
              }}>
                <TouchableOpacity
                  onPress={() => {
                    Linking.openURL(displayUrl).catch((err) => {
                      logger.error("Error opening PDF in external app:", err);
                    });
                  }}
                  style={{ marginRight: 16 }}
                >
                  <Download size={24} color="#fff"></Download>
                </TouchableOpacity>
                <TouchableOpacity onPress={onClose}>
                  <X size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            )}
          />
        </View>
      </Modal>
    );
  }


  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      {/* Main container with gesture handling */}
      <Animated.View
        style={[styles.modalBackground, animatedBackgroundStyle]}
        {...panResponder.panHandlers}
      >
        {/* X button visual indicator (not touchable) */}
        <View style={styles.xButtonContainer}>
          <View style={styles.xButton}>
            <X size={24} color="#fff" />
          </View>
        </View>

        {/* Main content container with animations */}
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale }
              ]
            }
          ]}
        >
          {/* Content */}
          <View style={styles.contentContainer}>
            <WebView
              source={{
                uri: `https://docs.google.com/viewer?url=${encodeURIComponent(displayUrl)}&embedded=true`,
              }}
              style={styles.webview}
              onLoadEnd={() => setIsLoading(false)}
              onError={(error) => {
                logger.error('MediaViewerModal WebView error:', error.nativeEvent);
              }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              scalesPageToFit={true}
            />
          </View>

          {/* Loading indicator */}
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    width: '100%',
    paddingTop: X_BUTTON_TOP,
    paddingBottom: X_BUTTON_TOP, // Assuming same vertical scale
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    overflow: 'hidden',
  },
  xButtonContainer: {
    position: 'absolute',
    top: X_BUTTON_TOP,
    right: X_BUTTON_RIGHT,
    width: X_BUTTON_SIZE,
    height: X_BUTTON_SIZE,
    zIndex: 10,
    pointerEvents: 'none', // Make this non-interactive
  },
  xButton: {
    width: X_BUTTON_SIZE,
    height: X_BUTTON_SIZE,
    borderRadius: X_BUTTON_SIZE / 2,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: X_BUTTON_PADDING,
  },
  image: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MediaViewerModal;
