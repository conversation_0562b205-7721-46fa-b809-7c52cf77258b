export default {
  expo: {
    name: "August",
    slug: "august-mobile-app",
    version: "1.2.6",
    owner: "august-ai",
    orientation: "portrait",
    scheme: "myapp",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    splash: {
      image: "./assets/images/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#206E55",
      animationDuration: 1000,
      dark: {
        image: "./assets/images/splash-icon.png",
        backgroundColor: "#206E55"
      }
    },
    notification: {
      icon: "./assets/images/a-white.png",
      color: "#206E55",
      androidMode: "collapse",
      androidCollapsedTitle: "August",
      iosDisplayInForeground: true,
      androidNotificationIcon: "./assets/images/a-transparent.png",
      androidNotificationColor: "#206E55",
      androidNotificationChannelId: "august-notifications",
      androidNotificationChannelName: "August"
    },
    android: {
      package: "com.augustai.mobileapp",
      targetSdkVersion: 35,
      googleServicesFile: "./google-services.json",
      icon: "./assets/images/adaptive-icon.png",
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      androidNavigationBar: {
        backgroundColor: "#FFFFFF", // White color
      },      
      config: {
        cleartextTraffic: false
      },
      permissions: [
        "INTERNET",
        "ACCESS_NETWORK_STATE",
        "NOTIFICATIONS",
        "RECEIVE_BOOT_COMPLETED",
        "SCHEDULE_EXACT_ALARM",
        "USE_FULL_SCREEN_INTENT", 
        "ACCESS_WIFI_STATE"
      ],
       jsEngine: "hermes"
    },
    "ios": {
      "infoPlist": {
        "ITSAppUsesNonExemptEncryption": false,
        "NSCameraUsageDescription": "August uses the camera to let you take photos of meals, lab-reports, prescriptions and any other health related images that you want to share for your health concerns.",
        "NSPhotoLibraryUsageDescription": "August accesses your photo library so you can upload images or pdfs of meals, lab-reports, prescriptions and any other health related images that you want to share for your health concerns.",
         "NSMicrophoneUsageDescription": "August accesses your microphone to record audio and send voice notes"
      },
      icon: {
        "dark": "./assets/images/adaptive-icon.png",
        "light": "./assets/images/adaptive-icon.png",
      },
      supportsTablet: true,
      bundleIdentifier: "com.augustai.ios",
      "buildNumber": "9",
       jsEngine: "hermes"
    },
    plugins: [
      [
        "expo-network-security-config",
        {
          "pinsets": [
            {
              "name": "meetaugust",
              "pins": [
                "sha256/qoE8g92hCoxaOhm0ySA+ro8T7+X0dc4bTSNqSUJ4G2I="
              ]
            },
            {
              "name": "beyondhealth",
              "pins": [
                "sha256/RGcnpK9yF/q1o7pg7R0+AO+4o/Px4nNfY6d70A+8k2E="
              ]
            }
          ],
          "domainConfig": [
            {
              "domain": "meetaugust.ai",
              "pinSet": "meetaugust",
              "includeSubdomains": true
            },
            {
              "domain": "getbeyondhealth.com",
              "pinSet": "beyondhealth",
              "includeSubdomains": true
            }
          ]
        }
      ],
      [
        "./plugins/withFacebookSDK.js",
        {
          "appId": "2002225050518699",
          "appName": "August Mobile",
          "clientToken": "6fe09543d4da5aeafbbc211f7484f8d2"
        }
      ],
      [
        "expo-build-properties",
        {
          android: {
            targetSdkVersion: 35
          }
        }
      ],
      "expo-localization",
      "expo-router",
      "expo-audio",
      "expo-av", 
      "expo-secure-store",
      "./withCustomAndroidManifest.js",
      ["expo-notifications", {
        iosDisplayInForeground: true,
        androidMode: "default"
      }],
      ["react-native-appsflyer", {
        devKey: "uVpTeB7n2rHoxLwPKxvoEW",
        appId: "**********",
        isDebug: true
      }]
    ],    
    experiments: {
      typedRoutes: true
    },
    extra: {
      eas: {
        projectId: "de365926-a5da-43d5-94a1-a3b9a3ff8cbf"
      },
      GATEKEEPER_URL: "https://api.getbeyondhealth.com",
      TENANT: "august",
    },
    updates: {
      url: "https://u.expo.dev/de365926-a5da-43d5-94a1-a3b9a3ff8cbf",
      enabled: true,
      checkAutomatically: "ON_LOAD",
      fallbackToCacheTimeout: 0
    },
    // runtimeVersion: "1.0.0",
    runtimeVersion: {
      policy: "appVersion"
    }
  }
}