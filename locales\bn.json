{"common": {"error": "ত্রুটি", "yes": "হ্যাঁ", "no": "না", "sometimes": "মাঝে মাঝে", "close": "বন্ধ করুন", "cancel": "বাতিল কর<PERSON>ন", "save": "সংরক্ষণ করুন", "next": "পরবর্তী", "loading": "লোড হচ্ছে...", "version": "v0.0.1.7"}, "welcome": "অগাস্টের সাথে কথা বলা শুরু করতে লগইন করুন", "notFound": {"title": "ওপ্স!", "message": "এই স্ক্রিনটি বিদ্যমান নেই।", "goHome": "হোম স্ক্রিনে যান!"}, "library": {"title": "স্বাস্থ্য লাইব্রেরি"}, "specialists": {"title": "বিশেষজ্ঞরা", "description": "আরও নির্দিষ্ট স্বাস্থ্য সমস্যা সম্পর্কে বিশেষজ্ঞ স্বাস্থ্য পেশাদারদের সাথে পরামর্শ করুন। নীচে একটি বিশেষজ্ঞ নির্বাচন করুন:", "generalPhysician": {"title": "সাধারণ চিকিৎসক", "description": "সাধারণ স্বাস্থ্য সমস্যা এবং প্রাথমিক চিকিৎসার জন্য।"}, "nutritionist": {"title": "পুষ্টিবিদ", "description": "আ<PERSON><PERSON>র, পুষ্টি এবং ওজন ব্যবস্থাপনা পরামর্শের জন্য।"}, "cardiologist": {"title": "হৃদরোগ বিশেষজ্ঞ", "description": "হৃদয় সম্পর্কিত উদ্বেগ এবং হৃদরোগের জন্য।"}, "neurologist": {"title": "স্নায়ুবিশেষজ্ঞ", "description": "মস্তিষ্ক, মেরুদণ্ড এবং স্নায়ুতন্ত্রের সমস্যার জন্য।"}, "oncologist": {"title": "অনকোলজিস্ট", "description": "ক্যান্সার সম্পর্কিত উদ্বেগ এবং চিকিৎসার জন্য।"}, "endocrinologist": {"title": "এন্ডোক্রিনোলজিস্ট", "description": "হরমোন সম্পর্কিত ব্যাধি এবং ডায়াবেটিস ব্যবস্থাপনার জন্য।"}, "dermatologist": {"title": "ত্বকরোগ বিশেষজ্ঞ", "description": "ত্বক, চুল এবং নখের অবস্থার জন্য।"}, "psychiatrist": {"title": "মানসিক চিকিৎসক", "description": "মানসিক স্বাস্থ্য সমস্যা এবং মানসিক সুস্থতার জন্য।"}}, "profile": {"title": "প্রোফাইল", "defaultName": "অতিথি", "namePlaceholder": "আপন<PERSON>র নাম লিখুন", "saving": "সংরক্ষণ করা হচ্ছে...", "noPhoneNumber": "কোন ফোন নম্বর নেই", "loggingOut": "লগ আউট করা হচ্ছে...", "about": {"title": "সম্পর্কে", "description": "অগাস্ট সম্পর্কে আরও জানুন"}, "whatsapp": {"title": "WhatsApp", "description": "WhatsApp এ অগাস্টের সাথে চ্যাট করুন"}, "refer": {"title": "রেফার করুন", "description": "অগাস্ট পছন্দ করেছেন? আপনার বন্ধুদের সাথে শেয়ার করুন"}, "deleteAccount": {"title": "অ্যাকাউন্ট মুছে ফেলুন", "description": "আপনাকে বিদায় জানাতে আমরা দুঃখিত"}, "logout": {"title": "লগ আউট", "description": "শীঘ্রই ফিরে আসুন। আম<PERSON>া আপনাকে মিস করব"}, "shareMessage": "👋হেই, আমি যে অসাধারণ অ্যাপটি ব্যবহার করছি তা দেখুন!\n\n\n\n➡️দ্রুত, নির্ভরযোগ্য স্বাস্থ্য তথ্য এবং নির্দেশনা পেতে আমি অগাস্ট ব্যবহার করছি। এটি যেন আপনার পকেটে একজন ডাক্তার! এখানে দেখুন:", "error": {"loadFailed": "ব্যবহারকার<PERSON>র তথ্য লোড করতে ব্যর্থ হয়েছে", "fetchError": "ব্যব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> তথ্য আনতে একটি ত্রুটি হয়েছে", "updateNameFailed": "নাম আপডেট করতে ব্যর্থ হয়েছে", "updateNameError": "নাম আপডেট করার সময় একটি ত্রুটি হয়েছে", "loadFoodData": "খাবারের তথ্য লোড করতে ব্যর্থ হয়েছে", "logoutError": "লগ আউট করার সময় ত্রুটি:", "shareError": "বার্তা শেয়ার করার সময় ত্রুটি:"}}, "error": {"title": "কিছু ভুল হয়েছে", "checkLogs": "আরও বিস্তারিত জানার জন্য দয়া করে আপনার ডিভাইসের লগগুলি পরীক্ষা করুন।", "unknown": "অজানা ত্রুটি", "unknownFile": "অজানা ফাইল", "unknownLine": "অজানা লাইন", "unknownColumn": "অজানা কলাম"}, "auth": {"phone": {"selectCountry": "দেশ নির্বাচন করুন", "searchCountries": "দেশ অনুসন্ধান করুন", "validation": {"invalidPhone": "অনুগ্রহ করে একটি বৈধ ফোন নম্বর প্রবেশ করান", "invalidDigits": "অনুগ্রহ করে একটি বৈধ ফোন নম্বর প্রবেশ করান (7-15 সংখ্যা)"}}, "header": {"title": "আপনার স্বাস্থ্য সম্পর্কিত উদ্বেগগুলির স্পষ্টতা দ্রুত এবং ব্যক্তিগতভাবে পান", "subtitle": "চিন্তাশীল নির্দেশনা। কোনো তাড়াহুড়ো নেই। কোনো বিভ্রান্তি নেই।", "emphasis": "শুধু স্পষ্টতা।"}, "greeting": "হাই 👋", "phoneNumber": "ফোন নম্বর", "requestOTP": "OTP অনুরোধ করুন", "otp": {"title": "ওয়ান-টাইম পাসওয়ার্ড", "verify": "OTP যাচাই করুন", "sending": "প্রেরণ করা হচ্ছে...", "countdown": "{{countdown}} সেকেন্ডের মধ্যে OTP পুনঃপ্রেরণ করুন", "resend": "OTP পুনঃপ্রেরণ করুন", "sentTo": "OTP প্রেরণ করা হয়েছে ", "whatsappSuffix": " WhatsApp এ"}, "disclaimer": {"prefix": "সাই<PERSON> আপ করে, আ<PERSON><PERSON><PERSON> আমাদের ", "continuePrefix": "চালিয়ে যাওয়ার মাধ্যমে, আ<PERSON><PERSON><PERSON> আমাদের ", "termsOfService": "সেবা শর্ত<PERSON>বলী", "and": " এবং ", "privacyPolicy": "গোপনীয়তা নীতি", "whatsappConsent": "এবং WhatsApp এর মাধ্যমে আপডেট এবং অনুস্মারক পেতে সম্মতি দিন।"}}, "onboarding": {"preAuth": {"welcome": {"title": "অগাস্ট এ স্বাগতম!", "buttonText": "চলুন শুরু করা যাক"}}, "postAuth": {"step1": {"title": "হেই!", "subtitle": "আমি অগাস্ট 👋", "description": "আমাকে আপনার ডিভাইসের আরামদায়ক কোণার মতো ভাবুন যেখানে আপনি আপনার সকল স্বাস্থ্য সংক্রান্ত কৌতূহল অন্বেষণ করেন।", "subdescription": "মনে হওয়া সবকিছু জিজ্ঞাসা করতে দ্বিধা করবেন না। কোনো বিচার নেই, কোনো সীমা নেই!", "placeholder": "আমি আপনাকে কী ডাকব?"}, "step2": {"title": "হাই {{userName}},", "subtitle": "এখানে আমি কী করতে পারি:", "features": {"health": {"title": "উত্তর দিন আপনার", "subtitle": "স্বাস্থ্য সংক্রান্ত প্রশ্নের"}, "nutrition": {"title": "ট্র্যাক করুন সেই", "subtitle": "ম্যাক্র<PERSON>গুলি"}, "reports": {"title": "বিশ্লেষণ করুন", "subtitle": "প্রতিবেদন"}}}}, "pills": {"thoughtful": "চিন্ত<PERSON><PERSON>ীল", "careful": "সাবধান", "accurate": "সঠিক"}, "features": {"symptoms": {"title": "আপনার লক্ষণগুলি পরীক্ষা করুন", "description": "আমি এক সপ্তাহ ধরে বমি বমি ভাব অনুভব করছি। আমার সাথে কী হচ্ছে?"}, "prescriptions": {"title": "আপনার প্রেসক্রিপশনগুলি বিশ্লেষণ করুন", "description": "ডাক্তারের মতো প্রেসক্রিপশন আপলোড করুন এবং বুঝুন।"}, "medicine": {"title": "আপনার ওষুধ সম্পর্কে জানুন", "description": "আমার PCOS এর জন্য মেটফর্মিন আমার ADHD পিলসের সাথে ইন্টারঅ্যাক্ট করে কি?"}, "plans": {"title": "ব্যক্তিগতকৃত পরিকল্পনা পান", "description": "আমার HbA1c লেভেল কমাতে আপনি কি আমাকে পুষ্টি ও ফিটনেস পরিকল্পনা দিতে পারেন?"}}, "buttons": {"getStarted": "শুর<PERSON> করুন", "next": "পরবর্তী"}, "errors": {"nameRequired": "অনুগ্রহ করে আপনার নাম প্রবেশ করান"}}, "tabs": {"chat": "চ্যাট", "discover": "আবিষ্কার", "nutrition": "পুষ্টি", "personalize": "ব্যক্তিগতকরণ"}, "chat": {"nav": {"title": "অগাস্ট"}, "me": "আম<PERSON>", "augustName": "অগাস্ট", "input": {"placeholder": "অগাস্ট কে জিজ্ঞাসা করুন...", "disclaimer": "অগাস্ট ভুল করতে পারে।  ডাক্তারের সাথে নিশ্চিত করুন"}, "list": {"loadingMessages": "বার্তা লোড হচ্ছে...", "noMessages": "এখন<PERSON> কোন বার্তা নেই।  আলা<PERSON> শুরু করুন!"}, "connection": {"offlineMessage": "মনে হচ্ছে আপনি অফলাইনে আছেন। বার্তা পাঠাতে পুনরায় সংযোগ স্থাপন করুন।", "connecting": "সংযোগ স্থাপন করছে...", "tryAgain": "পুনরায় চেষ্টা করুন"}, "prompts": {"uploadReport": "রিপোর্ট আপলোড করুন", "speakInHindi": "হিন্দিতে কথা বলুন", "notFeelingWell": "আমি ভালো বোধ করছি না", "whatIsMyBMI": "আমার BMI কত?", "nutritionAdvice": "পুষ্টি পরামর্শ", "sleepBetter": "ভালো ঘুমোন"}, "citations": {"referenceText": "এই আলাপচারিতার বিস্তারিত জানার জন্য, দয়া করে দেখুন:"}, "actions": {"copiedToClipboard": "ক্লিপবোর্ডে কপি করা হয়েছে", "copied": "কপি করা হয়েছে"}, "share": {"introText": "👋হেই, দেখুন আমি অগাস্টের সাথে কী আলাপ করেছি:\n\n", "downloadText": "\n\n➡️আপনার বন্ধুত্বপূর্ণ AI স্বাস্থ্য সঙ্গী অগাস্টের সাথে আলাপ করার জন্য ডাউনলোড করুন:\n"}}, "discover": {"nav": {"title": "আবিষ্কার করুন"}, "categories": {"all": "সব", "heartHealth": "হৃদরোগের স্বাস্থ্য", "nutrition": "পুষ্টি", "mentalHealth": "মানসিক স্বাস্থ্য", "fitness": "ফিটনেস", "wellness": "সুস্বাস্থ্য"}, "cards": {"empty": "এই বিভাগের জন্য কোন কার্ড পাওয়া যায়নি"}, "sections": {"features": "বৈশিষ্ট্য"}, "features": {"healthLibrary": {"title": "স্বাস্থ্য লাইব্রেরি", "description": "সম্পূর্ণ বিনামূল্যে বিশ্বস্ত, নির্ভরযোগ্য এবং সর্বশেষতম চিকিৎসা তথ্য অ্যাক্সেস করুন।"}, "nutritionTracker": {"title": "পুষ্টি ট্র্যাকার", "description": "কখনো ভেবেছেন কি আপনি শুধু আপনার খাবারের একটি ছবি আপলোড করে আপনার সমস্ত পুষ্টির লক্ষ্য ট্র্যাক করতে পারেন? অগাস্ট তাই করতে পারে!"}, "multilingualSupport": {"title": "বহুভাষী সহায়তা", "description": "আপনি যে কোন ভাষায় অগাস্টের সাথে যোগাযোগ করতে পারেন যাতে আপনি আরামদায়ক বোধ করেন! অগাস্ট সবসময় আপনাকে শুনতে, সাহায্য করতে এবং প্রয়োজন হলে আপনার সাথে সাড়া দিতে এখানে আছে।", "samplePrompt": "হিন্দিতে কথা বলুন"}, "labReportAnalysis": {"title": "ল্যাব রিপোর্ট বিশ্লেষণ", "description": "যখন আপনি অগাস্টের সাথে আপনার ল্যাব রিপোর্ট সম্পর্কে কথা বলেন, তখন আপনি অত্যন্ত নির্ভুলতা পান। অগাস্ট ৯৮.৪% বায়োমার্কার এক্সট্রাকশন সঠিকতার সাথে ৪.৭ মিলিয়নেরও বেশি রিপোর্ট প্রক্রিয়া করেছে।"}}}, "nutrition": {"nav": {"title": "পুষ্টি"}, "meals": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> খাবার", "subtitle": "প্রতিটি খাবারের ম্যাক্রো দেখতে ট্যাপ করুন"}, "upload": {"loading": "ছবি আপলোড হচ্ছে..."}, "defaultFoodName": "খাদ্য পণ্য", "today": "আজ", "unknownTime": "অজানা সময়", "calories": "🔥 ক্যালোরি", "proteins": "🥩 প্রোটিন", "carbs": "🍞 কার্বোহাইড্রেট", "sugars": "🍬 চিনি", "fat": "🥑 ফ্যাট", "caloriesLabel": "ক্যালোরি", "proteinLabel": "প্রোটিন", "carbohydratesLabel": "কার্বোহাইড্রেট", "fatLabel": "ফ্যাট", "sugarLabel": "চিনি", "tips": "টিপস:", "macroBreakdown": "ম্যাক্রোনিউট্রিয়েন্ট বিশ্লেষণ", "noMacroData": "এই খাদ্য পণ্যের জন্য কোন ম্যাক্রোনিউট্রিয়েন্ট তথ্য পাওয়া যায়নি।", "disclaimer": "শুধুমাত্র শিক্ষামূলক ব্যবহারের জন্য। আরও জানুন", "disclaimerLink": "এখানে", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "আপনার লিঙ্গ কি?", "subtitle": "এটি আপনার কাস্টম প্ল্যান ক্যালিব্রেট করতে ব্যবহার করা হবে।", "male": "পুরুষ", "female": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "অন্যান্য"}, "age": {"title": "আপনার বয়স কত?", "subtitle": "এটি আপনার দৈনিক চাহিদা গণনা করতে ব্যবহার করা হবে।"}, "measurements": {"title": "উচ্চতা ও ওজন", "subtitle": "দয়া করে আপনার উচ্চতা সেন্টিমিটার এবং ওজন কিলোগ্রামে লিখুন।"}, "activity": {"title": "ক্রিয়<PERSON>কলাপের স্তর", "subtitle": "আপনি কত ঘন ঘন ব্যায়াম করেন?", "none": "কোন ব্যায়াম নেই", "moderate": "মধ্যম", "high": "উচ্চ"}, "goal": {"title": "ওজন লক্ষ্য", "subtitle": "আপনি কি অর্জন করতে চান?", "increase": "বৃদ্ধি", "maintain": "<PERSON><PERSON><PERSON><PERSON><PERSON> রাখা", "decrease": "কমানো"}, "targetWeight": {"title": "লক্ষ্য ওজন", "subtitle": "আপনার লক্ষ্য ওজন কিলোগ্রামে কত?"}, "setup": {"title": "আপনার পরিকল্পনা সেট আপ করা", "subtitle": "দয়া করে অপেক্ষা করুন যতক্ষণ না আমরা আপনার পুষ্টি পরিকল্পনা প্রস্তুত করি।"}, "review": {"title": "আপনার পরিকল্পনা পর্যালোচনা করুন", "subtitle": "আপনার পুষ্টি পরিকল্পনা পর্যালোচনা এবং কাস্টমাইজ করুন।"}, "height": {"label": "উচ্চতা (cm)"}, "weight": {"label": "ওজন (kg)"}}, "error": {"updateFailed": "পুষ্টি তথ্য আপডেট করতে ব্যর্থ হয়েছে। দয়া করে আবার চেষ্টা করুন।", "parsingError": "খাদ্য তথ্য পার্সিং করার সময় ত্রুটি:", "fetchReportsFailed": "রিপোর্ট তথ্য আনতে ব্যর্থ হয়েছে। দয়া করে আবার চেষ্টা করুন।", "missingReportId": "রিপোর্ট ID অনুপস্থিত"}}, "personalize": {"nav": {"title": "ব্যক্তিগতকরণ"}, "button": {"saving": "সংরক্ষণ করা হচ্ছে", "review": "পর্যালোচনা", "saveNext": "সংরক্ষণ ও পরবর্তী"}}, "basicInfo": {"title": "আসুন আপনাকে আরও ভালোভাবে জানি", "subtitle": "এই তথ্য আমাদের আপনার স্বাস্থ্যের সুপারিশগুলিকে ব্যক্তিগতকৃত করতে সাহায্য করে", "age": {"question": "আপনার বয়স কত?", "placeholder": "আপনার বয়<PERSON> লিখুন"}, "sex": {"question": "আপনার লিঙ্গ কি?", "placeholder": "আপনার লিঙ্গ নির্বাচন করুন", "male": "পুরুষ", "female": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "অন্যান্য"}, "height": {"question": "আপনার উচ্চতা কত? (cm)", "placeholder": "আপনার উচ্চতা লিখুন"}, "weight": {"question": "আপনার ওজন কত? (kg)", "placeholder": "আপন<PERSON>র ও<PERSON><PERSON> লিখুন"}}, "lifestyle": {"title": "আপনার জীবনযাত্রার অভ্যাস", "subtitle": "আপনার দৈনন্দিন অভ্যাস বোঝা আমাদের আরও ভালো পরামর্শ দিতে সাহায্য করে", "diet": {"question": "আপনি কি ধরণের খাদ্য অনুসরণ করেন?", "placeholder": "আপন<PERSON><PERSON> খাদ্য নির্বাচন করুন", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonVegetarian": "মাংসাহারী", "vegan": "শুদ্ধ নিরামিষাশী", "pescatarian": "মা<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keto": "কিটো", "paleo": "প্যালিও"}, "exercise": {"question": "আপনি কি নিয়মিত ব্যায়াম করেন?"}, "drinking": {"question": "আপনি কি মদ্যপান করেন?"}, "smoking": {"question": "আপনি কি ধূমপান করেন?"}, "sleep": {"question": "আপনি প্রতি রাতে কত ঘন্টা ঘুমান?", "value": "{{sleep}} ঘন্টা"}, "hydration": {"question": "আপনি প্রতিদিন কত কাপ পানি পান করেন?", "value": "{{hydration}} কা<PERSON> ({{liters}}L)"}}, "allergies": {"title": "আপনার কি কোনও অ্যালার্জি আছে?", "subtitle": "আপনার অ্যালার্জি জানা আমাদের আরও নিরাপদ পরামর্শ দিতে সাহায্য করে", "allergyIndex": "অ্যালার্জি {{index}}", "name": {"question": "আপনার কি কি অ্যালার্জি আছে?", "placeholder": "অ্যালার্জি লিখুন (যেমন, বাদাম, ধুলো)"}, "severity": {"question": "এই অ্যালার্জি কতটা তীব্র?", "placeholder": "তীব্রতা নির্বাচন করুন", "mild": "হালকা", "moderate": "মাঝারি", "severe": "তীব্র"}, "addButton": "আরেকটি অ্যালার্জি যোগ করুন", "noAllergiesButton": "আমার কোন অ্যালার্জি নেই"}, "medications": {"title": "ঔষধ ও সম্পূরক", "subtitle": "আমাদের জানান আপনি বর্তমানে কোন ঔষধ বা সম্পূরক সেবন করছেন", "medicationIndex": "ঔষধ {{index}}", "name": {"label": "ঔষধের নাম", "placeholder": "ঔষধের নাম লিখুন"}, "startDate": {"question": "আপনি কখন থেকে এটি সেবন শুরু করেছেন?", "placeholder": "তা<PERSON><PERSON><PERSON> নির<PERSON>বাচন করুন"}, "type": {"label": "ঔষধের ধরণ", "shortTerm": "স্বল<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ী", "longTerm": "দীর্ঘমেয়াদী"}, "dose": {"label": "মাত্রা", "placeholder": "পরিমাণ"}, "unit": {"label": "একক"}, "frequency": {"label": "প্রায়োগিকতা", "placeholder": "বার", "perDay": "প্রতিদিন", "perWeek": "প্রতি সপ্তাহে", "perMonth": "প্রতি মাসে", "perYear": "প্রতি বছরে"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "আরেকটি ঔষধ যোগ করুন", "noMedicationsButton": "আমি কোন ঔষধ সেবন করি না", "calendar": {"title": "শুরুর তা<PERSON><PERSON><PERSON> নির্বাচন করুন"}}, "conditions": {"title": "চিকিৎসাগত অবস্থা", "subtitle": "আমাদের জানান আপনার কোন চিকিৎসাগত অবস্থা আছে বা অতীতে ছিল", "conditionIndex": "অবস্থা {{index}}", "name": {"label": "অবস্থার নাম", "placeholder": "অবস্থা লিখুন (যেমন, হাঁপানি, ইত্যাদি)"}, "since": {"question": "কখন থেকে আপনার এই অবস্থা হয়েছে?", "placeholder": "তা<PERSON><PERSON><PERSON> নির<PERSON>বাচন করুন"}, "current": {"question": "এটি কি বর্তমানে আপনাকে বিরক্ত করছে?"}, "medicated": {"question": "এর জন্য কি আপনি কোন ঔষধ সেবন করছেন?"}, "addButton": "আরেকটি অবস্থা যোগ করুন", "noConditionsButton": "আমার কোন চিকিৎসাগত অবস্থা নেই", "calendar": {"title": "তা<PERSON><PERSON><PERSON> নির<PERSON>বাচন করুন"}}, "reproductive": {"title": "প্রজনন স্বাস্থ্য", "subtitle": "এই তথ্য আমাদের আরও ব্যক্তিগতকৃত স্বাস্থ্য সুপারিশ প্রদানে সাহায্য করে", "menstruation": {"question": "আপনার কি কখনও ঋতুস্রাব হয়েছে?", "detailsTitle": "ঋতুস্রাবের বিস্তারিত", "regularity": {"question": "আপনার চক্র কতটা নিয়মিত?", "regular": "নিয়মিত", "irregular": "অনিয়মিত", "notSure": "নিশ্চিত নই"}, "cycleLength": {"label": "গড় চক্রের দৈর্ঘ্য (দিন)", "placeholder": "চক্রের দৈর্ঘ্য লিখুন"}, "flowDays": {"label": "প্রবাহের দিন: {{flowDays}}", "min": "১ দিন", "max": "১৫ দিন"}, "padsPerDay": {"label": "প্রতিদিন প্যাড/ট্যাম্পন: {{padsPerDay}}", "min": "১", "max": "১৫"}, "symptoms": {"question": "আপনার ঋতুস্রাবের সময় কোন লক্ষণ আছে?", "placeholder": "লক্ষণগুলি লিখুন (যেমন, পেটে ব্যথা, মাথাব্যথা)"}}, "childbirth": {"question": "আপনার কি সন্তান প্রসবের অভিজ্ঞতা আছে?", "detailsTitle": "সন্তান প্রসবের বিস্তারিত", "children": {"label": "সন্তানের সংখ্যা"}, "pregnancies": {"label": "গর্ভাবস্থার সংখ্যা"}, "complications": {"question": "গর্ভাবস্থা বা সন্তান প্রসবের সময় কোন জটিলতা ছিল?", "placeholder": "জটিলত<PERSON><PERSON><PERSON><PERSON><PERSON> (যদি থাকে) লিখুন"}}}, "review": {"title": "আপনার তথ্য পর্যালোচনা করুন", "subtitle": "জমা দেওয়<PERSON><PERSON> আগে আপনি যে তথ্য দিয়েছেন তা পর্যালোচনা করুন", "sections": {"basicInfo": "মৌলিক তথ্য", "lifestyle": "জীবনযাত্রার ধরণ", "allergies": "অ্যালার্জি", "medications": "ঔষধ ও সম্পূরক", "conditions": "চিকিৎসাগত অবস্থা", "reproductive": "প্রজনন স্বাস্থ্য", "menstruationDetails": "ঋতুস্রাবের বিস্তারিত", "childbirthDetails": "সন্তান প্রসবের বিস্তারিত"}, "fields": {"age": "বয়স:", "sex": "লিঙ্গ:", "height": "উচ্চতা:", "weight": "ওজন:", "diet": "খাদ্য:", "exercise": "ব্যায়াম:", "drinking": "মদ্যপান:", "smoking": "ধূমপান:", "sleep": "ঘুম:", "hydration": "জলের পরিমাণ:", "allergyIndex": "{{index}} অ্যালার্জি:", "dose": "মাত্রা:", "frequency": "ঘনত্ব:", "type": "প্রকার:", "since": "যেহেতু:", "currentlyActive": "বর্তমানে সক্রিয়:", "takingMedication": "ঔষধ সেবন করছেন:", "hasMenstruated": "ঋতুস্রাব হয়েছে:", "regularity": "নিয়মিততা:", "cycleLength": "চক্রের দৈর্ঘ্য:", "flowDays": "প্রবাহের দিন:", "padsPerDay": "প্রতিদিন প্যাড/ট্যাম্পন:", "hasChildbirth": "সন্তান প্রসবের অভিজ্ঞতা আছে:", "children": "সন্তান:", "pregnancies": "গর্ভাবস্থা:"}, "notProvided": "প্র<PERSON><PERSON>ন করা হয়নি", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "প্রতিদিন {{sleep}} ঘন্টা ঘুম", "hydration": "প্রতিদিন {{hydration}} কাপ ({{liters}}L) জল", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} দিন"}, "noData": {"allergies": "কোন অ্যালার্জি প্রদান করা হয়নি", "medications": "কোন ঔষধ প্রদান করা হয়নি", "conditions": "কোন চিকিৎসাগত অবস্থা প্রদান করা হয়নি"}, "submitButton": "তথ্য জমা দিন"}, "success": {"title": "তথ্য আপডেট হয়েছে!", "message": "আপনার স্বাস্থ্য তথ্য প্রদানের জন্য ধন্যবাদ। আমরা আপনার অভিজ্ঞতা ব্যক্তিগতকৃত করতে এবং আরও ভাল সুপারিশ প্রদান করতে এটি ব্যবহার করব।", "benefits": {"insights": "ব্যক্তিগতকৃত স্বাস্থ্য অন্তর্দৃষ্টি", "reminders": "উন্নত ঔষধ স্মারক", "recommendations": "তৈরি স্বাস্থ্য সুপারিশ"}, "continueButton": "ড্যাশবোর্ডে যান"}, "permissions": {"microphonePermissionDenied": "মাইক্রোফোন পারমিশন প্রত্যাখ্যাত", "microphoneAccessDescription": "অগাস্টকে অডিও রেকর্ড করতে এবং ভয়েস নোট পাঠাতে আপনার মাইক্রোফোন অ্যাক্সেস করার প্রয়োজন", "permissionDenied": "পারমিশন প্রত্যাখ্যাত", "cameraPermissionRequired": "এটি কাজ কর<PERSON><PERSON> জন্য আমাদের ক্যামেরা পারমিশনের প্রয়োজন!", "mediaLibraryPermissionRequired": "এটি কাজ করার জন্য আমাদের মিডিয়া লাইব্রেরি পারমিশনের প্রয়োজন!"}, "voiceRecording": {"recordingTooLong": "রেকর্ডিং খুব লম্বা", "recordingTooLongMessage": "ভয়েস রেকর্ডিং ৫ মিনিটের কম হতে হবে। দয়া করে ছোট্ট একটি বার্তা রেকর্ড করুন।"}, "errors": {"uploadFailed": "আপলোড ব্যর্থ", "voiceUploadFailed": "ভয়েস রেকর্ডিং আপলোড করা যায়নি।", "voiceRecordingFailed": "ভয়েস রেকর্ডিং পাঠাতে ব্যর্থ", "failedToStopRecording": "রেকর্ডিং বন্ধ করতে ব্যর্থ", "photoUploadFailed": "ছবি আপলোড করা যায়নি।", "failedToTakePhoto": "ছবি তুলতে ব্যর্থ", "imageUploadFailed": "ছবি আপলোড করা যায়নি: {{fileName}}", "failedToPickImage": "ছবি নির্বাচন করতে ব্যর্থ", "documentUploadFailed": "ডকুমেন্ট আপলোড করা যায়নি: {{fileName}}", "failedToPickDocument": "ডকুমেন্ট নির্বাচন করতে ব্যর্থ"}, "audioPlayer": {"downloadingAudio": "অডিও ডাউনলোড হচ্ছে...", "loadingAudio": "অডিও লোড হচ্ছে..."}, "mediaProcessing": {"processingFile": "আপনার ফাইল প্রসেসিং করা হচ্ছে", "uploadingSecuring": "ফাইল আপলোড এবং সুরক্ষিত করা হচ্ছে...", "analyzingContent": "ডকুমেন্ট কন্টেন্ট বিশ্লেষণ করা হচ্ছে...", "extractingInfo": "মূল তথ্য বের করা হচ্ছে...", "processingInsights": "ইনসাইট প্রসেসিং করা হচ্ছে...", "preparingResponse": "বিস্তারিত প্রতিক্রিয়া প্রস্তুত করা হচ্ছে...", "finalizingResponse": "প্রতিক্রিয়া চূড়ান্ত করা হচ্ছে..."}, "attachments": {"voiceMessage": "ভয়েস বার্তা", "image": "[ছবি]", "pdf": "[পিডিএফ]", "voice": "[ভয়েস নোট]"}, "pdf": {"loadingPdf": "পিডিএফ লোড হচ্ছে..."}, "dateTime": {"yesterday": "গতকাল, "}, "navbar": {"defaultTitle": "অগাস্ট", "selectedCount": "নির্বাচিত"}, "mediaUpload": {"photoLibrary": "ছবি লাইব্রেরি", "takePhoto": "ছবি তুলুন", "chooseFile": "ফাইল নির্বাচন করুন"}, "comingSoon": {"title": "চলছে!", "description": " বর্তমানে উন্নয়নধীন। আপডেটের জন্য অপেক্ষা করুন!", "buttonText": "বুঝলাম!"}, "clipboard": {"success": "লিঙ্ক ক্লিপবোর্ডে কপি করা হয়েছে"}, "mediaPhotos": {"emptyState": "এখন<PERSON> কোনো এন্ট্রি নেই।"}, "foodDetail": {"defaultFoodName": "খাব<PERSON><PERSON><PERSON><PERSON> নাম", "nutrition": {"totalCalories": "মোট ক্যালোরি", "proteins": "প্রোটিন", "carbs": "কার্বোহাইড্রেট", "fat": "ফ্যাট", "sugars": "চিনি", "fibers": "ফাইবার"}}, "reports": {"defaultTitle": "মিডিয়া আইটেম", "defaultFoodName": "খাব<PERSON><PERSON><PERSON><PERSON> নাম", "defaultName": "ডকুমেন্ট", "openButton": "বহিরাগত ভিউয়ারে খুলুন", "biomarker": {"headerBiomarker": "বায়<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>র", "headerValue": "মান", "headerRefRange": "রেফারেন্স রেঞ্জ", "headerStatus": "স্ট্যাটাস"}, "noData": "কোন<PERSON> বায়<PERSON><PERSON><PERSON>র্কার তথ্য পাওয়া যায়নি"}, "setup": {"title": "আ<PERSON><PERSON><PERSON> আপনার জন্য সবকিছু সেট আপ করছি", "inProgress": "চলছে...", "progressMessages": {"0": "দৈনিক ক্যালোরি গণনা করা হচ্ছে", "1": "ম্যাক্রো স্প্লিট অপ্টিমাইজ করা হচ্ছে", "2": "মিলে প্ল্যান তৈরি করা হচ্ছে", "3": "স্বাস্থ্য স্কোর গণনা করা হচ্ছে", "4": "সেটআপ চূড়ান্ত করা হচ্ছে"}, "checklistItems": {"0": "আপনার স্বাস্থ্য তথ্য বিশ্লেষণ করা হচ্ছে", "1": "অপ্টিমাল পুষ্টি পরিকল্পনা গণনা করা হচ্ছে", "2": "আপনার সুপারিশগুলি ব্যক্তিগতকৃত করা হচ্ছে", "3": "আপনার খাবারের পরামর্শ তৈরি করা হচ্ছে", "4": "আপনার সেটআপ চূড়ান্ত করা হচ্ছে"}}, "foodEntry": {"emptyState": "এখন<PERSON> কোন খাবারের তথ্য নেই।  এটি যোগ করতে আপনার খাবারের একটি ছবি তুলুন!"}, "nutritionReview": {"congratulations": "অভিনন্দন!", "subtitle": "আপনার কাস্টম নিউট্রিশন প্ল্যান প্রস্তুত", "submitButton": "চলুন শুরু করা যাক!", "dailyTargetsTitle": "আপনার দৈনিক পুষ্টি লক্ষ্য", "macroLabels": {"calories": "ক্যালোরি", "carbs": "কার্বোহাইড্রেট", "protein": "প্রোটিন", "fats": "ফ্যাট"}, "recommendations": {"title": "আপনার লক্ষ্যে পৌঁছানোর উপায়:", "healthScores": "আপনার রুটিন উন্নত করতে স্বাস্থ্য স্কোর ব্যবহার করুন", "trackFood": "আপনার খাবারের পরিমাণ নিয়মিত ট্র্যাক করুন", "followCalories": "আপনার দৈনিক ক্যালোরি সুপারিশ অনুসরণ করুন", "balanceMacros": "আপনার কার্বোহাইড্রেট, প্রোটিন এবং ফ্যাটের পরিমাণ ভারসাম্য রাখুন"}}, "editModal": {"titlePrefix": "সম্পা<PERSON>না করুন ", "cancelButton": "বাতিল", "saveButton": "পরবর্তী"}, "processing": {"stages": {"scanning": "খাবার স্ক্যান করছে...", "identifying": "উপাদান চিহ্নিত করছে...", "extracting": "পুষ্টি উপাদান নিষ্কাশন করছে...", "finalizing": "ফলাফল চূড়ান্ত করছে..."}, "error": {"defaultMessage": "কোন খাবার পাওয়া যায়নি", "subtitle": "অন্য কোণ থেকে চেষ্টা করুন"}, "retakeButton": "ছবিটি পুনরায় নিতে ট্যাপ করুন", "notification": "কাজ শেষ হলে আমরা আপনাকে জানাব!"}, "chart": {"title": "সময়ের সাথে পুষ্টি ট্র্যাকিং", "selectNutrient": "পুষ্টি নির্বাচন করুন:", "emptyState": "এখন<PERSON> কোন পুষ্টি তথ্য পাওয়া যায়নি।", "dropdown": {"calories": "ক্যালোরি", "protein": "প্রোটিন", "carbs": "কার্বোহাইড্রেট", "fat": "ফ্যাট", "sugars": "শর্ক<PERSON>া"}}, "foodModal": {"defaultName": "খাব<PERSON><PERSON><PERSON><PERSON> নাম", "defaultDate": "আজ", "defaultTime": "অজানা সময়", "saveChanges": "পরিবর্তনগুলি সংরক্ষণ করুন", "error": {"title": "ত্রুটি", "message": "পুষ্টি তথ্য আপডেট করতে ব্যর্থ হয়েছে।  দয়া করে আবার চেষ্টা করুন।"}, "nutrition": {"calories": "🔥 ক্যালোরি", "proteins": "🥩 প্রোটিন", "carbs": "🍞 কার্বোহাইড্রেট", "sugars": "🍬 শর্করা", "fat": "🥑 ফ্যাট"}, "macroBreakdown": {"title": "ম্যাক্রোনিউট্রিয়েন্ট বিশ্লেষণ", "noData": "এই খাবারের জন্য কোন ম্যাক্রোনিউট্রিয়েন্ট তথ্য পাওয়া যায়নি।"}, "macroLabels": {"calories": "ক্যালোরি", "protein": "প্রোটিন", "carbs": "কার্বোহাইড্রেট", "fat": "ফ্যাট", "sugar": "শর্ক<PERSON>া"}}, "infoModal": {"title": "বিস্তারিত তথ্য", "edit": "সম্পা<PERSON>না করুন", "save": "সংরক্ষণ করুন", "saving": "সংরক্ষণ করছে..", "enterValue": "মান লিখুন", "notSet": "নির্ধারিত নয়", "age": "বয়স", "heightCm": "উচ্চতা (সেমি)", "weightKg": "<PERSON><PERSON><PERSON> (কেজি)", "targetWeight": "লক্ষ্য ওজন", "nutritionTargets": "পুষ্টি লক্ষ্য", "protein": "প্রোটিন", "carbs": "কার্বোহাইড্রেট", "fats": "ফ্যাট", "gm": "গ্রাম", "editNote": "মানগুলি লিখুন অথবা স্বয়ংক্রিয়ভাবে গণনা করার জন্য খালি রাখুন।", "autoCalculateNote": "আপনার তথ্যের উপর ভিত্তি করে ম্যাক্রো স্বয়ংক্রিয়ভাবে গণনা করা হয়।", "validation": {"ageMin": "বয়স অন্তত ১৮ হতে হবে", "ageMax": "বয়স ১২৫ এর কম হতে হবে", "heightMin": "উচ্চতা অন্তত ৫০ সেমি হতে হবে", "heightMax": "উচ্চতা ২৫০ সেমি এর কম হতে হবে", "weightMin": "ওজন অন্তত ৩০ কেজি হতে হবে", "weightMax": "ওজন ৫০০ কেজি এর কম হতে হবে", "targetWeightMin": "লক্ষ্য ওজন অন্তত ৩০ কেজি হতে হবে", "targetWeightMax": "লক্ষ্য ওজন ৫০০ কেজি এর কম হতে হবে", "proteinMin": "প্রোটিন ০ অথবা তার বেশি হতে হবে", "carbsMin": "কার্বোহাইড্রেট ০ অথবা তার বেশি হতে হবে", "fatsMin": "ফ্যাট ০ অথবা তার বেশি হতে হবে"}}, "tracker": {"calories": "ক্যালোরি", "protein": "প্রোটিন", "carbs": "কার্বোহাইড্রেট", "fat": "ফ্যাট", "excess": "অতিরিক্ত", "remaining": "বাকি"}, "specialistConstants": {"nutritionist": {"name": "পুষ্টিবিদ", "description": "আহার, পুষ্টি এবং স্বাস্থ্যকর খাদ্য অভ্যাস সম্পর্কে বিশেষজ্ঞ পরামর্শ", "featureName": "পুষ্টি বিশেষজ্ঞ"}, "cardiologist": {"name": "হৃদরোগ বিশেষজ্ঞ", "description": "হৃদয়ের স্বাস্থ্য এবং হৃদরোগের বিশেষজ্ঞ", "featureName": "হৃদরোগ বিশেষজ্ঞ"}, "neurologist": {"name": "স্নায়ুবিশেষজ্ঞ", "description": "মস্তিষ্ক, মেরুদণ্ড এবং স্নায়ুতন্ত্রের ব্যাধি নিয়ে কাজ করেন", "featureName": "স্নায়ুবিজ্ঞান বিশেষজ্ঞ"}, "oncologist": {"name": "অনকোলজিস্ট", "description": "ক্যান্সারের রোগ নির্ণয় এবং চিকিৎসার বিকল্প সম্পর্কে বিশেষজ্ঞ", "featureName": "অনকোলজি বিশেষজ্ঞ"}, "endocrinologist": {"name": "এন্ডোক্রিনোলজিস্ট", "description": "হরম<PERSON>নজনিত অবস্থা এবং বিপাকীয় ব্যাধি বিশেষজ্ঞ", "featureName": "এন্ডোক্রিনোলজি বিশেষজ্ঞ"}}, "discoverCards": {"categories": {"nutrition": "পুষ্টি", "heartHealth": "হৃদরোগ স্বাস্থ্য", "mentalHealth": "মানসিক স্বাস্থ্য", "fitness": "ফিটনেস", "wellness": "সুস্বাস্থ্য"}, "titles": {"vitaminB12Recovery": "ভিটামিন বি১২ ঘাটতি থেকে সেরে ওঠতে কত সময় লাগে", "vitaminDeficiencyGanglion": "কোন ভিটামিনের ঘাটতি গ্যাংলিওন সিস্টের কারণ হয়?", "vitaminDeficiencyHairFall": "কোন ভিটামিনের ঘাটতি চুল পড়ার কারণ হয়", "vitaminWaters": "ভিটামিন ওয়াটার কি আপনার জন্য ভালো?", "cholesterolHeadaches": "উচ্চ কলেস্টেরল কি মাথাব্যথা করে?", "cholesterolEyes": "চোখে দেখা যায় এমন উচ্চ কলেস্টেরলের লক্ষণগুলি কী কী", "diabetesHeadaches": "ডায়াবেটিস কি মাথাব্যথা হতে পারে?", "chestPainDrinking": "দুধ খাওয়ার পর বুকে ব্যথা কেন হয়", "stressDizziness": "স্ট্রেসে মাথা ঘোরা হতে পারে?", "bulimiaFace": "বুলিমিয়<PERSON> মুখ কী", "kneeTwitch": "আমার হাঁটু কেন কাঁপে?", "noseTwitching": "নাক কেন কেঁপে ওঠে", "piriformisVsSciatica": "পিরিফর্মিস্ সিন্ড্রোম এবং সায়াটিকার মধ্যে পার্থক্য কী কী?", "shoulderBladePinched": "কোমরের পাতায় আটকে থাকা স্নায়ু কীভাবে মুক্ত করা যায়", "shoulderPinched": "কো<PERSON>রে আটকে থাকা স্নায়ু কীভাবে মুক্ত করা যায়", "meniscusTorn": "কিভাবে স্বাভাবিকভাবে ছিঁড়ে যাওয়া মেনিস্কাস সারানো যায়", "hydrateQuickly": "দ্রুত কীভাবে তরল পান করবেন", "periodConstipation": "ঋতুস্রাবের সময় কোষ্ঠকাঠিন্য হওয়া কি স্বাভাবিক?", "acneScars": "এক সপ্তাহের মধ্যে স্বাভাবিকভাবে ব্রণের দাগ কীভাবে দূর করবেন", "perimenopausePregnancy": "পেরিমেনোপজের সময় কি গর্ভবতী হওয়া সম্ভব?"}, "descriptions": {"vitaminB12Recovery": "ভিটামিন B12-এর ঘাটতি থেকে সেরে ওঠার সময়কাল এবং আপনার শক্তি বৃদ্ধি করার কার্যকর প্রতিকার সম্পর্কে জানুন।", "vitaminDeficiencyGanglion": "শরীরে গ্যাংলিয়ন সিস্টের বিকাশে ভিটামিনের ঘাটতির সাথে সম্পর্ক সম্পর্কে জানুন।", "vitaminDeficiencyHairFall": "কিভাবে প্রয়োজনীয় ভিটামিনের অভাব চুল পড়ার দিকে নিয়ে যেতে পারে এবং তা প্রতিরোধ করার জন্য আপনি কী করতে পারেন তা সম্পর্কে জানুন।", "vitaminWaters": "আপনার দৈনন্দিন পুষ্টির অংশ হিসাবে ভিটামিন ওয়াটারের সুবিধা এবং সম্ভাব্য অসুবিধাগুলি সম্পর্কে জানুন।", "cholesterolHeadaches": "উচ্চ কোলেস্টেরলের মাত্রা এবং মাথাব্যথার সূত্রপাতের মধ্যে সম্ভাব্য সম্পর্ক পরীক্ষা করুন।", "cholesterolEyes": "জানুন কিভাবে উচ্চ কোলেস্টেরল আপনার চোখে প্রকাশিত হতে পারে এবং কোন লক্ষণগুলির দিকে নজর রাখতে হবে।", "diabetesHeadaches": "দৈনন্দিন জীবনে ডায়াবেটিস এবং মাথাব্যথার ঘটনার মধ্যে সম্পর্ক তদন্ত করুন।", "chestPainDrinking": "নির্দিষ্ট কিছু পানীয় পান করার পর বুকে ব্যথা হওয়ার কারণগুলি সম্পর্কে জানুন।", "stressDizziness": "জানুন কিভাবে চাপ আপনার ভারসাম্য এবং সামগ্রিক সুস্থতাকে প্রভাবিত করতে পারে, যার ফলে মাথা ঘোরা হতে পারে।", "bulimiaFace": "বুলিমিয়ার শারীরিক লক্ষণগুলি বুঝুন, যার মধ্যে মুখের চেহারার উপর প্রভাব অন্তর্ভুক্ত।", "kneeTwitch": "অনিচ্ছাকৃত হাঁটু টানের পিছনে সম্ভাব্য কারণগুলি এবং এর চাপ বা ক্লান্তির সাথে সম্পর্ক সম্পর্কে তদন্ত করুন।", "noseTwitching": "নাক টানের সম্ভাব্য কারণগুলি এবং এর উদ্বেগ বা অন্যান্য কারণগুলির সাথে সম্পর্ক সম্পর্কে জানুন।", "piriformisVsSciatica": "আপনার অবস্থা আরও ভালভাবে বুঝতে পিরিফর্মিটিস সিন্ড্রোম এবং সায়াটিকার লক্ষণগুলি তুলনা করুন।", "shoulderBladePinched": "আপনার কাঁধের ব্লেডে আটকে থাকা স্নায়ু উপশম করার এবং গতিশীলতা পুনরুদ্ধার করার কার্যকর কৌশলগুলি আবিষ্কার করুন।", "shoulderPinched": "কাঁধের এলাকায় স্নায়ু সংকোচন উপশম করার জন্য সহজ ব্যায়াম এবং স্ট্রেচ সম্পর্কে জানুন।", "meniscusTorn": "ছিড়ে যাওয়া মেনিস্কাসের নিরাময়কে সমর্থন করার জন্য প্রাকৃতিক পদ্ধতি এবং ব্যায়ামগুলি অন্বেষণ করুন।", "hydrateQuickly": "দ্রুত এবং কার্যকরভাবে পুনরায় জলের পরিমাণ বৃদ্ধি করার এবং সর্বোত্তম শারীরিক জলের পরিমাণ বজায় রাখার উপায়গুলি খুঁজে বের করুন।", "periodConstipation": "ঋতুস্রাবের সময় কোষ্ঠকাঠিন্যের কারণগুলি বুঝুন এবং প্রাকৃতিক প্রতিকারগুলি সম্পর্কে জানুন।", "acneScars": "দ্রুত ব্রণের দাগের উপস্থিতি কমাতে প্রাকৃতিক প্রতিকার এবং ত্বকের যত্নের টিপস আবিষ্কার করুন।", "perimenopausePregnancy": "পেরি<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, উর্বরতা বিবেচনা এবং জীবনের এই পর্যায়ে কী আশা করা যায় সে সম্পর্কে জানুন।"}}}