import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, View, TouchableOpacity, Text, TextInput, ScrollView, Image, Linking, Share } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { Stack, useRouter } from 'expo-router';
import { useAuthStore, useUser } from '@/store/auth-store';
import { 
  Info, 
  LogOut, 
  Edit, 
  ArrowRight, 
  Phone, 
  MessageSquare,
  ChevronLeft,
  Trash2,
  Share2
} from 'lucide-react-native';
import { getUserData, updateUserData } from '@/services/userService';
import logger from '@/utils/logger/logger';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { defaultFormData, useFormDataStore } from '@/store/formDataStore';
import { useTranslation } from 'react-i18next';
import { LanguageButton } from '@/components/common/LanguageButton';
import { LanguageSwitcher } from '@/components/common/LanguageSwitcher';

function ProfileScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const [displayName, setDisplayName] = useState(t('profile.defaultName'));
  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState(t('profile.defaultName'));
  const [isSaving, setIsSaving] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLanguageSwitcherVisible, setIsLanguageSwitcherVisible] = useState(false);
  const { resetForm } = useFormDataStore()

  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await getUserData();
        //logger.info('User data:', response);
        if (response.success) {
          setDisplayName(response.user?.name || t('profile.defaultName'));
          setNewName(response.user?.name || t('profile.defaultName'));
        } else {
          setError(t('profile.error.loadFailed'));
        }
      } catch (err: unknown) {
        setError(t('profile.error.fetchError'));
        logger.error(err instanceof Error ? err.message : String(err));
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [t]);
  
  const handleUpdateName = useCallback(async () => {
    if (!newName.trim()) return;
    
    setIsSaving(true);
    setError(null);
    
    try {
      const response = await updateUserData(newName);
      if (response.success) {
        setDisplayName(newName);
        setIsEditingName(false);
      } else {
        setError(t('profile.error.updateNameFailed'));
      }
    } catch (err: unknown) {
      setError(t('profile.error.updateNameError'));
      logger.error(err instanceof Error ? err.message : String(err));
    } finally {
      setIsSaving(false);
    }
  }, [newName, setDisplayName, setIsEditingName, setError, setIsSaving, t]);

  const user = useUser();
  const { logout } = useAuthStore();

  const handleSignOut = useCallback(async () => {
    setIsLoggingOut(true);
    try {
      // Use the auth store's logout method to clear the token from SecureStore

      resetForm();
      await logout();
      
    } catch (error) {
      logger.error(t('profile.error.logoutError'), error);
    } finally {
      setIsLoggingOut(false);
    }
  }, [logout, router, setIsLoggingOut, t]);

  const handleAboutClick = useCallback(() => {
    Linking.openURL('https://meetaugust.ai');
  }, []);

  const handleReferClick = useCallback(() => {
    try {
      const appStoreLink = "https://app.meetaugust.ai/join/app";
      const shareText = `${t('profile.shareMessage')}\n${appStoreLink}`;
      Share.share({ message: shareText });
    } catch (error) {
      logger.error(t('profile.error.shareError'), error);
    }
    
  }, [t]);

  const handleDeleteClick = useCallback(() => {
    Linking.openURL('https://app.meetaugust.ai/delete-account');
  }, []);

  const handleWhatsappClick = useCallback(() => {
    Linking.openURL('https://app.meetaugust.ai/redirect/wa?message=Hey%20there!');
  }, []);

  const handleLanguageButtonPress = useCallback(() => {
    setIsLanguageSwitcherVisible(true);
  }, []);

  const handleLanguageSwitcherClose = useCallback(() => {
    setIsLanguageSwitcherVisible(false);
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom TopNavBar */}
      <View style={styles.topNavBar}>
        <View style={styles.navBarContent}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <ChevronLeft size={24} color={colors.gray[800]} />
          </TouchableOpacity>
          <Text style={styles.navBarTitle}>{t('profile.title')}</Text>
          <View style={styles.navBarRight}>
            <LanguageButton onPress={handleLanguageButtonPress} />
          </View>
        </View>
      </View>
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.profileCard}>
            {isEditingName ? (
              <View style={styles.editNameContainer}>
                <TextInput
                  style={styles.nameInput}
                  value={newName}
                  onChangeText={setNewName}
                  placeholder={t('profile.namePlaceholder')}
                  autoFocus
                />
                <View style={styles.editButtonsContainer}>
                  <TouchableOpacity
                    style={[styles.editButton, styles.saveButton]}
                    onPress={handleUpdateName}
                    disabled={isSaving}
                  >
                    <Text style={styles.saveButtonText}>{isSaving ? t('profile.saving') : t('common.save')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.editButton, styles.cancelButton]}
                    onPress={() => {
                      setIsEditingName(false);
                      setNewName(displayName);
                    }}
                    disabled={isSaving}
                  >
                    <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View style={styles.nameContainer}>
                <View style={styles.nameTextContainer}>
                  <Text style={styles.greeting}>👋 {displayName}</Text>
                  <View style={styles.phoneContainer}>
                    <Phone size={16} color={colors.gray[500]} />
                    <Text style={styles.phoneText}>{user?.phone || t('profile.noPhoneNumber')}</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.editIconButton}
                  onPress={() => setIsEditingName(true)}
                >
                  <Edit size={18} color={colors.primary} />
                </TouchableOpacity>
              </View>
            )}
          </View>
          
          <View style={styles.menuList}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleAboutClick}
            >
              <View style={styles.menuIconContainer}>
                <Info size={20} color={colors.primary} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{t('profile.about.title')}</Text>
                <Text style={styles.menuDescription}>{t('profile.about.description')}</Text>
              </View>
              <ArrowRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
            
            <View style={styles.divider} />
            
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleWhatsappClick}
            >
              <View style={styles.menuIconContainer}>
                <MessageSquare size={20} color={colors.primary} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{t('profile.whatsapp.title')}</Text>
                <Text style={styles.menuDescription}>{t('profile.whatsapp.description')}</Text>
              </View>
              <ArrowRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
            <View style={styles.divider} />
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleReferClick}
            >
              <View style={styles.menuIconContainer}>
                <Share2 size={20} color={colors.primary} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{t('profile.refer.title')}</Text>
                <Text style={styles.menuDescription}>{t('profile.refer.description')}</Text>
              </View>
              <ArrowRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
            <View style={styles.divider} />
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleDeleteClick}
            >
              <View style={styles.menuIconContainer}>
                <Trash2 size={20} color={colors.primary} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{t('profile.deleteAccount.title')}</Text>
                <Text style={styles.menuDescription}>{t('profile.deleteAccount.description')}</Text>
              </View>
              <ArrowRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
            <View style={styles.divider} />
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleSignOut}
              disabled={isLoggingOut}
            >
              <View style={styles.menuIconContainer}>
                <LogOut size={20} color={colors.primary} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{t('profile.logout.title')}</Text>
                <Text style={styles.menuDescription}>
                  {isLoggingOut ? t('profile.loggingOut') : t('profile.logout.description')}
                </Text>
              </View>
              <ArrowRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Footer with logo and version */}
        <View style={styles.footer}>
          <Image 
            source={{ uri: 'https://augustbuckets.blob.core.windows.net/mobile-app-assets/mobile-logo-white.png' }}
            style={styles.footerLogo}
            resizeMode="contain"
          />
          <Text style={styles.versionText}>{t('common.version')}</Text>
        </View>
      </ScrollView>

      {/* Language Switcher Modal */}
      <LanguageSwitcher
        visible={isLanguageSwitcherVisible}
        onClose={handleLanguageSwitcherClose}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  topNavBar: {
    backgroundColor: colors.white,
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateVerticalScale(16),
  },
  navBarContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navBarRight: {
    marginLeft: 'auto',
  },
  backButton: {
    padding: moderateScale(8),
    marginRight: moderateScale(4),
    width: moderateScale(44),
    height: moderateVerticalScale(44),
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: moderateScale(24),
    fontWeight: '600',
    color: colors.gray[800],
  },
  navBarTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: colors.gray[800],
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: moderateScale(16),
  },
  profileCard: {
    backgroundColor: colors.white,
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    marginBottom: moderateVerticalScale(16),
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: moderateVerticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 2,
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameTextContainer: {
    flex: 1,
  },
  greeting: {
    fontSize: moderateScale(18),
    fontWeight: '700',
    marginBottom: moderateVerticalScale(4),
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: moderateVerticalScale(4),
  },
  phoneText: {
    fontSize: moderateScale(13),
    color: colors.gray[600],
    marginLeft: moderateScale(6),
  },
  editIconButton: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  editNameContainer: {
    width: '100%',
  },
  nameInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: moderateScale(8),
    padding: moderateScale(10),
    fontSize: moderateScale(16),
    marginBottom: moderateVerticalScale(12),
  },
  editButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: moderateScale(12),
  },
  editButton: {
    flex: 1,
    padding: moderateScale(10),
    borderRadius: moderateScale(8),
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  saveButtonText: {
    color: colors.white,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: colors.gray[200],
  },
  cancelButtonText: {
    color: colors.gray[700],
  },
  menuList: {
    backgroundColor: colors.white,
    borderRadius: moderateScale(16),
    overflow: 'hidden',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: moderateVerticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: moderateScale(14),
  },
  menuIconContainer: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(20),
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: moderateScale(12),
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: moderateScale(15),
    fontWeight: '600',
    marginBottom: moderateVerticalScale(4),
  },
  menuDescription: {
    fontSize: moderateScale(13),
    color: colors.gray[600],
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginHorizontal: moderateScale(16),
  },
  footer: {
    alignItems: 'center',
    paddingVertical: moderateVerticalScale(24),
    marginTop: moderateVerticalScale(16),
  },
  footerLogo: {
    width: moderateScale(120),
    height: moderateVerticalScale(30),
    marginBottom: moderateVerticalScale(8),
  },
  versionText: {
    fontSize: moderateScale(12),
    color: colors.gray[500],
  },
});

export default React.memo(ProfileScreen);
