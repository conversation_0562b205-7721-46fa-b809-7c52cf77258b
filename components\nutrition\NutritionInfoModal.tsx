import React, { useEffect, useState } from "react"
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Pressable,
} from "react-native"
import { X, Weight, Target, Ruler, Save, Pencil, User } from "lucide-react-native"
import { moderateScale, moderateVerticalScale, scale } from "react-native-size-matters"
import { useTheme } from "@/src/theme/ThemeContext"
import { Theme } from "@/src/theme/types"
import { useUserDataStore } from "@/store/userDataStore"
import logger from '@/utils/logger/logger';
import { z } from 'zod'
import getMacroPerDay from "@/utils/nutrition/getMacroPerDay"
import { useTranslation } from 'react-i18next'

const getUserSchema = (t: any) => z.object({
    age: z.coerce.number().min(18, t('infoModal.validation.ageMin')).max(125, t('infoModal.validation.ageMax')),
    height: z.coerce.number().min(50, t('infoModal.validation.heightMin')).max(250, t('infoModal.validation.heightMax')),
    weight: z.coerce.number().min(30, t('infoModal.validation.weightMin')).max(500, t('infoModal.validation.weightMax')),
    desiredWeight: z.coerce.number().min(30, t('infoModal.validation.targetWeightMin')).max(500, t('infoModal.validation.targetWeightMax')),
    dailyProtein: z.coerce.number().min(0, t('infoModal.validation.proteinMin')),
    dailyCarbohydrates: z.coerce.number().min(0, t('infoModal.validation.carbsMin')),
    dailyFats: z.coerce.number().min(0, t('infoModal.validation.fatsMin')),
})
interface UserData {
    age: number,
    height: number
    weight: number
    desiredWeight: number
    dailyProtein: number
    dailyCarbohydrates: number
    dailyFats: number
}

interface Props {
    isOpen: boolean
    onClose: () => void
    userData: UserData
}

const UserInfoModal = ({ isOpen, onClose, userData }: Props) => {
    const { t } = useTranslation()
    const { theme } = useTheme()
    const styles = createStyles(theme)

    const [formData, setFormData] = useState<UserData>(userData)
    const [isEditing, setIsEditing] = useState(false)
    const [isSaving, setIsSaving] = useState(false)
    const { nutritionData, updateNutrition } = useUserDataStore()
    const [errors, setErrors] = useState<Partial<Record<keyof UserData, string>>>({})

    useEffect(() => {
        if (isOpen) {
            setErrors({});
            setFormData(userData);
            setIsEditing(false);
        }
    }, [isOpen, userData])

    const handleChange = (field: keyof UserData, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }))
    }

    const handleSubmit = async () => {
        if (!isEditing) {
            setIsEditing(true)
            return
        }
        const result = getUserSchema(t).safeParse(formData)

        if (!result.success) {
            const fieldErrors: Partial<Record<keyof UserData, string>> = {}
            result.error.errors.forEach(err => {
                const field = err.path[0] as keyof UserData
                fieldErrors[field] = err.message
            })
            setErrors(fieldErrors)
            return
        }

        const calculateMacros = ((String(nutritionData.age) !== String(formData.age))
            || (String(nutritionData.height) !== String(formData.height))
            || (String(nutritionData.weight) !== String(formData.weight))
            || (String(nutritionData.desiredWeight) !== String(formData.desiredWeight)))

        const updatedTargetMacros: any = getMacroPerDay({
            ...nutritionData,
            age: formData.age,
            height: formData.height,
            weight: formData.weight,
            desiredWeight: formData.desiredWeight
        });

        setErrors({})
        setIsSaving(true)

        const payload = Object.fromEntries(
            Object.entries({
                ...formData,
                dailyProtein: calculateMacros ? updatedTargetMacros?.macros?.protein : formData.dailyProtein,
                dailyCarbohydrates: calculateMacros ? updatedTargetMacros?.macros?.carbs : formData.dailyCarbohydrates,
                dailyFats: calculateMacros ? updatedTargetMacros?.macros?.fat : formData.dailyFats,
                dailyCalories: calculateMacros ? updatedTargetMacros?.calories : nutritionData.dailyCalories,
                sex: nutritionData.sex
            }).map(([k, v]) => [k, v.toString()])
        )

        await updateNutrition(payload as any).finally(() => {
            setIsEditing(false)
            setIsSaving(false)
            onClose()
        })
    }

    const renderEditableField = (
        label: string,
        value: number | null,
        field: keyof UserData,
        icon: JSX.Element,
    ) => (
        <View style={styles.fieldContainer}>
            <View style={styles.labelRow}>
                <View style={styles.iconCircle}>{icon}</View>
                <Text style={styles.labelText}>{label}</Text>
            </View>
            {isEditing ? (
                <>
                    <TextInput
                        keyboardType="numeric"
                        inputMode="numeric"
                        style={[styles.input, errors[field] && { borderColor: 'red' }]}
                        value={value?.toString() || ""}
                        onChangeText={(text) => handleChange(field, text)}
                        placeholder={t('infoModal.enterValue')}
                    />
                    {errors[field] && (
                        <Text style={{ color: 'red', fontSize: 12 }}>{errors[field]}</Text>
                    )}
                </>
            ) : (
                <Text style={styles.valueText}>{value || t('infoModal.notSet')}</Text>
            )}
        </View>
    )

    const renderMacro = (
        label: string,
        value: number | null,
        field: keyof UserData,
        color: string,
        unit: string,
    ) => (
        <View style={styles.macroContainer}>
            <View style={[styles.macroCircle, { backgroundColor: color }]}>
                {isEditing ? (
                    <TextInput
                        keyboardType="numeric"
                        inputMode="numeric"
                        style={styles.macroInput}
                        value={value?.toString() || ""}
                        onChangeText={(text) => handleChange(field, text)}
                        textAlign="center"
                        placeholderTextColor="#999"
                    />
                ) : (
                    <>
                        <Text style={styles.macroValue}>{value || 0}</Text>
                        <Text style={styles.unitText}>{unit}</Text>
                    </>
                )}
            </View>
            <Text style={styles.macroLabel}>{label}</Text>
        </View>
    )

    return (
        <Modal visible={isOpen} animationType="fade" transparent>
            <Pressable style={styles.backdrop} onPress={() => {
                setIsEditing(false);
                setErrors({});
                setFormData(userData);
                onClose();
            }} />


            <View style={styles.modalWrapper}>
                <ScrollView contentContainerStyle={styles.modalContent}>
                    <View style={styles.header}>
                        <Text style={styles.title}>{t('infoModal.title')}</Text>
                        <View style={{ flexDirection: "row", gap: moderateScale(12), alignItems: "center" }}>
                            <TouchableOpacity disabled={isSaving} onPress={() => { handleSubmit() }} style={styles.editButton}>
                                {isEditing ? <Save size={scale(16)} color="#166534" /> : <Pencil size={scale(16)} color="#166534" />}
                                <Text style={styles.editText}>{isEditing ? `${isSaving ? t('infoModal.saving') : t('infoModal.save')}` : t('infoModal.edit')}</Text>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                setIsEditing(false);
                                setErrors({});
                                setFormData(userData);
                                onClose();
                            }}>
                                <X
                                    size={20}
                                    color={theme.colors.gray[600]}
                                    hitSlop={20}
                                />
                            </TouchableOpacity>

                        </View>
                    </View>

                    <View style={styles.fieldsGrid}>
                        {renderEditableField(t('infoModal.age'), formData.age, "age", <User size={14} color={theme.colors.primary[700]} />)}
                        {renderEditableField(t('infoModal.heightCm'), formData.height, "height", <Ruler size={14} color={theme.colors.primary[700]} />)}
                        {renderEditableField(t('infoModal.weightKg'), formData.weight, "weight", <Weight size={14} color={theme.colors.primary[700]} />)}
                        {renderEditableField(t('infoModal.targetWeight'), formData.desiredWeight, "desiredWeight", <Target size={14} color={theme.colors.primary[700]} />)}
                    </View>

                    <Text style={styles.subtitle}>{t('infoModal.nutritionTargets')}</Text>
                    <View style={styles.macroRow}>
                        {renderMacro(t('infoModal.protein'), formData.dailyProtein, "dailyProtein", "#8D6E63", t('infoModal.gm'))}
                        {renderMacro(t('infoModal.carbs'), formData.dailyCarbohydrates, "dailyCarbohydrates", "#F4A261", t('infoModal.gm'))}
                        {renderMacro(t('infoModal.fats'), formData.dailyFats, "dailyFats", "#FFD166", t('infoModal.gm'))}
                    </View>

                    <Text style={styles.note}>
                        {isEditing
                            ? t('infoModal.editNote')
                            : t('infoModal.autoCalculateNote')}
                    </Text>
                </ScrollView>
            </View>
        </Modal>
    )
}

export default UserInfoModal

const createStyles = (theme: Theme) =>
    StyleSheet.create({
        backdrop: {
            position: "absolute",
            top: 0, bottom: 0, left: 0, right: 0,
            backgroundColor: "rgba(0,0,0,0.4)",
        },
        modalWrapper: {
            marginTop: moderateVerticalScale(100),
            marginHorizontal: moderateScale(20),
            backgroundColor: theme.colors.secondary[50],
            borderRadius: moderateScale(theme.radii["2xl"]),
            padding: moderateScale(14),
            paddingVertical: moderateScale(24),
            elevation: 5,
        },
        modalContent: {
            padding: moderateScale(8)
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: moderateVerticalScale(16),
        },
        title: {
            fontSize: moderateScale(theme.fontSize.lg),
            fontWeight: "600",
            color: theme.colors.gray[900],
        },
        editButton: {
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: "#dcfce7",
            paddingHorizontal: scale(10),
            paddingVertical: moderateScale(4),
            borderRadius: scale(6),
        },
        editText: {
            marginLeft: scale(6),
            color: "#15803d",
            fontWeight: "600",
        },
        fieldsGrid: {
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-between",
        },
        fieldContainer: {
            width: "48%",
            backgroundColor: "#fff",
            padding: moderateScale(12),
            paddingLeft: moderateScale(10),
            borderRadius: moderateScale(theme.radii.md),
            marginBottom: moderateVerticalScale(10),
            borderColor: theme.colors.gray[300],
            borderWidth: 1,
        },
        labelRow: {
            flexDirection: "row",
            alignItems: "center",
            marginBottom: moderateVerticalScale(4),
        },
        iconCircle: {
            backgroundColor: theme.colors.primary[100],
            width: moderateScale(24),
            height: moderateScale(24),
            borderRadius: moderateScale(14),
            alignItems: "center",
            justifyContent: "center",
            marginRight: moderateScale(4),
        },
        labelText: {
            fontSize: moderateScale(theme.fontSize.xs),
            color: theme.colors.gray[600],
        },
        input: {
            fontSize: moderateScale(theme.fontSize.md),
            borderBottomWidth: 1,
            borderColor: theme.colors.gray[300],
            paddingVertical: moderateVerticalScale(2),
            fontWeight: "500",
        },
        valueText: {
            fontSize: moderateScale(theme.fontSize.md),
            fontWeight: "500",
            color: theme.colors.gray[800],
            marginLeft: moderateScale(28)
        },
        subtitle: {
            fontSize: moderateScale(theme.fontSize.md),
            fontWeight: "600",
            color: theme.colors.gray[900],
            marginTop: moderateVerticalScale(16),
            marginBottom: moderateVerticalScale(8),
        },
        macroRow: {
            flexDirection: "row",
            justifyContent: "space-around",
        },
        macroContainer: {
            alignItems: "center",
        },
        macroCircle: {
            width: moderateScale(80),
            height: moderateScale(80),
            borderRadius: moderateScale(40),
            alignItems: "center",
            justifyContent: "center",
            marginBottom: moderateVerticalScale(6),
            position: "relative",
        },
        macroInput: {
            backgroundColor: "#fff",
            width: moderateScale(60),
            height: moderateVerticalScale(30),
            borderRadius: moderateScale(8),
            fontSize: moderateScale(theme.fontSize.sm),
            fontWeight: "600",
            textAlign: "center",
        },
        macroValue: {
            color: "#fff",
            fontWeight: "700",
            fontSize: moderateScale(theme.fontSize.lg),
            position: "absolute",
            top: moderateVerticalScale(20),
        },
        unitText: {
            color: "#fff",
            fontSize: moderateScale(theme.fontSize.xs),
            position: "absolute",
            bottom: moderateVerticalScale(16),
        },
        macroLabel: {
            fontSize: moderateScale(theme.fontSize.xs),
            color: theme.colors.gray[600],
        },
        note: {
            fontSize: moderateScale(theme.fontSize.xs),
            textAlign: "center",
            marginTop: moderateVerticalScale(12),
            color: theme.colors.gray[600],
        },
    })
