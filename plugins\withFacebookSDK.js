const { withInfoPlist, withAndroidManifest, withStringsXml, withMainApplication } = require('@expo/config-plugins');

/**
 * Expo config plugin for react-native-fbsdk-next
 * @param {Object} config - The expo config
 * @param {Object} props - Plugin configuration
 * @param {string} props.appId - Facebook App ID
 * @param {string} props.appName - Facebook App Name
 * @param {string} props.clientToken - Facebook Client Token
 * @param {string} [props.scheme] - Custom URL scheme (defaults to fb{appId})
 * @param {boolean} [props.autoLogAppEventsEnabled] - Auto log app events (default: true)
 * @param {boolean} [props.advertiserIDCollectionEnabled] - Advertiser ID collection (default: true)
 */
module.exports = function withFacebookSDK(config, props) {
  if (!props?.appId) {
    throw new Error('Facebook App ID is required');
  }
  if (!props?.appName) {
    throw new Error('Facebook App Name is required');
  }
  if (!props?.clientToken) {
    throw new Error('Facebook Client Token is required');
  }

  const {
    appId,
    appName,
    clientToken,
    scheme = `fb${appId}`,
    autoLogAppEventsEnabled = true,
    advertiserIDCollectionEnabled = true,
  } = props;

  // iOS Configuration
  config = withInfoPlist(config, (config) => {
    // Add Facebook configuration
    config.modResults.FacebookAppID = appId;
    config.modResults.FacebookClientToken = clientToken;
    config.modResults.FacebookDisplayName = appName;
    config.modResults.FacebookAutoLogAppEventsEnabled = autoLogAppEventsEnabled;
    config.modResults.FacebookAdvertiserIDCollectionEnabled = advertiserIDCollectionEnabled;

    // Add URL schemes
    if (!config.modResults.CFBundleURLTypes) {
      config.modResults.CFBundleURLTypes = [];
    }
    
    const existingFbScheme = config.modResults.CFBundleURLTypes.find(
      (urlType) => urlType.CFBundleURLSchemes?.includes(scheme)
    );

    if (!existingFbScheme) {
      config.modResults.CFBundleURLTypes.push({
        CFBundleURLSchemes: [scheme],
      });
    }

    // Add LSApplicationQueriesSchemes
    if (!config.modResults.LSApplicationQueriesSchemes) {
      config.modResults.LSApplicationQueriesSchemes = [];
    }

    const fbSchemes = [
      'fbapi',
      'fb-messenger-share-api',
      'fbauth2',
      'fbshareextension',
    ];

    fbSchemes.forEach((fbScheme) => {
      if (!config.modResults.LSApplicationQueriesSchemes.includes(fbScheme)) {
        config.modResults.LSApplicationQueriesSchemes.push(fbScheme);
      }
    });

    return config;
  });

  // Android Configuration
  config = withAndroidManifest(config, (config) => {
    const manifest = config.modResults;
    const mainApplication = manifest.manifest.application[0];

    // Add Facebook App ID
    if (!mainApplication['meta-data']) {
      mainApplication['meta-data'] = [];
    }

    // Remove existing Facebook meta-data to avoid duplicates
    mainApplication['meta-data'] = mainApplication['meta-data'].filter(
      (meta) => !meta.$['android:name']?.startsWith('com.facebook')
    );

    // Add Facebook App ID
    mainApplication['meta-data'].push({
      $: {
        'android:name': 'com.facebook.sdk.ApplicationId',
        'android:value': `@string/facebook_app_id`,
      },
    });

    // Add Facebook Client Token
    mainApplication['meta-data'].push({
      $: {
        'android:name': 'com.facebook.sdk.ClientToken',
        'android:value': `@string/facebook_client_token`,
      },
    });

    // Add Facebook Auto Log App Events
    mainApplication['meta-data'].push({
      $: {
        'android:name': 'com.facebook.sdk.AutoLogAppEventsEnabled',
        'android:value': autoLogAppEventsEnabled.toString(),
      },
    });

    // Add Facebook Advertiser ID Collection
    mainApplication['meta-data'].push({
      $: {
        'android:name': 'com.facebook.sdk.AdvertiserIDCollectionEnabled',
        'android:value': advertiserIDCollectionEnabled.toString(),
      },
    });

    // Add Facebook Activity
    if (!mainApplication.activity) {
      mainApplication.activity = [];
    }

    const facebookActivity = mainApplication.activity.find(
      (activity) => activity.$['android:name'] === 'com.facebook.FacebookActivity'
    );

    if (!facebookActivity) {
      mainApplication.activity.push({
        $: {
          'android:name': 'com.facebook.FacebookActivity',
          'android:configChanges':
            'keyboard|keyboardHidden|screenLayout|screenSize|orientation',
          'android:label': '@string/app_name',
        },
      });
    }

    // Add Custom Tab Activity
    const customTabActivity = mainApplication.activity.find(
      (activity) => activity.$['android:name'] === 'com.facebook.CustomTabActivity'
    );

    if (!customTabActivity) {
      mainApplication.activity.push({
        $: {
          'android:name': 'com.facebook.CustomTabActivity',
          'android:exported': 'true',
        },
        'intent-filter': [
          {
            action: [{ $: { 'android:name': 'android.intent.action.VIEW' } }],
            category: [
              { $: { 'android:name': 'android.intent.category.DEFAULT' } },
              { $: { 'android:name': 'android.intent.category.BROWSABLE' } },
            ],
            data: [{ $: { 'android:scheme': scheme } }],
          },
        ],
      });
    }

    // Add internet permission if not present
    if (!manifest.manifest['uses-permission']) {
      manifest.manifest['uses-permission'] = [];
    }

    const internetPermission = manifest.manifest['uses-permission'].find(
      (perm) => perm.$['android:name'] === 'android.permission.INTERNET'
    );

    if (!internetPermission) {
      manifest.manifest['uses-permission'].push({
        $: { 'android:name': 'android.permission.INTERNET' },
      });
    }

    return config;
  });

  // Add string resources for Android
  config = withStringsXml(config, (config) => {
    // modResults for withStringsXml is an object with resources property
    if (!config.modResults?.resources?.string) {
      config.modResults = {
        resources: {
          string: []
        }
      };
    }
    
    // Remove existing Facebook strings to avoid duplicates
    if (config.modResults.resources.string) {
      config.modResults.resources.string = config.modResults.resources.string.filter(
        (item) => !(item.$.name === 'facebook_app_id' || item.$.name === 'facebook_client_token')
      );
    }
    
    // Add Facebook App ID
    config.modResults.resources.string.push({
      $: { name: 'facebook_app_id' },
      _: appId,
    });
    
    // Add Facebook Client Token
    config.modResults.resources.string.push({
      $: { name: 'facebook_client_token' },
      _: clientToken,
    });
    
    return config;
  });

  return config;
};
