import React, { useEffect } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import logger from '@/utils/logger/logger';

export default function SplashScreen() {
  useEffect(() => {
    logger.info('splash screen')
    router.replace('/chat');
    }, []);

  return (
    <View style={styles.container}>
      <Image 
        source={{ uri: 'https://augustbuckets.blob.core.windows.net/mobile-app-assets/Onboarding/splash.png' }}
        style={styles.splashImage}
        resizeMode="contain"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  splashImage: {
    width: '100%',
    height: '100%',
  },
});