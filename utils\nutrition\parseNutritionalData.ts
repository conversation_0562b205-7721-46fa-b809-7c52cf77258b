// Create a memoization cache for parseNutritionalData
const parseCache = new Map<string, any>();
const MAX_CACHE_SIZE = 50; // Limit cache size to prevent memory leaks

export const parseNutritionalData = (text: string) => {
  // Return null early if text is empty
  if (!text) return null;
  
  // Use text as cache key
  if (parseCache.has(text)) {
    return parseCache.get(text);
  }
  
  // Calculate result if not in cache
  const result = parseNutritionalDataImpl(text);
  
  // Manage cache size
  if (parseCache.size > MAX_CACHE_SIZE) {
    const firstKey = parseCache.keys().next().value;
    if (firstKey !== undefined) {
      parseCache.delete(firstKey);
    }
  }
  
  // Store result in cache
  parseCache.set(text, result);
  
  return result;
};

// Implementation function separated from the memoized wrapper
const parseNutritionalDataImpl = (text: string) => {
  if (!text) return null;
  
  const lines = text.split('\n');
  const nutritionStartIndex = lines.findIndex((line: string) => 
    line.toLowerCase().includes('- calories') || 
    line.toLowerCase().includes('total calories') ||
    line.toLowerCase().includes('🔥 calories')
  );
  
  if (nutritionStartIndex === -1) return null;

  // Enhanced filter to capture lines with nutritional information (even without emojis)
const nutritionLines = lines
.slice(nutritionStartIndex)
.filter((line: string) => line.trim() && (
  line.includes('Total Calories') ||
  line.includes('Proteins') ||
  line.includes('Carbs') ||
  line.includes('Sugars') ||
  line.includes('Fat') ||
  line.includes('Fibers') ||
  line.match(/(\d+\s*kcal|\d+\s*g)/) // Matches kcal or g values
));

// Ensure we add the appropriate emoji for each nutrient
const emojiMap = {
'calories': '🔥',
'total calories': '🔥',
'protein': '🥚',
'proteins': '🥚',
'carbs': '🍞',
'carbohydrates': '🍞',
'sugar': '🍬',
'sugars': '🍬',
'fat': '🥑',
'fats': '🥑',
'fiber': '🌾',
'fibers': '🌾'
};

return nutritionLines
.map((line: string) => {
  const cleanLine = line
    .replace(/^[- ]*/, '')
    .replace(/^[^\w\s]+ /, '');

  const [label, value] = cleanLine.split(':').map((item: string) => item.trim());

  // Identify the emoji based on the label
  const labelLower = label.toLowerCase();
  const emoji = Object.entries(emojiMap).find(([key]) =>
    labelLower.includes(key)
  )?.[1] || ''; // Default to empty string if no match found

  return {
    label: `${emoji} ${label}`,
    value: value || '-'
  };
})
.filter((item: {label: string, value: string}) => item.label && item.value);

};
