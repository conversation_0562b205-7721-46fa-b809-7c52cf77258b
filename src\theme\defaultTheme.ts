import { Theme } from './types';

const defaultTheme: Theme = {
  colors: {
    primary: {
      50: "#E6F0EC",
      100: "#C0D9CE",
      200: "#97C0AF",
      300: "#6DA790",
      400: "#4E9478",
      main: "#206E55", // primary from colors.ts
      600: "#185544", // mainmaryDark from colors.ts
      700: "#17573E",
      800: "#134A34",
      900: "#0E3D2A",
    },
    secondary: {
      50: "#FFFFFF", // white from colors.ts
      100: "#FFFFFF",
      200: "#FFFFFF",
      300: "#FFFFFF",
      400: "#FFFFFF",
      main: "#B6FFBB",
      600: "#F5F5F5",
      700: "#E0E0E0",
      800: "#CCCCCC",
      900: "#B8B8B8",
    },
    gray: { 
      50: "#F9FAFB",
      100: "#F3F4F6",
      200: "#E5E7EB",
      300: "#D1D5DB",
      400: "#9CA3AF",
      main: "#6B7280",
      600: "#4B5563",
      700: "#374151",
      800: "#1F2937",
      900: "#111827",
    },
    errorLight:"#FFEBEE",
    error: "#EF4444", // Matches colors.ts
    warning: "#F59E0B", // Matches colors.ts
    success: "#10B981", // Matches colors.ts
    info: "#3B82F6", // Matches colors.ts
    background: {
      primary: "#FFFFFF",
      secondary: "#F9FAFB",
    },
    text: {
      primary: "#1F2937", // gray.800
      secondary: "#4B5563", // gray.600
      tertiary: "#6B7280", // gray.500
    },
    specialistColors: [
      "#E9F5FF", // Light blue
      "#FFF0E9", // Light orange
      "#F0FFEA", // Light green
      "#F9E9FF", // Light purple
      "#FFFDE9", // Light yellow
    ],
    cardGradients: [
      ["#4A6FFF", "#84A9FF"], // Blue gradient
      ["#FF6B6B", "#FFA3A3"], // Red gradient
      ["#63C7FF", "#A9E2FF"], // Light blue gradient
      ["#FF9F7F", "#FFD0C2"], // Orange gradient
      ["#7C65E6", "#B8A9FF"], // Purple gradient
    ],
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    "2xl": 40,
    "3xl": 64,
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    "2xl": 24,
    "3xl": 32,
    "4xl": 40,
    "5xl":48
  },
  radii: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    "2xl": 24,
    "3xl": 28,
    '4xl':32,
    full: 9999,
  },
  fonts: {
    heading: "System",
    body: "System",
  },
};

// const darkTheme: Theme = {
//   ...defaultTheme,
//   colors: {
//     primary: {
//       50: "#0E3D2A",
//       100: "#134A34",
//       200: "#17573E",
//       300: "#1C6349",
//       400: "#206E55",
//       main: "#4E9478",
//       600: "#6DA790",
//       700: "#97C0AF",
//       800: "#C0D9CE",
//       900: "#E6F0EC",
//     },
//     secondary: defaultTheme.colors.secondary,
//     gray: {
//       50: "#111827",
//       100: "#1F2937",
//       200: "#374151",
//       300: "#4B5563",
//       400: "#6B7280",
//       main: "#9CA3AF",
//       600: "#D1D5DB",
//       700: "#E5E7EB",
//       800: "#F3F4F6",
//       900: "#F9FAFB",
//     },
//     error: "#FCA5A5",
//     warning: "#FCD34D",
//     success: "#34D399",
//     info: "#93C5FD",
//     background: {
//       primary: "#111827",
//       secondary: "#1F2937",
//     },
//     text: {
//       primary: "#F9FAFB",
//       secondary: "#E5E7EB",
//       tertiary: "#D1D5DB",
//     },
//     specialistColors: [
//       "#1A2F4B", // Dark blue
//       "#2F261A", // Dark orange
//       "#1A2F1A", // Dark green
//       "#2F1A2F", // Dark purple
//       "#2F2F1A", // Dark yellow
//     ],
//     cardGradients: [
//       ["#1A3399", "#2952CC"], // Dark blue gradient
//       ["#991A1A", "#CC2929"], // Dark red gradient
//       ["#1A6699", "#2989CC"], // Dark light blue gradient
//       ["#994D1A", "#CC6629"], // Dark orange gradient
//       ["#4D1A99", "#6629CC"], // Dark purple gradient
//     ],
//   },
// };

// const blueTheme: Theme = {
//   ...defaultTheme,
//   colors: {
//     ...defaultTheme.colors,
//     primary: {
//       50: '#EFF6FF',
//       100: '#DBEAFE',
//       200: '#BFDBFE',
//       300: '#93C5FD',
//       400: '#60A5FA',
//       main: '#3B82F6',
//       600: '#2563EB',
//       700: '#1D4ED8',
//       800: '#1E40AF',
//       900: '#1E3A8A',
//     },
//     specialistColors: [
//       "#D6E8FF", // Light blue variant
//       "#FFE8D6", // Light orange variant
//       "#D6FFE8", // Light green variant
//       "#E8D6FF", // Light purple variant
//       "#FFEED6", // Light yellow variant
//     ],
//     cardGradients: [
//       ["#3366FF", "#66A3FF"], // Blue theme blue gradient
//       ["#FF6666", "#FFA3A3"], // Blue theme red gradient
//       ["#66CCFF", "#A3E8FF"], // Blue theme light blue gradient
//       ["#FF9966", "#FFCCA3"], // Blue theme orange gradient
//       ["#6666FF", "#A3A3FF"], // Blue theme purple gradient
//     ],
//   },
// };

// const purpleTheme: Theme = {
//   ...defaultTheme,
//   colors: {
//     ...defaultTheme.colors,
//     primary: {
//       50: '#F5F3FF',
//       100: '#EDE9FE',
//       200: '#DDD6FE',
//       300: '#C4B5FD',
//       400: '#A78BFA',
//       main: '#8B5CF6',
//       600: '#7C3AED',
//       700: '#6D28D9',
//       800: '#5B21B6',
//       900: '#4C1D95',
//     },
//     specialistColors: [
//       "#E3E0FF", // Purple tinted light blue
//       "#FFE0E3", // Purple tinted light orange
//       "#E0FFE3", // Purple tinted light green
//       "#F3E0FF", // Light purple
//       "#FFF5E0", // Purple tinted light yellow
//     ],
//     cardGradients: [
//       ["#6B4BFF", "#9E85FF"], // Purple theme blue gradient
//       ["#FF4B6B", "#FF85A3"], // Purple theme red gradient
//       ["#4B6BFF", "#85A3FF"], // Purple theme light blue gradient
//       ["#FF6B4B", "#FFA385"], // Purple theme orange gradient
//       ["#8B4BFF", "#B585FF"], // Purple theme purple gradient
//     ],
//   },
// };

// const roseTheme: Theme = {
//   ...defaultTheme,
//   colors: {
//     ...defaultTheme.colors,
//     primary: {
//       50: '#FFF1F2',
//       100: '#FFE4E6',
//       200: '#FECDD3',
//       300: '#FDA4AF',
//       400: '#FB7185',
//       main: '#F43F5E',
//       600: '#E11D48',
//       700: '#BE123C',
//       800: '#9F1239',
//       900: '#881337',
//     },
//     specialistColors: [
//       "#FFE0E6", // Rose tinted light blue
//       "#FFE6E0", // Rose tinted light orange
//       "#E6FFE0", // Rose tinted light green
//       "#FFE0FF", // Rose tinted light purple
//       "#FFFFE0", // Rose tinted light yellow
//     ],
//     cardGradients: [
//       ["#FF4B6B", "#FF85A3"], // Rose theme blue gradient
//       ["#FF4B4B", "#FF8585"], // Rose theme red gradient
//       ["#4BFFB5", "#85FFD1"], // Rose theme light blue gradient
//       ["#FFB54B", "#FFD185"], // Rose theme orange gradient
//       ["#FF4BB5", "#FF85D1"], // Rose theme purple gradient
//     ],
//   },
// };

export const themes = {
  default: defaultTheme,
  // dark: darkTheme,
  // blue: blueTheme,
  // purple: purpleTheme,
  // rose: roseTheme,
} as const;

export type ThemeName = keyof typeof themes;
export { defaultTheme };




