import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { StyleSheet, View, Text, Dimensions, TouchableOpacity, Platform, Animated } from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors } from '@/constants/colors';
import { useNutritionStore, useNutritionHistory } from '@/store/nutritionStore';
import { ChevronDown } from 'lucide-react-native';
import { parseNutritionalData } from '@/utils/nutrition/parseNutritionalData';
import logger from '@/utils/logger/logger';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

// Define chart data types
type ChartDataset = {
  data: number[];
  color: string;
  strokeWidth: number;
};

type ChartData = {
  labels: string[];
  datasets: ChartDataset[];
  legend: string[];
};

type Point = {
  x: number;
  y: number;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.lg),
      padding: moderateScale(16),
      borderWidth:moderateScale(1),
      borderColor:theme.colors.gray[300]
    },
    pickerContainer: {
      marginBottom: moderateVerticalScale(10),
    },
    pickerLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      fontWeight: "500",
      marginBottom: moderateVerticalScale(8),
      color: theme.colors.gray[700],
    },
    dropdownContainer: {
      position: "relative",
      zIndex: 9999,
    },
    dropdownButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(12),
      backgroundColor: theme.colors.secondary[50],
    },
    dropdownButtonContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    dropdownButtonText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[800],
    },
    colorIndicator: {
      width: moderateScale(12),
      height: moderateScale(12),
      borderRadius: moderateScale(theme.radii.xs),
      marginRight: moderateScale(8),
    },
    dropdownMenu: {
      position: "absolute",
      top: "100%",
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      marginTop: moderateVerticalScale(4),
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: moderateScale(4),
      elevation: 5,
      zIndex: 9999,
      overflow:"hidden"
    },
    dropdownItem: {
      padding: moderateScale(12),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    dropdownItemContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    dropdownItemSelected: {
      backgroundColor: theme.colors.gray[100],
    },
    dropdownItemText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[800],
    },
    dropdownItemTextSelected: {
      color: theme.colors.primary.main,
      fontWeight: "500",
    },
    chartContainer: {
      alignItems: "center",
    },
    chartTitle: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "500",
      color: colors.black,
      marginTop: moderateVerticalScale(10),
      marginBottom: moderateVerticalScale(15),
      textAlign: "center",
    },
    emptyContainer: {
      padding: moderateScale(24),
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.lg),
      borderColor : theme.colors.gray[300],
      borderWidth : moderateScale(1)

    },
    emptyText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray.main,
      textAlign: "center",
    },
    lineChartContainer: {
      width: "100%",
      marginTop: 0,
      marginLeft:moderateScale(24),
      marginRight:moderateScale(32),
      paddingBottom: moderateVerticalScale(16),
    },
    lineChartHeader: {
      flexDirection: "row",
      justifyContent: "flex-end",
      marginBottom: moderateVerticalScale(8),
    },
    lineChartLegend: {
      flexDirection: "row",
    },
    lineChartLegendItem: {
      flexDirection: "row",
      alignItems: "center",
      marginLeft: moderateScale(16),
    },
    lineChartLegendColor: {
      width: moderateScale(12),
      height: moderateScale(12),
      borderRadius: moderateScale(theme.radii.xs),
      marginRight: moderateScale(4),
    },
    lineChartLegendText: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[700],
      fontWeight: "500",
    },
    lineChartContent: {
      flexDirection: "row",
      marginTop: moderateVerticalScale(8),
    },
    yAxisLabels: {
      width: moderateScale(40),
      height: moderateVerticalScale(180),
      justifyContent: "space-between",
      alignItems: "flex-end",
      paddingRight: moderateScale(8),
      paddingBottom: moderateVerticalScale(24),
    },
    axisLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[600],
      fontWeight: "500",
    },
    chartArea: {
      flex: 1,
      height: moderateVerticalScale(180),
      position: "relative",
      marginLeft: moderateScale(4),
    },
    gridLine: {
      position: "absolute",
      left: 0,
      right: 0,
      height: 1,
      backgroundColor: theme.colors.gray[200],
      zIndex: 1,
    },
    verticalGridLine: {
      position: "absolute",
      width: 1,
      backgroundColor: theme.colors.gray[200],
      zIndex: 1,
    },
    xAxisLine: {
      position: "absolute",
      left: 0,
      right: 0,
      bottom: moderateVerticalScale(30),
      height: 1,
      backgroundColor: theme.colors.gray[400],
      zIndex: 2,
    },
    lineContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: moderateVerticalScale(30),
      zIndex: 5,
      overflow: "visible",
    },
    svgContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      zIndex: 10,
    },
    nativeLineChart: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 10,
    },
    nativeLine: {
      position: "absolute",
      backgroundColor: theme.colors.primary.main,
      transformOrigin: "left center",
    },
    dataPoint: {
      position: "absolute",
      width: moderateScale(50),
      height: moderateScale(50),
      borderRadius: moderateScale(theme.radii.xs),
      backgroundColor: theme.colors.primary.main,
      borderWidth: 2,
      borderColor: theme.colors.secondary[50],
      zIndex: 15,
    },
    xAxisLabels: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      height: moderateVerticalScale(30),
    },
    xAxisLabel: {
      position: "absolute",
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[600],
      textAlign: "center",
      width: moderateScale(30),
      fontWeight: "500",
    },
    tooltip: {
      position: "absolute",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderRadius: moderateScale(10),
      padding: moderateScale(8),
      zIndex: 20,
      width: moderateScale(80),
    },
    tooltipDate: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(10),
      fontWeight: "500",
      textAlign: "center",
      marginBottom: moderateVerticalScale(4),
    },
    tooltipValue: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(10),
      textAlign: "center",
    },
  });

// Define nutrient color mapping
const nutrientColors: Record<string, string> = {
  calories: '#3A8A71', // energy / heat
  protein:  '#8D6E63', // solid / grounded
  carbs:    '#F4A261', // wheat / grain
  fat:      '#FFD166', // butter / rich
  sugars:   '#F472B6', // sweet / candy
};

// Helper function to determine which indices should show labels
const getVisibleLabelIndices = (totalPoints: number): number[] => {
  if (totalPoints <= 7) {
    // If 7 or fewer points, show all labels
    return Array.from({ length: totalPoints }, (_, i) => i);
  }
  
  // For more than 7 points, show a subset
  const indices = [0]; // Always show first point
  
  // Calculate step size to show approximately 5-7 labels
  const step = Math.ceil(totalPoints / 6);
  
  // Add evenly spaced indices
  for (let i = step; i < totalPoints - 1; i += step) {
    indices.push(i);
  }
  
  // Always show last point
  if (totalPoints > 1 && !indices.includes(totalPoints - 1)) {
    indices.push(totalPoints - 1);
  }
  
  return indices;
};

// Web-compatible line chart implementation
type WebCompatibleLineChartProps = {
  chartData: ChartData;
  selectedNutrient: string;
};

const WebCompatibleLineChart = React.memo(({ chartData, selectedNutrient }: WebCompatibleLineChartProps) => {
  // State to track which point is selected for tooltip
    const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  const [selectedPoint, setSelectedPoint] = useState<number | null>(null);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const screenWidth = Dimensions.get('window').width;

  const maxValue = Math.max(...chartData.datasets[0].data, 1);

  // Responsive chart dimensions
  const chartHeight = moderateVerticalScale(180);
  const chartWidth = screenWidth - moderateScale(80);

  // Responsive padding object
  const chartPadding = {
    top: moderateVerticalScale(10),
    right: moderateScale(30),
    bottom: moderateVerticalScale(30),
    left: moderateScale(30),
  };
  
  // Get color for selected nutrient
  const lineColor = nutrientColors[selectedNutrient] || colors.primary;
  
  // Function to handle point selection
  const handlePointSelect = useCallback((index: number) => {
    logger.info('THE POINT IS BEING TOUCHED');
    // Clear any existing timeout
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
    
    // Set the selected point
    setSelectedPoint(index);
    
    // Set timeout to clear the selection after 3 seconds
    tooltipTimeoutRef.current = setTimeout(() => {
      setSelectedPoint(null);
    }, 3000);
  }, []);
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, []);
  
  // Animation state
  const [animationProgress, setAnimationProgress] = useState(0);
  const animationRef = useRef<number | null>(null);
  
  // Reset and start animation when data or selected nutrient changes
  useEffect(() => {
    setAnimationProgress(0);
    
    const startTime = Date.now();
    const duration = 300; // Animation duration in ms
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      setAnimationProgress(progress);
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [chartData.datasets[0].data, selectedNutrient]);
  
  // Calculate points for the line with padding and animation
  const points: Point[] = useMemo(() => chartData.datasets[0].data.map((value: number, index: number) => {
    // Adjust x calculation to account for padding and ensure points stay within boundaries
    const x = chartPadding.left + (index / (chartData.labels.length - 1 || 1)) * (chartWidth - chartPadding.left - chartPadding.right);
    // Adjust y calculation to account for padding, animation, and ensure points stay within boundaries
    const animatedValue = value * animationProgress;
    const y = chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) - (animatedValue / maxValue) * (chartHeight - chartPadding.top - chartPadding.bottom);
    return { x, y };
  }), [chartData.datasets, chartData.labels, chartWidth, chartPadding, animationProgress, maxValue]);
  
  // Create the path for the line with bezier curves for smooth transitions
  const path = useMemo(() => {
    let pathString = '';
    if (points.length > 0) {
      pathString = `M ${points[0].x} ${points[0].y}`;
      
      for (let i = 1; i < points.length; i++) {
        // Use cubic bezier curves for smoother transitions
        const prevPoint = points[i - 1];
        const currentPoint = points[i];
        
        // Calculate control points for the curve
        // This creates a smoother curve by placing control points at a distance from each point
        const controlPointX1 = prevPoint.x + (currentPoint.x - prevPoint.x) / 3;
        const controlPointY1 = prevPoint.y;
        const controlPointX2 = prevPoint.x + 2 * (currentPoint.x - prevPoint.x) / 3;
        const controlPointY2 = currentPoint.y;
        
        pathString += ` C ${controlPointX1} ${controlPointY1}, ${controlPointX2} ${controlPointY2}, ${currentPoint.x} ${currentPoint.y}`;
      }
    }
    return pathString;
  }, [points]);
  
  return (
    <View style={styles.lineChartContainer}>
      <View style={styles.lineChartContent}>
        {/* Y-axis labels with proper alignment */}
        <View style={styles.yAxisLabels}>
          <Text style={styles.axisLabel}>{maxValue}</Text>
          <Text style={styles.axisLabel}>{Math.round(maxValue * 0.75)}</Text>
          <Text style={styles.axisLabel}>{Math.round(maxValue * 0.5)}</Text>
          <Text style={styles.axisLabel}>{Math.round(maxValue * 0.25)}</Text>
          <Text style={styles.axisLabel}>0</Text>
        </View>
        
        <View style={styles.chartArea}>
          {/* Grid lines aligned with y-axis labels */}
          <View style={[styles.gridLine, { top: chartPadding.top }]} />
          <View style={[styles.gridLine, { top: chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * 0.25 }]} />
          <View style={[styles.gridLine, { top: chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * 0.5 }]} />
          <View style={[styles.gridLine, { top: chartPadding.top + (chartHeight - chartPadding.top - chartPadding.bottom) * 0.75 }]} />
          <View style={[styles.gridLine, { top: chartHeight - chartPadding.bottom }]} />
          
          {/* Vertical grid lines */}
          {chartData.labels.map((_, index) => {
            const x = chartPadding.left + (index / (chartData.labels.length - 1 || 1)) * 
                    (chartWidth - chartPadding.left - chartPadding.right);
            return (
              <View 
                key={`vgrid-${index}`}
                style={[
                  styles.verticalGridLine, 
                  { 
                    left: x,
                    height: chartHeight - chartPadding.bottom - chartPadding.top,
                    top: chartPadding.top
                  }
                ]} 
              />
            );
          })}
          
          {/* X-axis line */}
          <View style={styles.xAxisLine} />
          
          {/* Line chart */}
          <View style={styles.lineContainer}>
            {Platform.OS === 'web' ? (
              <svg width={chartWidth} height={chartHeight} style={styles.svgContainer}>
                {/* Area fill under the curve with gradient */}
                <defs>
                  <linearGradient id={`gradient-${selectedNutrient}`} x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor={lineColor} stopOpacity="0.3" />
                    <stop offset="100%" stopColor={lineColor} stopOpacity="0.05" />
                  </linearGradient>
                </defs>
                
                {/* Area fill path */}
                <path 
                  d={`${path} L ${points[points.length-1]?.x || 0} ${chartHeight - chartPadding.bottom} L ${points[0]?.x || 0} ${chartHeight - chartPadding.bottom} Z`} 
                  fill={`url(#gradient-${selectedNutrient})`}
                  strokeWidth="0"
                />
                
                {/* Path for connecting line between points - increased stroke width and z-index */}
                <path 
                  d={path} 
                  stroke={lineColor} 
                  strokeWidth="3" 
                  fill="none" 
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                
                {/* Data points with click handlers */}
                {points.map((point: Point, index: number) => (
                  <circle 
                    key={index} 
                    cx={point.x} 
                    cy={point.y} 
                    r={selectedPoint === index ? "7" : "5"} 
                    fill={lineColor} 
                    stroke={colors.white}
                    strokeWidth="2"
                    onClick={() => handlePointSelect(index)}
                    style={{ cursor: 'pointer' }}
                  />
                ))}
              </svg>
            ) : (
              // Fallback for native platforms with connecting lines
              <View style={styles.nativeLineChart}>
                {/* Draw connecting lines for native platforms */}
                {points.length > 1 && points.map((point: Point, index: number) => {
                  // Skip the first point for lines (we draw lines from current to previous point)
                  if (index === 0) return null;
                  
                  const prevPoint = points[index - 1];
                  return (
                    <View 
                      key={`line-${index}`}
                      style={[
                        styles.nativeLine,
                        {
                          left: prevPoint.x,
                          top: prevPoint.y,
                          width: Math.sqrt(Math.pow(point.x - prevPoint.x, 2) + Math.pow(point.y - prevPoint.y, 2)),
                          transform: [{ 
                            rotate: `${Math.atan2(point.y - prevPoint.y, point.x - prevPoint.x)}rad` 
                          }],
                          height: 3, // Line thickness
                          backgroundColor: lineColor,
                        }
                      ]}
                    />
                  );
                })}
                
                {/* Draw the points on top of the lines with touch handlers */}
                {points.map((point: Point, index: number) => (
                  <TouchableOpacity 
                    key={`point-${index}`}
                    style={[
                      styles.dataPoint,
                      {
                        left: point.x - (selectedPoint === index ? 6 : 5),
                        top: point.y - (selectedPoint === index ? 6 : 5),
                        width: selectedPoint === index ? 15 : 12, // Larger when selected
                        height: selectedPoint === index ? 15 : 12, // Larger when selected
                        borderWidth: 2,
                        backgroundColor: lineColor,
                      }
                    ]}
                    onPress={() => handlePointSelect(index)}
                  />
                ))}
              </View>
            )}
          </View>
          
          {/* X-axis labels positioned below the x-axis - only show subset when many points */}
          <View style={styles.xAxisLabels}>
            {getVisibleLabelIndices(chartData.labels.length).map((index: number) => (
              <Text 
                key={index} 
                style={[
                  styles.xAxisLabel,
                  { 
                    left: chartPadding.left + (index / (chartData.labels.length - 1 || 1)) * 
                          (chartWidth - chartPadding.left - chartPadding.right) - 15,
                    top: 5 // Position below x-axis
                  }
                ]}
              >
                {chartData.labels[index]}
              </Text>
            ))}
          </View>
          
          {/* Tooltip for selected point */}
          {selectedPoint !== null && (
            <View 
              style={[
                styles.tooltip,
                {
                  left: Math.max(10, Math.min(chartWidth - 90, points[selectedPoint].x - 40)),
                  top: Math.max(10, points[selectedPoint].y - 45),
                }
              ]}
            >
              <Text style={styles.tooltipDate}>{chartData.labels[selectedPoint]}</Text>
              <Text style={styles.tooltipValue}>
                {t(`chart.dropdown.${selectedNutrient}`)}: {chartData.datasets[0].data[selectedPoint]}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
});

// Define dropdown option type
type DropdownOption = {
  label: string;
  value: string;
};

// Custom dropdown component to replace Picker
type CustomDropdownProps = {
  selectedValue: string;
  onValueChange: (value: string) => void;
  options: DropdownOption[];
};

const CustomDropdown = React.memo(({ selectedValue, onValueChange, options }: CustomDropdownProps) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.dropdownContainer}>
      <TouchableOpacity 
        style={styles.dropdownButton}
        onPress={() => {
          // Add Mixpanel tracking here
          trackUserInteraction('Nutrition Graph Dropdown Clicked', {
            currentSelection: selectedValue,
            isOpening: !isOpen
          });
          
          setIsOpen(!isOpen);
        }}
      >
        <View style={styles.dropdownButtonContent}>
          <View 
            style={[
              styles.colorIndicator, 
              { backgroundColor: nutrientColors[selectedValue] || colors.primary }
            ]} 
          />
          <Text style={styles.dropdownButtonText}>
            {t(`chart.dropdown.${selectedValue}`)}
          </Text>
        </View>
        <ChevronDown size={20} color={colors.gray[700]} />
      </TouchableOpacity>
      
      {isOpen && (
        <View style={styles.dropdownMenu}>
          {options.map((option: DropdownOption) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.dropdownItem,
                selectedValue === option.value && styles.dropdownItemSelected
              ]}
              onPress={() => {
                onValueChange(option.value);
                setIsOpen(false);
              }}
            >
              <View style={styles.dropdownItemContent}>
                <View 
                  style={[
                    styles.colorIndicator, 
                    { backgroundColor: nutrientColors[option.value] || colors.primary }
                  ]} 
                />
                <Text 
                  style={[
                    styles.dropdownItemText,
                    selectedValue === option.value && styles.dropdownItemTextSelected
                  ]}
                >
                  {option.label}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
});

type NutritionChartProps = {
  reports?: any[];
};

const NutritionChart = React.memo(({ reports = [] }: NutritionChartProps) => {
  const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  const [selectedNutrient, setSelectedNutrient] = useState('calories');
  const nutritionHistory = useNutritionHistory();
  
  const groupedData = useMemo(() => reports.reduce((acc: Record<string, Record<string, number>>, report: any) => {
    if (!report.timestamp || report.type !== 'FOOD_ITEM') return acc;
    
    const date = new Date(report.timestamp).toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        sugars: 0
      };
    }
    
    if (report.food_images && report.food_images.calories !== null && report.food_images.calories !== undefined && report.food_images.calories !== 0) {
      // Use structured data directly
      acc[date].calories += parseInt(report.food_images.calories || 0);
      acc[date].protein += parseInt(report.food_images.protein || 0);
      acc[date].carbs += parseInt(report.food_images.carbohydrates || 0);
      acc[date].fat += parseInt(report.food_images.fat || 0);
      acc[date].sugars += parseInt(report.food_images.sugar || 0);
    } 
    // Fallback to parsing processed_text if available
    else if (report.processed_text || report.processed_output) {
      const processedText = report.processed_text || report.processed_output;
      if (processedText) {
        const parsedData = parseNutritionalData(processedText);
        
        if (parsedData && parsedData.length > 0) {
          parsedData.forEach((item: any) => {
            const label = item.label.toLowerCase();
            const valueMatch = item.value.match(/(\d+(\.\d+)?)/);
            const value = valueMatch ? parseInt(valueMatch[0]) : 0;
            
            if (label.includes('calories') || label.includes('total calories')) {
              acc[date].calories += value;
            } else if (label.includes('protein')) {
              acc[date].protein += value;
            } else if (label.includes('carb')) {
              acc[date].carbs += value;
            } else if (label.includes('fat')) {
              acc[date].fat += value;
            } else if (label.includes('sugar')) {
              acc[date].sugars += value;
            }
          });
        }
      }
    }
    
    return acc;
  }, {}), [reports]);
  
  const dates = useMemo(() => Object.keys(groupedData).sort(), [groupedData]);
  const values = useMemo(() => dates.map(date => groupedData[date][selectedNutrient]), [dates, groupedData, selectedNutrient]);
  
  const formattedDates = useMemo(() => dates.map(date => {
    const d = new Date(date);
    return `${d.getMonth() + 1}/${d.getDate()}`;
  }), [dates]);
  
  // Chart data
  const chartData = useMemo(() => ({
    labels: formattedDates,
    datasets: [
      {
        data: values,
        color: nutrientColors[selectedNutrient] || colors.primary,
        strokeWidth: 2,
      },
    ],
    legend: [selectedNutrient.charAt(0).toUpperCase() + selectedNutrient.slice(1)],
  }), [formattedDates, values, selectedNutrient]);

  // Dropdown options
  const dropdownOptions = useMemo(() => [
    { label: t('chart.dropdown.calories'), value: 'calories' },
    { label: t('chart.dropdown.protein'), value: 'protein' },
    { label: t('chart.dropdown.carbs'), value: 'carbs' },
    { label: t('chart.dropdown.fat'), value: 'fat' },
    { label: t('chart.dropdown.sugars'), value: 'sugars' }
  ], [t]);
  
  // Handle nutrient selection change
  const handleNutrientChange = useCallback((value: string) => {
    setSelectedNutrient(value);
  }, []);
  
  // If we have no data, show a message
  if (dates.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>{t('chart.emptyState')}</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.pickerContainer}>
        <Text style={styles.chartTitle}>{t('chart.title')}</Text>
        <Text style={styles.pickerLabel}>{t('chart.selectNutrient')}</Text>
        <CustomDropdown
          selectedValue={selectedNutrient}
          onValueChange={handleNutrientChange}
          options={dropdownOptions}
        />
      </View>
      
      <View style={styles.chartContainer}>
        <WebCompatibleLineChart 
          chartData={chartData} 
          selectedNutrient={selectedNutrient} 
        />
      </View>
    </View>
  );
});

// const styles = StyleSheet.create({
//   container: {
//     backgroundColor: colors.white,
//     borderRadius: moderateScale(16),
//     padding: moderateScale(16),
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.1,
//     shadowRadius: moderateScale(4),
//     elevation: 2,
//   },
//   pickerContainer: {
//     marginBottom: moderateVerticalScale(10),
//   },
//   pickerLabel: {
//     fontSize: moderateScale(12),
//     fontWeight: '500',
//     marginBottom: moderateVerticalScale(8),
//     color: colors.gray[700],
//   },
//   dropdownContainer: {
//     position: 'relative',
//     zIndex: 9999,
//   },
//   dropdownButton: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(12),
//     backgroundColor: colors.white,
//   },
//   dropdownButtonContent: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   dropdownButtonText: {
//     fontSize: moderateScale(14),
//     color: colors.gray[800],
//   },
//   colorIndicator: {
//     width: moderateScale(12),
//     height: moderateScale(12),
//     borderRadius: moderateScale(6),
//     marginRight: moderateScale(8),
//   },
//   dropdownMenu: {
//     position: 'absolute',
//     top: '100%',
//     left: 0,
//     right: 0,
//     backgroundColor: colors.white,
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     marginTop: moderateVerticalScale(4),
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.1,
//     shadowRadius: moderateScale(4),
//     elevation: 5,
//     zIndex: 9999,
//   },
//   dropdownItem: {
//     padding: moderateScale(12),
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   dropdownItemContent: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   dropdownItemSelected: {
//     backgroundColor: colors.gray[100],
//   },
//   dropdownItemText: {
//     fontSize: moderateScale(14),
//     color: colors.gray[800],
//   },
//   dropdownItemTextSelected: {
//     color: colors.primary,
//     fontWeight: '500',
//   },
//   chartContainer: {
//     alignItems: 'center',
//   },
//   chartTitle: {
//     fontSize: moderateScale(16),
//     fontWeight: '500',
//     color: colors.black,
//     marginTop: moderateVerticalScale(10),
//     marginBottom: moderateVerticalScale(15),
//     textAlign: 'center',
//   },
//   emptyContainer: {
//     padding: moderateScale(24),
//     alignItems: 'center',
//     justifyContent: 'center',
//     backgroundColor: colors.white,
//     borderRadius: moderateScale(16),
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.1,
//     shadowRadius: moderateScale(4),
//     elevation: 2,
//   },
//   emptyText: {
//     fontSize: moderateScale(16),
//     color: colors.gray[500],
//     textAlign: 'center',
//   },
//   lineChartContainer: {
//     width: '100%',
//     marginTop: 0,
//     paddingBottom: moderateVerticalScale(16),
//   },
//   lineChartHeader: {
//     flexDirection: 'row',
//     justifyContent: 'flex-end',
//     marginBottom: moderateVerticalScale(8),
//   },
//   lineChartLegend: {
//     flexDirection: 'row',
//   },
//   lineChartLegendItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginLeft: moderateScale(16),
//   },
//   lineChartLegendColor: {
//     width: moderateScale(12),
//     height: moderateScale(12),
//     borderRadius: moderateScale(6),
//     marginRight: moderateScale(4),
//   },
//   lineChartLegendText: {
//     fontSize: moderateScale(12),
//     color: colors.gray[700],
//     fontWeight: '500',
//   },
//   lineChartContent: {
//     flexDirection: 'row',
//     marginTop: moderateVerticalScale(8),
//   },
//   yAxisLabels: {
//     width: moderateScale(40),
//     height: moderateVerticalScale(180),
//     justifyContent: 'space-between',
//     alignItems: 'flex-end',
//     paddingRight: moderateScale(8),
//     paddingTop: moderateVerticalScale(10),
//     paddingBottom: moderateVerticalScale(30),
//   },
//   axisLabel: {
//     fontSize: moderateScale(10),
//     color: colors.gray[600],
//     fontWeight: '500',
//   },
//   chartArea: {
//     flex: 1,
//     height: moderateVerticalScale(180),
//     position: 'relative',
//     marginLeft: moderateScale(4),
//   },
//   gridLine: {
//     position: 'absolute',
//     left: 0,
//     right: 0,
//     height: 1,
//     backgroundColor: colors.gray[200],
//     zIndex: 1,
//   },
//   verticalGridLine: {
//     position: 'absolute',
//     width: 1,
//     backgroundColor: colors.gray[200],
//     zIndex: 1,
//   },
//   xAxisLine: {
//     position: 'absolute',
//     left: 0,
//     right: 0,
//     bottom: moderateVerticalScale(30),
//     height: 1,
//     backgroundColor: colors.gray[400],
//     zIndex: 2,
//   },
//   lineContainer: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: moderateVerticalScale(30),
//     zIndex: 5,
//     overflow: 'visible',
//   },
//   svgContainer: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     zIndex: 10,
//   },
//   nativeLineChart: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     zIndex: 10,
//   },
//   nativeLine: {
//     position: 'absolute',
//     backgroundColor: colors.primary,
//     transformOrigin: 'left center',
//   },
//   dataPoint: {
//     position: 'absolute',
//     width: moderateScale(50),
//     height: moderateScale(50),
//     borderRadius: moderateScale(5),
//     backgroundColor: colors.primary,
//     borderWidth: 2,
//     borderColor: colors.white,
//     zIndex: 15,
//   },
//   xAxisLabels: {
//     position: 'absolute',
//     bottom: 0,
//     left: 0,
//     right: 0,
//     height: moderateVerticalScale(30),
//   },
//   xAxisLabel: {
//     position: 'absolute',
//     fontSize: moderateScale(10),
//     color: colors.gray[600],
//     textAlign: 'center',
//     width: moderateScale(30),
//     fontWeight: '500',
//   },
//   tooltip: {
//     position: 'absolute',
//     backgroundColor: 'rgba(0, 0, 0, 0.8)',
//     borderRadius: moderateScale(6),
//     padding: moderateScale(8),
//     zIndex: 20,
//     width: moderateScale(80),
//   },
//   tooltipDate: {
//     color: colors.white,
//     fontSize: moderateScale(10),
//     fontWeight: '500',
//     textAlign: 'center',
//     marginBottom: moderateVerticalScale(4),
//   },
//   tooltipValue: {
//     color: colors.white,
//     fontSize: moderateScale(10),
//     textAlign: 'center',
//   },
// });

export default NutritionChart;
