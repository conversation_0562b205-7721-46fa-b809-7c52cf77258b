import axios from 'axios'; // Keep for type checking
import axiosInstance from './axiosInstance';
import { Message } from '@/store/chatStore';
import logger from '@/utils/logger/logger';
import { getEnvironmentVariable } from '@/utils/getEnvironmentVariable';
import { trackUserInteraction, trackOperation, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';
import { AppEventsLogger } from 'react-native-fbsdk-next';

interface ChatHistoryResponse {
  success: boolean;
  chats: {
    [key: string]: {
      message: string;
      id: string;
      data: any;
      message_type: string;
      role: 'human' | 'assistant';
      source: string;
      phone: string;
      timestamp: string;
      meta: {
        source: string;
        user_id: string;
        response_id: string;
        source_response_ids: any;
      };
    } | number; // For the length property
  };
  last_message_id?: string;
}

// Interface for message request
interface SendMessageRequest {
  text: string;
  providerMessageId: string;
  attachment: string | null | undefined;
  fileExtension: string | null;
  messageType: string;
  sender: string;
  source: string;
  phoneNumber: string;
  timestamp: number;
  requestId: string;
}

export const fetchChatHistory = async (
  limit: number = 100,
  lastMessageId?: string
): Promise<ChatHistoryResponse> => {
  try {
    if(!limit)limit=100;
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/get-chats?limit=${limit}${
      lastMessageId ? `&last_message_id=${lastMessageId}` : ''
    }`;
    
    logger.info(`Fetching chat history from: ${url}`);
    trackOperation('Fetch Chat History', OperationStatus.INITIATED, {
      limit,
      hasLastMessageId: !!lastMessageId
    });
    
    const response = await axiosInstance.get(url);
    // Filter out promotion messages
    if (response.data.chats) {
      const originalCount = response.data.chats.length;
      response.data.chats = response.data.chats.filter(
        (chat: any) => chat.message_type !== 'promotion'
      );
      const filteredCount = response.data.chats.length;
      
      if (originalCount !== filteredCount) {
        logger.info(`Filtered out ${originalCount - filteredCount} promotion messages`);
      }
    }
    trackOperation('Fetch Chat History', OperationStatus.SUCCESS, {
      messageCount: response.data.chats.length,
      hasLastMessageId: !!response.data.last_message_id
    });
    //logger.info('bhai',response.data)
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error('Error fetching chat history:', {
          status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });
      }
      
      trackOperation('Fetch Chat History', OperationStatus.FAILURE, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message
      });
    } else {
      logger.error('Error fetching chat history:', error);
      trackOperation('Fetch Chat History', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
    }
    throw error;
  }
};

export const sendMessage = async (
  message: SendMessageRequest
): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/c/${getEnvironmentVariable("TENANT")}/webhook`;
    
    logger.info(`Sending message to: ${url}`, {
      messageId: message.providerMessageId,
      messageType: message.messageType,
      hasAttachment: !!message.attachment
    });
    
    // Track user interaction for sending a message
    trackUserInteraction('Send Message', {
      messageType: message.messageType,
      hasAttachment: !!message.attachment,
      source: message.source
    });
    
    // Track the API operation
    trackOperation('Send Message API', OperationStatus.INITIATED, {
      messageId: message.providerMessageId,
      messageType: message.messageType,
      hasAttachment: !!message.attachment,
      source: message.source
    });
    
    // Add detailed logging for image and PDF files
    if (message.messageType === 'image' || message.messageType === 'pdf' || message.messageType == 'voice') {
      logger.info(`Sending ${message.messageType} payload to webhook:`, {
        messageId: message.providerMessageId,
        messageType: message.messageType,
        attachment: message.attachment,
        fileExtension: message.fileExtension,
        text: message.text,
        sender: message.sender,
        source: message.source,
        timestamp: message.timestamp,
        requestId: message.requestId
      });
      
      trackOperation(`Upload ${message.messageType.charAt(0).toUpperCase() + message.messageType.slice(1)}`, OperationStatus.INITIATED, {
        messageId: message.providerMessageId,
        hasText: !!message.text,
        fileExtension: message.fileExtension
      });
    }
    
    const response = await axiosInstance.post(url, message, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    logger.info('Message sent status:', response.status);
    logger.apiResponse('POST send message', response.data);
    
    trackOperation('Send Message API', OperationStatus.SUCCESS, {
      status: response.status,
      messageId: message.providerMessageId,
      messageType: message.messageType
    });
    
    // For file uploads, track success separately
    if (message.messageType === 'image' || message.messageType === 'pdf' || message.messageType === 'voice') {
      AppEventsLogger.logEvent(`uploaded_${message.messageType}`)
      trackOperation(`Upload ${message.messageType.charAt(0).toUpperCase() + message.messageType.slice(1)}`, OperationStatus.SUCCESS, {
        messageId: message.providerMessageId,
        fileExtension: message.fileExtension
      });
    }else{
      AppEventsLogger.logEvent('message_sent');
    }
    
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error('Error sending message:', {
          status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });
      }
  
      
      trackOperation('Send Message API', OperationStatus.FAILURE, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        messageId: message.providerMessageId,
        messageType: message.messageType,
        message: error.message
      });
      
      // For file uploads, track failure separately
      if (message.messageType === 'image' || message.messageType === 'pdf' || message.messageType === 'voice') {
        trackOperation(`Upload ${message.messageType.charAt(0).toUpperCase() + message.messageType.slice(1)}`, OperationStatus.FAILURE, {
          messageId: message.providerMessageId,
          fileExtension: message.fileExtension,
          status: error.response?.status,
          message: error.message
        });
      }
    } else {
      logger.error('Error sending message:', error);
      trackOperation('Send Message API', OperationStatus.FAILURE, {
        messageId: message.providerMessageId,
        messageType: message.messageType,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // For file uploads, track failure separately
      if (message.messageType === 'image' || message.messageType === 'pdf' || message.messageType === 'voice') {
        trackOperation(`Upload ${message.messageType.charAt(0).toUpperCase() + message.messageType.slice(1)}`, OperationStatus.FAILURE, {
          messageId: message.providerMessageId,
          fileExtension: message.fileExtension,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    throw error;
  }
};