import { useEffect, useMemo, useCallback } from "react";
import { StyleSheet, View, Text, TouchableOpacity, StatusBar, ActivityIndicator, Platform, Keyboard } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { Stack, useNavigation } from "expo-router";
import TopNavbar from "@/components/navigation/TopNavbar";
import { OperationStatus, trackOperation, trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import { FormProgress } from "@/components/personalize/FormProgress";
import { FormStepper } from "@/components/personalize/FromStepper";
import { BasicInfoStep } from "@/components/personalize/steps/BasicInfoStep";
import { LifestyleStep } from "@/components/personalize/steps/LifestyleStep";
import { AllergiesStep } from "@/components/personalize/steps/AllergiesStep";
import { MedicationsStep } from "@/components/personalize/steps/MedicationsStep";
import { ConditionsStep } from "@/components/personalize/steps/ConditionsStep";
import { ReproductiveStep } from "@/components/personalize/steps/ReproductiveStep";
import { ReviewStep } from "@/components/personalize/steps/ReviewStep";
import { SuccessScreen } from "@/components/personalize/SuccessScreen";
import { defaultFormData, useFormDataStore } from "@/store/formDataStore";
import logger from "@/utils/logger/logger";
import { useUserDataStore } from "@/store/userDataStore";
import * as Haptics from "expo-haptics";
import { trackClarityEvent } from "@/utils/clarity/clarity-utils";
import { moderateScale, verticalScale, scale } from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { mediumHaptic } from "@/utils/haptics/haptics";
import { useTranslation } from "react-i18next";
import { getMedicalData } from "@/services/medicalService";
import { useScreenTracking } from "../currentScreen";

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    footer: {
      padding: moderateScale(8),
      paddingHorizontal: moderateScale(16),
      borderTopWidth: 1,
      borderTopColor: theme.colors.gray[200],
      backgroundColor: theme.colors.secondary[50],
    },
    button: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: moderateScale(14),
      borderRadius: moderateScale(theme.radii.md),
      gap: moderateScale(theme.spacing.sm),
    },
    nextButton: {
      backgroundColor: theme.colors.primary.main,
    },
    buttonText: {
      color: theme.colors.secondary[50],
      fontSize: scale(theme.fontSize.md),
      fontWeight: "600",
    },
  });

export default function PersonalizeScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { currentStep, updateCurrentStep, submitForm, isLoading, setOriginalData, resetForm } = useFormDataStore();
  const navigation = useNavigation();
  const TAB_BAR_HEIGHT = Platform.OS === 'ios' ? 76 : 64;

  useScreenTracking('Personalize', { screen: "Personalize" });


  useEffect(() => {
    const initilize = async () => {
      const medicalResponse = await getMedicalData();
      const globalMedicalData = medicalResponse?.success ? medicalResponse.medicalData : null;
      // logger.info("GLOBAL MEDICAL DATA:::",globalMedicalData)
      const check = globalMedicalData ? (
        (globalMedicalData.patient_profile && 
        globalMedicalData.social_history) ||
        globalMedicalData.allergies?.length ||
        globalMedicalData.condition_details?.length ||
        globalMedicalData.menstrual_history ||
        globalMedicalData.obstetric_history 
      ) : false;
      if (check) {
        updateCurrentStep(6)
        setOriginalData(globalMedicalData);
        trackUserInteraction('Medical Data Loaded', {
          hasData: true,
          dataSource: 'global_store',
          sections: Object.keys(globalMedicalData)
        });
      }
      else{
        updateCurrentStep(0);
        resetForm();
      }
    }
    initilize();
  }, [setOriginalData]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      trackOperation('Keyboard Listener Setup', OperationStatus.INITIATED);

      const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
        navigation.setOptions({ tabBarStyle: { display: 'none' } });
        trackClarityEvent('Keyboard_Shown', { platform: Platform.OS, timestamp: new Date().toISOString() });
      });

      const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
        navigation.setOptions({
          tabBarStyle: { display: 'flex', height: TAB_BAR_HEIGHT, paddingTop: 4 }
        });
        trackClarityEvent('Keyboard_Hidden', { platform: Platform.OS, timestamp: new Date().toISOString() });
      });

      trackOperation('Keyboard Listener Setup', OperationStatus.SUCCESS);

      return () => {
        showSubscription.remove();
        hideSubscription.remove();
        trackOperation('Keyboard Listener Cleanup', OperationStatus.SUCCESS);
      };
    }
  }, [navigation]);

  const steps = useMemo(() => [
    { id: "basic", title: "Basic Info", component: BasicInfoStep },
    { id: "lifestyle", title: "Lifestyle", component: LifestyleStep },
    { id: "allergies", title: "Allergies", component: AllergiesStep },
    { id: "medications", title: "Medications", component: MedicationsStep },
    { id: "conditions", title: "Conditions", component: ConditionsStep },
    { id: "reproductive", title: "Reproductive", component: ReproductiveStep },
    { id: "review", title: "Review", component: ReviewStep },
  ], []);

  const handleNext = useCallback(async () => {
    if(Platform.OS === 'ios'){
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } else {
      await mediumHaptic()
    }
    const success = await submitForm();
    if (success) {
      logger.info("Save and Next Button Clicked and Data saved");
      if (currentStep < steps.length - 1) {
        updateCurrentStep(currentStep + 1);
        trackUserInteraction("Form Navigation", { action: "next", toStep: steps[currentStep + 1].id });
      }
    }
  }, [currentStep, steps, submitForm, updateCurrentStep]);

  const handleSubmitSuccess = useCallback(() => {
    updateCurrentStep(7);
    trackUserInteraction("Form Submitted", { success: true });
  }, [updateCurrentStep]);

  if (currentStep === 7) {
    return <SuccessScreen />;
  }

  return (
    <SafeAreaView style={styles.container} edges={["bottom"]}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('personalize.nav.title')} showProfile={true} />
      <StatusBar barStyle="dark-content" />
      <FormProgress currentStep={currentStep} onStepPress={updateCurrentStep} />
      <FormStepper currentStep={currentStep}>
        {steps.map((step, index) => {
          const StepComponent = step.component;
          return <StepComponent key={step.id} stepIndex={index} onSubmitSuccess={handleSubmitSuccess} />;
        })}
      </FormStepper>
      {currentStep !== steps.length - 1 && (
        <View style={styles.footer}>
          <TouchableOpacity style={[styles.button, styles.nextButton]} onPress={handleNext}>
            {isLoading ? (
              //@ts-ignore
              <>
                <ActivityIndicator size="small" color={colors.white} />
                <Text style={styles.buttonText}>{t('personalize.button.saving')}</Text>
              </>
            ) : (
              <Text style={styles.buttonText}>{currentStep === steps.length-2 ? t('personalize.button.review') : t('personalize.button.saveNext')}</Text>
            )}
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   footer: {
//     padding: moderateScale(8),
//     paddingHorizontal: moderateScale(16),
//     borderTopWidth: 1,
//     borderTopColor: colors.gray[200],
//     backgroundColor: colors.white,
//   },
//   button: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     padding: moderateScale(14),
//     borderRadius: moderateScale(12),
//     gap: moderateScale(8),
//   },
//   nextButton: {
//     backgroundColor: colors.primary,
//   },
//   buttonText: {
//     color: colors.white,
//     fontSize: scale(16),
//     fontWeight: "600",
//   },
// });
