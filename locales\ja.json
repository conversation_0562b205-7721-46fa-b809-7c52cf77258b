{"common": {"error": "エラー", "yes": "はい", "no": "いいえ", "sometimes": "時々", "close": "閉じる", "cancel": "キャンセル", "save": "保存", "next": "次へ", "loading": "読み込み中...", "version": "v0.0.1.7"}, "welcome": "Augustとの会話を始めるためにログインしてください", "notFound": {"title": "あれれ？", "message": "この画面は存在しません。", "goHome": "ホーム画面へ！"}, "library": {"title": "ヘルスライブラリ"}, "specialists": {"title": "専門家", "description": "より具体的な健康上の懸念事項については、専門の医療従事者にご相談ください。下記から専門家を選択してください。", "generalPhysician": {"title": "総合診療医", "description": "一般的な健康上の懸念事項やプライマリケアについて。"}, "nutritionist": {"title": "栄養士", "description": "食事、栄養、体重管理のアドバイスについて。"}, "cardiologist": {"title": "心臓専門医", "description": "心臓関連の懸念事項や心血管の健康について。"}, "neurologist": {"title": "神経学者", "description": "脳、脊髄、神経系の問題について。"}, "oncologist": {"title": "腫瘍学者", "description": "がん関連の懸念事項や治療について。"}, "endocrinologist": {"title": "内分泌学者", "description": "ホルモン関連の障害や糖尿病の管理について。"}, "dermatologist": {"title": "皮膚科医", "description": "皮膚、髪、爪の状態について。"}, "psychiatrist": {"title": "精神科医", "description": "精神的な健康上の懸念事項や心理的な健康について。"}}, "profile": {"title": "プロフィール", "defaultName": "ゲスト", "namePlaceholder": "お名前を入力してください", "saving": "保存中...", "noPhoneNumber": "電話番号なし", "loggingOut": "ログアウト中...", "about": {"title": "Augustについて", "description": "Augustについて詳しく知ろう"}, "whatsapp": {"title": "WhatsApp", "description": "WhatsAppでAugustとチャット"}, "refer": {"title": "紹介", "description": "Augustを気に入っていただけましたか？お友達と共有しましょう"}, "deleteAccount": {"title": "アカウント削除", "description": "ご利用いただきありがとうございました"}, "logout": {"title": "ログアウト", "description": "またすぐに来てくださいね。寂しくなります。"}, "shareMessage": "👋こんにちは！私が使っているこの素晴らしいアプリをチェックしてみてください！\n\n\n\n➡️私はAugustを使って、迅速で信頼できる健康情報とガイダンスを得ています。まるでポケットに医師がいるようなものです！こちらからチェックしてください：", "error": {"loadFailed": "ユーザーデータの読み込みに失敗しました", "fetchError": "ユーザーデータの取得中にエラーが発生しました", "updateNameFailed": "名前の更新に失敗しました", "updateNameError": "名前の更新中にエラーが発生しました", "loadFoodData": "食事データの読み込みに失敗しました", "logoutError": "ログアウト中にエラーが発生しました：", "shareError": "メッセージの共有中にエラーが発生しました："}}, "error": {"title": "何か問題が発生しました", "checkLogs": "詳細については、デバイスのログを確認してください。", "unknown": "不明なエラー", "unknownFile": "不明なファイル", "unknownLine": "不明な行", "unknownColumn": "不明な列"}, "auth": {"phone": {"selectCountry": "国を選択", "searchCountries": "国を検索", "validation": {"invalidPhone": "有効な電話番号を入力してください", "invalidDigits": "有効な電話番号を入力してください（7～15桁）"}}, "header": {"title": "健康上の懸念事項を即座に、そしてプライベートに明確にしましょう", "subtitle": "丁寧なガイダンス。急がない。混乱しない。", "emphasis": "ただ明確に。"}, "greeting": "こんにちは 👋", "phoneNumber": "電話番号", "requestOTP": "OTPをリクエスト", "otp": {"title": "ワンタイムパスワード", "verify": "OTPを検証", "sending": "送信中...", "countdown": "{{countdown}}秒後にOTPを再送信", "resend": "OTPを再送信", "sentTo": "OTPを送信しました ", "whatsappSuffix": " WhatsAppへ"}, "disclaimer": {"prefix": "登録することにより、あなたは私たちの ", "continuePrefix": "続行することにより、あなたは私たちの ", "termsOfService": "利用規約", "and": " と ", "privacyPolicy": "プライバシーポリシー", "whatsappConsent": "に同意し、WhatsAppを介して更新とリマインダーを受け取ることに同意します。"}}, "onboarding": {"preAuth": {"welcome": {"title": "Augustへようこそ！", "buttonText": "始めましょう"}}, "postAuth": {"step1": {"title": "こんにちは！", "subtitle": "私はAugustです 👋", "description": "あなたのデバイスの快適なコーナーと考えてください。\nそこで、健康に関するあらゆる疑問を探求できます。", "subdescription": "気になっていることは何でもお気軽にご質問ください。\n批判はありません。制限もありません！", "placeholder": "あなたのお名前は何ですか？"}, "step2": {"title": "{{userName}}さん、こんにちは！", "subtitle": "私ができること：", "features": {"health": {"title": "あなたの", "subtitle": "健康に関する質問に答えます"}, "nutrition": {"title": "あなたの", "subtitle": "マクロを追跡します"}, "reports": {"title": "レポートを", "subtitle": "分析します"}}}}, "pills": {"thoughtful": "思慮深い", "careful": "慎重な", "accurate": "正確な"}, "features": {"symptoms": {"title": "症状をチェック", "description": "1週間前から吐き気がします。どうしたのでしょうか？"}, "prescriptions": {"title": "処方箋を分析", "description": "処方箋をアップロードして、医師のように理解しましょう。"}, "medicine": {"title": "薬について知る", "description": "PCOSのメトホルミンは、ADHDの薬と相互作用しますか？"}, "plans": {"title": "パーソナライズされたプランを入手", "description": "HbA1cレベルを下げるための栄養とフィットネスプランを立てていただけますか？"}}, "buttons": {"getStarted": "開始", "next": "次へ"}, "errors": {"nameRequired": "名前を入力してください"}}, "tabs": {"chat": "チャット", "discover": "発見", "nutrition": "栄養", "personalize": "パーソナライズ"}, "chat": {"nav": {"title": "8月"}, "me": "私", "augustName": "August", "input": {"placeholder": "Augustに質問...", "disclaimer": "Augustは間違いをする可能性があります。医師に確認してください"}, "list": {"loadingMessages": "メッセージを読み込んでいます...", "noMessages": "まだメッセージがありません。会話を始めましょう！"}, "connection": {"offlineMessage": "オフラインのようです。メッセージを送信するには再接続してください。", "connecting": "接続中...", "tryAgain": "再試行"}, "prompts": {"uploadReport": "レポートをアップロード", "speakInHindi": "ヒンディー語で話してください", "notFeelingWell": "気分が良くありません", "whatIsMyBMI": "私のBMIは？", "nutritionAdvice": "栄養アドバイス", "sleepBetter": "よく眠る"}, "citations": {"referenceText": "この会話の詳細については、以下を参照してください。"}, "actions": {"copiedToClipboard": "クリップボードにコピーされました", "copied": "コピーされました"}, "share": {"introText": "👋ねえ、Augustとの会話をみて！\n\n", "downloadText": "\n\n➡️フレンドリーなAIヘルスコンパニオンAugustとチャットするには、Augustをダウンロードしてください：\n"}}, "discover": {"nav": {"title": "発見"}, "categories": {"all": "すべて", "heartHealth": "心臓の健康", "nutrition": "栄養", "mentalHealth": "メンタルヘルス", "fitness": "フィットネス", "wellness": "健康"}, "cards": {"empty": "このカテゴリにはカードがありません"}, "sections": {"features": "機能"}, "features": {"healthLibrary": {"title": "ヘルスライブラリ", "description": "信頼できる最新の情報に無料でアクセスできます。"}, "nutritionTracker": {"title": "栄養トラッカー", "description": "食べ物の写真をアップロードして、栄養目標を追跡できます！Augustならそれが可能です！"}, "multilingualSupport": {"title": "多言語対応", "description": "Augustは、あなたが快適な言語でコミュニケーションできます！Augustはいつでもあなたをサポートします。", "samplePrompt": "ヒンディー語で話してください"}, "labReportAnalysis": {"title": "検査レポート分析", "description": "Augustに検査レポートについて話すと、非常に正確な結果が得られます。Augustは470万件以上のレポートを処理し、バイオマーカー抽出の精度は98.4％です。"}}}, "nutrition": {"nav": {"title": "栄養"}, "meals": {"title": "あなたの食事", "subtitle": "各食事のマクロを調べるにはタップしてください"}, "upload": {"loading": "画像をアップロードしています…"}, "defaultFoodName": "食品", "today": "今日", "unknownTime": "不明な時間", "calories": "🔥 カロリー", "proteins": "🥩 タンパク質", "carbs": "🍞 炭水化物", "sugars": "🍬 糖分", "fat": "🥑 脂肪", "caloriesLabel": "カロリー", "proteinLabel": "タンパク質", "carbohydratesLabel": "炭水化物", "fatLabel": "脂肪", "sugarLabel": "糖分", "tips": "ヒント：", "macroBreakdown": "マクロ栄養素の内訳", "noMacroData": "この食品にはマクロ栄養素データがありません。", "disclaimer": "教育目的でのみ使用できます。詳細はこちら", "disclaimerLink": "こちら", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "性別は？", "subtitle": "これは、あなたのカスタムプランを調整するために使用されます。", "male": "男性", "female": "女性", "other": "その他"}, "age": {"title": "年齢は？", "subtitle": "これは、あなたの毎日の必要量を計算するために使用されます。"}, "measurements": {"title": "身長と体重", "subtitle": "身長をセンチメートル、体重をキログラムで入力してください。"}, "activity": {"title": "活動レベル", "subtitle": "どのくらいの頻度で運動しますか？", "none": "運動なし", "moderate": "適度", "high": "高"}, "goal": {"title": "体重目標", "subtitle": "何を達成したいですか？", "increase": "増加", "maintain": "維持", "decrease": "減少"}, "targetWeight": {"title": "目標体重", "subtitle": "目標体重をキログラムで入力してください。"}, "setup": {"title": "プランの設定", "subtitle": "栄養プランの準備中ですのでお待ちください。"}, "review": {"title": "プランを確認する", "subtitle": "栄養プランを確認してカスタマイズしてください。"}, "height": {"label": "身長（cm）"}, "weight": {"label": "体重（kg）"}}, "error": {"updateFailed": "栄養データの更新に失敗しました。もう一度お試しください。", "parsingError": "食品データの解析エラー：", "fetchReportsFailed": "レポートデータの取得に失敗しました。もう一度お試しください。", "missingReportId": "レポートIDがありません"}}, "personalize": {"nav": {"title": "パーソナライズ"}, "button": {"saving": "保存中", "review": "確認", "saveNext": "保存して次へ"}}, "basicInfo": {"title": "あなたについてもっと知りたいです", "subtitle": "この情報は、あなたの健康に関する推奨事項をパーソナライズするのに役立ちます", "age": {"question": "年齢は？", "placeholder": "年齢を入力してください"}, "sex": {"question": "性別は？", "placeholder": "性別を選択してください", "male": "男性", "female": "女性", "other": "その他"}, "height": {"question": "身長は？（cm）", "placeholder": "身長を入力してください"}, "weight": {"question": "体重は？（kg）", "placeholder": "体重を入力してください"}}, "lifestyle": {"title": "あなたのライフスタイル", "subtitle": "日々の習慣を知ることで、より良いアドバイスができます", "diet": {"question": "どのような食事をされていますか？", "placeholder": "食事の種類を選択してください", "vegetarian": "菜食主義者", "nonVegetarian": "菜食主義者以外", "vegan": "完全菜食主義者", "pescatarian": "ベジタリアン（魚介類可）", "keto": "ケトジェニック", "paleo": "パレオ"}, "exercise": {"question": "定期的に運動をしていますか？"}, "drinking": {"question": "お酒を飲みますか？"}, "smoking": {"question": "喫煙しますか？"}, "sleep": {"question": "1晩に何時間睡眠を取っていますか？", "value": "{{sleep}}時間"}, "hydration": {"question": "1日にコップ何杯の水を飲みますか？", "value": "{{hydration}}杯 ({{liters}}L)"}}, "allergies": {"title": "アレルギーはありますか？", "subtitle": "アレルギーを知ることで、より安全なアドバイスができます", "allergyIndex": "アレルギー{{index}}", "name": {"question": "何のアレルギーがありますか？", "placeholder": "アレルギーを入力してください（例：ピーナッツ、ハウスダスト）"}, "severity": {"question": "アレルギーの重症度はどのくらいですか？", "placeholder": "重症度を選択してください", "mild": "軽度", "moderate": "中等度", "severe": "重度"}, "addButton": "アレルギーを追加", "noAllergiesButton": "アレルギーはありません"}, "medications": {"title": "服用中の薬やサプリメント", "subtitle": "現在服用している薬やサプリメントについて教えてください", "medicationIndex": "薬{{index}}", "name": {"label": "薬の名前", "placeholder": "薬の名前を入力してください"}, "startDate": {"question": "いつから服用していますか？", "placeholder": "日付を選択してください"}, "type": {"label": "薬の種類", "shortTerm": "短期", "longTerm": "長期"}, "dose": {"label": "服用量", "placeholder": "量"}, "unit": {"label": "単位"}, "frequency": {"label": "頻度", "placeholder": "回数", "perDay": "1日", "perWeek": "1週間", "perMonth": "1ヶ月", "perYear": "1年"}, "units": {"mg": "mg", "ml": "ml", "iu": "IU", "puffs": "吸入回数", "drops": "滴", "tsp": "小さじ", "tbsp": "大さじ", "cups": "カップ"}, "addButton": "薬を追加", "noMedicationsButton": "薬は服用していません", "calendar": {"title": "開始日を選択"}}, "conditions": {"title": "持病", "subtitle": "過去または現在、持病について教えてください", "conditionIndex": "持病{{index}}", "name": {"label": "病名", "placeholder": "病名を入力してください（例：喘息など）"}, "since": {"question": "いつからその症状がありますか？", "placeholder": "日付を選択してください"}, "current": {"question": "現在も症状はありますか？"}, "medicated": {"question": "この症状に対して薬を服用していますか？"}, "addButton": "持病を追加", "noConditionsButton": "持病はありません", "calendar": {"title": "日付を選択"}}, "reproductive": {"title": "生殖器に関する健康", "subtitle": "この情報は、よりパーソナルな健康に関する推奨事項を提供するのに役立ちます", "menstruation": {"question": "生理を経験したことがありますか？", "detailsTitle": "生理の詳細", "regularity": {"question": "生理周期の規則性はどの程度ですか？", "regular": "規則的", "irregular": "不規則", "notSure": "わからない"}, "cycleLength": {"label": "平均生理周期の長さ（日）", "placeholder": "生理周期の長さを入力してください"}, "flowDays": {"label": "生理期間（日）：{{flowDays}}", "min": "1日", "max": "15日"}, "padsPerDay": {"label": "1日あたりのナプキン/タンポン数：{{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "生理期間中の症状はありますか？", "placeholder": "症状を入力してください（例：けいれん、頭痛）"}}, "childbirth": {"question": "出産経験はありますか？", "detailsTitle": "出産の詳細", "children": {"label": "子供の数"}, "pregnancies": {"label": "妊娠回数"}, "complications": {"question": "妊娠中または出産中に合併症はありましたか？", "placeholder": "合併症があれば入力してください"}}}, "review": {"title": "情報を確認してください", "subtitle": "送信する前に、入力した情報を確認してください", "sections": {"basicInfo": "基本情報", "lifestyle": "ライフスタイル", "allergies": "アレルギー", "medications": "薬とサプリメント", "conditions": "病状", "reproductive": "生殖器に関する健康", "menstruationDetails": "生理の詳細", "childbirthDetails": "出産の詳細"}, "fields": {"age": "年齢：", "sex": "性別：", "height": "身長：", "weight": "体重：", "diet": "食事：", "exercise": "運動：", "drinking": "飲酒：", "smoking": "喫煙：", "sleep": "睡眠：", "hydration": "水分補給：", "allergyIndex": "{{index}}のアレルギー：", "dose": "投与量：", "frequency": "頻度：", "type": "種類：", "since": "いつから：", "currentlyActive": "現在服用中：", "takingMedication": "薬を服用中：", "hasMenstruated": "生理を経験したか：", "regularity": "規則性：", "cycleLength": "生理周期の長さ：", "flowDays": "生理期間：", "padsPerDay": "1日あたりのナプキン/タンポン数：", "hasChildbirth": "出産経験：", "children": "子供の人数：", "pregnancies": "妊娠回数："}, "notProvided": "未入力", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "1日{{sleep}}時間", "hydration": "1日{{hydration}}カップ（{{liters}}L）", "allergySeverity": "{{name}}（{{severity}}）", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}}日"}, "noData": {"allergies": "アレルギーの情報がありません", "medications": "薬の情報がありません", "conditions": "病状の情報がありません"}, "submitButton": "情報を送信"}, "success": {"title": "情報が更新されました！", "message": "健康情報をご提供いただきありがとうございます。この情報を使用して、お客様の体験をパーソナライズし、より良い推奨事項を提供します。", "benefits": {"insights": "パーソナルな健康に関する洞察", "reminders": "より良い服薬リマインダー", "recommendations": "個別化された健康に関する推奨事項"}, "continueButton": "ダッシュボードへ進む"}, "permissions": {"microphonePermissionDenied": "マイクのアクセスが拒否されました", "microphoneAccessDescription": "Augustは音声録音と音声メモの送信のためにマイクへのアクセスが必要です", "permissionDenied": "アクセス拒否", "cameraPermissionRequired": "この機能を使用するには、カメラへのアクセス許可が必要です！", "mediaLibraryPermissionRequired": "この機能を使用するには、メディアライブラリへのアクセス許可が必要です！"}, "voiceRecording": {"recordingTooLong": "録音時間が長すぎます", "recordingTooLongMessage": "音声録音は5分以内にしてください。もっと短いメッセージを録音してください。"}, "errors": {"uploadFailed": "アップロード失敗", "voiceUploadFailed": "音声録音のアップロードに失敗しました。", "voiceRecordingFailed": "音声録音の送信に失敗しました", "failedToStopRecording": "録音の停止に失敗しました", "photoUploadFailed": "写真のアップロードに失敗しました。", "failedToTakePhoto": "写真の撮影に失敗しました", "imageUploadFailed": "画像のアップロードに失敗しました: {{fileName}}", "failedToPickImage": "画像の選択に失敗しました", "documentUploadFailed": "ドキュメントのアップロードに失敗しました: {{fileName}}", "failedToPickDocument": "ドキュメントの選択に失敗しました"}, "audioPlayer": {"downloadingAudio": "オーディオをダウンロードしています…", "loadingAudio": "オーディオを読み込んでいます…"}, "mediaProcessing": {"processingFile": "ファイルを処理しています", "uploadingSecuring": "ファイルをアップロードして保護しています…", "analyzingContent": "ドキュメントの内容を分析しています…", "extractingInfo": "重要な情報を抽出しています…", "processingInsights": "インサイトを処理しています…", "preparingResponse": "詳細な回答を準備しています…", "finalizingResponse": "回答を最終処理しています…"}, "attachments": {"voiceMessage": "音声メッセージ", "image": "[画像]", "pdf": "[PDF]", "voice": "[音声メモ]"}, "pdf": {"loadingPdf": "PDFを読み込んでいます…"}, "dateTime": {"yesterday": "昨日、"}, "navbar": {"defaultTitle": "august", "selectedCount": "選択済み"}, "mediaUpload": {"photoLibrary": "写真ライブラリ", "takePhoto": "写真を撮る", "chooseFile": "ファイルを選択"}, "comingSoon": {"title": "近日公開！", "description": "は現在開発中です。アップデートをお待ちください！", "buttonText": "了解しました！"}, "clipboard": {"success": "リンクをクリップボードにコピーしました"}, "mediaPhotos": {"emptyState": "まだエントリがありません。"}, "foodDetail": {"defaultFoodName": "食品", "nutrition": {"totalCalories": "総カロリー", "proteins": "タンパク質", "carbs": "炭水化物", "fat": "脂肪", "sugars": "糖分", "fibers": "食物繊維"}}, "reports": {"defaultTitle": "メディアアイテム", "defaultFoodName": "食品", "defaultName": "ドキュメント", "openButton": "外部ビューアで開く", "biomarker": {"headerBiomarker": "バイオマーカー", "headerValue": "値", "headerRefRange": "基準範囲", "headerStatus": "状態"}, "noData": "バイオマーカーデータがありません"}, "setup": {"title": "設定中です", "inProgress": "処理中です…", "progressMessages": {"0": "1日のカロリーを計算中", "1": "マクロ栄養素の比率を最適化中", "2": "食事プランを作成中", "3": "健康スコアを計算中", "4": "設定を最終処理中"}, "checklistItems": {"0": "健康データを分析中", "1": "最適な栄養プランを計算中", "2": "推奨事項をパーソナライズ中", "3": "食事の提案を作成中", "4": "設定を最終処理中"}}, "foodEntry": {"emptyState": "まだ食事の記録がありません。食事の写真を撮って追加してください！"}, "nutritionReview": {"congratulations": "おめでとうございます！", "subtitle": "あなたのカスタム栄養プランの準備ができました", "submitButton": "始めましょう！", "dailyTargetsTitle": "毎日の栄養目標", "macroLabels": {"calories": "カロリー", "carbs": "炭水化物", "protein": "タンパク質", "fats": "脂肪"}, "recommendations": {"title": "目標達成方法：", "healthScores": "健康スコアを使って日課を改善しましょう", "trackFood": "食事摂取量を継続的に記録する", "followCalories": "1日のカロリー摂取目標を守ってください", "balanceMacros": "炭水化物、タンパク質、脂肪の摂取量をバランスよく摂ってください"}}, "editModal": {"titlePrefix": "編集", "cancelButton": "キャンセル", "saveButton": "次"}, "processing": {"stages": {"scanning": "食品をスキャンしています…", "identifying": "材料を特定しています…", "extracting": "栄養素を抽出中…", "finalizing": "結果を最終処理しています…"}, "error": {"defaultMessage": "食べ物は検出されませんでした", "subtitle": "別の角度から試してみてください"}, "retakeButton": "写真を撮り直すにはタップ", "notification": "完了したらお知らせします！"}, "chart": {"title": "経時的な栄養追跡", "selectNutrient": "栄養素を選択：", "emptyState": "栄養データはまだありません。", "dropdown": {"calories": "カロリー", "protein": "タンパク質", "carbs": "炭水化物", "fat": "脂肪", "sugars": "砂糖"}}, "foodModal": {"defaultName": "食品", "defaultDate": "今日", "defaultTime": "不明な時間", "saveChanges": "変更を保存", "error": {"title": "エラー", "message": "栄養データの更新に失敗しました。もう一度お試しください。"}, "nutrition": {"calories": "🔥 カロリー", "proteins": "🥩 タンパク質", "carbs": "🍞 炭水化物", "sugars": "🍬砂糖", "fat": "🥑 脂肪"}, "macroBreakdown": {"title": "マクロ栄養素内訳", "noData": "この食品の主要栄養素データはありません。"}, "macroLabels": {"calories": "カロリー", "protein": "タンパク質", "carbs": "炭水化物", "fat": "脂肪", "sugar": "砂糖"}}, "infoModal": {"title": "詳細情報", "edit": "編集", "save": "保存", "saving": "保存中…", "enterValue": "値を入力してください", "notSet": "設定されていません", "age": "年齢", "heightCm": "身長(cm)", "weightKg": "重量 (kg)", "targetWeight": "目標体重", "nutritionTargets": "栄養目標", "protein": "タンパク質", "carbs": "炭水化物", "fats": "脂肪", "gm": "gm", "editNote": "値を入力するか、自動計算のために空欄のままにしてください。", "autoCalculateNote": "マクロはデータに基づいて自動計算されます。", "validation": {"ageMin": "18歳以上である必要があります", "ageMax": "年齢は125歳未満でなければなりません", "heightMin": "高さは50cm以上である必要があります", "heightMax": "高さは250cm未満でなければなりません", "weightMin": "重量は少なくとも30kg必要です", "weightMax": "重量は500kg未満でなければなりません。", "targetWeightMin": "目標重量は30kg以上である必要があります。", "targetWeightMax": "目標重量は500kg未満でなければなりません。", "proteinMin": "タンパク質は0以上でなければならない", "carbsMin": "炭水化物は0以上でなければならない", "fatsMin": "脂肪は0以上でなければならない"}}, "tracker": {"calories": "カロリー", "protein": "タンパク質", "carbs": "炭水化物", "fat": "脂肪", "excess": "過剰", "remaining": "残りの"}, "specialistConstants": {"nutritionist": {"name": "栄養士", "description": "食事、栄養、健康的な食習慣に関する専門家のアドバイス", "featureName": "栄養専門家"}, "cardiologist": {"name": "心臓専門医", "description": "心臓の健康と心血管疾患を専門としています", "featureName": "心臓専門医"}, "neurologist": {"name": "神経学者", "description": "脳、脊髄、神経系の病気の専門家です", "featureName": "神経専門医"}, "oncologist": {"name": "腫瘍学者", "description": "がんの診断と治療法を専門としています", "featureName": "腫瘍専門医"}, "endocrinologist": {"name": "内分泌学者", "description": "ホルモンの状態と代謝異常の専門家です", "featureName": "内分泌専門医"}}, "discoverCards": {"categories": {"nutrition": "栄養", "heartHealth": "心臓の健康", "mentalHealth": "心の健康", "fitness": "フィットネス", "wellness": "健康増進"}, "titles": {"vitaminB12Recovery": "ビタミンB12欠乏症から回復するにはどれくらいかかりますか", "vitaminDeficiencyGanglion": "どのビタミンの欠乏症がガングリオン嚢腫を引き起こしますか", "vitaminDeficiencyHairFall": "どのビタミンの欠乏症が脱毛を引き起こしますか", "vitaminWaters": "ビタミンウォーターは体に良いですか", "cholesterolHeadaches": "高コレステロールは頭痛を引き起こしますか", "cholesterolEyes": "目に現れる高コレステロールの症状は何ですか", "diabetesHeadaches": "糖尿病は頭痛を引き起こしますか", "chestPainDrinking": "飲酒後に胸が痛むのはなぜですか", "stressDizziness": "ストレスはめまいを引き起こしますか", "bulimiaFace": " булимияフェイスとは何ですか", "kneeTwitch": "膝がぴくぴくするのはなぜですか", "noseTwitching": "鼻がぴくぴくするのはなぜですか", "piriformisVsSciatica": "梨状筋症候群と坐骨神経痛の違いは何ですか", "shoulderBladePinched": "肩甲骨の神経の挟みを解消するにはどうすればよいですか", "shoulderPinched": "肩の神経の挟みを解消するにはどうすればよいですか", "meniscusTorn": "半月板の損傷を自然に治すにはどうすればよいですか", "hydrateQuickly": "素早く水分補給するにはどうすればよいですか", "periodConstipation": "生理中に便秘になるのは普通ですか", "acneScars": "にきび跡を1週間で自然に消すにはどうすればよいですか", "perimenopausePregnancy": "閉経前には妊娠できますか"}, "descriptions": {"vitaminB12Recovery": "ビタミンB12欠乏症の回復期間と、エネルギーレベルを高める効果的な治療法を発見してください。", "vitaminDeficiencyGanglion": "体内のビタミン欠乏とガングリオンの発生との関連性を探る。", "vitaminDeficiencyHairFall": "必須ビタミン不足が抜け毛につながる仕組みと、それを防ぐ方法を学びましょう。", "vitaminWaters": "毎日の栄養におけるビタミンウォーターの利点と潜在的な欠点を明らかにする。", "cholesterolHeadaches": "高コレステロール値と頭痛の発症との関連性を検討する。", "cholesterolEyes": "コレステロール値が高いことが目にどのように現れ、どのような症状に注意すべきか学びましょう。", "diabetesHeadaches": "日常生活における糖尿病と頭痛発生の関連性を調査する。", "chestPainDrinking": "特定の飲料を摂取した後、胸痛が起こる理由を探る。", "stressDizziness": "ストレスがバランス感覚と全体的な健康に影響し、めまいを引き起こす仕組みを詳しく見てみましょう。", "bulimiaFace": "булиミアの身体的兆候、特に顔貌への影響を理解する。", "kneeTwitch": "不随意な膝のけいれんの原因と、ストレスや疲労との関連性を調査する。", "noseTwitching": "鼻のぴくつきとその原因、不安やその他の要因との関連について学びましょう。", "piriformisVsSciatica": "梨状筋症候群と坐骨神経痛の症状を比較して、自分の状態をよりよく理解しましょう。", "shoulderBladePinched": "肩甲骨の神経を圧迫する痛みを和らげ、可動域を回復させる効果的なテクニックを発見しましょう。", "shoulderPinched": "肩の神経圧迫を軽減する簡単な運動とストレッチを学びましょう。", "meniscusTorn": "半月板損傷の治癒をサポートする自然療法と運動を探る。", "hydrateQuickly": "迅速かつ効果的に水分補給し、最適な体水分を維持する方法を見つけ出す。", "periodConstipation": "生理中の便秘の原因を理解し、自然療法を学ぶ。", "acneScars": "ニキビ跡を早く目立たなくする自然療法とスキンケアのヒントを発見しましょう。", "perimenopausePregnancy": "閉経前後の時期、妊娠の可能性、そしてこの人生の段階で何が起こるかについて学びましょう。"}}}