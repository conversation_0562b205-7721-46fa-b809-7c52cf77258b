import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthState } from '@/types/auth';
import {User} from '@/types/user';
import { secureStorage } from '@/services/secure-storage-service';
import { logoutFromServer } from '@/services/auth-service';
import logger from '@/utils/logger/logger';
import { disconnectWebPubSub } from '@/services/webPubSubService';
import { useMemo } from 'react';
import { router } from 'expo-router';
// We're now using SecureStore through the secureStorage service for the token
// and AsyncStorage for other non-sensitive state

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      // token is removed from state and stored only in SecureStore
      user: null,
      isLoading: true,
      isAuthenticated: false,
      error: null,
      
      // Token methods
      getAccessToken: async (): Promise<string | null> => {
        return await secureStorage.getAccessToken();
      },
      
      getRefreshToken: async (): Promise<string | null> => {
        return await secureStorage.getRefreshToken();
      },
      
      setAccessToken: async (token: string | null) => {
        // Store the access token in SecureStore
        if (token) {
          await secureStorage.setAccessToken(token);
        } else {
          // Only remove the access token, not the refresh token
          await secureStorage.getAccessToken().then(() => {
            // Only delete if it exists
            secureStorage.setAccessToken('');
          }).catch(() => {});
        }
        
        // Update the authentication state
        set({ 
          isAuthenticated: !!token,
          error: null
        });
      },
      
      setRefreshToken: async (token: string | null) => {
        // Store the refresh token in SecureStore
        if (token) {
          await secureStorage.setRefreshToken(token);
        } else {
          // Only remove the refresh token, not the access token
          await secureStorage.getRefreshToken().then(() => {
            // Only delete if it exists
            secureStorage.setRefreshToken('');
          }).catch(() => {});
        }
      },
      
      setTokens: async (accessToken: string | null, refreshToken: string | null) => {
        // Store both tokens
        if (accessToken) {
          await secureStorage.setAccessToken(accessToken);
        }
        
        if (refreshToken) {
          await secureStorage.setRefreshToken(refreshToken);
        }
        
        if (!accessToken && !refreshToken) {
          await secureStorage.removeTokens();
        }
        
        // Update the authentication state based on access token
        set({ 
          isAuthenticated: !!accessToken,
          error: null
        });
      },
      
      setUser: (user: User | null) => 
        set({ user }),
      
      setIsLoading: (isLoading: boolean) => 
        set({ isLoading }),
      
      setError: (error: string | null) => 
        set({ error }),
      
      logout: async () => {
        try {
          // Get the refresh token before removing it
          const accessToken = await secureStorage.getAccessToken();
          
          // Call the server logout endpoint if we have a refresh token
          if (accessToken) {
            await logoutFromServer(accessToken);
          }
          
          // Remove all tokens from SecureStore
          await secureStorage.removeTokens();
          await AsyncStorage.removeItem('form-data-storage')

          // Disconnect from WebPubSub
          await disconnectWebPubSub();

          router.push('/auth/phone');
          // Update the state
          set({ 
            user: null, 
            isAuthenticated: false,
            error: null
          });
        } catch (error) {
          logger.error('Error during logout:', error);
          // Still clear tokens and state even if server logout fails
          
          await secureStorage.removeTokens();

          set({ 
            user: null, 
            isAuthenticated: false,
            error: null
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Initialize the authentication state from SecureStore
const initializeAuthState = async () => {
  try {
    const accessToken = await secureStorage.getAccessToken();
    if (accessToken) {
      // Only update the authentication state
      useAuthStore.setState({ 
        isAuthenticated: true 
      });
    }
  } catch (error) {
    logger.error('Error initializing auth state from SecureStore:', error);
  }
};

// Initialize authentication state on app startup
initializeAuthState();

// Custom hooks for memoized selectors
export const useUser = () => {
  const user = useAuthStore(state => state.user);
  return useMemo(() => user, [user?.id]);
};

export const useIsAuthenticated = () => {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  return useMemo(() => isAuthenticated, [isAuthenticated]);
};

export const useAuthLoading = () => {
  const isLoading = useAuthStore(state => state.isLoading);
  return useMemo(() => isLoading, [isLoading]);
};

export const useAuthError = () => {
  const error = useAuthStore(state => state.error);
  return useMemo(() => error, [error]);
};
