import React, { useState, useMemo } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/hooks/useLanguage';
import { Language } from '@/services/languageService';
import { X, Search, Globe, Check } from 'lucide-react-native';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import logger from '@/utils/logger/logger';

interface LanguageSwitcherProps {
  visible: boolean;
  onClose: () => void;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  visible,
  onClose,
}) => {
  const { t } = useTranslation();
  const {
    currentLanguage,
    isManuallySelected,
    isLoading,
    supportedLanguages,
    changeLanguage,
    resetToDeviceLanguage,
  } = useLanguage();

  const [searchQuery, setSearchQuery] = useState('');
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  // Filter languages based on search query
  const filteredLanguages = useMemo(() => {
    if (!searchQuery.trim()) {
      return supportedLanguages;
    }

    const query = searchQuery.toLowerCase();
    return supportedLanguages.filter(
      (language) =>
        language.name.toLowerCase().includes(query) ||
        language.nativeName.toLowerCase().includes(query) ||
        language.code.toLowerCase().includes(query)
    );
  }, [supportedLanguages, searchQuery]);

  const handleLanguageSelect = async (languageCode: string) => {
    if (languageCode === currentLanguage) {
      onClose();
      return;
    }

    try {
      setIsChangingLanguage(true);
      await changeLanguage(languageCode);
      onClose();
    } catch (error) {
      logger.error('Error changing language:', error);
    } finally {
      setIsChangingLanguage(false);
    }
  };

  const handleResetToDevice = async () => {
    try {
      setIsChangingLanguage(true);
      await resetToDeviceLanguage();
      onClose();
    } catch (error) {
      logger.error('Error resetting to device language:', error);
    } finally {
      setIsChangingLanguage(false);
    }
  };

  const renderLanguageItem = (language: Language) => {
    const isSelected = language.code === currentLanguage;
    
    return (
      <TouchableOpacity
        key={language.code}
        style={[styles.languageItem, isSelected && styles.selectedLanguageItem]}
        onPress={() => handleLanguageSelect(language.code)}
        disabled={isChangingLanguage}
      >
        <View style={styles.languageInfo}>
          <Text style={[styles.languageName, isSelected && styles.selectedText]}>
            {language.nativeName}
          </Text>
          <Text style={[styles.languageSubtitle, isSelected && styles.selectedSubtext]}>
            {language.name}
          </Text>
        </View>
        {isSelected && (
          <Check size={20} color={colors.primary} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Globe size={24} color={colors.primary} />
            <Text style={styles.headerTitle}>{t('languageSwitcher.title')}</Text>
          </View>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            disabled={isChangingLanguage}
          >
            <X size={24} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Search size={20} color={colors.gray[400]} />
            <TextInput
              style={styles.searchInput}
              placeholder={t('languageSwitcher.searchPlaceholder')}
              value={searchQuery}
              onChangeText={setSearchQuery}
              editable={!isChangingLanguage}
            />
          </View>
        </View>

        {/* Reset to Device Language Option */}
        {isManuallySelected && (
          <View style={styles.resetSection}>
            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleResetToDevice}
              disabled={isChangingLanguage}
            >
              <Text style={styles.resetButtonText}>
                {t('languageSwitcher.useDeviceLanguage')}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Language List */}
        <ScrollView style={styles.languageList} showsVerticalScrollIndicator={false}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>{t('common.loading')}</Text>
            </View>
          ) : (
            <>
              {filteredLanguages.map(renderLanguageItem)}
              {filteredLanguages.length === 0 && (
                <View style={styles.noResultsContainer}>
                  <Text style={styles.noResultsText}>
                    {t('languageSwitcher.noResults')}
                  </Text>
                </View>
              )}
            </>
          )}
        </ScrollView>

        {/* Loading Overlay */}
        {isChangingLanguage && (
          <View style={styles.loadingOverlay}>
            <View style={styles.loadingCard}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.changingText}>
                {t('languageSwitcher.changingLanguage')}
              </Text>
            </View>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateVerticalScale(16),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: moderateScale(8),
  },
  closeButton: {
    padding: moderateScale(8),
  },
  searchContainer: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateVerticalScale(12),
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: moderateScale(12),
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateVerticalScale(10),
  },
  searchInput: {
    flex: 1,
    fontSize: moderateScale(16),
    marginLeft: moderateScale(8),
    color: colors.gray[800],
  },
  resetSection: {
    paddingHorizontal: moderateScale(16),
    paddingBottom: moderateVerticalScale(8),
  },
  resetButton: {
    backgroundColor: colors.gray[100],
    borderRadius: moderateScale(8),
    paddingVertical: moderateVerticalScale(12),
    paddingHorizontal: moderateScale(16),
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: colors.primary,
  },
  languageList: {
    flex: 1,
    paddingHorizontal: moderateScale(16),
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: moderateVerticalScale(16),
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(12),
    marginVertical: moderateVerticalScale(2),
  },
  selectedLanguageItem: {
    backgroundColor: colors.primary + '10',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: moderateVerticalScale(2),
  },
  languageSubtitle: {
    fontSize: moderateScale(14),
    color: colors.gray[600],
  },
  selectedText: {
    color: colors.primary,
  },
  selectedSubtext: {
    color: colors.primary + 'CC',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: moderateVerticalScale(40),
  },
  loadingText: {
    fontSize: moderateScale(16),
    color: colors.gray[600],
    marginTop: moderateVerticalScale(12),
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: moderateVerticalScale(40),
  },
  noResultsText: {
    fontSize: moderateScale(16),
    color: colors.gray[600],
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingCard: {
    backgroundColor: colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(24),
    alignItems: 'center',
    minWidth: moderateScale(150),
  },
  changingText: {
    fontSize: moderateScale(16),
    color: colors.gray[800],
    marginTop: moderateVerticalScale(12),
    textAlign: 'center',
  },
});
