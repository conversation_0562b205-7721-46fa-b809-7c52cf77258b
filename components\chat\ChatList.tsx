import React, {
  useRef,
  useEffect,
  useState,
  ReactNode,
  isValidElement,
  useCallback,
  useMemo,
} from "react";
import {
  StyleSheet,
  FlatList,
  View,
  Text,
  Platform,
  ActivityIndicator,
  Animated,
  TouchableOpacity,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ViewToken,
  Keyboard,
  Linking,
  RefreshControl
} from "react-native";
import {
  ArrowDown,
  ChevronDown,
  Share2,
  RefreshCw
} from "lucide-react-native";
import { useUserDataStore } from "@/store/userDataStore";
import logger from "@/utils/logger/logger";
import { useChatStore, useMessages, useIsLoading, useIsWaitingForResponse } from "@/store/chatStore";
import MessageBubble from "./MessageBubble";
import WaitingResponseIndicator from "./WaitingResponseIndicator";
import { colors } from "@/constants/colors";
import {
  trackUserInteraction,
  trackOperation,
  OperationStatus,
} from "@/utils/mixpanel/mixpanel-utils";
import { fetchChatHistory as fetchChatHistoryFromService } from "@/services/chatService";
import { useAuthStore, useIsAuthenticated } from "@/store/auth-store";
import {
  moderateScale,
  moderateVerticalScale,
} from "react-native-size-matters";
import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import { setExpoPushToken } from "@/services/userService";
import { Share } from "react-native";
import * as Haptics from "expo-haptics";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { isWebPubSubConnected } from "@/services/webPubSubService";
import MediaProcessingCard from "./MediaProcessingCard";
import { softHaptic } from "@/utils/haptics/haptics";
import ChatSkeleton from "../common/loaders/chatSkeleton";

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      position: "relative", // Important for absolute positioning of the indicator
    },
    list: {
      flex: 1,
    },
    disclaimer: {
      textAlign: "center",
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
    },
    contentContainer: {
      paddingTop: moderateVerticalScale(16),
      paddingBottom: moderateVerticalScale(15),
    },
    emptyContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: moderateVerticalScale(32),
    },
    emptyText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray.main,
      textAlign: "center",
    },
    scrollToBottomButton: {
      position: "absolute",
      left: "50%",
      transform: [{ translateX: -20 }], // Can stay fixed
      bottom: moderateVerticalScale(150),
      width: moderateScale(35),
      height: moderateScale(35),
      borderRadius: moderateScale(theme.radii.xl),
      backgroundColor: theme.colors.secondary[50],
      alignItems: "center",
      justifyContent: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.25,
      shadowRadius: moderateScale(3.84),
      elevation: 5,
    },
    overlayContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(255, 255, 255, 0.7)",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 10,
    },
    loadingIndicator: {
      backgroundColor: "white",
      padding: moderateScale(20),
      borderRadius: moderateScale(10),
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.25,
      shadowRadius: moderateScale(3.84),
      elevation: 5,
    },
    loadingText: {
      marginTop: moderateVerticalScale(10),
      color: theme.colors.gray[700],
      fontSize: moderateScale(theme.fontSize.sm),
    },
    selectedMessagesContainer: {
      position: "absolute",
      top: -45,
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
      elevation: 3,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      zIndex: 1000,
    },
    selectionHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: moderateScale(12),
      paddingVertical: moderateVerticalScale(12),
      height: moderateVerticalScale(56),
    },
    cancelButton: {
      paddingHorizontal: moderateScale(12),
      paddingVertical: moderateVerticalScale(8),
    },
    link: {
      color: '#007bff', // blue link color
    },
    cancelButtonText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray[600],
      fontWeight: "500",
    },
    selectedMessagesText: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "500",
      color: theme.colors.gray[700],
    },
    shareButton: {
      paddingHorizontal: moderateScale(10),
      paddingVertical: moderateVerticalScale(8),
      backgroundColor: theme.colors.primary.main,
      borderRadius: moderateScale(theme.radii.sm),
    },
    shareButtonText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.secondary[50],
      fontWeight: "600",
    },
  });

interface ChatListProps {
  children?: ReactNode;
}

interface MessageSelectionState {
  isInSelectionMode: boolean;
  isSelected: boolean;
  isFirstSelected: boolean;
}

const ChatList = React.memo<ChatListProps>(({ children }) => {
  const { theme } = useTheme();
  const isConnected = isWebPubSubConnected()
  const styles = useMemo(() => createStyles(theme), [theme]);
  // Use memoized selectors
  const messages = useMessages();
  const isLoading = useIsLoading();
  const isWaitingForResponse = useIsWaitingForResponse();
  const [refreshing, setRefreshing] = useState(false);

  // Get other values and functions from the store
  const { lastUserMessageTimestamp, fetchOlderMessages, fetchChatHistory } = useChatStore();
  const [loadingOlderMessages, setLoadingOlderMessages] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const [isActiveConversation, setIsActiveConversation] = useState(false);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [showScrollToBottomButton, setShowScrollToBottomButton] =
    useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showMediaCard, setShowMediaCard] = useState(false);

  const [firstVisibleMessageId, setFirstVisibleMessageId] = useState<
    string | null
  >(null);

  const viewabilityConfigRef = useRef({
    itemVisiblePercentThreshold: 10,
  });
  const onViewableItemsChangedRef = useRef(
    ({
      viewableItems,
      changed,
    }: {
      viewableItems: Array<ViewToken>;
      changed: Array<ViewToken>;
    }) => {
      if (viewableItems.length > 0 && viewableItems[0].item) {
        // Only update if not refreshing to avoid capturing newly loaded messages
        if (!isRefreshing) {
          // Store the ID of the first visible message
          setFirstVisibleMessageId(viewableItems[0].item.id);
        }
      }
    }
  );

  const flatListRef = useRef<FlatList>(null);
  const prevMessageCountRef = useRef(0);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageIdRef = useRef<string | undefined>(
    messages.length > 0 ? messages[messages.length - 1]?.id : undefined
  );

  // Ref to track whether initial load has completed
  const initialLoadCompletedRef = useRef(false);
  const failedReconnectAttemptsRef = useRef(0);

  useEffect(() => {
    let animation: any;
    if (isRetrying) {
      animation = Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      );
      animation.start();
    } else {
      animation?.stop();
      rotateAnim.setValue(0);
    }

    return () => animation?.stop();
  }, [isRetrying]);

  // Function to handle permission check and token retrieval
  async function registerForPushNotificationsAsync() {
    let token;
    if (Device.isDevice) {
      trackOperation(
        "Push Notification Registration",
        OperationStatus.INITIATED
      );

      // Check for existing permissions
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== "granted") {


        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;


      }
      if (finalStatus !== "granted") {
        trackOperation(
          "Push Notification Registration",
          OperationStatus.FAILURE,
          {
            reason: "permission_denied",
            status: finalStatus,
          }
        );
        return;
      }

      token = (await Notifications.getExpoPushTokenAsync()).data;
      trackOperation("Push Token Retrieved", OperationStatus.SUCCESS, {
        tokenLength: token.length,
      });
    } else {
      logger.error("Failed to get push token - not a physical device");
      trackOperation(
        "Push Notification Registration",
        OperationStatus.FAILURE,
        {
          reason: "not_physical_device",
        }
      );
    }
    if (Platform.OS === "android") {
      await Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
      trackOperation(
        "Android Notification Channel Setup",
        OperationStatus.SUCCESS
      );
    }
    return token;
  }

  useEffect(() => {
    const timeout = setTimeout(() => {
      registerForPushNotificationsAsync()
        .then((token) => {
          if (token) {
            trackOperation(
              "Push Token Server Registration",
              OperationStatus.INITIATED,
              { tokenLength: token.length }
            );
            setExpoPushToken(token)
              .then(() => {
                trackOperation(
                  "Push Token Server Registration",
                  OperationStatus.SUCCESS
                );
              })
              .catch((error) => {
                if ((error as any)?.response?.status !== 401) {
                  logger.error("Failed to send push token to server:", error);
                }
                trackOperation(
                  "Push Token Server Registration",
                  OperationStatus.FAILURE,
                  {
                    error:
                      error instanceof Error ? error.message : String(error),
                  }
                );
              });
          }
        })
        .catch((error) => {
          if ((error as any)?.response?.status !== 401) {
            logger.error("Error while getting push token:", error);
            trackOperation("Push Notification Registration", OperationStatus.FAILURE, {
              error: error instanceof Error ? error.message : String(error),
            });
          }
        });
    }, 5000); // Delay 5 seconds

    return () => clearTimeout(timeout); // Cleanup on unmount
  }, []);

  // Initial load of chat history
  useEffect(() => {
    useChatStore.getState().setupNetworkListener();
    useChatStore
      .getState()
      .fetchChatHistory()
      .then(() => {
        logger.info("Initial chat history loaded successfully");
        trackOperation('chat history loaded', OperationStatus.SUCCESS)

        // Update last message ID ref after initial load
        const currentMessages = useChatStore.getState().messages;
        if (currentMessages.length > 0) {
          lastMessageIdRef.current =
            currentMessages[currentMessages.length - 1].id;
        }
      })
      .catch((error: any) => {
        if ((error as any)?.response?.status !== 401) {
          logger.error("Error fetching initial chat history:", error);
          trackOperation('failed to load chat history', OperationStatus.FAILURE)
        }
      });

    // Also fetch user data (nutrition and medical)
    useUserDataStore
      .getState()
      .fetchUserData()
      .then(() => {
        logger.info("User data loaded successfully");
      })
      .catch((error: any) => {
        if (error?.response?.status !== 401) {
          logger.error("Error fetching user data:", error);
        }
      });
  }, []);

  // Update message count and last message ID when messages change
  useEffect(() => {
    if (messages.length > 0) {
      lastMessageIdRef.current = messages[messages.length - 1].id;
    }

    // Update the ref with current message count
    const prevCount = prevMessageCountRef.current;
    prevMessageCountRef.current = messages.length;

    const hasNewMessages = messages.length > prevCount;
    if (hasNewMessages && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];

      // Always scroll to bottom for user messages
      if (lastMessage.sender === "user") {
        // First attempt with short delay
        setTimeout(() => {
          scrollToBottom();
          // Second attempt to ensure complete scrolling
          setTimeout(() => {
            scrollToBottom();
          }, 200);
        }, 250);
      }
      else if (lastMessage.sender === 'bot' && isAtBottom) {
        setTimeout(() => {
          scrollToBottom();
        }, 150);
      }
    }
  }, [messages, isAtBottom]);

  // Store the ref in the chat store for external access
  useEffect(() => {
    if (flatListRef.current) {
      useChatStore.setState({ chatListRef: flatListRef });
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    if (flatListRef.current && messages.length > 0) {
      requestAnimationFrame(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      });
    }
  }, [messages.length]);

  const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;

    // In inverted list, we load older messages when scrolling near the bottom
    // (which is actually the top of the chat history)
    const nearEnd = contentOffset.y + layoutMeasurement.height >= contentSize.height - 100;
    if (nearEnd && !loadingOlderMessages) {
      // Set loading state to prevent multiple triggers
      setLoadingOlderMessages(true);
      fetchOlderMessages().finally(() => {
        setLoadingOlderMessages(false);
      });
    }

    // In inverted list, we're at the "bottom" (newest messages) when contentOffset.y is near 0
    const isAtBottomNow = contentOffset.y <= 10;

    setIsAtBottom(isAtBottomNow);

    // Show scroll to bottom button when we're not at the bottom and have scrolled a significant amount
    setShowScrollToBottomButton(
      !isAtBottomNow && messages.length > 0 && contentOffset.y > 500
    );
  }, [loadingOlderMessages, fetchOlderMessages, messages.length]);

  // Function to check for new messages without updating state if no new messages
  const checkForNewMessages = useCallback(async () => {
    logger.info("Checking for new messages");
    trackOperation("Chat Check For New Messages", OperationStatus.INITIATED);

    try {
      const currentLastMessageId =
        messages.length > 0 ? messages[messages.length - 1].id : undefined;

      const response = await fetchChatHistoryFromService(50);

      // Check if there are any messages in the response
      if (!response.chats || response.chats.length === 0) {
        logger.info("No messages in response");
        trackOperation("Chat Check Completed", OperationStatus.SUCCESS, {
          newMessages: false,
        });
        return false;
      }
      // Get the number of messages from the length property
      const messageCount = response.chats.length as number;
      logger.info(
        `Successfully received chat history with ${messageCount} messages`
      );
      const messagesArray = Object.entries(response.chats)
        .filter(
          ([key, value]) =>
            key !== "length" && typeof value === "object" && value !== null
        )
        .map(([_, chat]) => chat as any);

      const sortedMessages = messagesArray.sort((a, b) => {
        const timestampA = new Date(a.timestamp).getTime();
        const timestampB = new Date(b.timestamp).getTime();
        return timestampA - timestampB;
      });

      const responseLastMessageId =
        sortedMessages.length > 0
          ? sortedMessages[sortedMessages.length - 1].id
          : undefined;

      // If the last message IDs are different, there are new messages
      const hasNewMessages = responseLastMessageId !== currentLastMessageId;
      if (hasNewMessages) {
        await useChatStore.getState().fetchChatHistory();
        lastMessageIdRef.current = responseLastMessageId;
      }

      return hasNewMessages;
    } catch (error) {
      logger.error("Error checking for new messages:", error);
      return false;
    }
  }, [messages]);

  const isProcessingFile = useCallback(() => {
    const len = messages.length;
    const now = Date.now();

    if (len < 2) return false;

    const lastMessage = messages[len - 1];
    const secondLastMessage = messages[len - 2];
    const thirdLastMessage = len >= 3 ? messages[len - 3] : null;

    const isServerAcknowledgment = (msg: any) =>
      (msg?.type === 'server' || msg?.sender === 'bot') &&
      (msg?.messageText || msg?.text) &&
      msg?.text?.toLowerCase().includes('thanks for sharing this file');

    const isUserAttachment = (msg: any) =>
      msg?.sender === 'user' &&
      msg?.attachments && msg?.attachments?.length > 0;

    const isUserTextMessage = (msg: any) =>
      msg?.sender === 'user' && !msg?.attachments && !!msg?.text;

    const allRecent = (msgA: any, msgB: any) =>
      now - msgA?.timestamp < 2 * 60 * 1000 &&
      now - msgB?.timestamp < 2 * 60 * 1000;

    // CASE 1: user uploads file, then receives thanks
    const case1 =
      isUserAttachment(secondLastMessage) &&
      isServerAcknowledgment(lastMessage) &&
      allRecent(secondLastMessage, lastMessage);

    // CASE 2: user sends image, then a follow-up text, then receives thanks
    const case2 =
      isUserAttachment(thirdLastMessage) &&
      isUserTextMessage(secondLastMessage) &&
      isServerAcknowledgment(lastMessage) &&
      allRecent(thirdLastMessage, lastMessage);
    return case1 || case2;
  }, [messages]);

  // Auto-refresh effect - check for new messages every minute
  useEffect(() => {
    const setupRefreshTimeout = () => {
      refreshTimeoutRef.current = setTimeout(async () => {
        const isAuthenticated = useIsAuthenticated();

        // Skip refresh if user is not authenticated, actively chatting, or waiting for a response
        if (!isAuthenticated) {
          setupRefreshTimeout();
          return;
        }
        // Skip refresh if user is actively chatting or waiting for a response
        if (!isWaitingForResponse && !isActiveConversation) {
          logger.info("Auto-refreshing chat history");

          try {
            // Check for new messages without updating state if no new messages
            const hasNewMessages = await checkForNewMessages();

            if (hasNewMessages) {
              logger.info("Chat refreshed with new messages");
            } else {
              logger.info("Chat refreshed, no new messages");
            }
          } catch (error) {
            logger.error("Error in auto-refresh:", error);
          }
        } else {
          logger.info("Skipping auto-refresh: user is in active conversation");
        }

        setupRefreshTimeout();
      }, 60000); // 1 minute
    };

    // Start the refresh cycle
    setupRefreshTimeout();

    // Clean up on unmount
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [isWaitingForResponse, isActiveConversation, messages]);

  // Update active conversation state based on user activity
  useEffect(() => {
    if (isWaitingForResponse) {
      setIsActiveConversation(true);

      // Reset active conversation flag after response is received
      const resetActiveTimeout = setTimeout(() => {
        setIsActiveConversation(false);
      }, 10000); // 10 seconds after response

      return () => clearTimeout(resetActiveTimeout);
    }
  }, [isWaitingForResponse]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        if (messages.length > 0) {
          //scrollToBottom();
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, [messages.length]);

  const getUniqueMessages = useCallback((messages: any[]) => {
    const seen = new Set();
    const OFFLINE_MESSAGE_TEXT = "Looks like you're offline. Reconnect to send messages.";

    // Find if there's an offline reconnect message
    let offlineMessage = null;

    // First pass - check if we have an offline message and filter out duplicates
    const filteredMessages = messages.filter((message) => {
      const duplicate = seen.has(message.id);
      seen.add(message.id);

      // Check if it's an offline message
      if (message.sender === "bot" && message.text === OFFLINE_MESSAGE_TEXT && !isConnected) {
        offlineMessage = message;
        // Filter out the offline message here - we'll add it back later
        return false;
      }

      return !duplicate;
    });

    // Sort messages (inverted order for FlatList)
    const sortedMessages = filteredMessages.reverse();

    // Extract latest 5 voice messages
    const recentVoiceIds = sortedMessages
      .filter(
        (m) =>
          m.attachments &&
          (m.attachments[0]?.type === 'voice' || m.attachments["0"]?.type === 'voice')
      )
      .slice(0, 5)
      .map((m) => m.id);

    // Annotate messages with isRecent flag for voice messages
    const annotatedMessages = sortedMessages.map((message) => {
      if (message.attachments && (message.attachments[0]?.type === 'voice' || message.attachments["0"]?.type === 'voice')) {
        return {
          ...message,
          isRecent: recentVoiceIds.includes(message.id),
        };
      }
      return message;
    });

    // If we have an offline message and we're not connected, add it to the beginning
    // (which will be the end/bottom when rendered in inverted FlatList)
    if (offlineMessage && !isConnected) {
      annotatedMessages.unshift(offlineMessage);
    }

    return annotatedMessages;
  }, [isConnected]);
  const { selectedMessages, clearSelectedMessages } = useChatStore();

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    const newMessages = await checkForNewMessages();
    setRefreshing(false);
  }, [checkForNewMessages]);

  const getMessageSelectionState = useCallback(
    (messageId: string): MessageSelectionState => ({
      isInSelectionMode: selectedMessages.length > 0,
      isSelected: selectedMessages.includes(messageId),
      isFirstSelected: selectedMessages.length === 1 && selectedMessages[0] === messageId
    }),
    [selectedMessages]
  );

  const handlePress = useCallback((messageId: string) => {
    const currentSelected = useChatStore.getState().selectedMessages;
    // Only trigger haptics for the first selection
    if (currentSelected.length === 0) {
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } else {
        softHaptic()
      }
    }
    useChatStore.getState().toggleMessageSelection(messageId);
  }, []);

  const renderListHeaderComponent = () => {
    const lastMessage = messages[messages.length - 1];
    const secondLastMessage = messages[messages.length - 2];
    const thirdLastMessage = messages[messages.length - 3];
    const shouldShowFileProcessing = isProcessingFile();

    let attachmentSourceMessage: any = null;

    if (
      secondLastMessage?.attachments &&
      Object.values(secondLastMessage.attachments).some((item: any) => typeof item === 'object' && item?.uri)
    ) {
      attachmentSourceMessage = secondLastMessage; // Case 1
    } else if (
      thirdLastMessage?.attachments &&
      Object.values(thirdLastMessage.attachments).some((item: any) => typeof item === 'object' && item?.uri)
    ) {
      attachmentSourceMessage = thirdLastMessage; // Case 2
    }

    const attachmentsArray = attachmentSourceMessage?.attachments
      ? Object.values(attachmentSourceMessage.attachments).filter(
        (item: any) => typeof item === 'object' && item?.uri
      )
      : [];

    const firstAttachment = attachmentsArray[0];

    const offlineMessage = messages.find(
      (message) =>
        message && message.sender === "bot" && message.text === "Looks like you're offline. Reconnect to send messages."
    );

    const shouldShowWaitingIndicator =
      messages.length > 0 &&
      isWaitingForResponse &&
      lastMessage.sender === "user" &&
      Date.now() - lastMessage.timestamp < 1 * 60 * 1000 &&
      !shouldShowFileProcessing &&
      !offlineMessage;

    return (
      <>
        {shouldShowFileProcessing && firstAttachment && typeof firstAttachment === 'object' && (
          <MediaProcessingCard
            //@ts-ignore
            isPdf={firstAttachment?.type === 'pdf'} isImage={firstAttachment?.type === 'image'}
          />
        )}

        {shouldShowWaitingIndicator && (
          <WaitingResponseIndicator />
        )}
        {/* {Platform.OS === 'ios' && (
          <Text style={styles.disclaimer}>
            For educational use only. Learn more{' '}
            <Text style={styles.link} onPress={() => Linking.openURL('https://www.meetaugust.ai/en/library/behind-august')}>
              here
            </Text>.
          </Text>
        )} */}
        <View style={{ height: 0 }} />
      </>
    );
  };

  const handleTryAgain = async () => {
    if (failedReconnectAttemptsRef.current >= 4) {
      useAuthStore.getState().logout();
      // router.replace('/auth/phone')
    }
    setIsRetrying(true);
    setTimeout(() => { }, 2000); /// timeouts for testing loader can remove later
    setTimeout(() => { }, 2000);
    setTimeout(async () => {
      try {
        await useChatStore.getState().initializeWebPubSub();
        failedReconnectAttemptsRef.current = 0;

        // When successfully connected, remove this message from the chat
        if (isWebPubSubConnected()) {
          // Remove the reconnect message from store
          useChatStore.setState((state) => ({
            messages: state.messages.filter(
              msg => !(msg.sender === "bot" && msg.text === "Looks like you're offline. Reconnect to send messages.")
            )
          }));
        }
        // Success - loader will stop
      } catch (error) {
        // Error - loader will stop
        failedReconnectAttemptsRef.current++
        logger.error("Failed to reconnect:", error);
      } finally {
        setIsRetrying(false);
      }
    }, 2000);
  };

  return (
    <View style={styles.container}>
      {/* { isLoading && getUniqueMessages(messages).length === 0 ? <ChatSkeleton/> : */}
      <FlatList
        ref={flatListRef}
        style={styles.list}
        data={getUniqueMessages(messages)}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          // special offline system message
          if (
            item.sender === "bot" &&
            item.text ===
            "Looks like you're offline. Reconnect to send messages." &&
            !isConnected
          ) {
            const spin = rotateAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ["0deg", "360deg"],
            });

            return (
              <View
                key={item.id}
                style={{
                  marginVertical: 12,
                  padding: 12,
                  // marginLeft: 16,
                  backgroundColor: "#F3F4F6",
                  borderRadius: 8,
                  flexDirection: "row",
                  maxWidth: "85%",
                  marginHorizontal: "auto",
                  borderWidth: 1,
                  borderColor: "red",
                  alignSelf: "center",
                  alignItems: "center",
                }}
              >
                <Animated.View
                  style={{
                    transform: [{ rotate: spin }],
                    marginRight: 8,
                  }}
                >
                  <RefreshCw color="#1F2937" size={16} />
                </Animated.View>
                <Text style={{ color: "#1F2937", flex: 1 }}>{item.text}</Text>
                <TouchableOpacity
                  onPress={handleTryAgain}
                  disabled={isRetrying}
                  style={{
                    padding: 8,
                    opacity: isRetrying ? 0.6 : 1,
                  }}
                >
                  <Text
                    style={{
                      color: "#000000",
                      fontWeight: "600",
                    }}
                  >
                    {isRetrying ? "Connecting..." : "Try Again"}
                  </Text>
                </TouchableOpacity>
              </View>
            );
          }
          // normal message bubble
          return (
            <MessageBubble
              selectedMessages={selectedMessages}
              handlePress={handlePress}
              message={item}
              selectionState={getMessageSelectionState(item.id)}
            />
          );
        }}
        contentContainerStyle={styles.contentContainer}
        initialNumToRender={35}
        maxToRenderPerBatch={20}
        onContentSizeChange={() => {
          if (!initialLoadCompletedRef.current) {
            flatListRef.current?.scrollToOffset({
              offset: 0,
              animated: false,
            });
            initialLoadCompletedRef.current = true;
          }
        }}
        scrollEnabled={true}
        onScrollToIndexFailed={(error) => {
          setTimeout(() => {
            flatListRef.current?.scrollToOffset({
              offset: 0,
              animated: true,
            });
          }, 300);
        }}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        onViewableItemsChanged={onViewableItemsChangedRef.current}
        viewabilityConfig={viewabilityConfigRef.current}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
        inverted={true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            // The following two props are crucial for a better UX with inverted lists
            progressViewOffset={10} // Helps position the spinner better
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {isLoading
                ? "Loading messages..."
                : "No messages yet. Start a conversation!"}
            </Text>
          </View>
        }
        ListHeaderComponent={renderListHeaderComponent()}
      />
      {/* } */}

      {/* Scroll to newest messages button */}
      {showScrollToBottomButton && (
        <TouchableOpacity
          style={styles.scrollToBottomButton}
          onPress={scrollToBottom}
          activeOpacity={0.7}
        >
          <ChevronDown size={24} color={colors.primary} />
        </TouchableOpacity>
      )}

      {/* Render children */}
      <View>{children}</View>
    </View>
  );
});

export default ChatList;
