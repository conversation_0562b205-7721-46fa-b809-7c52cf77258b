import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, StyleSheet, Keyboard } from 'react-native';
import { colors } from '@/constants/colors';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';

interface OtpInputProps {
  length: number;
  onOtpComplete: (otp: string) => void;
  autoFocus?: boolean;
}

const OtpInput: React.FC<OtpInputProps> = ({ 
  length = 6, 
  onOtpComplete,
  autoFocus = false
}) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(''));
  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    // Initialize refs array
    inputRefs.current = inputRefs.current.slice(0, length);
    
    // Auto focus on first input if enabled
    if (autoFocus && inputRefs.current[0]) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }
  }, [length, autoFocus]);

  useEffect(() => {
    // Check if OTP is complete
    const otpValue = otp.join('');
    if (otpValue.length === length) {
      onOtpComplete(otpValue);
    }
  }, [otp, length, onOtpComplete]);

  const handleChange = (text: string, index: number) => {
    // Only allow digits
    if (!/^\d*$/.test(text)) return;

    const newOtp = [...otp];
    
    // Handle paste of multiple digits
    if (text.length > 1) {
      // Limit to the remaining boxes
      const pastedText = text.substring(0, length - index);
      
      // Fill current and next boxes
      for (let i = 0; i < pastedText.length; i++) {
        if (index + i < length) {
          newOtp[index + i] = pastedText[i];
        }
      }
      
      setOtp(newOtp);
      
      // Focus on the next empty input or the last one
      const nextIndex = Math.min(index + pastedText.length, length - 1);
      inputRefs.current[nextIndex]?.focus();
      return;
    }

    // Handle single digit
    newOtp[index] = text;
    setOtp(newOtp);
    
    // Auto-focus next input if value is entered
    if (text && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // Handle backspace
    if (e.nativeEvent.key === 'Backspace') {
      const newOtp = [...otp];
      
      // If current input is empty, focus previous input and clear it
      if (!otp[index] && index > 0) {
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      } else {
        // Clear current input
        newOtp[index] = '';
        setOtp(newOtp);
      }
    }
  };

  const handleFocus = (index: number) => {
    // When an input is focused, select all text
    inputRefs.current[index]?.setNativeProps({ selection: { start: 0, end: 1 } });
  };

  return (
    <View style={styles.container}>
      {Array(length).fill(0).map((_, index) => (
        <TextInput
          key={index}
          ref={(ref) => (inputRefs.current[index] = ref)}
          style={[
            styles.input,
            otp[index] ? styles.inputFilled : null
          ]}
          value={otp[index]}
          onChangeText={(text) => handleChange(text, index)}
          onKeyPress={(e) => handleKeyPress(e, index)}
          onFocus={() => handleFocus(index)}
          keyboardType="number-pad"
          maxLength={length} // Allow paste of full OTP
          selectTextOnFocus
          selectionColor={colors.primary}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginVertical: moderateVerticalScale(20),
    paddingHorizontal: moderateScale(4)
  },
  input: {
    width: moderateScale(45),
    height: moderateVerticalScale(55),
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: moderateScale(8),
    textAlign: 'center',
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: colors.black,
  },
  inputFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.gray[50],
  },
});

export default OtpInput;
