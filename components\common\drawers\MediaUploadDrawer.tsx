import React, { useRef, useEffect, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, Animated, TouchableWithoutFeedback, PanResponder, Dimensions } from 'react-native';
import { Image as ImageIcon, FileText, Camera } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import logger from '@/utils/logger/logger';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from 'react-i18next';

type MediaUploadDrawerProps = {
  visible: boolean;
  onClose: () => void;
  onTakePhoto: () => void;
  onChooseImage: () => void;
  onChooseFile?: (() => void) | null;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.4)",
      justifyContent: "flex-end",
    },
    drawerContainer: {
      backgroundColor: theme.colors.secondary[50],
      borderTopLeftRadius: moderateScale(theme.radii.xl),
      borderTopRightRadius: moderateScale(theme.radii.xl),
      paddingBottom: moderateVerticalScale(30),
      paddingTop: moderateVerticalScale(5),
    },
    dragIndicator: {
      width: moderateScale(40),
      height: moderateVerticalScale(4),
      backgroundColor: theme.colors.gray[300],
      borderRadius: moderateScale(2),
      alignSelf: "center",
      marginBottom: moderateVerticalScale(16),
    },
    option: {
      padding: moderateScale(12),
      marginVertical: moderateVerticalScale(4), // Add vertical spacing between options
    },
    optionContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    iconContainer: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderRadius: moderateScale(theme.radii.xl),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
      marginRight: moderateScale(20),
    },
    optionText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
  });

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const DISMISS_THRESHOLD = SCREEN_HEIGHT * 0.15; // 15% of screen height
const VELOCITY_THRESHOLD = 0.7; // Velocity threshold for quick flick

const MediaUploadDrawer = ({ visible, onClose, onTakePhoto, onChooseImage, onChooseFile }: MediaUploadDrawerProps) => {
  const translateY = useRef(new Animated.Value(300)).current;
  const [isDragging, setIsDragging] = useState(false);
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { t } = useTranslation();

  // Create PanResponder to handle swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to significant vertical movement
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
        translateY.setValue(0);
        translateY.setOffset(0);
      },
      onPanResponderMove: (_, gestureState) => {
        // Only allow downward movement (positive dy)
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        setIsDragging(false);
        
        // Determine if we should dismiss based on distance or velocity
        const shouldDismiss =
          gestureState.dy > DISMISS_THRESHOLD ||
          gestureState.vy > VELOCITY_THRESHOLD;

        if (shouldDismiss) {
          // Animate to bottom of screen and close
          Animated.timing(translateY, {
            toValue: SCREEN_HEIGHT,
            duration: 100,
            useNativeDriver: true,
          }).start(() => {
            onClose();
          });
        } else {
          // Animate back to original position
          Animated.spring(translateY, {
            toValue: 0,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          }).start();
        }
      },
      onPanResponderTerminate: () => {
        setIsDragging(false);
        // Animate back to original position
        Animated.spring(translateY, {
          toValue: 0,
          tension: 40,
          friction: 7,
          useNativeDriver: true,
        }).start();
      },
    })
  ).current;

  useEffect(() => {
    if (visible) {
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(translateY, {
        toValue: 300,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View 
              {...panResponder.panHandlers}
              style={[
                styles.drawerContainer,
                { transform: [{ translateY }] }
              ]}
            >
              <View style={styles.dragIndicator} />
              
              <TouchableOpacity 
                style={styles.option}
                onPress={onChooseImage}
                activeOpacity={0.6}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                pressRetentionOffset={{ top: 20, bottom: 20, left: 20, right: 20 }}
              >
                <View style={styles.optionContent}>
                  <View style={styles.iconContainer}>
                    <ImageIcon size={24} color={colors.primary} />
                  </View>
                  <Text style={styles.optionText}>{t('mediaUpload.photoLibrary')}</Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.option}
                onPress={onTakePhoto}
                activeOpacity={0.6}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                pressRetentionOffset={{ top: 20, bottom: 20, left: 20, right: 20 }}
              >
                <View style={styles.optionContent}>
                  <View style={styles.iconContainer}>
                    <Camera size={24} color={colors.primary} />
                  </View>
                  <Text style={styles.optionText}>{t('mediaUpload.takePhoto')}</Text>
                </View>
              </TouchableOpacity>
              
              {onChooseFile && (
                <TouchableOpacity 
                  style={styles.option}
                  onPress={onChooseFile}
                  activeOpacity={0.6}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  pressRetentionOffset={{ top: 20, bottom: 20, left: 20, right: 20 }}
                >
                  <View style={styles.optionContent}>
                    <View style={styles.iconContainer}>
                      <FileText size={24} color={colors.primary} />
                    </View>
                    <Text style={styles.optionText}>{t('mediaUpload.chooseFile')}</Text>
                  </View>
                </TouchableOpacity>
              )}
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

// const styles = StyleSheet.create({
//   overlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.4)',
//     justifyContent: 'flex-end',
//   },
//   drawerContainer: {
//     backgroundColor: colors.white,
//     borderTopLeftRadius: moderateScale(20),
//     borderTopRightRadius: moderateScale(20),
//     paddingBottom: moderateVerticalScale(30),
//     paddingTop: moderateVerticalScale(5),
//   },
//   dragIndicator: {
//     width: moderateScale(40),
//     height: moderateVerticalScale(4),
//     backgroundColor: colors.gray[300],
//     borderRadius: moderateScale(2),
//     alignSelf: 'center',
//     marginBottom: moderateVerticalScale(16),
//   },
//   option: {
//     padding: moderateScale(12),
//     marginVertical: moderateVerticalScale(4), // Add vertical spacing between options
//   },
//   optionContent: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   iconContainer: {
//     width: moderateScale(40),
//     height: moderateScale(40),
//     borderRadius: moderateScale(20),
//     backgroundColor: colors.gray[100],
//     alignItems: 'center',
//     justifyContent: 'center',
//     marginRight: moderateScale(20),
//   },
//   optionText: {
//     fontSize: moderateScale(15),
//     color: colors.black,
//   },
// });

export default MediaUploadDrawer;
