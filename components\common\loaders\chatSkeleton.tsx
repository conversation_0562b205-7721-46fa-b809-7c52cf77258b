import React, { useEffect, useMemo, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { colors } from '@/constants/colors';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    messagesContainer: {
      flex: 1,
      padding: 16,
      gap: theme.spacing.md,
    },
    messageContainer: {
      flexDirection: "row",
      marginBottom: 8,
      maxWidth: "80%",
    },
    leftMessage: {
      alignSelf: "flex-start",
    },
    rightMessage: {
      alignSelf: "flex-end",
    },
    messageText: {
      height: 60,
      borderRadius: theme.radii.md,
      backgroundColor: theme.colors.gray[200],
    },
    leftText: {
      width: "70%",
    },
    rightText: {
      width: "60%",
    },
    inputContainer: {
      flexDirection: "row",
      padding: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.gray[200],
      alignItems: "center",
      gap: 12,
    },
    inputBox: {
      flex: 1,
      height: 48,
      borderRadius: theme.radii["2xl"],
      backgroundColor: theme.colors.gray[200],
    },
    sendButton: {
      width: 48,
      height: 48,
      borderRadius: theme.radii["2xl"],
      backgroundColor: theme.colors.gray[200],
    },
  });

const MessageSkeleton = ({ isLeft = true, delay = 0 }) => {
const { theme } = useTheme();
const styles = useMemo(() => createStyles(theme), [theme]);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,   
      delay: delay,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <Animated.View 
      style={[
        styles.messageContainer, 
        isLeft ? styles.leftMessage : styles.rightMessage,
        { opacity: fadeAnim }
      ]}
    >
      <View style={[styles.messageText, isLeft ? styles.leftText : styles.rightText]} />
    </Animated.View>
  );
};

const ChatSkeleton = () => {
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.container}>
      <View style={styles.messagesContainer}>
        <MessageSkeleton isLeft={true} delay={0} />
        <MessageSkeleton isLeft={false} delay={200} />
        <MessageSkeleton isLeft={true} delay={400} />
        <MessageSkeleton isLeft={false} delay={600} />
        <MessageSkeleton isLeft={true} delay={800} />
        <MessageSkeleton isLeft={false} delay={1000} />
        <MessageSkeleton isLeft={true} delay={1200} />
      </View>
      {/* <View style={styles.inputContainer}>
        <View style={styles.inputBox} />
        <View style={styles.sendButton} />
      </View> */}
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   messagesContainer: {
//     flex: 1,
//     padding: 16,
//     gap: 16,
//   },
//   messageContainer: {
//     flexDirection: 'row',
//     marginBottom: 8,
//     maxWidth: '80%',
//   },
//   leftMessage: {
//     alignSelf: 'flex-start',
//   },
//   rightMessage: {
//     alignSelf: 'flex-end',
//   },
//   messageText: {
//     height: 60,
//     borderRadius: 12,
//     backgroundColor: colors.gray[200],
//   },
//   leftText: {
//     width: '70%',
//   },
//   rightText: {
//     width: '60%',
//   },
//   inputContainer: {
//     flexDirection: 'row',
//     padding: 16,
//     borderTopWidth: 1,
//     borderTopColor: colors.gray[200],
//     alignItems: 'center',
//     gap: 12,
//   },
//   inputBox: {
//     flex: 1,
//     height: 48,
//     borderRadius: 24,
//     backgroundColor: colors.gray[200],
//   },
//   sendButton: {
//     width: 48,
//     height: 48,
//     borderRadius: 24,
//     backgroundColor: colors.gray[200],
//   },
// });

export default ChatSkeleton;