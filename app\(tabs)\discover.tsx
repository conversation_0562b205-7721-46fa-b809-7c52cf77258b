import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { useRouter } from "expo-router";
import { useDrawerStore } from "@/store/drawerStore";
import { useScrollStore } from "@/store/scrollStore";
import Toast from "react-native-root-toast";
import {
  FileText,
  Utensils,
  MessageSquare,
  FileImage,
  Heart,
  Copy,
  ExternalLink,
  Mic,
} from "lucide-react-native";
import { colors } from "@/constants/colors";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Text,
  Platform,
  Linking,
  Share,
  Image,
} from "react-native";
import * as Clipboard from "expo-clipboard";
import ComingSoonModal from "@/components/common/modals/ComingSoonModal";
import {
  trackUserInteraction,
  trackOperation,
  OperationStatus,
} from "@/utils/mixpanel/mixpanel-utils";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import TopNavbar from "@/components/navigation/TopNavbar";
import SpecialistCard from "@/components/discover/SpecialistCard";
import SwipeableDeckCard from "@/components/discover/SwipeableDeckCard";
import FeatureBlock from "@/components/discover/FeatureBlock";
import CategoryTabs from "@/components/discover/CategoryTabs";
import Snackbar from "@/components/common/modals/Snackbar";

import { specialists } from "@/constants/specialists";
import { discoverCards } from "@/constants/discoverCards";

import normalize from "react-native-normalize";
import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from "react-native-size-matters";
import { useTranslation } from "react-i18next";
import { useScreenTracking } from "../currentScreen";

const SCREEN_HEIGHT = Dimensions.get("window").height;
const SCREEN_WIDTH = Dimensions.get("window").width;

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    scrollView: {
      flex: 1,
      paddingTop: moderateVerticalScale(16),
    },
    scrollContent: {
      paddingBottom: moderateVerticalScale(32),
    },
    section: {
      zIndex: 10,
      padding: moderateScale(16),
      paddingTop: 0,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: moderateVerticalScale(8),
      marginLeft: moderateScale(4),
    },
    sectionTitle: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "500",
      color: theme.colors.gray[700],
    },
    seeAllText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.primary.main,
      fontWeight: "500",
    },
    featureContainser: {
      marginTop: moderateVerticalScale(8),
    },
    noCardsContainer: {
      height: moderateVerticalScale(400),
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.background.primary,
      borderRadius: moderateScale(theme.radii.xl),
      width: SCREEN_WIDTH - moderateScale(32),
    },
    noCardsText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray[600],
      textAlign: "center",
      padding: moderateScale(20),
    },
    specialistsContainer: {
      paddingRight: moderateScale(16),
      paddingLeft: moderateScale(4),
      marginBottom: moderateVerticalScale(16),
    },
    cardsSection: {
      height: moderateVerticalScale(470),
      marginBottom: moderateVerticalScale(8),
    },
    cardsContainer: {
      height: moderateVerticalScale(400),
      alignItems: "center",
      justifyContent: "flex-start",
      width: "100%",
      paddingTop: moderateVerticalScale(10),
      marginBottom: moderateVerticalScale(20),
    },
    cardIndicators: {
      flexDirection: "row",
      justifyContent: "center",
      marginTop: moderateVerticalScale(16),
    },
    indicator: {
      width: moderateScale(8),
      height: moderateVerticalScale(8),
      borderRadius: moderateScale(theme.radii.xs),
      backgroundColor: theme.colors.gray[300],
      marginHorizontal: moderateScale(4),
    },
    activeIndicator: {
      backgroundColor: theme.colors.primary.main,
      width: moderateScale(16),
    },
    actionButton: {
      padding: moderateScale(4),
      marginLeft: moderateScale(8),
    },
    footer: {
      alignItems: "center",
      paddingVertical: moderateVerticalScale(8),
      marginTop: 0,
    },
    footerLogo: {
      width: moderateScale(100),
      height: moderateVerticalScale(40),
      marginBottom: moderateVerticalScale(8),
    },
    versionText: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
    },
  });

function DiscoverScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("all");
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState("");

  const scrollRef = useRef<ScrollView>(null);
  const setDiscoverRef = useScrollStore((state) => state.setDiscoverRef);

  useEffect(() => {
    if (scrollRef.current) {
      setDiscoverRef(scrollRef);
    }
  }, []);

  useScreenTracking('Discover', { screen: "Discover" });


  const categories = useMemo(() => [
    { key: "all", label: t("discover.categories.all"), match: null },
    { key: "heartHealth", label: t("discover.categories.heartHealth"), match: "Heart Health" },
    { key: "nutrition", label: t("discover.categories.nutrition"), match: "Nutrition" },
    { key: "mentalHealth", label: t("discover.categories.mentalHealth"), match: "Mental Health" },
    { key: "fitness", label: t("discover.categories.fitness"), match: "Fitness" },
    { key: "wellness", label: t("discover.categories.wellness"), match: "Wellness" },
  ], [t]);

  const filteredCards = useMemo(() => {
    if (activeCategory === "all") {
      return [...discoverCards].sort(() => Math.random() - 0.5);
    } else {
      const matchValue = categories.find(c => c.key === activeCategory)?.match;
      return discoverCards.filter((card) => card.category === matchValue);
    }
  }, [activeCategory, categories]);

  useEffect(() => {
    setCurrentCardIndex(0);
    if (activeCategory !== t('discover.categories.all')) {
      trackUserInteraction("Category Selection", {
        category: activeCategory,
        screen: "Discover",
      });
    }
  }, [activeCategory, t]);

  const handleSpecialistPress = useCallback((specialist: any) => {
    trackUserInteraction("Specialist Card Tap", {
      specialistId: specialist.id,
      specialistName: specialist.name,
      specialistFeature: specialist.featureName,
    });
    setSelectedFeature(specialist.featureName);
    setIsModalVisible(true);
  }, []);

  const handleSwipe = useCallback(
    (direction: string, cardIndex: number) => {
      // Check if the card exists before accessing its properties
      if (cardIndex >= 0 && cardIndex < filteredCards.length) {
        const currentCard = filteredCards[cardIndex];
        trackUserInteraction("Card Swipe", {
          direction,
          cardId: currentCard.id,
          cardTitle: currentCard.title,
          cardCategory: currentCard.category,
        });
      }
    },
    [filteredCards]
  );

  const handleSwipeLeft = useCallback(
    (cardIndex: number) => handleSwipe("left", cardIndex),
    [handleSwipe]
  );
  const handleSwipeRight = useCallback(
    (cardIndex: number) => handleSwipe("right", cardIndex),
    [handleSwipe]
  );

  const handleIndexChange = useCallback((index: number) => {
    setCurrentCardIndex(index);
  }, []);

  const handleCategoryPress = useCallback((category: any) => {
    setActiveCategory(category);
  }, []);

  const handleCardPress = useCallback((slug: any) => {
    const url = `https://www.meetaugust.ai/en/library/blog/view/${slug}`;
    trackUserInteraction("Card Content Open", {
      slug,
      url,
      method: "external_link",
    });
    trackOperation("External Link Open", OperationStatus.INITIATED, {
      url,
      type: "card_content",
    });
    Linking.openURL(url)
      .then(() =>
        trackOperation("External Link Open", OperationStatus.SUCCESS, {
          url,
          type: "card_content",
        })
      )
      .catch((error) =>
        trackOperation("External Link Open", OperationStatus.FAILURE, {
          url,
          type: "card_content",
          error: error.message,
        })
      );
  }, []);

  const handleCopyLink = useCallback((slug: any) => {
    const url = `https://www.meetaugust.ai/en/library/blog/view/${slug}`;
    trackOperation("Copy to Clipboard", OperationStatus.INITIATED, {
      content: "card_link",
      slug,
    });
    Clipboard.setStringAsync(url)
      .then(() => {
        trackOperation("Copy to Clipboard", OperationStatus.SUCCESS, {
          content: "card_link",
          slug,
        });
        if (Platform.OS === "ios") {
          Toast.show(t('clipboard.success'), {
            duration: Toast.durations.SHORT,
            position: Toast.positions.BOTTOM,
            shadow: true,
            animation: true,
            hideOnPress: true,
            delay: 0,
          });
        }
        //setShowSnackbar(true);
        //  setTimeout(() => setShowSnackbar(false), 3000);
      })
      .catch((error) => {
        trackOperation("Copy to Clipboard", OperationStatus.FAILURE, {
          content: "card_link",
          slug,
          error: error.message,
        });
      });
  }, []);

  const handleShareLink = useCallback(async (slug: any) => {
    const url = `https://www.meetaugust.ai/en/library/blog/view/${slug}`;
    trackUserInteraction("Share Initiated", { content_type: "card", slug });
    trackOperation("Share Content", OperationStatus.INITIATED, {
      content_type: "card",
      slug,
      url,
    });
    try {
      await Share.share({
        message: url,
        url: Platform.OS === "ios" ? url : undefined,
      });
      trackOperation("Share Content", OperationStatus.SUCCESS, {
        content_type: "card",
        slug,
        url,
      });
    } catch (error) {
      trackOperation("Share Content", OperationStatus.FAILURE, {
        content_type: "card",
        slug,
        url,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }, []);

  const renderActionButton = useCallback(
    (icon: React.ReactNode, onPress: () => void) => (
      <TouchableOpacity style={styles.actionButton} onPress={onPress}>
        {icon}
      </TouchableOpacity>
    ),
    []
  );
  const [isScrollEnabled, setIsScrollEnabled] = useState(true);
  return (
    <SafeAreaView style={styles.container} edges={[]}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('discover.nav.title')} />

      <ScrollView
        ref={scrollRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        scrollEnabled={isScrollEnabled}
      >
        {/* Category Tabs */}
        <CategoryTabs
          categories={categories.map((c) => c.label)}
          activeCategory={categories.find((c) => c.key === activeCategory)?.label || ""}
          onCategoryPress={(pressedLabel) => {
            const matched = categories.find((c) => c.label === pressedLabel);
            if (matched) {
              setActiveCategory(matched.key);
            }
          }}
        />

        {/* Swipeable Cards Section */}
        <View style={styles.cardsSection}>
          <View style={styles.cardsContainer}>
            {filteredCards.length > 0 ? (
              <SwipeableDeckCard
                isScrollEnabled={isScrollEnabled}
                setIsScrollEnabled={setIsScrollEnabled}
                key={activeCategory} // Add key to force re-render on category change
                cards={filteredCards}
                onSwipeLeft={handleSwipeLeft}
                onSwipeRight={handleSwipeRight}
                onCardPress={(card) => handleCardPress(card.slug)}
                onCopy={(card) => handleCopyLink(card.slug)}
                onShare={(card) => handleShareLink(card.slug)}
                currentIndex={
                  filteredCards.length > 0
                    ? currentCardIndex % filteredCards.length
                    : 0
                }
                onIndexChange={handleIndexChange}
              />
            ) : (
              <View style={styles.noCardsContainer}>
                <Text style={styles.noCardsText}>
                  {t('discover.cards.empty')}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.cardIndicators}>
            {filteredCards.length > 0 &&
              Array.from({ length: Math.min(5, filteredCards.length) }).map(
                (_, index) => {
                  const isActive =
                    index ===
                    Math.min(4, currentCardIndex % filteredCards.length);
                  const isLastDot = index === 4 && currentCardIndex >= 4;
                  return (
                    <View
                      key={index}
                      style={[
                        styles.indicator,
                        (isActive || isLastDot) && styles.activeIndicator,
                      ]}
                    />
                  );
                }
              )}
          </View>
        </View>

        {/* Specialists Section */}
        {/*<View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Specialists</Text>
            <TouchableOpacity
              onPress={() => {
                trackUserInteraction("See All Tap", { section: "Specialists" });
                router.push("/specialists");
              }}
            >
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.specialistsContainer}
          >
            {specialists.map((specialist) => (
              <SpecialistCard
                key={specialist.id}
                specialist={specialist}
                onPress={handleSpecialistPress}
              />
            ))}
          </ScrollView>
        </View>*/}

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('discover.sections.features')}</Text>
          <View style={styles.featureContainser}>
            <FeatureBlock
              title={t('discover.features.healthLibrary.title')}
              description={t('discover.features.healthLibrary.description')}
              icon={
                <FileText
                  size={normalize(22)}
                  color={theme.colors.primary.main}
                />
              }
              backgroundColor="#edfadf"
              actions={[
                renderActionButton(
                  <Copy size={18} color={theme.colors.primary.main} />,
                  () => {
                    const url = "https://www.meetaugust.ai/en/library";
                    trackUserInteraction("Feature Action", {
                      feature: "Health Library",
                      action: "copy_link",
                    });

                    trackOperation(
                      "Copy to Clipboard",
                      OperationStatus.INITIATED,
                      {
                        content: "feature_link",
                        feature: "Health Library",
                      }
                    );

                    Clipboard.setStringAsync(url)
                      .then(() => {
                        trackOperation(
                          "Copy to Clipboard",
                          OperationStatus.SUCCESS,
                          {
                            content: "feature_link",
                            feature: "Health Library",
                          }
                        );

                        if (Platform.OS === "ios") {
                          Toast.show(t('clipboard.success'), {
                            duration: Toast.durations.SHORT,
                            position: Toast.positions.BOTTOM,
                            shadow: true,
                            animation: true,
                            hideOnPress: true,
                            delay: 0,
                          });
                        }
                        // setShowSnackbar(true);
                        // setTimeout(() => setShowSnackbar(false), 3000);
                      })
                      .catch((error) => {
                        trackOperation(
                          "Copy to Clipboard",
                          OperationStatus.FAILURE,
                          {
                            content: "feature_link",
                            feature: "Health Library",
                            error:
                              error instanceof Error
                                ? error.message
                                : String(error),
                          }
                        );
                      });
                  }
                ),
                renderActionButton(
                  <ExternalLink
                    size={normalize(18)}
                    color={theme.colors.primary.main}
                  />,
                  () => {
                    trackUserInteraction("Feature Action", {
                      feature: "Health Library",
                      action: "open",
                    });
                    router.push("/library");
                  }
                ),
              ]}
              onPress={() => {
                trackUserInteraction("Feature Card Tap", {
                  feature: "Health Library",
                });
                router.push("/library");
              }}
            />

            <FeatureBlock
              title={t('discover.features.nutritionTracker.title')}
              description={t('discover.features.nutritionTracker.description')}
              icon={<Utensils size={normalize(22)} color="#E4A088" />} // Darker shade of fae7df
              backgroundColor="#fae7df"
              actions={[
                renderActionButton(
                  <ExternalLink size={18} color="#E4A088" />,
                  () => {
                    trackUserInteraction("Feature Action", {
                      feature: "Nutrition Tracker",
                      action: "open",
                    });
                    router.push("/nutrition");
                  }
                ),
              ]}
              onPress={() => {
                trackUserInteraction("Feature Card Tap", {
                  feature: "Nutrition Tracker",
                });
                router.push("/nutrition");
              }}
            />

            <FeatureBlock
              title={t('discover.features.multilingualSupport.title')}
              description={t('discover.features.multilingualSupport.description')}
              icon={<MessageSquare size={normalize(22)} color="#2196F3" />} // Keeping blue
              backgroundColor="#dfe7fa"
              actions={[
                renderActionButton(
                  <MessageSquare size={normalize(18)} color="#2196F3" />,
                  () => {
                    trackUserInteraction("Feature Action", {
                      feature: "Multilingual Support",
                      action: "open",
                      method: "Text",
                    });
                    router.push({
                      pathname: "/chat",
                      params: { setPrompt: t('discover.features.multilingualSupport.samplePrompt') },
                    });
                  }
                ),
              ]}
              onPress={() => {
                trackUserInteraction("Feature Card Tap", {
                  feature: "Multilingual Support",
                });
                router.push({
                  pathname: "/chat",
                  params: { setPrompt: "Hindi mein baat karo" },
                });
              }}
            />

            <FeatureBlock
              title={t('discover.features.labReportAnalysis.title')}
              description={t('discover.features.labReportAnalysis.description')}
              icon={<FileImage size={normalize(18)} color="#E4A4B1" />} // Darker shade of ffdfe7
              backgroundColor="#ffdfe7"
              actions={[
                renderActionButton(
                  <FileText size={18} color="#E4A4B1" />,
                  () => {
                    trackUserInteraction("Feature Action", {
                      feature: "Lab Report Analysis",
                      action: "open",
                    });
                    // Set the drawer to open and then navigate to chat
                    useDrawerStore.getState().setUploadDrawerOpen(true);
                    router.push("/chat");
                  }
                ),
              ]}
              onPress={() => {
                trackUserInteraction("Feature Card Tap", {
                  feature: "Lab Report Analysis",
                });
                // Set the drawer to open and then navigate to chat
                useDrawerStore.getState().setUploadDrawerOpen(true);
                router.push("/chat");
              }}
            />
          </View>
        </View>

        <View style={styles.footer}>
          <Image
            source={{
              uri: "https://augustbuckets.blob.core.windows.net/mobile-app-assets/mobile-logo-white.png",
            }}
            style={styles.footerLogo}
            resizeMode="contain"
          />
          <Text style={styles.versionText}>{t('common.version')}</Text>
        </View>
      </ScrollView>

      {showSnackbar && Platform.OS === "ios" && (
        <Snackbar
          message={snackbarMessage}
          onDismiss={() => setShowSnackbar(false)}
        />
      )}
      <ComingSoonModal
        isVisible={isModalVisible}
        onClose={() => {
          trackUserInteraction("Modal Close", {
            modal: "ComingSoon",
            feature: selectedFeature,
          });
          setIsModalVisible(false);
        }}
        featureName={selectedFeature}
      />
    </SafeAreaView>
  );
}
export default React.memo(DiscoverScreen);
