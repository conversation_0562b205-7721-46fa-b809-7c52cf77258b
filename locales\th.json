{"common": {"error": "ข้อผิดพลาด", "yes": "ใช่", "no": "ไม่", "sometimes": "บางครั้ง", "close": "ปิด", "cancel": "ยกเลิก", "save": "บันทึก", "next": "ถัดไป", "loading": "กำลังโหลด...", "version": "v0.0.1.7"}, "welcome": "เข้าสู่ระบบเพื่อเริ่มพูดคุยกับ August", "notFound": {"title": "โอ๊ะโอ!", "message": "หน้าจอนี้ไม่มีอยู่", "goHome": "ไปที่หน้าจอหลัก!"}, "library": {"title": "คลังสุขภาพ"}, "specialists": {"title": "ผู้เชี่ยวชาญ", "description": "ปรึกษาผู้เชี่ยวชาญด้านสุขภาพสำหรับข้อกังวลด้านสุขภาพที่เฉพาะเจาะจงมากขึ้น เลือกผู้เชี่ยวชาญด้านล่าง:", "generalPhysician": {"title": "แพทย์ทั่วไป", "description": "สำหรับข้อกังวลด้านสุขภาพทั่วไปและการดูแลเบื้องต้น"}, "nutritionist": {"title": "นักโภชนาการ", "description": "สำหรับคำแนะนำเกี่ยวกับอาหาร โภชนาการ และการควบคุมน้ำหนัก"}, "cardiologist": {"title": "แพทย์ผู้เชี่ยวชาญด้านหัวใจ", "description": "สำหรับข้อกังวลเกี่ยวกับหัวใจและสุขภาพหัวใจและหลอดเลือด"}, "neurologist": {"title": "แพทย์ผู้เชี่ยวชาญด้านระบบประสาท", "description": "สำหรับปัญหาเกี่ยวกับสมอง ไขสันหลัง และระบบประสาท"}, "oncologist": {"title": "แพทย์ผู้เชี่ยวชาญด้านมะเร็ง", "description": "สำหรับข้อกังวลและการรักษาที่เกี่ยวข้องกับมะเร็ง"}, "endocrinologist": {"title": "แพทย์ผู้เชี่ยวชาญด้านต่อมไร้ท่อ", "description": "สำหรับโรคที่เกี่ยวข้องกับฮอร์โมนและการควบคุมโรคเบาหวาน"}, "dermatologist": {"title": "แพทย์ผู้เชี่ยวชาญด้านผิวหนัง", "description": "สำหรับสภาพผิว ผม และเล็บ"}, "psychiatrist": {"title": "จิตแพทย์", "description": "สำหรับข้อกังวลด้านสุขภาพจิตและความเป็นอยู่ที่ดีทางจิตวิทยา"}}, "profile": {"title": "โปรไฟล์", "defaultName": "ผู้เยี่ยมชม", "namePlaceholder": "ป้อนชื่อของคุณ", "saving": "กำลังบันทึก...", "noPhoneNumber": "ไม่มีหมายเลขโทรศัพท์", "loggingOut": "กำลังออกจากระบบ...", "about": {"title": "เกี่ยวกับ", "description": "เรียนรู้เพิ่มเติมเกี่ยวกับ August"}, "whatsapp": {"title": "WhatsApp", "description": "แชทกับ August บน WhatsApp"}, "refer": {"title": "แนะนำ", "description": "ชอบ August? แชร์กับเพื่อนของคุณ"}, "deleteAccount": {"title": "ลบบัญชี", "description": "เสียใจที่คุณต้องไป"}, "logout": {"title": "ออกจากระบบ", "description": "กลับมาเร็วๆ นะ เราจะคิดถึงคุณ"}, "shareMessage": "👋 สวัสดี ลองใช้แอปสุดเจ๋งนี้ดูนะ!\n\n\n\n➡️ฉันใช้ August เพื่อรับข้อมูลและคำแนะนำด้านสุขภาพที่รวดเร็วและเชื่อถือได้ มันเหมือนกับมีหมออยู่ในกระเป๋าของคุณ! ตรวจสอบได้ที่นี่:", "error": {"loadFailed": "ไม่สามารถโหลดข้อมูลผู้ใช้ได้", "fetchError": "เกิดข้อผิดพลาดขณะดึงข้อมูลผู้ใช้", "updateNameFailed": "ไม่สามารถอัปเดตชื่อได้", "updateNameError": "เกิดข้อผิดพลาดขณะอัปเดตชื่อ", "loadFoodData": "ไม่สามารถโหลดข้อมูลอาหารได้", "logoutError": "ข้อผิดพลาดระหว่างการออกจากระบบ:", "shareError": "ข้อผิดพลาดในการแชร์ข้อความ:"}}, "error": {"title": "มีบางอย่างผิดพลาด", "checkLogs": "โปรดตรวจสอบบันทึกอุปกรณ์ของคุณสำหรับรายละเอียดเพิ่มเติม", "unknown": "ข้อผิดพลาดไม่ทราบสาเหตุ", "unknownFile": "ไฟล์ไม่ทราบสาเหตุ", "unknownLine": "บรรทัดไม่ทราบสาเหตุ", "unknownColumn": "คอลัมน์ไม่ทราบสาเหตุ"}, "auth": {"phone": {"selectCountry": "เลือกประเทศ", "searchCountries": "ค้นหาประเทศ", "validation": {"invalidPhone": "กรุณากรอกหมายเลขโทรศัพท์ที่ถูกต้อง", "invalidDigits": "กรุณากรอกหมายเลขโทรศัพท์ที่ถูกต้อง (7-15 หลัก)"}}, "header": {"title": "รับคำตอบเกี่ยวกับข้อกังวลด้านสุขภาพของคุณได้ทันทีและเป็นส่วนตัว", "subtitle": "คำแนะนำที่ใส่ใจ ไม่เร่งรีบ ไม่สับสน", "emphasis": "เพียงแค่ความกระจ่าง"}, "greeting": "สวัสดี 👋", "phoneNumber": "หมายเลขโทรศัพท์", "requestOTP": "ขอ OTP", "otp": {"title": "รหัสผ่านแบบครั้งเดียว", "verify": "ยืนยัน OTP", "sending": "กำลังส่ง...", "countdown": "ส่ง OTP ใหม่ได้อีกครั้งใน {{countdown}} วินาที", "resend": "ส่ง OTP ใหม่", "sentTo": "OTP ส่งไปที่ ", "whatsappSuffix": "ทาง Whatsapp"}, "disclaimer": {"prefix": "เมื่อลงทะเบียน คุณยอมรับข้อกำหนดในการให้บริการของเรา ", "continuePrefix": "เมื่อดำเนินการต่อ คุณยอมรับข้อกำหนดในการให้บริการของเรา ", "termsOfService": "ข้อกำหนดในการให้บริการ", "and": " และ ", "privacyPolicy": "นโยบายความเป็นส่วนตัว", "whatsappConsent": "และยินยอมรับการอัปเดตและการแจ้งเตือนจากเราผ่านทาง WhatsApp"}}, "onboarding": {"preAuth": {"welcome": {"title": "ยินดีต้อนรับสู่ August!", "buttonText": "เริ่มกันเลย"}}, "postAuth": {"step1": {"title": "สวัสดี!", "subtitle": "ฉันคือ August 👋", "description": "คิดว่าฉันเป็นมุมพักผ่อนสบายๆ บนอุปกรณ์ของคุณ ที่คุณสามารถสำรวจข้อสงสัยด้านสุขภาพทั้งหมดได้", "subdescription": "อย่าลังเลที่จะถามอะไรก็ได้ที่คุณคิด ไม่ตัดสิน ไม่จำกัด!", "placeholder": "ฉันจะเรียกคุณว่าอะไรดี?"}, "step2": {"title": "สวัสดี {{userName}},", "subtitle": "นี่คือสิ่งที่ฉันทำได้:", "features": {"health": {"title": "ตอบคำถาม", "subtitle": "เกี่ยวกับสุขภาพของคุณ"}, "nutrition": {"title": "ติดตาม", "subtitle": "สารอาหาร"}, "reports": {"title": "วิเคราะห์", "subtitle": "รายงาน"}}}}, "pills": {"thoughtful": "ใส่ใจ", "careful": "ระมัดระวัง", "accurate": "แม่นยำ"}, "features": {"symptoms": {"title": "ตรวจสอบอาการของคุณ", "description": "ฉันคลื่นไส้มาหนึ่งสัปดาห์แล้ว เกิดอะไรขึ้นกับฉัน?"}, "prescriptions": {"title": "วิเคราะห์ใบสั่งยาของคุณ", "description": "อัปโหลดและทำความเข้าใจใบสั่งยาเหมือนแพทย์"}, "medicine": {"title": "รู้จักยาของคุณ", "description": "เมตฟอร์มินสำหรับ PCOS ของฉันมีปฏิกิริยากับยา ADHD ของฉันหรือไม่?"}, "plans": {"title": "รับแผนส่วนบุคคล", "description": "คุณสามารถให้แผนโภชนาการและการออกกำลังกายเพื่อลดระดับ HbA1c ของฉันได้ไหม?"}}, "buttons": {"getStarted": "เริ่มต้น", "next": "ถัดไป"}, "errors": {"nameRequired": "กรุณากรอกชื่อของคุณ"}}, "tabs": {"chat": "แชท", "discover": "ค้นหา", "nutrition": "โภชนาการ", "personalize": "ปรับแต่ง"}, "chat": {"nav": {"title": "สิงหาคม"}, "me": "ฉัน", "augustName": "สิงหาคม", "input": {"placeholder": "ถามสิงหาคม...", "disclaimer": "สิงหาคมอาจมีข้อผิดพลาด โปรดตรวจสอบกับแพทย์"}, "list": {"loadingMessages": "กำลังโหลดข้อความ...", "noMessages": "ยังไม่มีข้อความ เริ่มการสนทนาเลย!"}, "connection": {"offlineMessage": "ดูเหมือนว่าคุณกำลังออฟไลน์ เชื่อมต่อเพื่อส่งข้อความ", "connecting": "กำลังเชื่อมต่อ...", "tryAgain": "ลองอีกครั้ง"}, "prompts": {"uploadReport": "อัปโหลดรายงาน", "speakInHindi": "พูดภาษาฮินดี", "notFeelingWell": "ฉันไม่สบาย", "whatIsMyBMI": "ดัชนีมวลกายของฉันคือเท่าไหร่?", "nutritionAdvice": "คำแนะนำด้านโภชนาการ", "sleepBetter": "นอนหลับให้ดีขึ้น"}, "citations": {"referenceText": "สำหรับรายละเอียดเพิ่มเติมเกี่ยวกับการสนทนานี้ โปรดดูที่:"}, "actions": {"copiedToClipboard": "คัดลอกไปยังคลิปบอร์ดแล้ว", "copied": "คัดลอกแล้ว"}, "share": {"introText": "👋 สวัสดี ดูการสนทนาที่ฉันมีกับสิงหาคม:\n\n", "downloadText": "\n\n➡️ดาวน์โหลดสิงหาคมเพื่อแชทกับเพื่อน AI ด้านสุขภาพของคุณ:\n"}}, "discover": {"nav": {"title": "ค้นพบ"}, "categories": {"all": "ทั้งหมด", "heartHealth": "สุขภาพหัวใจ", "nutrition": "โภชนาการ", "mentalHealth": "สุขภาพจิต", "fitness": "ออกกำลังกาย", "wellness": "สุขภาพและความเป็นอยู่ที่ดี"}, "cards": {"empty": "ไม่มีการ์ดสำหรับหมวดหมู่นี้"}, "sections": {"features": "คุณสมบัติ"}, "features": {"healthLibrary": {"title": "คลังสุขภาพ", "description": "เข้าถึงข้อมูลทางการแพทย์ที่น่าเชื่อถือและทันสมัยได้ฟรี"}, "nutritionTracker": {"title": "ตัวติดตามโภชนาการ", "description": "เคยสงสัยไหมว่าคุณสามารถอัปโหลดรูปภาพอาหารของคุณและติดตามเป้าหมายโภชนาการทั้งหมดของคุณได้หรือไม่? สิงหาคมทำได้!"}, "multilingualSupport": {"title": "การสนับสนุนหลายภาษา", "description": "คุณสามารถสื่อสารกับสิงหาคมได้ทุกภาษาที่คุณถนัด! สิงหาคมพร้อมที่จะรับฟัง สนับสนุน และตอบกลับคุณทุกเมื่อที่คุณต้องการ", "samplePrompt": "พูดภาษาฮินดี"}, "labReportAnalysis": {"title": "การวิเคราะห์รายงานห้องปฏิบัติการ", "description": "เมื่อคุณพูดคุยกับสิงหาคมเกี่ยวกับรายงานห้องปฏิบัติการของคุณ คุณจะได้รับความแม่นยำอย่างมาก สิงหาคมได้ประมวลผลรายงานมากกว่า 4.7 ล้านรายงานโดยมีความแม่นยำในการสกัดชีวเครื่องหมาย 98.4%"}}}, "nutrition": {"nav": {"title": "โภชนาการ"}, "meals": {"title": "อาหารของคุณ", "subtitle": "แตะเพื่อดูสารอาหารในแต่ละมื้อ"}, "upload": {"loading": "กำลังอัปโหลดรูปภาพ..."}, "defaultFoodName": "รายการอาหาร", "today": "วันนี้", "unknownTime": "เวลาไม่ทราบ", "calories": "🔥 แคลอรี่", "proteins": "🥩 โปรตีน", "carbs": "🍞 คาร์โบไฮเดรต", "sugars": "🍬 น้ำตาล", "fat": "🥑 ไขมัน", "caloriesLabel": "แคลอรี่", "proteinLabel": "โปรตีน", "carbohydratesLabel": "คาร์โบไฮเดรต", "fatLabel": "ไขมัน", "sugarLabel": "น้ำตาล", "tips": "เคล็ดลับ:", "macroBreakdown": "การแบ่งสารอาหารมหภาค", "noMacroData": "ไม่มีข้อมูลสารอาหารมหภาคสำหรับรายการอาหารนี้", "disclaimer": "เพื่อการศึกษาเท่านั้น เรียนรู้เพิ่มเติม", "disclaimerLink": "ที่นี่", "unit": {"kcal": "kcal", "g": "กรัม"}, "form": {"gender": {"title": "เพศของคุณคืออะไร?", "subtitle": "ข้อมูลนี้จะใช้ในการปรับแต่งแผนของคุณ", "male": "ชาย", "female": "หญิง", "other": "อื่นๆ"}, "age": {"title": "อายุของคุณคืออะไร?", "subtitle": "ข้อมูลนี้จะใช้ในการคำนวณความต้องการประจำวันของคุณ"}, "measurements": {"title": "ส่วนสูงและน้ำหนัก", "subtitle": "กรุณากรอกส่วนสูงของคุณเป็นเซนติเมตรและน้ำหนักเป็นกิโลกรัม"}, "activity": {"title": "ระดับกิจกรรม", "subtitle": "คุณออกกำลังกายบ่อยแค่ไหน?", "none": "ไม่ออกกำลังกาย", "moderate": "ปานกลาง", "high": "สูง"}, "goal": {"title": "เป้าหมายน้ำหนัก", "subtitle": "คุณต้องการบรรลุอะไร?", "increase": "เพิ่ม", "maintain": "รักษา", "decrease": "ลด"}, "targetWeight": {"title": "น้ำหนักเป้าหมาย", "subtitle": "น้ำหนักเป้าหมายของคุณเป็นกิโลกรัมเท่าใด?"}, "setup": {"title": "การตั้งค่าแผนของคุณ", "subtitle": "โปรดรอสักครู่ในขณะที่เรากำลังเตรียมแผนโภชนาการของคุณ"}, "review": {"title": "ตรวจสอบแผนของคุณ", "subtitle": "ตรวจสอบและปรับแต่งแผนโภชนาการของคุณ"}, "height": {"label": "ส่วนสูง (ซม.)"}, "weight": {"label": "น้ำหนัก (กก.)"}}, "error": {"updateFailed": "ไม่สามารถอัปเดตข้อมูลโภชนาการได้ โปรดลองอีกครั้ง", "parsingError": "ข้อผิดพลาดในการแยกวิเคราะห์ข้อมูลอาหาร:", "fetchReportsFailed": "ไม่สามารถดึงข้อมูลรายงานได้ โปรดลองอีกครั้ง", "missingReportId": "รหัสรายงานหายไป"}}, "personalize": {"nav": {"title": "ปรับแต่ง"}, "button": {"saving": "กำลังบันทึก", "review": "ตรวจสอบ", "saveNext": "บันทึกและถัดไป"}}, "basicInfo": {"title": "มาทำความรู้จักคุณให้มากขึ้นกันเถอะ", "subtitle": "ข้อมูลนี้ช่วยให้เราสามารถปรับแต่งคำแนะนำด้านสุขภาพของคุณได้", "age": {"question": "คุณอายุเท่าไหร่?", "placeholder": "กรอกอายุของคุณ"}, "sex": {"question": "เพศของคุณคืออะไร?", "placeholder": "เลือกเพศของคุณ", "male": "ชาย", "female": "หญิง", "other": "อื่นๆ"}, "height": {"question": "ส่วนสูงของคุณเท่าไหร่? (ซม.)", "placeholder": "กรอกส่วนสูงของคุณ"}, "weight": {"question": "น้ำหนักของคุณเท่าไหร่? (กก.)", "placeholder": "กรอกน้ำหนักของคุณ"}}, "lifestyle": {"title": "นิสัยการใช้ชีวิตของคุณ", "subtitle": "การทำความเข้าใจนิสัยประจำวันของคุณจะช่วยให้เราให้คำแนะนำที่ดีขึ้นได้", "diet": {"question": "คุณทานอาหารประเภทใด?", "placeholder": "เลือกอาหารของคุณ", "vegetarian": "มังสวิรัติ", "nonVegetarian": "ไม่ใช่มังสวิรัติ", "vegan": "วีแกน", "pescatarian": "เปสคาทาเรียน", "keto": "คีโต", "paleo": "พาเลโอ"}, "exercise": {"question": "คุณออกกำลังกายเป็นประจำหรือไม่?"}, "drinking": {"question": "คุณดื่มแอลกอฮอล์หรือไม่?"}, "smoking": {"question": "คุณสูบบุหรี่หรือไม่?"}, "sleep": {"question": "คุณนอนหลับกี่ชั่วโมงต่อคืน?", "value": "{{sleep}} ชั่วโมง"}, "hydration": {"question": "คุณดื่มน้ำกี่แก้วต่อวัน?", "value": "{{hydration}} แก้ว ({{liters}} ลิตร)"}}, "allergies": {"title": "คุณมีอาการแพ้หรือไม่?", "subtitle": "การรู้จักอาการแพ้ของคุณจะช่วยให้เราให้คำแนะนำที่ปลอดภัยยิ่งขึ้น", "allergyIndex": "อาการแพ้ {{index}}", "name": {"question": "คุณแพ้อะไรบ้าง?", "placeholder": "ป้อนอาการแพ้ (เช่น ถั่วลิสง ฝุ่น)"}, "severity": {"question": "อาการแพ้รุนแรงแค่ไหน?", "placeholder": "เลือกความรุนแรง", "mild": "เล็กน้อย", "moderate": "ปานกลาง", "severe": "รุนแรง"}, "addButton": "เพิ่มอาการแพ้อื่น", "noAllergiesButton": "ฉันไม่มีอาการแพ้"}, "medications": {"title": "ยาและอาหารเสริม", "subtitle": "บอกเราเกี่ยวกับยาหรืออาหารเสริมที่คุณกำลังทานอยู่", "medicationIndex": "ยา {{index}}", "name": {"label": "ชื่อยา", "placeholder": "ป้อนชื่อยา"}, "startDate": {"question": "คุณเริ่มทานเมื่อไหร่?", "placeholder": "เลือกวันที่"}, "type": {"label": "ประเภทยา", "shortTerm": "ระยะสั้น", "longTerm": "ระยะยาว"}, "dose": {"label": "ขนาดยา", "placeholder": "ปริมาณ"}, "unit": {"label": "หน่วย"}, "frequency": {"label": "ความถี่", "placeholder": "ครั้ง", "perDay": "ต่อวัน", "perWeek": "ต่อสัปดาห์", "perMonth": "ต่อเดือน", "perYear": "ต่อปี"}, "units": {"mg": "มก.", "ml": "มล.", "iu": "iu", "puffs": "ครั้ง", "drops": "หยด", "tsp": "ช้อนชา", "tbsp": "ช้อนโต๊ะ", "cups": "ถ้วย"}, "addButton": "เพิ่มยาอื่น", "noMedicationsButton": "ฉันไม่ได้ทานยา", "calendar": {"title": "เลือกวันที่เริ่มต้น"}}, "conditions": {"title": "โรคประจำตัว", "subtitle": "บอกเราเกี่ยวกับโรคประจำตัวที่คุณมีหรือเคยมีในอดีต", "conditionIndex": "โรค {{index}}", "name": {"label": "ชื่อโรค", "placeholder": "ป้อนโรค (เช่น โรคหอบหืด ฯลฯ)"}, "since": {"question": "คุณเป็นโรคนี้มาตั้งแต่เมื่อไหร่?", "placeholder": "เลือกวันที่"}, "current": {"question": "ตอนนี้ยังเป็นอยู่หรือไม่?"}, "medicated": {"question": "คุณกำลังทานยาสำหรับโรคนี้หรือไม่?"}, "addButton": "เพิ่มโรคอื่น", "noConditionsButton": "ฉันไม่มีโรคประจำตัว", "calendar": {"title": "เลือกวันที่"}}, "reproductive": {"title": "สุขภาพระบบสืบพันธุ์", "subtitle": "ข้อมูลนี้จะช่วยให้เราสามารถให้คำแนะนำด้านสุขภาพที่ตรงกับความต้องการของคุณได้มากขึ้น", "menstruation": {"question": "คุณเคยมีประจำเดือนหรือไม่?", "detailsTitle": "รายละเอียดประจำเดือน", "regularity": {"question": "รอบประจำเดือนของคุณเป็นอย่างไร?", "regular": "ปกติ", "irregular": "ไม่ปกติ", "notSure": "ไม่แน่ใจ"}, "cycleLength": {"label": "ระยะเวลาเฉลี่ยของรอบประจำเดือน (วัน)", "placeholder": "ป้อนระยะเวลาของรอบประจำเดือน"}, "flowDays": {"label": "จำนวนวันที่มีประจำเดือน: {{flowDays}}", "min": "1 วัน", "max": "15 วัน"}, "padsPerDay": {"label": "ผ้าอนามัย/แทมปอนต่อวัน: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "มีอาการใดๆ ระหว่างมีประจำเดือนหรือไม่?", "placeholder": "ป้อนอาการ (เช่น ปวดท้อง ประจำเดือน ปวดหัว)"}}, "childbirth": {"question": "คุณเคยคลอดบุตรหรือไม่?", "detailsTitle": "รายละเอียดการคลอดบุตร", "children": {"label": "จำนวนบุตร"}, "pregnancies": {"label": "จำนวนครั้งที่ตั้งครรภ์"}, "complications": {"question": "มีภาวะแทรกซ้อนใดๆ ระหว่างตั้งครรภ์หรือคลอดบุตรหรือไม่?", "placeholder": "ป้อนภาวะแทรกซ้อน (ถ้ามี)"}}}, "review": {"title": "ตรวจสอบข้อมูลของคุณ", "subtitle": "โปรดตรวจสอบข้อมูลที่คุณได้กรอกก่อนส่ง", "sections": {"basicInfo": "ข้อมูลพื้นฐาน", "lifestyle": "ไลฟ์สไตล์", "allergies": "อาการแพ้", "medications": "ยาและอาหารเสริม", "conditions": "โรคประจำตัว", "reproductive": "สุขภาพระบบสืบพันธุ์", "menstruationDetails": "รายละเอียดประจำเดือน", "childbirthDetails": "รายละเอียดการคลอดบุตร"}, "fields": {"age": "อายุ:", "sex": "เพศ:", "height": "ส่วนสูง:", "weight": "น้ำหนัก:", "diet": "อาหาร:", "exercise": "การออกกำลังกาย:", "drinking": "การดื่มแอลกอฮอล์:", "smoking": "การสูบบุหรี่:", "sleep": "การนอนหลับ:", "hydration": "การดื่มน้ำ:", "allergyIndex": "อาการแพ้ {{index}}:", "dose": "ขนาดยา:", "frequency": "ความถี่:", "type": "ประเภท:", "since": "ตั้งแต่:", "currentlyActive": "กำลังใช้งานอยู่:", "takingMedication": "กำลังรับประทานยา:", "hasMenstruated": "เคยมีประจำเดือน:", "regularity": "ความสม่ำเสมอ:", "cycleLength": "ระยะเวลาของรอบประจำเดือน:", "flowDays": "จำนวนวันที่มีประจำเดือน:", "padsPerDay": "ผ้าอนามัย/แทมปอนต่อวัน:", "hasChildbirth": "เคยคลอดบุตร:", "children": "จำนวนบุตร:", "pregnancies": "จำนวนครั้งที่ตั้งครรภ์:"}, "notProvided": "ไม่ได้ระบุ", "units": {"cm": "{{height}} ซม.", "kg": "{{weight}} กก."}, "values": {"sleepHours": "{{sleep}} ชั่วโมงต่อวัน", "hydration": "{{hydration}} แก้ว ({{liters}} ลิตร) ต่อวัน", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} วัน"}, "noData": {"allergies": "ไม่มีข้อมูลอาการแพ้", "medications": "ไม่มีข้อมูลยา", "conditions": "ไม่มีข้อมูลโรคประจำตัว"}, "submitButton": "ส่งข้อมูล"}, "success": {"title": "อัปเดตข้อมูลเรียบร้อยแล้ว!", "message": "ขอบคุณสำหรับการให้ข้อมูลสุขภาพของคุณ เราจะใช้ข้อมูลนี้เพื่อปรับแต่งประสบการณ์ของคุณและให้คำแนะนำที่ดีขึ้น", "benefits": {"insights": "ข้อมูลเชิงลึกด้านสุขภาพที่ตรงกับความต้องการของคุณ", "reminders": "การแจ้งเตือนการรับประทานยาที่ดีขึ้น", "recommendations": "คำแนะนำด้านสุขภาพที่เหมาะสมกับคุณ"}, "continueButton": "ไปยังแดชบอร์ด"}, "permissions": {"microphonePermissionDenied": "ไม่อนุญาตให้เข้าถึงไมโครโฟน", "microphoneAccessDescription": "August ต้องการเข้าถึงไมโครโฟนของคุณเพื่อบันทึกเสียงและส่งข้อความเสียง", "permissionDenied": "ไม่อนุญาต", "cameraPermissionRequired": "เราต้องการสิทธิ์การเข้าถึงกล้องเพื่อให้ใช้งานได้!", "mediaLibraryPermissionRequired": "เราต้องการสิทธิ์การเข้าถึงคลังสื่อเพื่อให้ใช้งานได้!"}, "voiceRecording": {"recordingTooLong": "บันทึกเสียงนานเกินไป", "recordingTooLongMessage": "การบันทึกเสียงต้องน้อยกว่า 5 นาที โปรดบันทึกข้อความที่สั้นกว่านี้"}, "errors": {"uploadFailed": "อัปโหลดล้มเหลว", "voiceUploadFailed": "ไม่สามารถอัปโหลดการบันทึกเสียงได้", "voiceRecordingFailed": "ส่งการบันทึกเสียงล้มเหลว", "failedToStopRecording": "หยุดการบันทึกไม่สำเร็จ", "photoUploadFailed": "ไม่สามารถอัปโหลดรูปภาพได้", "failedToTakePhoto": "ถ่ายรูปไม่สำเร็จ", "imageUploadFailed": "ไม่สามารถอัปโหลดรูปภาพได้: {{fileName}}", "failedToPickImage": "เลือกภาพไม่สำเร็จ", "documentUploadFailed": "ไม่สามารถอัปโหลดเอกสารได้: {{fileName}}", "failedToPickDocument": "เลือกเอกสารไม่สำเร็จ"}, "audioPlayer": {"downloadingAudio": "กำลังดาวน์โหลดเสียง...", "loadingAudio": "กำลังโหลดเสียง..."}, "mediaProcessing": {"processingFile": "กำลังประมวลผลไฟล์ของคุณ", "uploadingSecuring": "กำลังอัปโหลดและรักษาความปลอดภัยไฟล์...", "analyzingContent": "กำลังวิเคราะห์เนื้อหาเอกสาร...", "extractingInfo": "กำลังดึงข้อมูลสำคัญ...", "processingInsights": "กำลังประมวลผลข้อมูลเชิงลึก...", "preparingResponse": "กำลังเตรียมคำตอบโดยละเอียด...", "finalizingResponse": "กำลังสรุปคำตอบ..."}, "attachments": {"voiceMessage": "ข้อความเสียง", "image": "[รูปภาพ]", "pdf": "[ไฟล์ PDF]", "voice": "[บันทึกเสียง]"}, "pdf": {"loadingPdf": "กำลังโหลดไฟล์ PDF..."}, "dateTime": {"yesterday": "เมื่อวานนี้, "}, "navbar": {"defaultTitle": "august", "selectedCount": "เลือก"}, "mediaUpload": {"photoLibrary": "คลังภาพ", "takePhoto": "ถ่ายรูป", "chooseFile": "เลือกไฟล์"}, "comingSoon": {"title": "เร็วๆ นี้!", "description": " กำลังอยู่ระหว่างการพัฒนา ติดตามข้อมูลอัปเดต!", "buttonText": "เข้าใจแล้ว!"}, "clipboard": {"success": "คัดลอกลิงก์ไปยังคลิปบอร์ดแล้ว"}, "mediaPhotos": {"emptyState": "ยังไม่มีรายการ"}, "foodDetail": {"defaultFoodName": "รายการอาหาร", "nutrition": {"totalCalories": "แคลอรี่ทั้งหมด", "proteins": "โปรตีน", "carbs": "คาร์โบไฮเดรต", "fat": "ไขมัน", "sugars": "น้ำตาล", "fibers": "ไฟเบอร์"}}, "reports": {"defaultTitle": "รายการสื่อ", "defaultFoodName": "รายการอาหาร", "defaultName": "เอกสาร", "openButton": "เปิดในตัวแสดงผลภายนอก", "biomarker": {"headerBiomarker": "ชีวเครื่องหมาย", "headerValue": "ค่า", "headerRefRange": "ช่วงอ้างอิง", "headerStatus": "สถานะ"}, "noData": "ไม่มีข้อมูลชีวเครื่องหมาย"}, "setup": {"title": "เรากำลังตั้งค่าทุกอย่างให้คุณ", "inProgress": "กำลังดำเนินการ...", "progressMessages": {"0": "กำลังคำนวณแคลอรี่รายวัน", "1": "กำลังปรับแต่งการแบ่งแมโคร", "2": "กำลังสร้างแผนอาหาร", "3": "กำลังคำนวณคะแนนสุขภาพ", "4": "กำลังสรุปการตั้งค่า"}, "checklistItems": {"0": "กำลังวิเคราะห์ข้อมูลสุขภาพของคุณ", "1": "กำลังคำนวณแผนโภชนาการที่เหมาะสม", "2": "กำลังปรับแต่งคำแนะนำของคุณ", "3": "กำลังสร้างคำแนะนำอาหารของคุณ", "4": "กำลังสรุปการตั้งค่าของคุณ"}}, "foodEntry": {"emptyState": "ยังไม่มีรายการอาหาร  ถ่ายรูปอาหารของคุณเพื่อเพิ่มรายการ!"}, "nutritionReview": {"congratulations": "ขอแสดงความยินดี!", "subtitle": "แผนโภชนาการของคุณพร้อมแล้ว", "submitButton": "เริ่มกันเลย!", "dailyTargetsTitle": "เป้าหมายโภชนาการรายวันของคุณ", "macroLabels": {"calories": "แคลอรี่", "carbs": "คาร์โบไฮเดรต", "protein": "โปรตีน", "fats": "ไขมัน"}}, "editModal": {"titlePrefix": "แก้ไข ", "cancelButton": "ยกเลิก", "saveButton": "ถัดไป"}, "processing": {"stages": {"scanning": "กำลังสแกนอาหาร...", "identifying": "กำลังระบุส่วนผสม...", "extracting": "กำลังแยกสารอาหาร...", "finalizing": "กำลังสรุปผล..."}, "error": {"defaultMessage": "ไม่พบอาหาร", "subtitle": "ลองมุมกล้องอื่นดู"}, "retakeButton": "แตะเพื่อถ่ายภาพใหม่", "notification": "เราจะแจ้งให้คุณทราบเมื่อเสร็จสิ้น!"}, "chart": {"title": "การติดตามโภชนาการตามเวลา", "selectNutrient": "เลือกสารอาหาร:", "emptyState": "ยังไม่มีข้อมูลโภชนาการ", "dropdown": {"calories": "แคลอรี่", "protein": "โปรตีน", "carbs": "คาร์โบไฮเดรต", "fat": "ไขมัน", "sugars": "น้ำตาล"}}, "foodModal": {"defaultName": "รายการอาหาร", "defaultDate": "วันนี้", "defaultTime": "เวลาไม่ทราบ", "saveChanges": "บันทึกการเปลี่ยนแปลง", "error": {"title": "ข้อผิดพลาด", "message": "ไม่สามารถอัปเดตข้อมูลโภชนาการได้ โปรดลองอีกครั้ง"}, "nutrition": {"calories": "🔥 แคลอรี่", "proteins": "🥩 โปรตีน", "carbs": "🍞 คาร์โบไฮเดรต", "sugars": "🍬 น้ำตาล", "fat": "🥑 ไขมัน"}, "macroBreakdown": {"title": "การแบ่งกลุ่มสารอาหารมหภาค", "noData": "ไม่มีข้อมูลสารอาหารมหภาคสำหรับรายการอาหารนี้"}, "macroLabels": {"calories": "แคลอรี่", "protein": "โปรตีน", "carbs": "คาร์โบไฮเดรต", "fat": "ไขมัน", "sugar": "น้ำตาล"}}, "infoModal": {"title": "ข้อมูลโดยละเอียด", "edit": "แก้ไข", "save": "บันทึก", "saving": "กำลังบันทึก...", "enterValue": "ป้อนค่า", "notSet": "ยังไม่ได้ตั้งค่า", "age": "อายุ", "heightCm": "ส่วนสูง (ซม.)", "weightKg": "น้ำหนัก (กก.)", "targetWeight": "น้ำหนักเป้าหมาย", "nutritionTargets": "เป้าหมายโภชนาการ", "protein": "โปรตีน", "carbs": "คาร์โบไฮเดรต", "fats": "ไขมัน", "gm": "กรัม", "editNote": "ป้อนค่าหรือเว้นว่างไว้สำหรับการคำนวณอัตโนมัติ", "autoCalculateNote": "สารอาหารมหภาคจะคำนวณโดยอัตโนมัติตามข้อมูลของคุณ", "validation": {"ageMin": "อายุต้องอย่างน้อย 18 ปี", "ageMax": "อายุต้องต่ำกว่า 125 ปี", "heightMin": "ส่วนสูงต้องอย่างน้อย 50 ซม.", "heightMax": "ส่วนสูงต้องต่ำกว่า 250 ซม.", "weightMin": "น้ำหนักต้องอย่างน้อย 30 กก.", "weightMax": "น้ำหนักต้องต่ำกว่า 500 กก.", "targetWeightMin": "น้ำหนักเป้าหมายต้องอย่างน้อย 30 กก.", "targetWeightMax": "น้ำหนักเป้าหมายต้องต่ำกว่า 500 กก.", "proteinMin": "โปรตีนต้อง 0 หรือมากกว่า", "carbsMin": "คาร์โบไฮเดรตต้อง 0 หรือมากกว่า", "fatsMin": "ไขมันต้อง 0 หรือมากกว่า"}}, "tracker": {"calories": "แคลอรี่", "protein": "โปรตีน", "carbs": "คาร์โบไฮเดรต", "fat": "ไขมัน", "excess": "เกิน", "remaining": "เหลือ"}, "specialistConstants": {"nutritionist": {"name": "นักโภชนาการ", "description": "คำแนะนำจากผู้เชี่ยวชาญเกี่ยวกับอาหาร โภชนาการ และนิสัยการกินที่ดีต่อสุขภาพ", "featureName": "ผู้เชี่ยวชาญด้านโภชนาการ"}, "cardiologist": {"name": "แพทย์ผู้เชี่ยวชาญด้านหัวใจ", "description": "ผู้เชี่ยวชาญด้านสุขภาพหัวใจและโรคหลอดเลือดหัวใจ", "featureName": "ผู้เชี่ยวชาญด้านหัวใจ"}, "neurologist": {"name": "แพทย์ผู้เชี่ยวชาญด้านระบบประสาท", "description": "มุ่งเน้นไปที่สมอง ไขสันหลัง และความผิดปกติของระบบประสาท", "featureName": "ผู้เชี่ยวชาญด้านระบบประสาท"}, "oncologist": {"name": "แพทย์ผู้เชี่ยวชาญด้านมะเร็ง", "description": "ผู้เชี่ยวชาญในการวินิจฉัยและรักษามะเร็ง", "featureName": "ผู้เชี่ยวชาญด้านมะเร็ง"}, "endocrinologist": {"name": "แพทย์ผู้เชี่ยวชาญด้านต่อมไร้ท่อ", "description": "ผู้เชี่ยวชาญด้านภาวะฮอร์โมนและความผิดปกติของการเผาผลาญ", "featureName": "ผู้เชี่ยวชาญด้านต่อมไร้ท่อ"}}, "discoverCards": {"categories": {"nutrition": "โภชนาการ", "heartHealth": "สุขภาพหัวใจ", "mentalHealth": "สุขภาพจิต", "fitness": "การออกกำลังกาย", "wellness": "สุขภาพและความเป็นอยู่ที่ดี"}, "titles": {"vitaminB12Recovery": "ใช้เวลานานแค่ไหนในการฟื้นตัวจากภาวะขาดวิตามินบี 12", "vitaminDeficiencyGanglion": "ภาวะขาดวิตามินชนิดใดที่ทำให้เกิดซีสต์เนื้องอก", "vitaminDeficiencyHairFall": "ภาวะขาดวิตามินชนิดใดที่ทำให้ผมร่วง", "vitaminWaters": "น้ำวิตามินดีต่อสุขภาพหรือไม่", "cholesterolHeadaches": "คอเลสเตอรอลสูงทำให้ปวดหัวได้หรือไม่", "cholesterolEyes": "อาการของคอเลสเตอรอลสูงที่สามารถมองเห็นได้ในดวงตาคืออะไร", "diabetesHeadaches": "โรคเบาหวานทำให้ปวดหัวได้หรือไม่", "chestPainDrinking": "ทำไมหน้าอกถึงเจ็บหลังจากดื่ม", "stressDizziness": "ความเครียดทำให้เวียนหัวได้หรือไม่", "bulimiaFace": "ใบหน้าของโรคบูลีเมียคืออะไร", "kneeTwitch": "ทำไมเข่าของฉันถึงกระตุก", "noseTwitching": "ทำไมจมูกถึงกระตุก", "piriformisVsSciatica": "อะไรคือความแตกต่างระหว่างกลุ่มอาการ Piriformis กับ โรคปลอกประสาทอักเสบ", "shoulderBladePinched": "วิธีการปลดปล่อยเส้นประสาทที่ถูกบีบในกระดูกสะบัก", "shoulderPinched": "วิธีการปลดปล่อยเส้นประสาทที่ถูกบีบในไหล่", "meniscusTorn": "วิธีการรักษาเยื่อหุ้มกระดูกอ่อนที่ฉีกขาดตามธรรมชาติ", "hydrateQuickly": "วิธีการเพิ่มความชุ่มชื้นอย่างรวดเร็ว", "periodConstipation": "เป็นเรื่องปกติที่จะท้องผูกในช่วงมีประจำเดือนหรือไม่", "acneScars": "วิธีการกำจัดรอยสิวอย่างเป็นธรรมชาติภายในหนึ่งสัปดาห์", "perimenopausePregnancy": "คุณสามารถตั้งครรภ์ได้ในช่วงวัยหมดประจำเดือนหรือไม่"}, "descriptions": {"vitaminB12Recovery": "ค้นพบไทม์ไลน์การฟื้นตัวจากภาวะขาดวิตามินบี 12 และวิธีการรักษาที่มีประสิทธิภาพเพื่อเพิ่มระดับพลังงานของคุณ", "vitaminDeficiencyGanglion": "สำรวจความเชื่อมโยงระหว่างภาวะขาดวิตามินและการพัฒนาของซีสต์แกงกลิออนในร่างกาย", "vitaminDeficiencyHairFall": "เรียนรู้เกี่ยวกับวิธีที่การขาดวิตามินที่จำเป็นสามารถนำไปสู่การผมร่วงและสิ่งที่คุณสามารถทำได้เพื่อป้องกัน", "vitaminWaters": "ค้นพบประโยชน์และข้อเสียที่อาจเกิดขึ้นของน้ำวิตามินในฐานะส่วนหนึ่งของโภชนาการประจำวันของคุณ", "cholesterolHeadaches": "ตรวจสอบความเชื่อมโยงที่เป็นไปได้ระหว่างระดับคอเลสเตอรอลสูงและการเริ่มมีอาการปวดหัว", "cholesterolEyes": "เรียนรู้ว่าคอเลสเตอรอลสูงสามารถแสดงออกในดวงตาของคุณได้อย่างไรและอาการใดที่ควรสังเกต", "diabetesHeadaches": "ตรวจสอบความสัมพันธ์ระหว่างโรคเบาหวานและการเกิดอาการปวดหัวในชีวิตประจำวัน", "chestPainDrinking": "สำรวจสาเหตุที่อยู่เบื้องหลังอาการเจ็บหน้าอกหลังจากการบริโภคเครื่องดื่มบางชนิด", "stressDizziness": "เจาะลึกถึงวิธีที่ความเครียดสามารถส่งผลกระทบต่อความสมดุลและความเป็นอยู่โดยรวมของคุณนำไปสู่ความวิงเวียนศีรษะ", "bulimiaFace": "ทำความเข้าใจสัญญาณทางกายภาพของโรคบูลีเมียรวมถึงผลกระทบต่อรูปลักษณ์ใบหน้า", "kneeTwitch": "ตรวจสอบสาเหตุที่อาจเกิดขึ้นเบื้องหลังอาการกระตุกที่เข่าโดยไม่สมัครใจและความสัมพันธ์กับความเครียดหรือความเหนื่อยล้า", "noseTwitching": "เรียนรู้เกี่ยวกับสาเหตุที่เป็นไปได้ของอาการกระตุกที่จมูกและความเชื่อมโยงกับความวิตกกังวลหรือปัจจัยอื่นๆ", "piriformisVsSciatica": "เปรียบเทียบอาการของโรคปิริฟอร์มิสและโรคไซแอติกาเพื่อให้เข้าใจสภาพของคุณได้ดียิ่งขึ้น", "shoulderBladePinched": "ค้นพบเทคนิคที่มีประสิทธิภาพในการบรรเทาเส้นประสาทที่ถูกบีบในกระดูกสะบักของคุณและฟื้นฟูการเคลื่อนไหว", "shoulderPinched": "เรียนรู้แบบฝึกหัดและการยืดกล้ามเนื้ออย่างง่ายเพื่อบรรเทาการบีบเส้นประสาทในบริเวณไหล่", "meniscusTorn": "สำรวจวิธีการทางธรรมชาติและแบบฝึกหัดเพื่อสนับสนุนการรักษาเมนิสกัสที่ฉีกขาด", "hydrateQuickly": "ค้นหาวิธีที่รวดเร็วและมีประสิทธิภาพในการเติมน้ำและรักษาความชุ่มชื้นในร่างกายให้เหมาะสม", "periodConstipation": "ทำความเข้าใจสาเหตุของอาการท้องผูกในช่วงมีประจำเดือนและเรียนรู้วิธีการรักษาทางธรรมชาติ", "acneScars": "ค้นพบวิธีการรักษาทางธรรมชาติและเคล็ดลับการดูแลผิวเพื่อลดรอยแผลเป็นจากสิวได้อย่างรวดเร็ว", "perimenopausePregnancy": "เรียนรู้เกี่ยวกับวัยหมดประจำเดือนช่วงเปลี่ยนผ่าน การพิจารณาเรื่องความอุดมสมบูรณ์ และสิ่งที่ควรคาดหวังในช่วงชีวิตนี้"}}}