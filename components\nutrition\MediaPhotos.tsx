import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  Image, 
  TouchableOpacity, 
  ActivityIndicator,
  Dimensions,
  Platform,
  LayoutChangeEvent
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from 'react-i18next';

// Responsive sizing helper functions
const { width: SCREEN_WIDTH } = Dimensions.get('window');

type MediaPhotosProps = {
  onFoodPress: (food: any) => void;
  reportsData?: any[];
  tempEntry?: any;
  isLoadingReports?: any;
  reportsError?: any;
};

const MediaPhotos: React.FC<MediaPhotosProps> = ({
  onFoodPress,
  reportsData = [],
  tempEntry,
}) => {
  const { t } = useTranslation();
  // Track which images are loading
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>(
    {}
  );
  const [containerWidth, setContainerWidth] = useState(SCREEN_WIDTH);

const [imageSource, setImageSource] = useState('');
  // Constants for grid layout
  const ITEMS_PER_ROW = 3;
  // Responsive grid spacing based on screen width
  const GRID_SPACING = Math.max(8, Math.round(SCREEN_WIDTH * 0.02));

  // Handle container layout to get actual width and padding
  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  };

  // Calculate item dimensions for exactly 3 items per row
  const calculateItemWidth = () => {
    // Account for horizontal padding (16px on each side)
    const horizontalPadding = 8;
    // Available width after accounting for padding
    const availableWidth = containerWidth - horizontalPadding;
    // Total space used by gaps between items
    const totalGapSpace = GRID_SPACING * (ITEMS_PER_ROW - 1);
    // Divide remaining space by number of items
    return Math.floor((availableWidth - totalGapSpace) / ITEMS_PER_ROW);
  };

  const itemWidth = calculateItemWidth();

//   // Filter reports to only include food items
//   const reportsData = reportsData.filter(
//     (report) => report.type === "FOOD_ITEM"
//   );

  if (reportsData.length === 0 && !tempEntry) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {t('mediaPhotos.emptyState')}
        </Text>
      </View>
    );
  }

  // Group reports by date
  const groupedReports = reportsData.reduce((acc, report) => {
    if (!report.timestamp) return acc;

    const date = new Date(report.timestamp).toISOString().split("T")[0];
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(report);
    return acc;
  }, {});

  // Add temp entry to today's date if it exists
  if (tempEntry) {
    const today = new Date().toISOString().split("T")[0];
    if (!groupedReports[today]) {
      groupedReports[today] = [];
    }
    // Add temp entry at the beginning of today's entries
    groupedReports[today].unshift(tempEntry);
  }

  // Convert to array and sort by date (newest first)
  const groupedReportsArray = Object.keys(groupedReports)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .map((date) => ({
      date,
      reports: groupedReports[date],
    }));

  // Function to handle image loading state
  const handleImageLoadStart = (reportId: string) => {
    setLoadingImages((prev) => ({ ...prev, [reportId]: true }));
  };

  const handleImageLoadEnd = (reportId: string) => {
    setLoadingImages((prev) => ({ ...prev, [reportId]: false }));
  };

  // Function to get the appropriate image URL (thumbnail or full)
  const getImageUrl = (report: any) => {
    // For temporary entries, use the local URI
    if (report.isTemp) {
      return report.url;
    }
    // Use thumbnail_url if available, otherwise fall back to the full url
    return report.thumbnail_url && report.thumbnail_status !== "failed"
      ? report.thumbnail_url
      : report.url;
  };

  type DateGroup = {
    date: string;
    reports: any[];
  };

  const renderDateGroup = (item: DateGroup) => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });

    return (
      <View style={styles.dateGroup} key={item.date}>
        <View style={styles.dateHeader}>
          <Text style={styles.dateText}>{formattedDate}</Text>
        </View>
        <View style={styles.entriesGrid}>
          {item.reports.map((report: any, index: number) => {
            const reportId = report.report_id || `report-${index}-${item.date}`;
            const isLoading = loadingImages[reportId] || false;
           const imageUrl = report.thumbnail_url && report.thumbnail_status == "success"
               ? report.thumbnail_url
               : report.url;

            return (
              <TouchableOpacity
                key={reportId}
                style={[
                  styles.entryContainer,
                  {
                    width: itemWidth,
                    height: itemWidth,
                    marginBottom: GRID_SPACING,
                    // Apply margin to all items except the last one in a row
                    marginRight:
                      (index + 1) % ITEMS_PER_ROW !== 0 ? GRID_SPACING : 0,
                    // Special handling for single item case
                    ...(item.reports.length === 1
                      ? { alignSelf: "flex-start" }
                      : {}),
                  },
                ]}
                onPress={() => !report.isTemp && onFoodPress(report)}
                disabled={report.isTemp}
              >
                {imageUrl && (
                  <>
                    <Image
                      source={{ uri: imageUrl }}
                      style={styles.foodImage}
                      defaultSource={require("@/assets/images/favicon.png")}
                      onLoadStart={() => handleImageLoadStart(reportId)}
                      onLoad={() => handleImageLoadEnd(reportId)}
                      onLoadEnd={() => handleImageLoadEnd(reportId)}
                    //   onError={() => setImageSource(report.url)}
                      resizeMode="cover"
                    />
                    {isLoading && (
                      <View style={styles.imageLoadingOverlay}>
                        <ActivityIndicator
                          size="small"
                          color={colors.primary}
                        />
                      </View>
                    )}
                  </>
                )}

                {/* Show loading indicator for temporary entries */}
                {report.isTemp && (
                  <View style={styles.loadingOverlay}>
                    <ActivityIndicator size="large" color={colors.primary} />
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container} onLayout={handleLayout}>
      {groupedReportsArray.map((item) => renderDateGroup(item))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 16,
    width: '100%', // Ensure container takes full width
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    textAlign: 'center',
  },
  dateGroup: {
    marginBottom: 24,
    width: '100%', 
  },
  dateHeader: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingBottom: 8,
    marginBottom: 20,
    width: '95%',
    paddingHorizontal: 16,
    alignSelf: 'center', 
  },
  dateText: {
    fontSize: 14,
    color: colors.gray[500],
    textAlign: 'center',
    marginBottom: 10
  },
  entriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start', // Align items to the start
    alignItems: 'flex-start', // Align items to the top
    width: '100%', // Ensure the grid takes full width
    alignContent: 'flex-start', // Ensure content is aligned to the start
    paddingHorizontal: 4, // Add padding to the grid
  },
  entryContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    position: 'relative',
    minWidth: 100, // Ensure a minimum width for single items
    minHeight: 100, // Ensure a minimum height for single items
    alignSelf: 'stretch', // Ensure the container stretches to fill its calculated width
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MediaPhotos;
