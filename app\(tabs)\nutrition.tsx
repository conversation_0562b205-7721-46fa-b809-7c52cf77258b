import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Platform, ScrollView, TextInput, ActivityIndicator, Keyboard, KeyboardAvoidingView, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import TopNavbar from '@/components/navigation/TopNavbar';
import NutritionTracker from '@/components/nutrition/NutritionTracker';
import FoodEntryList from '@/components/nutrition/FoodEntryList';
import NutritionChart from '@/components/nutrition/NutritionChart';
import { useNutritionStore } from '@/store/nutritionStore';
import { useUserDataStore } from '@/store/userDataStore';
import { useChatStore } from '@/store/chatStore';
import { Plus, ArrowLeft, ArrowRight } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import MediaUploadDrawer from '@/components/common/drawers/MediaUploadDrawer';
import { uploadMedia } from '@/services/mediaService';
import { sendMessage } from '@/services/chatService';
import { v4 as uuidv4 } from 'uuid';
import { usePhoneStore } from '@/store/phoneStore';
import { useUser } from '@/store/auth-store';
import { useNavigation, useRouter, usePathname, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Stack } from 'expo-router';
// import NutritionFoodModal from '@/components/nutrition/NutritionFoodModal';
import FoodProcessingCard from '@/components/nutrition/FoodProcessingCard';
import NutritionReviewStep from '@/components/nutrition/NutritionReviewStep';
import { getReportsData, updateNutritionData } from '@/services/nutritionService';
import logger from '@/utils/logger/logger';
import { z } from 'zod';
import { getWebPubSubClient } from '@/services/webPubSubService';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';
import { trackClarityEvent } from '@/utils/clarity/clarity-utils';
import WheelPicker from '@quidone/react-native-wheel-picker';
import SetupLoadingScreen from '@/components/nutrition/SetupLoadingScreen';
import { useScrollStore } from '@/store/scrollStore';
import { Theme } from "@/src/theme/types";
import { useTheme } from "@/src/theme/ThemeContext";
import NetInfo from '@react-native-community/netinfo';
import { useTranslation } from "react-i18next";
import { useScreenTracking } from '../currentScreen';
import { AppEventsLogger } from 'react-native-fbsdk-next';

const profileSchema = z.object({
  sex: z.enum(['male', 'female', 'other']),
  age: z.number().min(18, 'Please enter an age of 18 or above').max(125, 'This age seems incorrect'),
  height: z.number().min(50, 'Please enter a height above 50cm').max(250, 'Please enter a height below 250cm'),
  weight: z.number().min(30, 'Please enter a weight above 30kg').max(500, 'Please enter a weight below 500kg'),
  exerciseLevel: z.enum(['no exercise', 'moderate', 'high']),
  goal: z.enum(['increase', 'decrease', 'maintain']),
  desiredWeight: z.number().min(30, 'Please enter a weight above 30kg').max(500, 'Please enter a weight below 500kg')
});
const UPLOAD_TIMEOUT = 8000;

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    loadingOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: `${theme.colors.gray[300]}CC`, // CC in hex is equivalent to 0.8 opacity
      alignItems: "center",
      justifyContent: "center",
      zIndex: 1000,
    },
    loadingText: {
      fontSize: moderateScale(theme.fontSize.md), // 16
      marginTop: moderateVerticalScale(8),
      color: theme.colors.gray[600],
    },
    formScrollView: {
      flex: 1,
    },
    formScrollContent: {
      flexGrow: 1,
      paddingBottom: moderateVerticalScale(24),
    },
    buttonContainerInScroll: {
      marginTop: moderateVerticalScale(40),
      marginHorizontal: moderateScale(24),
      marginBottom: moderateVerticalScale(24),
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50], // was colors.white
    },
    content: {
      flex: 1,
      padding: moderateScale(16),
    },
    scrollContent: {
      paddingBottom: moderateVerticalScale(100),
    },
    chartContainer: {
      marginTop: moderateVerticalScale(24),
      marginBottom: moderateVerticalScale(24),
    },
    mealsContainer: {
      marginVertical: moderateVerticalScale(16),
      width: "100%",
      justifyContent: "center",
      alignItems: "center",
    },
    formNavigationContainer: {
      marginTop: moderateVerticalScale(80),
      paddingLeft: moderateScale(30),
      paddingRight: moderateScale(30),
    },
    sectionTitle: {
      fontSize: moderateScale(theme.fontSize.xl), // 20
      fontWeight: "600",
      marginBottom: moderateVerticalScale(8),
      marginLeft: moderateScale(4),
      marginTop: moderateVerticalScale(8),
      color: theme.colors.text.primary, // was colors.black
    },
    sectionSubtitle: {
      fontSize: moderateScale(theme.fontSize.sm), // 14
      color: theme.colors.gray[600],
      marginBottom: moderateVerticalScale(26),
      marginLeft: moderateScale(4),
    },
    fabContainer: {
      position: "absolute",
      bottom: moderateVerticalScale(24),
      right: moderateScale(24),
    },
    fab: {
      width: moderateScale(56),
      height: moderateScale(56),
      borderRadius: moderateScale(28),
      backgroundColor: theme.colors.primary.main, // was colors.primary
      alignItems: "center",
      justifyContent: "center",
      shadowColor: theme.colors.text.primary, // was colors.black
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.3,
      shadowRadius: moderateScale(3),
      elevation: 5,
    },
    disabledFab: {
      backgroundColor: theme.colors.gray[300], // Use a grayed out color when disabled
      shadowOpacity: 0.1, // Reduce shadow opacity when disabled
    },
    stepContainer: {
      flex: 1,
      justifyContent: "flex-start",
      alignItems: "center",
      padding: moderateScale(24),
      paddingTop: moderateVerticalScale(24),
      paddingBottom: moderateVerticalScale(10),
    },
    stepContent: {
      width: "100%",
      maxWidth: moderateScale(400),
    },
    stepTitleContainer: {
      marginBottom: moderateVerticalScale(64),
      alignItems: "center",
      textAlign: "center",
      width: "100%",
      maxWidth: moderateScale(400),
    },
    navigationContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: moderateScale(24),
      paddingBottom: moderateVerticalScale(8),
      position: "relative",
    },
    backButton: {
      position: "absolute",
      left: moderateScale(12),
      zIndex: 10,
      paddingBottom: moderateVerticalScale(8),
    },
    progressBarWrapper: {
      width: "100%",
      marginHorizontal: "auto",
      paddingLeft: moderateScale(16),
      alignSelf: "center",
    },
    progressBackground: {
      height: moderateVerticalScale(6),
      backgroundColor: theme.colors.gray[200],
      borderRadius: moderateScale(theme.radii.xs), // was 3
      overflow: "hidden",
    },
    progressFill: {
      height: "100%",
      backgroundColor: theme.colors.primary.main,
      borderRadius: moderateScale(theme.radii.xs), // was 3
    },
    wheelPickerContainer: {
      height: moderateVerticalScale(160),
      justifyContent: "center",
      backgroundColor: theme.colors.secondary[50], // was colors.white
    },
    heightWeightWheelPickerContainer: {
      height: moderateVerticalScale(160),
      width: "100%",
      justifyContent: "center",
      backgroundColor: theme.colors.secondary[50], // was colors.white
    },
    heightWeightContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    heightWeightInputLabel: {
      fontSize: moderateScale(theme.fontSize.sm), // 14
      width: "100%",
      color: theme.colors.gray[700],
      marginBottom: moderateVerticalScale(8),
      textAlign: "left",
    },
    title: {
      fontSize: moderateScale(theme.fontSize["2xl"]), // 24
      fontWeight: "700",
      color: theme.colors.text.primary, // was colors.black
      marginBottom: moderateVerticalScale(8),
      textAlign: "left",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm), // 14
      color: theme.colors.gray[600],
      textAlign: "center",
    },
    optionsContainer: {
      marginBottom: moderateVerticalScale(32),
    },
    optionButton: {
      paddingVertical: moderateVerticalScale(16),
      paddingHorizontal: moderateScale(24),
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.sm), // was 8
      marginBottom: moderateVerticalScale(12),
    },
    selectedOption: {
      backgroundColor: theme.colors.primary.main, // was colors.primary
      borderColor: theme.colors.primary.main, // was colors.primary
    },
    optionText: {
      fontSize: moderateScale(theme.fontSize.md), // 16
      color: theme.colors.text.primary, // was colors.black
      textAlign: "center",
    },
    selectedOptionText: {
      color: theme.colors.secondary[50], // was colors.white
      fontWeight: "500",
    },
    inputContainer: {
      marginBottom: moderateVerticalScale(16),
    },
    inputLabel: {
      fontSize: moderateScale(theme.fontSize.sm), // 14
      color: theme.colors.gray[700],
      marginBottom: moderateVerticalScale(8),
      textAlign: "left",
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.sm), // was 8
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateVerticalScale(12),
      textAlign: "center",
    },
    inputText: {
      fontSize: moderateScale(theme.fontSize.md), // 16
      color: theme.colors.gray[800],
    },
    inputError: {
      borderColor: "red",
    },
    errorText: {
      color: theme.colors.error, // was "red"
      fontSize: moderateScale(theme.fontSize.xs), // 12
      marginTop: moderateVerticalScale(4),
      textAlign: "center",
    },
    buttonContainer: {
      position: "absolute",
      bottom: moderateVerticalScale(40),
      left: moderateScale(24),
      right: moderateScale(24),
      width: "auto",
    },
    nextButton: {
      backgroundColor: theme.colors.primary.main, // was colors.primary
      paddingVertical: moderateVerticalScale(16),
      borderRadius: moderateScale(theme.radii.md), // was 12
      alignItems: "center",
      width: "100%",
    },
    nextButtonText: {
      fontSize: moderateScale(16),
      fontWeight: "500",
      color: theme.colors.secondary[50],
    },
    disabledButton: {
      backgroundColor: theme.colors.gray[300],
    },
    loadingContainer: {
      padding: moderateScale(16),
      alignItems: "center",
      justifyContent: "center",
    },
    errorContainer: {
      padding: moderateScale(16),
      backgroundColor: theme.colors.errorLight, // was FFEBEE
      borderRadius: moderateScale(theme.radii.sm), // was 8
      marginBottom: moderateVerticalScale(16),
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    buttonLoader: {
      marginLeft: 8,
    },
  });

function NutritionScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);

  const { nutritionData } = useUserDataStore();
  const [reportsData, setReportsData] = useState<any[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reportsError, setReportsError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showMediaDrawer, setShowMediaDrawer] = useState<boolean>(false);
  const [isUploadLoading, setIsUploadLoading] = useState<boolean>(false);
  const TAB_BAR_HEIGHT = Platform.OS === "ios" ? 76 : 64;
  const [showLoadingScreen, setShowLoadingScreen] = useState(false);
  const [isNextLoading, setIsNextLoading] = useState(false);

  const [tempFoodEntry, setTempFoodEntry] = useState<any | null>(null);
  const [processingImage, setProcessingImage] = useState<string | null>(null);
  const [processingError, setProcessingError] = useState<boolean>(false);
  const [processingErrorMessage, setProcessingErrorMessage] = useState<string>("No food detected");

  const [waitingForResponses, setWaitingForResponses] = useState<boolean>(false);
  const responseCountRef = useRef<number>(0);
  const lastRequestIdRef = useRef<string | null>(null);

  const [isRefreshing, setIsRefreshing] = useState(false);

  const navigation = useNavigation();
  const pathname = usePathname();
  const params = useLocalSearchParams();

  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [lastScrollTime, setLastScrollTime] = useState(0);

  const [latestReportIdBeforeProcessing, setLatestReportIdBeforeProcessing] = useState<string | null>(null);
  const [processingStartTime, setProcessingStartTime] = useState<number | null>(null);
  const [lastProcessedReportId, setLastProcessedReportId] = useState<string | null>(null);
  const mealsContainerRef = useRef<View>(null);

  const scrollViewRef = useRef<ScrollView>(null);
  useScreenTracking('Nutrition', { screen: "Nutrition" });

  const setNutritionRef = useScrollStore((state) => state.setNutritionRef);
  useEffect(() => {
    if (scrollViewRef.current) {
      setNutritionRef(scrollViewRef)
    }
  }, []);
  const scrollToMeals = useCallback(() => {
    logger.info('Scrolling to meals container');
    if (scrollViewRef.current) {
      logger.info('ScrollView reference is valid');
      // Get the height of the NutritionTracker and NutritionChart components
      // and scroll to just below them
      const approximatePosition = 1000; // Adjust based on your layout
      scrollViewRef.current.scrollTo({ y: approximatePosition, animated: true });
    }
  }, []);

  const fetchReportsData = useCallback(async () => {
    setIsLoadingReports(true);
    setReportsError(null);

    try {
      const reportsResponse = await getReportsData();
      if (reportsResponse?.success) {
        const reports = reportsResponse.files ||
          (reportsResponse.files && typeof reportsResponse.files === 'object'
            ? Object.values(reportsResponse.files)
            : []);

        setReportsData(reports);
      }
    } catch (error: any) {
      setReportsError(t('nutrition.error.fetchReportsFailed'));
      if (error?.response?.status !== 401) {
        logger.error('Error fetching reports data:', error);
      }

    } finally {
      setIsLoadingReports(false);
    }
  }, []);

  useEffect(() => {
    if (processingImage && waitingForResponses && processingStartTime) {

      const intervalId = setInterval(() => {
        const currentTime = Date.now();
        const elapsedTime = currentTime - processingStartTime;

        if (elapsedTime > 150000) {
          setProcessingError(true);
          setProcessingErrorMessage("Processing timed out. Please try again.");

          setTempFoodEntry(null);
          setProcessingImage(null);
          setLatestReportIdBeforeProcessing(null);
          setWaitingForResponses(false);
          responseCountRef.current = 0;

          setProcessingStartTime(null);
          clearInterval(intervalId);
        }
      }, 15000); // Check every 15 seconds

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [processingImage, waitingForResponses, processingStartTime]);

  useEffect(() => {
    const client = getWebPubSubClient();

    if (client) {
      const handleMessage = async (e: any) => {
        try {
          if (waitingForResponses) {
            logger.info('Received WebPubSub message while waiting for responses');
            
            // Increment response count
            responseCountRef.current += 1;
            logger.info(`Response count: ${responseCountRef.current}`);
            // Update chat store after 5 seconds
            setTimeout(async () => {
              try {
                const { fetchChatHistory } = useChatStore.getState();
                logger.info('Refreshing chat store after nutrition update');
                await fetchChatHistory();
              } catch (chatError) {
                logger.error('Error refreshing chat store:', chatError);
              }
            }, 5000);
            // After the second response (food type processing is complete)
            if (responseCountRef.current === 2) {
              logger.info('Second response received, checking for new report');

              try {
                // Store the ID we're comparing against for logging
                const oldReportId = latestReportIdBeforeProcessing;
                const reportsResponse = await getReportsData();

                if (reportsResponse?.success) {
                  const freshReports = reportsResponse.files ||
                    (reportsResponse.files && typeof reportsResponse.files === 'object'
                      ? Object.values(reportsResponse.files)
                      : []);
                  const latestFoodReport = freshReports.find((report: { type: string }) => report.type === 'FOOD_ITEM');
                  const currentLatestReportId = latestFoodReport?.report_id;

                  if (currentLatestReportId === oldReportId) {
                    setProcessingError(true);
                    setProcessingErrorMessage("No food detected");
                  } else {
                    setTempFoodEntry(null);
                    setProcessingImage(null);
                    setLatestReportIdBeforeProcessing(null);

                    setReportsData(freshReports);

                    const latestFoodReport = freshReports.find((report: { type: string }) => report.type === 'FOOD_ITEM');
                    if (latestFoodReport) {
                      setLastProcessedReportId(latestFoodReport.report_id);
                      if (global.currentScreen && global.currentScreen.includes('nutrition')) {
                        // Navigate to the food detail page
                        router.push({
                          pathname: '/nutrition-food-detail',
                          params: {
                            foodData: JSON.stringify(latestFoodReport)
                          }
                        });
                      }
                    }
                  }
                }
              } catch (error: any) {
                if ( error?.response?.status !== 401) {
                  logger.error('Error fetching reports data:', error);
                }
                setProcessingError(true);
                setProcessingErrorMessage("Error processing food");
              }
              try {
                const { fetchChatHistory } = useChatStore.getState();
                logger.info('Refreshing chat store after nutrition update');
                fetchChatHistory();
              } catch (chatError) {
                logger.error('Error refreshing chat store:', chatError);
              }

              // Reset processing start time
              setProcessingStartTime(null);

              // Reset waiting state
              setWaitingForResponses(false);
              responseCountRef.current = 0;
            }
          }
        } catch (error) {
          if ((error as any)?.response?.status !== 401) {
            logger.error('Error processing WebPubSub message:', error);
          }
        }
      };

      client.on("server-message", handleMessage);

      // Clean up
      return () => {
        client.off("server-message", handleMessage);
      };
    }
  }, [waitingForResponses, latestReportIdBeforeProcessing]);

  // Set up profile in nutrition store when nutritionData changes
  useEffect(() => {
    if (nutritionData) {
      logger.info('Nutrition data:', nutritionData);
      try {
        if (nutritionData.age && nutritionData.sex) {
          const profileData = {
            sex: nutritionData.sex as 'male' | 'female' | 'other',
            age: parseInt(nutritionData.age),
            height: parseInt(nutritionData.height),
            weight: parseInt(nutritionData.weight),
            exerciseLevel: nutritionData.exerciseLevel as 'no exercise' | 'moderate' | 'high',
            goal: nutritionData.goal as 'increase' | 'maintain' | 'decrease',
            desiredWeight: parseInt(nutritionData.desiredWeight),
          };

          // Validate with Zod schema
          const validatedProfile = profileSchema.parse(profileData);
          setProfile(validatedProfile);
        } else {
          logger.info('Nutrition data incomplete, clearing profile');
          setProfile(null);
        }
      } catch (validationError) {
        logger.info('Profile validation failed:', validationError);
        setProfile(null);
      }
    } else {
      logger.info('No valid nutrition data, clearing profile');
      setProfile(null);
    }
  }, [nutritionData]);

  // Fetch reports data when component mounts
  // Check for refreshData param when returning from food detail page
  useFocusEffect(
    useCallback(() => {
      if (params?.refreshData === 'true') {
        fetchReportsData();
        router.setParams({ refreshData: undefined });
      }
    }, [params?.refreshData])
  );

  // Fetch reports data when component mounts
  useEffect(() => {
    fetchReportsData();
  }, []);

  useEffect(() => {
    let wasOffline = false;
  
    const unsubscribe = NetInfo.addEventListener((state: any) => {
      const isConnected = state.isConnected && state.isInternetReachable;
  
      if (!isConnected) {
        if (!wasOffline) {
          logger.info('NutritionScreen: offline');
          wasOffline = true;
        }
      } else {
        if (wasOffline) {
          logger.info('NutritionScreen: back online');
          fetchReportsData().catch(error => {
            logger.error('Error refreshing nutrition data after reconnect:', error);
          });
          wasOffline = false;
        }
      }
    });
  
    return () => {
      unsubscribe();
    };
  }, [fetchReportsData]);  

  useEffect(() => {
    if (nutritionData) {
      const newFormData = { ...formData };

      // Only update fields that have valid values
      if (nutritionData.sex) newFormData.sex = nutritionData.sex;
      if (nutritionData.age) newFormData.age = nutritionData.age.toString();
      if (nutritionData.height) newFormData.height = nutritionData.height.toString();
      if (nutritionData.weight) newFormData.weight = nutritionData.weight.toString();
      if (nutritionData.exerciseLevel) newFormData.exerciseLevel = nutritionData.exerciseLevel;
      if (nutritionData.goal) newFormData.goal = nutritionData.goal;
      if (nutritionData.desiredWeight) newFormData.desiredWeight = nutritionData.desiredWeight.toString();

      setFormData(newFormData);
    }
  }, [nutritionData]);

  const user = useUser();
  const router = useRouter();

  // Get non-memoized values from the store
  const { profile, setProfile, addEntry } = useNutritionStore();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedFood, setSelectedFood] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [formData, setFormData] = useState({
    sex: '',
    age: '30',           // default age
    height: '170',       // default height
    weight: '60',        // default weight
    exerciseLevel: '',
    goal: '',
    desiredWeight: '60', // default desired weight
  });  
  const [errors, setErrors] = useState<Record<string, string>>({});

  const ageRange = useMemo(() => [...Array(103).keys()].map((index) => ({
    value: index + 18,
    label: (index + 18).toString(),
  })).filter(item => item.value <= 120), [])

  const weightRange = useMemo(() => [...Array(271).keys()].map((index) => ({
    value: index + 30,
    label: (index + 30).toString(),
  })).filter(item => item.value <= 300), [])

  const heightRange = useMemo(() => [...Array(201).keys()].map((index) => ({
    value: index + 50,
    label: (index + 50).toString(),
  })).filter(item => item.value <= 250), [])

  const desiredWeightRange = useMemo(() => [...Array(271).keys()].map((index) => ({
    value: index + 30,
    label: (index + 30).toString(),
  })).filter(item => item.value <= 300), [])

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setProcessingImage(null);
    setProcessingError(false);
    setTempFoodEntry(null);
    setWaitingForResponses(false);
    responseCountRef.current = 0;
    setLatestReportIdBeforeProcessing(null);
    setProcessingStartTime(null);
    try {

      await fetchReportsData();
      await useUserDataStore.getState().updateNutrition(profile || {});
    } catch (error) {
      if ((error as any)?.response?.status !== 401) {
        logger.error('Error refreshing data:', error);
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchReportsData, profile]);

  const steps = useMemo(() => [
    {
      title: t('nutrition.form.gender.title'),
      subtitle: t('nutrition.form.gender.subtitle'),
      field: 'sex',
      options: [t('nutrition.form.gender.male'), t('nutrition.form.gender.female'), t('nutrition.form.gender.other')],
    },
    {
      title: t('nutrition.form.age.title'),
      subtitle: t('nutrition.form.age.subtitle'),
      field: 'age',
      type: 'number',
      validation: (value: string) => {
        const age = parseInt(value);
        if (isNaN(age)) return "Please enter a valid age";
        if (age < 18) return "You must be at least 18 years old";
        if (age > 120) return "Please enter a valid age";
        return "";
      }
    },
    {
      title: t('nutrition.form.measurements.title'),
      subtitle: t('nutrition.form.measurements.subtitle'),
      fields: ['height', 'weight'],
      type: 'number',
      validation: {
        height: (value: string) => {
          const height = parseFloat(value);
          if (isNaN(height)) return "Please enter a valid height";
          if (height < 50) return "Height must be at least 50 cm";
          if (height > 250) return "Height must be less than 250 cm";
          return "";
        },
        weight: (value: string) => {
          const weight = parseFloat(value);
          if (isNaN(weight)) return "Please enter a valid weight";
          if (weight < 30) return "Weight must be at least 30 kg";
          if (weight > 300) return "Weight must be less than 300 kg";
          return "";
        }
      }
    },
    {
      title: t('nutrition.form.activity.title'),
      subtitle: t('nutrition.form.activity.subtitle'),
      field: 'exerciseLevel',
      options: [t('nutrition.form.activity.none'), t('nutrition.form.activity.moderate'), t('nutrition.form.activity.high')],
    },
    {
      title: t('nutrition.form.goal.title'),
      subtitle: t('nutrition.form.goal.subtitle'),
      field: 'goal',
      options: [t('nutrition.form.goal.increase'), t('nutrition.form.goal.maintain'), t('nutrition.form.goal.decrease')],
    },
    {
      title: t('nutrition.form.targetWeight.title'),
      subtitle: t('nutrition.form.targetWeight.subtitle'),
      field: 'desiredWeight',
      type: 'number',
      validation: (value: string) => {
        const weight = parseFloat(value);
        if (isNaN(weight)) return "Please enter a valid weight";
        if (weight < 30) return "Weight must be at least 30 kg";
        if (weight > 300) return "Weight must be less than 300 kg";
        return "";
      }
    },
    { 
      title: t('nutrition.form.setup.title'), 
      subtitle: t('nutrition.form.setup.subtitle'),
      isLoadingStep: true, 
    },
    { title: t('nutrition.form.review.title'), subtitle: t('nutrition.form.review.subtitle'), isReviewStep: true, },
  ], [t]);

  // Handle submission from the review step
  const handleReviewSubmit = useCallback(async (updatedData: any) => {
    setIsLoading(true);
    try {
      // Create profile object
      const profileData = {
        sex: formData.sex as 'male' | 'female' | 'other',
        age: parseInt(formData.age),
        height: parseInt(formData.height),
        weight: parseInt(formData.weight),
        exerciseLevel: formData.exerciseLevel as 'no exercise' | 'moderate' | 'high',
        goal: formData.goal as 'increase' | 'maintain' | 'decrease',
        desiredWeight: parseInt(formData.desiredWeight),
        dailyCalories: updatedData.dailyCalories,
        dailyProtein: updatedData.dailyProtein,
        dailyCarbohydrates: updatedData.dailyCarbohydrates,
        dailyFats: updatedData.dailyFats
      };

      setProfile(profileData);
      await updateNutritionData(profileData);
      await useUserDataStore.getState().updateNutrition(profileData);
      // Refresh reports data
      await fetchReportsData();
    } catch (error) {
      setShowLoadingScreen(false);
      // Enhanced error logging for Slack notification
      const errorDetails = {
        component: 'Nutrition Setup Loading Screen',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
      if ((error as any)?.response?.status !== 401) {
        logger.error('Error updating nutrition data:', error);
        logger.error(`Nutrition Setup Failed: ${JSON.stringify(errorDetails)}`);
      }
      setError(t('nutrition.error.updateFailed'));
    } finally {
      setIsLoading(false);
    }
  }, [formData, setProfile, fetchReportsData]);

  const sendToWebhook = useCallback(async (fileUrl: string, fileType: 'image' | 'pdf' | 'voice', requestId: string = uuidv4()) => {
    try {
      // Get phone number from phoneStore first, then fall back to auth store, then use a default
      const phoneStoreNumber = usePhoneStore.getState().phoneNumber;
      const phoneNumber = phoneStoreNumber || user?.phone || 'unknown';

      const messageId = uuidv4();

      // Create the message payload
      const messagePayload = {
        text: '',
        providerMessageId: messageId,
        attachment: fileUrl,
        fileExtension: fileType === 'image' ? '.jpg' : (fileType === 'voice' ? '.m4a' : '.pdf'),
        messageType: fileType,
        sender: 'human',
        source: 'MOBILE',
        phoneNumber,
        timestamp: Date.now(),
        requestId
      };

      // Send the message to webhook
      const response = await sendMessage(messagePayload);
      logger.info('Webhook response:', response);

      return response;
    } catch (error) {
      logger.error('Error sending to webhook:', error);
      throw error;
    }
  }, [user]);

  const handleAddFood = useCallback(() => {
    trackUserInteraction('Nutrition FAB Clicked', {
      hasProfile: !!profile,
      reportsCount: reportsData.length
    });

    trackClarityEvent('Add_Food_Button_Clicked', {
      hasProfile: !!profile,
      reportsCount: reportsData.length,
      timestamp: new Date().toISOString()
    });

    setShowMediaDrawer(true);
  }, [profile, reportsData.length]);

  const handleTakePhoto = useCallback(async () => {
    try {
      setIsUploadLoading(true);
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        alert('We need camera permissions to make this work!');
        setIsUploadLoading(false);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled) {
        
        const latestFoodReport = reportsData.find(report => report.type === 'FOOD_ITEM');
        setLatestReportIdBeforeProcessing(latestFoodReport?.report_id || null);

        const fileUri = result.assets[0].uri;

        try {
          setShowMediaDrawer(false);
            // Create a timeout promise
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error('Upload timed out after 7 seconds'));
            }, UPLOAD_TIMEOUT);
          });

          // Race between upload and timeout
          const uploadResponse = await Promise.race([
            uploadMedia(fileUri),
            timeoutPromise
          ]);
          logger.info('Photo upload response:', uploadResponse);

          if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
            AppEventsLogger.logEvent('nutrition_food_image_uploaded');
            const serverUrl = uploadResponse.files['0'].fileURL;
            logger.info('Extracted server URL:', serverUrl);

            const requestId = uuidv4();
            lastRequestIdRef.current = requestId;

            // Reset response counter and set waiting state
            responseCountRef.current = 0;
            setWaitingForResponses(true);
            setProcessingImage(fileUri);
            setProcessingError(false);

            // Set the processing start time
            setProcessingStartTime(Date.now());

            // Create a temporary food entry with loading state
            const tempEntry = {
              report_id: 'temp-' + Date.now(),
              url: fileUri,
              type: 'FOOD_ITEM',
              timestamp: new Date().toISOString(),
              isTemp: true, // Flag to indicate this is a temporary entry
            };

            setTempFoodEntry(tempEntry);

            scrollToMeals();

            // Send to webhook
            await sendToWebhook(serverUrl, 'image', requestId);

            addEntry({
              imageUri: fileUri,
              name: '',
              calories: 0,
              protein: 0,
              carbs: 0,
              fat: 0
            });
          } else {
            alert('Failed to upload image. Please try again.');
          }
        } catch (uploadError: any) {
          logger.error('Photo upload failed:', uploadError);
          if (uploadError.message === 'Upload timed out after 7 seconds') {
            alert('Upload timed out. Please try again.');
          } else {
            alert('Upload Failed. Could not upload the image.');
          }
        }
      }
    } catch (error) {
      alert('Failed to capture image. Please try again.');
    } finally {
      setIsUploadLoading(false);
      setShowMediaDrawer(false);
    }
  }, [sendToWebhook, addEntry, user, reportsData]);

  const handlePickImage = useCallback(async () => {
    try {
      setIsUploadLoading(true);
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        alert('We need media library permissions to make this work!');
        setIsUploadLoading(false);
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8
      });

      if (!result.canceled) {

        const latestFoodReport = reportsData.find(report => report.type === 'FOOD_ITEM');
        setLatestReportIdBeforeProcessing(latestFoodReport?.report_id || null);

        const fileUri = result.assets[0].uri;

        try {
          setShowMediaDrawer(false);
          // Upload the file to the server
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error('Upload timed out after 7 seconds'));
            }, UPLOAD_TIMEOUT);
          });
  
          // Race between upload and timeout
          const uploadResponse = await Promise.race([
            uploadMedia(fileUri),
            timeoutPromise
          ]);
          logger.info('Image upload response:', uploadResponse);

          // Extract the file URL from the response
          if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
            AppEventsLogger.logEvent('nutrition_food_image_uploaded');
            const serverUrl = uploadResponse.files['0'].fileURL;
            logger.info('Extracted server URL:', serverUrl);

            // Generate a request ID to track this upload
            const requestId = uuidv4();
            lastRequestIdRef.current = requestId;

            // Reset response counter and set waiting state
            responseCountRef.current = 0;
            setWaitingForResponses(true);
            setProcessingImage(fileUri);
            setProcessingError(false);

            // Set the processing start time
            setProcessingStartTime(Date.now());

            // Create a temporary food entry with loading state
            const tempEntry = {
              report_id: 'temp-' + Date.now(),
              url: fileUri,
              type: 'FOOD_ITEM',
              timestamp: new Date().toISOString(),
              isTemp: true, // Flag to indicate this is a temporary entry
            };

            setTempFoodEntry(tempEntry);
            scrollToMeals();
            await sendToWebhook(serverUrl, 'image', requestId);

            addEntry({
              imageUri: fileUri,
              name: '',
              calories: 0,
              protein: 0,
              carbs: 0,
              fat: 0
            });
          } else {
            alert('Failed to upload image. Please try again.');
          }
        } catch (uploadError: any) {
          logger.error('Image upload failed:', uploadError);
          if (uploadError.message === 'Upload timed out after 7 seconds') {
            alert('Upload timed out. Please try again.');
          } else {
            alert('Upload Failed. Could not upload the image.');
          }
        }
      }
    } catch (error) {
      alert('Failed to pick image. Please try again.');
    } finally {
      setIsUploadLoading(false);
      setShowMediaDrawer(false);
    }
  }, [sendToWebhook, addEntry, user, reportsData]);

  // Handle retaking a photo when food detection fails
  const handleRetakePhoto = useCallback(() => {
    // Clear error state
    setProcessingError(false);
    setProcessingImage(null);
    setTempFoodEntry(null);

    // Reset processing start time
    setProcessingStartTime(null);

    // Reset waiting state
    setWaitingForResponses(false);
    responseCountRef.current = 0;
    setShowMediaDrawer(true);
  }, []);

  // Handle closing the error card
  const handleCloseProcessingError = useCallback(() => {
    setProcessingError(false);
    setProcessingImage(null);
    setTempFoodEntry(null);

    // Reset processing start time
    setProcessingStartTime(null);

    // Reset waiting state
    setWaitingForResponses(false);
    responseCountRef.current = 0;
  }, []);

  const handleOptionSelect = useCallback((value: string, field: string | null = null) => {
    const currentConfig = steps[currentStep];
    const newFormData = { ...formData };
    if (Array.isArray(currentConfig.fields) && field) {
      newFormData[field as keyof typeof formData] = value;
    } else if (currentConfig.field) {
      newFormData[currentConfig.field as keyof typeof formData] = value.toLowerCase();
    }
    setFormData(newFormData);

    if (field) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    } else if (currentConfig.field) {
      setErrors(prev => ({ ...prev, [currentConfig.field]: '' }));
    }
  }, [currentStep, formData, steps]);

  // Debounce function to limit state updates
  const debounce = (func: Function, delay: number) => {
    let timer: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timer);
      timer = setTimeout(() => func(...args), delay);
    };
  };

  const validateCurrentStep = useMemo(() => {
    return () => {
      const step = steps[currentStep];
      const newErrors: Record<string, string> = {};

      if (Array.isArray(step.fields)) {
        step.fields.forEach(field => {
          const typedField = field as keyof typeof formData;
          if (!formData[typedField]) {
            newErrors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
          } else if (step.validation && typeof step.validation === 'object' && field in step.validation) {
            const validationFn = step.validation[field as keyof typeof step.validation];
            if (typeof validationFn === 'function') {
              const error = validationFn(formData[typedField]);
              if (error) newErrors[field] = error;
            }
          }
        });
      } else if (step.field) {
        const typedField = step.field as keyof typeof formData;
        if (!formData[typedField]) {
          newErrors[step.field] = `${step.field.charAt(0).toUpperCase() + step.field.slice(1)} is required`;
        } else if (step.validation && typeof step.validation === 'function') {
          const error = step.validation(formData[typedField]);
          if (error) newErrors[step.field] = error;
        }
      }

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };
  }, [currentStep, formData, steps]);

  const handleNext = useCallback(async () => {
    if (!validateCurrentStep()) return;

    setIsNextLoading(true);
    try {
      // For all steps except the last one
      if (currentStep < steps.length - 1) {
        // Add a small delay to show the loading state
        await new Promise(resolve => setTimeout(resolve, 200));
        trackUserInteraction('Filled nutrition form step', {step: currentStep})
        setCurrentStep(currentStep + 1);
      } else {
        // Create profile object
        const profileData = {
          sex: formData.sex as 'male' | 'female' | 'other',
          age: parseInt(formData.age),
          height: parseInt(formData.height),
          weight: parseInt(formData.weight),
          exerciseLevel: formData.exerciseLevel as 'no exercise' | 'moderate' | 'high',
          goal: formData.goal as 'increase' | 'maintain' | 'decrease',
          desiredWeight: parseInt(formData.desiredWeight),
        };

        setShowLoadingScreen(true);

        try {
          // Save profile to local store
          setProfile(profileData);

          // Update global store
          setIsLoading(true);
          logger.info('IM ACTUALLY COMMING HERE', profileData)
          await useUserDataStore.getState().updateNutrition(profileData);
          setIsLoading(false);

          // Refresh reports data
          await fetchReportsData();

          // Wait for loading screen animation to fully complete 
          setTimeout(() => {
            setShowLoadingScreen(false);
            // router.replace('/(tabs)/chat'); // or navigate to wherever you want after setup
          }, 12000);

        } catch (error) {
          setShowLoadingScreen(false);
          
          // Enhanced error logging for Slack notification
          const errorDetails = {
            component: 'Nutrition Setup Loading Screen',
            profileData: JSON.stringify(profileData),
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString()
          };
          if ((error as any)?.response?.status !== 401) {
            logger.error('Error updating nutrition data:', error);
            logger.error(`Nutrition Setup Failed: ${JSON.stringify(errorDetails)}`);
          }
          setIsLoading(false);
          setError(t('nutrition.error.updateFailed'));
        }
      }
    } finally {
      setIsNextLoading(false);
    }
  }, [currentStep, validateCurrentStep, steps.length, formData, setProfile, fetchReportsData, router]);

  const handleBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleFoodPress = useCallback((food: any) => {
    //const start = performance.now();
    //console.log(`[PERF] Tap started: ${start}`);
  
    trackUserInteraction('Food Detail Opened', {
      foodType: food.type || 'unknown',
      hasProcessedOutput: !!food.processed_output,
      timestamp: new Date().toISOString()
    });
  
    trackClarityEvent('Food_Detail_Viewed', {
      foodType: food.type || 'unknown',
      hasProcessedOutput: !!food.processed_output,
      timestamp: new Date().toISOString()
    });
  
    router.push({
      pathname: '/nutrition-food-detail',
      params: {
        foodData: JSON.stringify(food),
        //ts: start.toString(), // Optional: pass start timestamp to destination page
      }
    });
  }, [router]);  

  const renderStep = useMemo(() => {
    return () => {
      const step = steps[currentStep];

      if (step.isLoadingStep) {
        return (
          <SetupLoadingScreen 
            onComplete={() => {
              setCurrentStep(currentStep + 1);
            }} 
          />
        );
      }

      if (step.isReviewStep) {
        // Create a nutrition data object from the form data
        const nutritionDataForReview = {
          sex: formData.sex,
          age: formData.age,
          height: formData.height,
          weight: formData.weight,
          exerciseLevel: formData.exerciseLevel,
          goal: formData.goal,
          desiredWeight: formData.desiredWeight
        };
        return (
          <NutritionReviewStep 
            nutritionData={nutritionDataForReview} 
            onSubmit={handleReviewSubmit} 
            isLoading={isLoading} 
          />
        );
      }

      return (
        // <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.stepContainer}>
          <View style={styles.stepTitleContainer}>
            <Text style={styles.title}>{step.title}</Text>
            <Text style={styles.subtitle}>{step.subtitle}</Text>
          </View>

          <View style={styles.stepContent}>
            <View style={styles.optionsContainer}>
              {step.options ? (
                step.options.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.optionButton,
                      step.field && formData[step.field as keyof typeof formData] === option.toLowerCase() && styles.selectedOption,
                    ]}
                    onPress={() => handleOptionSelect(option)}
                  >
                    <Text style={[
                      styles.optionText,
                      step.field && formData[step.field as keyof typeof formData] === option.toLowerCase() && styles.selectedOptionText,
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                ))
              ) : Array.isArray(step.fields) ? (
                <View style={styles.heightWeightContainer}>
                  <View style={styles.inputContainer}>
                    <Text style={styles.heightWeightInputLabel}>
                      {" "}
                      {t('nutrition.form.height.label')}{" "}
                    </Text>
                    <View style={styles.heightWeightWheelPickerContainer}>
                      <WheelPicker
                        key={`height-${formData.height}`}
                        data={heightRange}
                        value={Number(formData.height)}
                        onValueChanged={({ item: { value } }) => {
                          const newFormData = {
                            ...formData,
                            height: value.toString(),
                          };
                          setFormData(newFormData);
                          setErrors((prev) => ({ ...prev, height: "" }));
                        }}
                      />
                    </View>
                    {errors[step.field as keyof typeof errors] ? (
                      <Text style={styles.errorText}>
                        {errors[step.field as keyof typeof errors]}
                      </Text>
                    ) : null}
                  </View>

                  <View style={styles.inputContainer}>
                    <Text style={styles.heightWeightInputLabel}>
                      {" "}
                      {t('nutrition.form.weight.label')}{" "}
                    </Text>
                    <View style={styles.wheelPickerContainer}>
                      <WheelPicker
                        data={weightRange}
                        value={Number(formData.weight)}
                        key={`height-${formData.height}`}
                        onValueChanged={({ item: { value } }) => {
                          const newFormData = {
                            ...formData,
                            weight: value.toString(),
                          };
                          setFormData(newFormData);
                          setErrors((prev) => ({ ...prev, weight: "" }));
                        }}
                      />
                    </View>
                    {errors[step.field as keyof typeof errors] ? (
                      <Text style={styles.errorText}>
                        {errors[step.field as keyof typeof errors]}
                      </Text>
                    ) : null}
                  </View>

                  {/* <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Height (cm)</Text>
                      <TextInput
                        style={[styles.input, errors.height ? styles.inputError : null]}
                        placeholder="Enter your height"
                        value={formData.height}
                        onChangeText={(text) => {
                          const newFormData = { ...formData, height: text };
                          setFormData(newFormData);
                          setErrors(prev => ({ ...prev, height: '' }));
                        }}
                        keyboardType="numeric"
                      />
                      {errors.height ? <Text style={styles.errorText}>{errors.height}</Text> : null}
                    </View>

                    <View style={[styles.inputContainer, { marginBottom: 40 }]}>
                      <Text style={styles.inputLabel}>Weight (kg)</Text>
                      <TextInput
                        style={[styles.input, errors.weight ? styles.inputError : null]}
                        placeholder="Enter your weight"
                        value={formData.weight}
                        onChangeText={(text) => {
                          const newFormData = { ...formData, weight: text };
                          setFormData(newFormData);
                          setErrors(prev => ({ ...prev, weight: '' }));
                        }}
                        keyboardType="numeric"
                      />
                      {errors.weight ? <Text style={styles.errorText}>{errors.weight}</Text> : null}
                    </View> */}
                </View>
              ) : step.type === "number" && step.field === "age" ? (
                <View style={styles.inputContainer}>
                  <View style={styles.wheelPickerContainer}>
                    <WheelPicker
                      data={ageRange}
                      key={`height-${formData.height}`}
                      value={Number(formData.age)}
                      onValueChanged={({ item: { value } }) => {
                        const newFormData = {
                          ...formData,
                          age: value.toString(),
                        };
                        setFormData(newFormData);
                        setErrors((prev) => ({ ...prev, age: "" }));
                      }}
                    />
                  </View>
                  {errors[step.field as keyof typeof errors] ? (
                    <Text style={styles.errorText}>
                      {errors[step.field as keyof typeof errors]}
                    </Text>
                  ) : null}
                </View>
              ) : step.type === "number" && step.field === "desiredWeight" ? (
                <View style={styles.inputContainer}>
                  <View style={styles.wheelPickerContainer}>
                    <WheelPicker
                      data={desiredWeightRange}
                      key={`height-${formData.height}`}
                      value={Number(formData.desiredWeight)}
                      onValueChanged={({ item: { value } }) => {
                        const newFormData = {
                          ...formData,
                          desiredWeight: value.toString(),
                        };
                        setFormData(newFormData);
                        setErrors((prev) => ({ ...prev, desiredWeight: "" }));
                      }}
                    />
                  </View>
                  {errors[step.field as keyof typeof errors] ? <Text style={styles.errorText}>{errors[step.field as keyof typeof errors]}</Text> : null}
                </View>
              ) : step.field ? (
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>{`Enter your ${step.field.charAt(0).toUpperCase() + step.field.slice(1)}`}</Text>
                  <TextInput
                    style={[styles.input, errors[step.field as keyof typeof errors] ? styles.inputError : null]}
                    placeholder={`Enter your ${step.field}`}
                    value={formData[step.field as keyof typeof formData]}
                    onChangeText={(text) => {
                      const newFormData = { ...formData };
                      newFormData[step.field as keyof typeof formData] = text;
                      setFormData(newFormData);
                      setErrors((prev) => ({ ...prev, [step.field]: "" }));
                    }}
                  />
                  {errors[step.field as keyof typeof errors] ? <Text style={styles.errorText}>{errors[step.field as keyof typeof errors]}</Text> : null}
                </View>
              ) : null}
            </View>
          </View>
        </View>
        // </TouchableWithoutFeedback>
      );
    };
  }, [currentStep, steps, formData, errors, handleOptionSelect]);

  const isStepComplete = useMemo(() => {
    return () => {
      const step = steps[currentStep];
      if (Array.isArray(step.fields)) {
        return step.fields.every(field => formData[field as keyof typeof formData].trim() !== '');
      }

      return step.field ? formData[step.field as keyof typeof formData].trim() !== '' : false;
    };
  }, [currentStep, formData, steps]);

  const isProfileValid = useMemo(() => {
    return () => {
      if (!profile) return false;

      try {
        profileSchema.parse(profile);
        return true;
      } catch (error) {
        logger.info('Profile validation failed:', error);
        return false;
      }
    };
  }, [profile]);

  useEffect(() => {
    // Define handlers directly inside useEffect
    const handleKeyboardShow = () => {
      setKeyboardVisible(true);
      navigation.setOptions({
        tabBarStyle: { display: "none" },
      });
    };

    const handleKeyboardHide = () => {
      setKeyboardVisible(false);
      navigation.setOptions({
        tabBarStyle: {
          height: TAB_BAR_HEIGHT,
          paddingTop: 4,
          borderTopWidth: 0,
          display: "inline",
        },
      });
    };

    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      handleKeyboardShow
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      handleKeyboardHide
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [navigation, TAB_BAR_HEIGHT]);

  const handleScroll = useCallback((event: any) => {
    const now = Date.now();
    if (now - lastScrollTime > 2000) {
      setLastScrollTime(now);
      const { y } = event.nativeEvent.contentOffset;
      trackUserInteraction('Nutrition Page Scrolled', {
        scrollPosition: y,
        screenHeight: event.nativeEvent.layoutMeasurement.height
      });
    }
  }, [lastScrollTime]);

  if (!isProfileValid()) {
    return (
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <SafeAreaView style={styles.container} edges={["bottom"]}>
          <Stack.Screen options={{ headerShown: false }} />

          {/* Navigation and progress bar container */}
          <View style={[styles.navigationContainer, styles.formNavigationContainer]}>
            {currentStep > 0 && (
              <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                <ArrowLeft size={20} color={colors.primary} />
              </TouchableOpacity>
            )}
            <View style={styles.progressBarWrapper}>
              <View style={styles.progressBackground}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${((currentStep) / (steps.length - 1)) * 100}%` }
                  ]}
                />
              </View>
            </View>
          </View>

          <ScrollView
            style={styles.formScrollView}
            contentContainerStyle={styles.formScrollContent}
            keyboardShouldPersistTaps="handled"
          >
            {renderStep()}

            {/* Button inside ScrollView instead of fixed position */}
            {!steps[currentStep].isReviewStep &&  !steps[currentStep].isLoadingStep && (
              <View style={styles.buttonContainerInScroll}>
                <TouchableOpacity
                  style={[styles.nextButton, !isStepComplete() && styles.disabledButton]}
                  onPress={handleNext}
                  disabled={!isStepComplete() || isNextLoading}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.nextButtonText}>
                      {t('common.next')}
                    </Text>
                    {isNextLoading && (
                      <ActivityIndicator
                        size="small"
                        color={colors.white}
                        style={styles.buttonLoader}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            )}

          </ScrollView>
        </SafeAreaView>
      </KeyboardAvoidingView>
    );
  }

  if (showLoadingScreen) {
    return <SetupLoadingScreen onComplete={() => {
      setShowLoadingScreen(false);
      // router.replace('/(tabs)/chat'); // or navigate to wherever you want after setup
    }} />;
  }

  return (
    <SafeAreaView style={styles.container} edges={[]}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('nutrition.nav.title')} />

      {/* Show error if reports data failed to load */}
      {reportsError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{reportsError}</Text>
        </View>
      )}

      {/* Main content */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        <NutritionTracker reports={reportsData} nutritionData={nutritionData} />

        <View style={styles.chartContainer}>
          <NutritionChart reports={reportsData} />
        </View>

        <View ref={mealsContainerRef}
          style={styles.mealsContainer}>
          <Text style={styles.sectionTitle}>{t('nutrition.meals.title')}</Text>
          <Text style={styles.sectionSubtitle}>
            {t('nutrition.meals.subtitle')}
          </Text>

          {/* Show processing card when an image is being processed */}
          {processingImage && (
            <FoodProcessingCard
              imageUri={processingImage}
              isError={processingError}
              errorMessage={processingErrorMessage}
              onRetake={handleRetakePhoto}
              onClose={handleCloseProcessingError}
            />
          )}

          {/* Don't pass tempEntry to FoodEntryList when we're showing the processing card */}
          <FoodEntryList
            onFoodPress={handleFoodPress}
            reports={reportsData}
            tempEntry={processingImage ? null : tempFoodEntry}
          />
        </View>
      </ScrollView>

      {/* example how we can use the media component which can render all media acc to the type=""  prop 
        <MediaGalleryScreen
          onFoodPress={handleFoodPress}
          reportsData={reportsData}
          title="Your Media"
          subtitle=" Tap to check out the macros in each meal"
          isLoadingReports={isLoadingReports}
          reportsError={reportsError}
          type="ALL"
        />*/}

      {/* Loading overlay for uploads */}
      {isUploadLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>{t('nutrition.upload.loading')}</Text>
        </View>
      )}

      {/* Food detail is now a separate page */}

      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={[
            styles.fab,
            ((!!processingImage && !processingError) || !!isUploadLoading) && styles.disabledFab
          ]}
          onPress={handleAddFood}
          disabled={(!!processingImage && !processingError) || !!isUploadLoading}
        >
          <Plus size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      <MediaUploadDrawer
        visible={showMediaDrawer}
        onClose={() => setShowMediaDrawer(false)}
        onTakePhoto={handleTakePhoto}
        onChooseImage={handlePickImage}
        onChooseFile={null}
      />
    </SafeAreaView>
  );
}

export default React.memo(NutritionScreen);

// const styles =
//  StyleSheet.create({
//   loadingOverlay: {
//     position: "absolute",
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: "rgba(255, 255, 255, 0.8)",
//     alignItems: "center",
//     justifyContent: "center",
//     zIndex: 1000,
//   },
//   loadingText: {
//     fontSize: moderateScale(16),
//     marginTop: moderateVerticalScale(8),
//     color: colors.gray[600],
//   },
//   formScrollView: {
//     flex: 1,
//   },
//   formScrollContent: {
//     flexGrow: 1,
//     paddingBottom: moderateVerticalScale(24),
//   },
//   buttonContainerInScroll: {
//     marginTop: moderateVerticalScale(40),
//     marginHorizontal: moderateScale(24),
//     marginBottom: moderateVerticalScale(24),
//   },
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   content: {
//     flex: 1,
//     padding: moderateScale(16),
//   },
//   scrollContent: {
//     paddingBottom: moderateVerticalScale(100),
//   },
//   chartContainer: {
//     marginTop: moderateVerticalScale(24),
//     marginBottom: moderateVerticalScale(24),
//   },
//   mealsContainer: {
//     marginVertical: moderateVerticalScale(16),
//     width: "100%",
//     justifyContent: "center",
//     alignItems: "center",
//   },
//   formNavigationContainer: {
//     marginTop: moderateVerticalScale(80),
//     paddingLeft: moderateScale(30),
//     paddingRight: moderateScale(30),
//   },
//   sectionTitle: {
//     fontSize: moderateScale(19),
//     fontWeight: "600",
//     marginBottom: moderateVerticalScale(8),
//     marginLeft: moderateScale(4),
//     marginTop: moderateVerticalScale(8),
//     color: colors.black,
//   },
//   sectionSubtitle: {
//     fontSize: moderateScale(15),
//     color: colors.gray[600],
//     marginBottom: moderateVerticalScale(26),
//     marginLeft: moderateScale(4),
//   },
//   fabContainer: {
//     position: "absolute",
//     bottom: moderateVerticalScale(24),
//     right: moderateScale(24),
//   },
//   fab: {
//     width: moderateScale(56),
//     height: moderateScale(56),
//     borderRadius: moderateScale(28),
//     backgroundColor: colors.primary,
//     alignItems: "center",
//     justifyContent: "center",
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.3,
//     shadowRadius: moderateScale(3),
//     elevation: 5,
//   },
//   disabledFab: {
//     backgroundColor: colors.gray[300], // Use a grayed out color when disabled
//     shadowOpacity: 0.1, // Reduce shadow opacity when disabled
//   },
//   stepContainer: {
//     flex: 1,
//     justifyContent: "flex-start",
//     alignItems: "center",
//     padding: moderateScale(24),
//     paddingTop: moderateVerticalScale(24),
//     paddingBottom: moderateVerticalScale(10),
//   },
//   stepContent: {
//     width: "100%",
//     maxWidth: moderateScale(400),
//   },
//   stepTitleContainer: {
//     marginBottom: moderateVerticalScale(64),
//     alignItems: "center",
//     textAlign: "center",
//     width: "100%",
//     maxWidth: moderateScale(400),
//   },
//   navigationContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     paddingHorizontal: moderateScale(24),
//     paddingBottom: moderateVerticalScale(8),
//     position: "relative",
//   },
//   backButton: {
//     position: "absolute",
//     left: moderateScale(12),
//     zIndex: 10,
//     paddingBottom: moderateVerticalScale(8),
//   },
//   progressBarWrapper: {
//     width: "100%",
//     marginHorizontal: "auto",
//     paddingLeft: moderateScale(16),
//     alignSelf: "center",
//   },
//   progressBackground: {
//     height: moderateVerticalScale(6),
//     backgroundColor: colors.gray[200],
//     borderRadius: moderateScale(3),
//     overflow: "hidden",
//   },
//   progressFill: {
//     height: "100%",
//     backgroundColor: colors.primary,
//     borderRadius: moderateScale(3),
//   },
//   wheelPickerContainer: {
//     height: moderateVerticalScale(160),
//     justifyContent: "center",
//     backgroundColor: colors.white,
//   },
//   heightWeightWheelPickerContainer: {
//     height: moderateVerticalScale(160),
//     width: "100%",
//     justifyContent: "center",
//     backgroundColor: colors.white,
//   },
//   heightWeightContainer: {
//     display: "flex",
//     flexDirection: "row",
//     justifyContent: "space-between",
//     width: "100%",
//   },
//   heightWeightInputLabel: {
//     fontSize: moderateScale(14),
//     width: "100%",
//     color: colors.gray[700],
//     marginBottom: moderateVerticalScale(8),
//     textAlign: "left",
//   },
//   title: {
//     fontSize: moderateScale(28),
//     fontWeight: "700",
//     color: colors.black,
//     marginBottom: moderateVerticalScale(8),
//     textAlign: "left",
//   },
//   subtitle: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//     textAlign: "center",
//   },
//   optionsContainer: {
//     marginBottom: moderateVerticalScale(32),
//   },
//   optionButton: {
//     paddingVertical: moderateVerticalScale(16),
//     paddingHorizontal: moderateScale(24),
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(8),
//     marginBottom: moderateVerticalScale(12),
//   },
//   selectedOption: {
//     backgroundColor: colors.primary,
//     borderColor: colors.primary,
//   },
//   optionText: {
//     fontSize: moderateScale(16),
//     color: colors.black,
//     textAlign: "center",
//   },
//   selectedOptionText: {
//     color: colors.white,
//     fontWeight: "500",
//   },
//   inputContainer: {
//     marginBottom: moderateVerticalScale(16),
//   },
//   inputLabel: {
//     fontSize: moderateScale(14),
//     color: colors.gray[700],
//     marginBottom: moderateVerticalScale(8),
//     textAlign: "left",
//   },
//   input: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(8),
//     paddingHorizontal: moderateScale(16),
//     paddingVertical: moderateVerticalScale(12),
//     textAlign: "center",
//   },
//   inputText: {
//     fontSize: moderateScale(16),
//     color: colors.gray[800],
//   },
//   inputError: {
//     borderColor: "red",
//   },
//   errorText: {
//     color: "red",
//     fontSize: moderateScale(12),
//     marginTop: moderateVerticalScale(4),
//     textAlign: "center",
//   },
//   buttonContainer: {
//     position: "absolute",
//     bottom: moderateVerticalScale(40),
//     left: moderateScale(24),
//     right: moderateScale(24),
//     width: "auto",
//   },
//   nextButton: {
//     backgroundColor: colors.primary,
//     paddingVertical: moderateVerticalScale(16),
//     borderRadius: moderateScale(12),
//     alignItems: "center",
//     width: "100%",
//   },
//   nextButtonText: {
//     fontSize: moderateScale(16),
//     fontWeight: "500",
//     color: colors.white,
//   },
//   disabledButton: {
//     backgroundColor: colors.gray[300],
//   },
//   loadingContainer: {
//     padding: moderateScale(16),
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   errorContainer: {
//     padding: moderateScale(16),
//     backgroundColor: "#FFEBEE",
//     borderRadius: moderateScale(8),
//     marginBottom: moderateVerticalScale(16),
//   },
//   buttonContent: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   buttonLoader: {
//     marginLeft: 8,
//   },
// });
