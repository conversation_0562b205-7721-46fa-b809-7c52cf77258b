import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  ActivityIndicator,
  Dimensions,
  Platform,
  LayoutChangeEvent
} from 'react-native';
import { Image } from 'expo-image';
import { colors } from '@/constants/colors';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import logger from '@/utils/logger/logger';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from 'react-i18next';

// Responsive sizing helper functions
const { width: SCREEN_WIDTH } = Dimensions.get('window');

type FoodEntryListProps = {
  onFoodPress: (food: any) => void;
  reports?: any[];
  tempEntry?: any;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      paddingBottom: moderateVerticalScale(16),
      width: "100%",
    },
    emptyContainer: {
      padding: moderateScale(24),
      alignItems: "center",
      justifyContent: "center",
    },
    emptyText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray.main,
      textAlign: "center",
    },
    dateGroup: {
      marginBottom: moderateVerticalScale(24),
      width: "100%",
    },
    dateHeader: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
      marginBottom: moderateVerticalScale(20),
      width: "95%",
      paddingHorizontal: moderateScale(16),
      alignSelf: "center",
    },
    dateText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray.main,
      textAlign: "center",
      marginBottom: moderateVerticalScale(10),
    },
    entriesGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "flex-start",
      alignItems: "flex-start",
      width: "100%",
      alignContent: "flex-start",
      paddingHorizontal: moderateScale(0),
    },
    entryContainer: {
      borderRadius: moderateScale(theme.radii.md),
      overflow: "hidden",
      backgroundColor: theme.colors.gray[100],
      position: "relative",
      minWidth: moderateScale(100),
      minHeight: moderateVerticalScale(100),
      alignSelf: "stretch",
    },
    foodImage: {
      width: "100%",
      height: "100%",
    },
    imageLoadingOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(255, 255, 255, 0.4)",
      justifyContent: "center",
      alignItems: "center",
    },
    loadingOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(255, 255, 255, 0.7)",
      justifyContent: "center",
      alignItems: "center",
    },
  });

const FoodEntryList = React.memo(({ onFoodPress, reports = [], tempEntry }: FoodEntryListProps) => {
  const { t } = useTranslation();
  // Track which images are loading
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});
  const [containerWidth, setContainerWidth] = useState(SCREEN_WIDTH);
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  
  // Constants for grid layout
  const ITEMS_PER_ROW = 3;
  // Responsive grid spacing based on screen width
  const GRID_SPACING = Math.max(8, Math.round(SCREEN_WIDTH * 0.02));
  
  // Handle container layout to get actual width and padding
  const handleLayout = useCallback((event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  }, []);
  
  // Calculate item dimensions for exactly 3 items per row
  const calculateItemWidth = useCallback(() => {
    // Use percentage of container width instead of fixed padding
    const availableWidth = containerWidth * 0.95; // 95% of container width
    // Adjust grid spacing for smaller screens
    const adjustedSpacing = Math.min(GRID_SPACING, Math.floor(availableWidth * 0.02));
    // Total space used by gaps between items
    const totalGapSpace = adjustedSpacing * (ITEMS_PER_ROW - 1);
    // Divide remaining space by number of items
    const width = Math.floor((availableWidth - totalGapSpace) / ITEMS_PER_ROW);
    // Ensure minimum width doesn't go below reasonable size
    return Math.max(width, Math.floor(availableWidth / 3.5));
  }, [containerWidth, GRID_SPACING, ITEMS_PER_ROW]);
  
  const itemWidth = useMemo(() => calculateItemWidth(), [calculateItemWidth]);
  // Filter reports to only include food items and transform fats to fat
  const foodReports = useMemo(() => 
    reports.filter(report => report.type === 'FOOD_ITEM')
      .map(report => {
        let updatedReport = { ...report };
        return updatedReport;
      })
  , [reports]);

  
  // Group reports by date - moved before conditional return to maintain hook order
  const groupedReports = useMemo(() => {
    const result = foodReports.reduce((acc, report) => {
      if (!report.timestamp) return acc;
      
      // Convert GMT timestamp to local date string
      const localDate = new Date(report.timestamp);
      const date = `${localDate.getFullYear()}-${String(localDate.getMonth() + 1).padStart(2, '0')}-${String(localDate.getDate()).padStart(2, '0')}`;
      
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(report);
      return acc;
    }, {});

    // Add temp entry to today's date if it exists
    if (tempEntry) {
      const now = new Date();
      const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      
      if (!result[today]) {
        result[today] = [];
      }
      result[today].unshift(tempEntry);
    }

    return result;
  }, [foodReports, tempEntry]);

  // Function to handle image loading state - moved before conditional return
  const handleImageLoadStart = useCallback((reportId: string) => {
    setLoadingImages(prev => ({ ...prev, [reportId]: true }));
  }, []);
  
  const handleImageLoadEnd = useCallback((reportId: string) => {
    setLoadingImages(prev => ({ ...prev, [reportId]: false }));
  }, []);
  
  // Define DateGroup type before all hooks
  type DateGroup = {
    date: string;
    reports: any[];
  };
  
  // Function to get the appropriate image URL (thumbnail or full) - moved before conditional return
  const getImageUrl = useCallback((report: any) => {
    // For temporary entries, use the local URI
    if (report.isTemp) {
      return report.url;
    }
    // Use thumbnail_url if available, otherwise fall back to the full url
    return report.thumbnail_url && report.thumbnail_status !== 'failed' 
      ? report.thumbnail_url 
      : report.url;
  }, []);
  
  // Convert to array and sort by date (newest first)
  const groupedReportsArray = useMemo(() => 
    Object.keys(groupedReports)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
      .map(date => ({
        date,
        reports: groupedReports[date]
      }))
  , [groupedReports]);
  
  // Define renderDateGroup before conditional return
  const renderDateGroup = useCallback((item: DateGroup) => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });

    return (
      <View style={styles.dateGroup} key={item.date}>
        <View style={styles.dateHeader}>
          <Text style={styles.dateText}>{formattedDate}</Text>
        </View>
        <View style={styles.entriesGrid}>
          {item.reports.map((report: any, index: number) => {
            const reportId = report.report_id || `report-${index}-${item.date}`;
            const isLoading = loadingImages[reportId] || false;
            const imageUrl = getImageUrl(report);
            
            return (
              <TouchableOpacity 
                key={reportId}
                style={[
                  styles.entryContainer,
                  { 
                    width: itemWidth,
                    height: itemWidth,
                    marginBottom: GRID_SPACING,
                    // Apply margin to all items except the last one in a row
                    marginRight: (index + 1) % ITEMS_PER_ROW !== 0 ? GRID_SPACING : 0,
                    // Special handling for single item case
                    ...(item.reports.length === 1 ? { alignSelf: 'flex-start' } : {})
                  }
                ]}
                onPress={() => !report.isTemp && onFoodPress(report)}
                disabled={report.isTemp}
              >
                {imageUrl && (
                  <>
                    <Image 
                      source={{ uri: imageUrl }} 
                      style={styles.foodImage} 
                      placeholder={require('@/assets/images/favicon.png')}
                      onLoadStart={() => handleImageLoadStart(reportId)}
                      onLoadEnd={() => handleImageLoadEnd(reportId)}
                      contentFit="cover"
                      transition={300}
                      cachePolicy="memory-disk"
                    />
                    {isLoading && (
                      <View style={styles.imageLoadingOverlay}>
                        <ActivityIndicator size="small" color={colors.primary} />
                      </View>
                    )}
                  </>
                )}
                
                {/* Show loading indicator for temporary entries */}
                {report.isTemp && (
                  <View style={styles.loadingOverlay}>
                    <ActivityIndicator size="large" color={colors.primary} />
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  }, [GRID_SPACING, ITEMS_PER_ROW, getImageUrl, handleImageLoadEnd, handleImageLoadStart, itemWidth, loadingImages, onFoodPress]);
  
  // If no entries, show empty state - moved after all hooks are called
  if (foodReports.length === 0 && !tempEntry) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>{t('foodEntry.emptyState')}</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container} onLayout={handleLayout}>
      {groupedReportsArray.map(item => renderDateGroup(item))}
    </View>
  );
});

// const styles = StyleSheet.create({
//   container: {
//     paddingBottom: moderateVerticalScale(16),
//     width: '100%',
//   },
//   emptyContainer: {
//     padding: moderateScale(24),
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   emptyText: {
//     fontSize: moderateScale(16),
//     color: colors.gray[500],
//     textAlign: 'center',
//   },
//   dateGroup: {
//     marginBottom: moderateVerticalScale(24),
//     width: '100%',
//   },
//   dateHeader: {
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//     marginBottom: moderateVerticalScale(20),
//     width: '95%',
//     paddingHorizontal: moderateScale(16),
//     alignSelf: 'center',
//   },
//   dateText: {
//     fontSize: moderateScale(14),
//     color: colors.gray[500],
//     textAlign: 'center',
//     marginBottom: moderateVerticalScale(10),
//   },
//   entriesGrid: {
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     justifyContent: 'flex-start',
//     alignItems: 'flex-start',
//     width: '100%',
//     alignContent: 'flex-start',
//     paddingHorizontal: moderateScale(4),
//   },
//   entryContainer: {
//     borderRadius: moderateScale(12),
//     overflow: 'hidden',
//     backgroundColor: colors.gray[100],
//     position: 'relative',
//     minWidth: moderateScale(100),
//     minHeight: moderateVerticalScale(100),
//     alignSelf: 'stretch',
//   },
//   foodImage: {
//     width: '100%',
//     height: '100%',
//   },
//   imageLoadingOverlay: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: 'rgba(255, 255, 255, 0.4)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   loadingOverlay: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: 'rgba(255, 255, 255, 0.7)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
// });

export default FoodEntryList;
