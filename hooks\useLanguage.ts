import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { languageService, Language, SUPPORTED_LANGUAGES } from '@/services/languageService';
import logger from '@/utils/logger/logger';

export interface UseLanguageReturn {
  currentLanguage: string;
  currentLanguageInfo: Language | undefined;
  isManuallySelected: boolean;
  isLoading: boolean;
  supportedLanguages: Language[];
  changeLanguage: (languageCode: string) => Promise<void>;
  resetToDeviceLanguage: () => Promise<void>;
  refreshLanguageState: () => Promise<void>;
}

/**
 * Custom hook for managing language state and operations
 */
export const useLanguage = (): UseLanguageReturn => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<string>(i18n.language || 'en');
  const [isManuallySelected, setIsManuallySelected] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Get current language info
  const currentLanguageInfo = languageService.getLanguageInfo(currentLanguage);

  /**
   * Refresh the language state from storage
   */
  const refreshLanguageState = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get current language and manual selection status
      const [activeLanguage, manuallySelected] = await Promise.all([
        languageService.getCurrentLanguage(),
        languageService.isLanguageManuallySelected(),
      ]);

      setCurrentLanguage(activeLanguage);
      setIsManuallySelected(manuallySelected);
      
      // Ensure i18n is in sync
      if (i18n.language !== activeLanguage) {
        await i18n.changeLanguage(activeLanguage);
      }
    } catch (error) {
      logger.error('Error refreshing language state:', error);
    } finally {
      setIsLoading(false);
    }
  }, [i18n]);

  /**
   * Change the app language
   */
  const changeLanguage = useCallback(async (languageCode: string) => {
    try {
      setIsLoading(true);
      
      await languageService.changeLanguage(languageCode);
      
      // Update local state
      setCurrentLanguage(languageCode);
      setIsManuallySelected(true);
      
      logger.info(`Language changed to: ${languageCode}`);
    } catch (error) {
      logger.error('Error changing language:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Reset to device language
   */
  const resetToDeviceLanguage = useCallback(async () => {
    try {
      setIsLoading(true);
      
      await languageService.resetToDeviceLanguage();
      
      // Update local state
      const deviceLanguage = languageService.getDeviceLanguage();
      setCurrentLanguage(deviceLanguage);
      setIsManuallySelected(false);
      
      logger.info(`Language reset to device language: ${deviceLanguage}`);
    } catch (error) {
      logger.error('Error resetting to device language:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize language state on mount
  useEffect(() => {
    refreshLanguageState();
  }, [refreshLanguageState]);

  // Listen to i18n language changes
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      if (lng !== currentLanguage) {
        setCurrentLanguage(lng);
      }
    };

    i18n.on('languageChanged', handleLanguageChange);
    
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n, currentLanguage]);

  return {
    currentLanguage,
    currentLanguageInfo,
    isManuallySelected,
    isLoading,
    supportedLanguages: SUPPORTED_LANGUAGES,
    changeLanguage,
    resetToDeviceLanguage,
    refreshLanguageState,
  };
};
