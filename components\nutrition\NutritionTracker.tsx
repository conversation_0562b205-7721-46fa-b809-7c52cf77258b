import React, { useMemo, useState } from 'react';
import { StyleSheet, View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { useNutritionStore, useDailyGoals } from '@/store/nutritionStore';
import getMacroPerDay from '@/utils/nutrition/getMacroPerDay';
import { Feather } from '@expo/vector-icons';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import logger from '@/utils/logger/logger';
import { Pen, Pencil } from "lucide-react-native"
import { parseNutritionalData } from '@/utils/nutrition/parseNutritionalData';
import UserInfoModal from './NutritionInfoModal';
import { useTranslation } from 'react-i18next';

type MacroCircleProps = {
  size: number;
  strokeWidth: number;
  color: string;
  backgroundColor: string;
  progress: number;
  value: number;
  unit: string;
  label: string;
  isExcess?: boolean;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    card: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.xl),
      padding: moderateScale(22),
      borderWidth:moderateScale(1),
      borderColor:colors.gray[300]
    },
    caloriesContainer: {
      alignItems: "center",
    },
    modalButton: {
      backgroundColor: colors.gray[100], 
      height: 36,
      width: 36,
      borderRadius: 18,
      alignItems: "center",
      justifyContent: "center",
      position:"absolute",
      top: moderateScale(16),
      right:moderateScale(16)
    },
    caloriesLabel: {
      fontSize: moderateScale(theme.fontSize.lg),
      color: colors.black,
      marginBottom: moderateVerticalScale(4),
      fontWeight: "600",
    },
    caloriesRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginVertical: moderateVerticalScale(8),
    },
    caloriesRemaining: {
      fontSize: moderateScale(theme.fontSize.md),
      color: "#666",
      marginTop: moderateVerticalScale(5),
    },
    divider: {
      height: 1,
      backgroundColor: "#EEEEEE",
      marginVertical: moderateVerticalScale(20),
    },
    macrosContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: moderateVerticalScale(20),
    },
    macroContainer: {
      alignItems: "center",
    },
    macroLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: "#666",
      marginTop: moderateVerticalScale(8),
    },
    excessLabel: {
      color: "#FF5757",
    },
    detailedMacros: {
      marginTop: moderateVerticalScale(5),
    },
    macroDetail: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: moderateVerticalScale(12),
      borderBottomWidth: 1,
      borderBottomColor: "#F0F0F0",
    },
    macroLabelContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    colorIndicator: {
      width: moderateScale(10),
      height: moderateScale(10),
      borderRadius: moderateScale(theme.radii.xs),
      marginRight: moderateScale(8),
    },
    macroDetailLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: "#333",
    },
    macroDetailValue: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: "#666",
    },
    chartContainer: {
      marginTop: moderateVerticalScale(24),
      marginBottom: moderateVerticalScale(24),
    },
  });

const MacroCircle = React.memo(({ 
  size, 
  strokeWidth, 
  color, 
  backgroundColor, 
  progress, 
  value, 
  unit, 
  label, 
  isExcess = false 
}: MacroCircleProps) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress * circumference);
  const circleColor = isExcess ? "#FF5757" : color;
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.macroContainer}>
      <Svg width={size} height={size}>
        {/* Background Circle */}
        <Circle
          stroke={backgroundColor}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
        />
        {/* Progress Circle */}
        <Circle
          stroke={circleColor}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
        {/* Value Text */}
        <SvgText
          x={size / 2}
          y={size / 2 - 5}
          fontSize="18"
          fontWeight="bold"
          fill="#333"
          textAnchor="middle"
        >
          {value}
        </SvgText>
        {/* Unit Text */}
        <SvgText
          x={size / 2}
          y={size / 2 + 15}
          fontSize="14"
          fill="#666"
          textAnchor="middle"
        >
          {unit}
        </SvgText>
      </Svg>
      <Text style={[styles.macroLabel, isExcess && styles.excessLabel]}>
        {/* {isExcess ? `Excess ${label}` : ((label=='Calories') ? `${label} consumed`: `${label} left`)}  */}
        {label}
      </Text>
    </View>
  );
});

type NutritionTrackerProps = {
  reports?: any[];
  nutritionData?: any;
};

const NutritionTracker = React.memo(({ reports = [], nutritionData }: NutritionTrackerProps) => {
  const { t } = useTranslation()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { profile } = useNutritionStore();
  const dailyGoals = useDailyGoals();
   const { theme } = useTheme();
   const styles = useMemo(() => createStyles(theme), [theme]);
   
   // Calculate target macros based on nutrition data if available
  const targetMacros = useMemo(() => {
    if (nutritionData) {
      // Check if explicit values are provided in nutritionData
      const hasExplicitValues = 
        nutritionData.dailyCalories || 
        nutritionData.dailyProtein || 
        nutritionData.dailyCarbohydrates || 
        nutritionData.dailyFats;
      
      if (hasExplicitValues) {
        // Use explicit values from nutritionData
        return {
          calories: parseInt(nutritionData.dailyCalories || '0'),
          macros: {
            protein: parseInt(nutritionData.dailyProtein || '0'),
            carbs: parseInt(nutritionData.dailyCarbohydrates || '0'),
            fat: parseInt(nutritionData.dailyFats || '0')
          }
        };
      }
      
      // Fall back to calculation if explicit values aren't provided
      return getMacroPerDay(nutritionData);
    }
    
    // Use dailyGoals if no nutritionData is provided
    return {
      calories: dailyGoals.calories,
      macros: {
        protein: dailyGoals.protein,
        carbs: dailyGoals.carbs,
        fat: dailyGoals.fat
      }
    };
  }, [nutritionData, dailyGoals]);  
  
  // Calculate today's consumption from reports
  const todayReports = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    return reports.filter(report => {
      if (!report.timestamp) return false;
      const reportDate = new Date(report.timestamp);
      const reportDateStr = reportDate.toISOString().split('T')[0];
      if (reportDateStr === today && report.type === 'FOOD_ITEM') return report;
    });
  }, [reports]);
  
  const consumed = useMemo(() => 
  todayReports.reduce((acc, report) => {
    // Use structured data directly instead of parsing from processed_output
    if (report.food_images && report.food_images.calories !== null && report.food_images.calories !== undefined && report.food_images.calories !== 0) {
      // Add main food item macros
      acc.calories += parseInt(report.food_images.calories || 0);
      acc.protein += parseInt(report.food_images.protein || 0);
      acc.carbs += parseInt(report.food_images.carbohydrates || 0);
      acc.fat += parseInt(report.food_images.fats || 0);
    }
    else if (report.processed_text || report.processed_output) {
      const processedText = report.processed_text || report.processed_output;
      if (processedText) {
        const parsedData = parseNutritionalData(processedText);
        
        if (parsedData && parsedData.length > 0) {
          parsedData.forEach((item: any) => {
            const label = item.label.toLowerCase();
            const valueMatch = item.value.match(/(\d+(\.\d+)?)/);
            const value = valueMatch ? parseInt(valueMatch[0]) : 0;
            
            if (label.includes('calories') || label.includes('total calories')) {
              acc.calories += value;
            } else if (label.includes('protein')) {
              acc.protein += value;
            } else if (label.includes('carb')) {
              acc.carbs += value;
            } else if (label.includes('fat')) {
              acc.fat += value;
            }
          });
        }
      }
    }
    
    return acc;
  }, { calories: 0, protein: 0, carbs: 0, fat: 0 })
, [todayReports]);

  
  const isExcess = useMemo(() => ({
    calories: consumed.calories > targetMacros.calories,
    protein: consumed.protein > targetMacros.macros.protein,
    carbs: consumed.carbs > targetMacros.macros.carbs,
    fat: consumed.fat > targetMacros.macros.fat,
  }), [consumed, targetMacros]);
  
  const difference = useMemo(() => ({
    calories: Math.abs(targetMacros.calories - consumed.calories),
    protein: Math.abs(targetMacros.macros.protein - consumed.protein),
    carbs: Math.abs(targetMacros.macros.carbs - consumed.carbs),
    fat: Math.abs(targetMacros.macros.fat - consumed.fat),
  }), [consumed, targetMacros]);
  
  // Calculate progress for the circular indicators
  const progress = useMemo(() => ({
    calories: Math.min(1, consumed.calories / targetMacros.calories),
    protein: Math.min(1, consumed.protein / targetMacros.macros.protein),
    carbs: Math.min(1, consumed.carbs / targetMacros.macros.carbs),
    fat: Math.min(1, consumed.fat / targetMacros.macros.fat),
  }), [consumed, targetMacros]);
  
  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <TouchableOpacity onPress={() => setIsModalOpen(true)} style={styles.modalButton}>
          <Pencil size={16} color="#15803d" hitSlop={20} />
        </TouchableOpacity>
        {/* Calories Section */}
        <View style={styles.caloriesContainer}>
          <Text style={styles.caloriesLabel}>{t('tracker.calories')}</Text>
          <View style={styles.caloriesRow}>
            <MacroCircle
              size={100}
              strokeWidth={8}
              color="#3A8A71"
              backgroundColor="#EEEEEE"
              progress={progress.calories}
              value={consumed.calories}
              unit="kcal"
              label={`${t('tracker.calories')} (${(progress.calories<=1) ? (progress.calories*100).toFixed(0)+"%" : t('tracker.excess')})`}
              isExcess={isExcess.calories}
            />
          </View>
          <Text style={styles.caloriesRemaining}>
            {difference.calories} {t('tracker.calories').toLowerCase()} {isExcess.calories ? t('tracker.excess') : t('tracker.remaining')}
          </Text>
        </View>
        
        {/* Divider */}
        <View style={styles.divider} />
        
        {/* Macros Section */}
        <View style={styles.macrosContainer}>
          <MacroCircle
            size={80}
            strokeWidth={7}
            color="#8D6E63"
            backgroundColor="#EEEEEE"
            progress={progress.protein}
            value={consumed.protein}
            unit="gm"
            label={`${t('tracker.protein')} (${(progress.protein<=1) ? (progress.protein*100).toFixed(0)+"%" : t('tracker.excess')})`}
            isExcess={isExcess.protein}
          />
          
          <MacroCircle
            size={80}
            strokeWidth={7}
            color="#F4A261"
            backgroundColor="#EEEEEE"
            progress={progress.carbs}
            value={consumed.carbs}
            unit="gm"
            label={`${t('tracker.carbs')} (${(progress.carbs<=1) ? (progress.carbs*100).toFixed(0)+"%" : t('tracker.excess')})`}
            isExcess={isExcess.carbs}
          />
          
          <MacroCircle
            size={80}
            strokeWidth={7}
            color="#FFD166"
            backgroundColor="#EEEEEE"
            progress={progress.fat}
            value={consumed.fat}
            unit="gm"
            label= {`${t('tracker.fat')} (${(progress.fat<=1) ? (progress.fat*100).toFixed(0)+"%" : t('tracker.excess')})`}
            isExcess={isExcess.fat}
          />
        </View>

        <UserInfoModal 
          isOpen={isModalOpen} 
          onClose={() => setIsModalOpen(false)} 
          userData={
            { 
              age : Number.parseInt(nutritionData.age) ,
              height: Number.parseInt(nutritionData.height),
              weight: Number.parseInt(nutritionData.weight),
              desiredWeight: Number.parseInt(nutritionData.desiredWeight),
              dailyProtein: Number.parseInt(nutritionData.dailyProtein) || targetMacros.macros.protein,
              dailyCarbohydrates: Number.parseInt(nutritionData.dailyCarbohydrates) || targetMacros.macros.carbs,
              dailyFats: Number.parseInt(nutritionData.dailyFats) || targetMacros.macros.fat
            }}
          />
        
        {/* Detailed Macros */}
        {/* <View style={styles.detailedMacros}>
          <View style={styles.macroDetail}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.colorIndicator, { backgroundColor: '#FF7A7A' }]} />
              <Text style={styles.macroDetailLabel}>Protein</Text>
            </View>
            <Text style={styles.macroDetailValue}>
              {consumed.protein}g / {targetMacros.macros.protein}g
            </Text>
          </View>
          
          <View style={styles.macroDetail}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.colorIndicator, { backgroundColor: '#E6A970' }]} />
              <Text style={styles.macroDetailLabel}>Carbs</Text>
            </View>
            <Text style={styles.macroDetailValue}>
              {consumed.carbs}g / {targetMacros.macros.carbs}g
            </Text>
          </View>
          
          <View style={styles.macroDetail}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.colorIndicator, { backgroundColor: '#5B9BD5' }]} />
              <Text style={styles.macroDetailLabel}>Fat</Text>
            </View>
            <Text style={styles.macroDetailValue}>
              {consumed.fat}g / {targetMacros.macros.fat}g
            </Text>
          </View>
        </View> */}
      </View>
    </View>
  );
});

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   card: {
//     backgroundColor: 'white',
//     borderRadius: moderateScale(20),
//     padding: moderateScale(22),
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.05,
//     shadowRadius: moderateScale(8),
//     elevation: 3,
//   },
//   caloriesContainer: {
//     alignItems: 'center',
//   },
//   caloriesLabel: {
//     fontSize: moderateScale(18),
//     color: colors.black,
//     marginBottom: moderateVerticalScale(4),
//     fontWeight: '600',
//   },
//   caloriesRow: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'center',
//     marginVertical: moderateVerticalScale(8),
//   },
//   caloriesRemaining: {
//     fontSize: moderateScale(15),
//     color: '#666',
//     marginTop: moderateVerticalScale(5),
//   },
//   divider: {
//     height: 1,
//     backgroundColor: '#EEEEEE',
//     marginVertical: moderateVerticalScale(20),
//   },
//   macrosContainer: {
//     flexDirection: 'row',
//     justifyContent: 'space-around',
//     marginBottom: moderateVerticalScale(20),
//   },
//   macroContainer: {
//     alignItems: 'center',
//   },
//   macroLabel: {
//     fontSize: moderateScale(13),
//     color: '#666',
//     marginTop: moderateVerticalScale(8),
//   },
//   excessLabel: {
//     color: '#FF5757',
//   },
//   detailedMacros: {
//     marginTop: moderateVerticalScale(5),
//   },
//   macroDetail: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     paddingVertical: moderateVerticalScale(12),
//     borderBottomWidth: 1,
//     borderBottomColor: '#F0F0F0',
//   },
//   macroLabelContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   colorIndicator: {
//     width: moderateScale(10),
//     height: moderateScale(10),
//     borderRadius: moderateScale(6),
//     marginRight: moderateScale(8),
//   },
//   macroDetailLabel: {
//     fontSize: moderateScale(15),
//     color: '#333',
//   },
//   macroDetailValue: {
//     fontSize: moderateScale(15),
//     color: '#666',
//   },
//   chartContainer: {
//     marginTop: moderateVerticalScale(24),
//     marginBottom: moderateVerticalScale(24),
//   },
// });

export default NutritionTracker;
