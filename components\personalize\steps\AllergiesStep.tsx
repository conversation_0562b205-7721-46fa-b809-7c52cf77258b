import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { AlertTriangle, ChevronDown, Plus, Minus } from "lucide-react-native";
import { useTranslation } from "react-i18next";
import { colors } from "@/constants/colors";
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import { useFormDataStore } from "@/store/formDataStore";
import logger from "@/utils/logger/logger";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface AllergiesStepProps {
  stepIndex: number;
  isActive?: boolean;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    content: { padding: moderateScale(16) },
    iconContainer: {
      width: moderateScale(64),
      height: moderateScale(64),
      borderRadius: moderateScale(theme.radii["4xl"]),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: scale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: scale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    allergyItem: {
      marginBottom: verticalScale(24),
      backgroundColor: theme.colors.gray[50],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
    },
    allergyHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: verticalScale(14),
    },
    label: {
      fontSize: scale(theme.fontSize.sm),
      fontWeight: "600",
      color: theme.colors.gray[800],
    },
    formField: { marginBottom: verticalScale(14) },
    fieldLabel: {
      fontSize: scale(theme.fontSize.xs),
      fontWeight: "500",
      marginBottom: verticalScale(8),
      color: theme.colors.gray[700],
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      fontSize: scale(theme.fontSize.sm),
    },
    selectInput: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(12),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    selectText: { fontSize: scale(theme.fontSize.sm), color: colors.black },
    placeholderText: {
      fontSize: scale(theme.fontSize.sm),
      color: theme.colors.gray[400],
    },
    dropdownOptionsContainer: {
      position: "absolute",
      top: moderateScale(60),
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      zIndex: 9999,
      maxHeight: moderateScale(116),
      elevation: 10,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    dropdownOption: {
      padding: moderateScale(14),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    dropdownOptionText: { fontSize: scale(14), color: colors.black },
    removeButton: {
      width: moderateScale(36),
      height: moderateScale(36),
      borderRadius: moderateScale(theme.radii.lg),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.secondary[50],
      marginBottom: verticalScale(16),
    },
    addButtonText: {
      color: theme.colors.primary.main,
      marginLeft: moderateScale(8),
      fontSize: scale(theme.fontSize.md),
      fontWeight: "500",
    },
    noAllergiesButton: { padding: moderateScale(14), alignItems: "center" },
    noAllergiesText: {
      color: theme.colors.gray[600],
      fontSize: scale(theme.fontSize.sm),
    },
  });

export const AllergiesStep: React.FC<AllergiesStepProps> = ({
  stepIndex,
  isActive = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const {
    formData,
    updateAllergies,
    currentStep,
    submitForm,
    updateCurrentStep,
  } = useFormDataStore();
  const [severityDropdowns, setSeverityDropdowns] = useState<
    Record<number, boolean>
  >({});

  const toggleSeverityDropdown = useCallback(
    (index: number) => {
      setSeverityDropdowns((prev) => ({
        ...prev,
        [index]: !prev[index],
      }));
      trackUserInteraction("Dropdown Toggle", {
        section: "allergies",
        field: "severity",
        index,
        state: !severityDropdowns[index] ? "open" : "closed",
      });
    },
    [severityDropdowns]
  );

  const addAllergy = useCallback(() => {
    updateAllergies([...formData.allergies, { name: "", severity: "" }]);
    trackUserInteraction("Form Item Added", {
      section: "allergies",
      itemType: "allergy",
      newCount: formData.allergies.length + 1,
    });
  }, [formData.allergies, updateAllergies]);

  const removeAllergy = useCallback(
    (index: number) => {
      if (formData.allergies.length > 1) {
        const removedAllergy = formData.allergies[index];
        const newAllergies = [...formData.allergies];
        newAllergies.splice(index, 1);
        updateAllergies(newAllergies);
        trackUserInteraction("Form Item Removed", {
          section: "allergies",
          itemType: "allergy",
          name: removedAllergy.name || "unnamed",
          newCount: formData.allergies.length - 1,
        });
      }
    },
    [formData.allergies, updateAllergies]
  );

  const updateAllergy = useCallback(
    (index: number, field: string, value: string) => {
      const newAllergies = [...formData.allergies];
      newAllergies[index] = { ...newAllergies[index], [field]: value };
      updateAllergies(newAllergies);
      if (value) {
        trackUserInteraction("Form Field Update", {
          section: "allergies",
          itemType: "allergy",
          field,
          index,
        });
      }
    },
    [formData.allergies, updateAllergies]
  );

  const handleNoAllergies = useCallback(async () => {
    updateAllergies([{ name: "No allergies", severity: "None" }]);
    trackUserInteraction("No Allergies Selected");
    const success = await submitForm();
    if (success) {
      logger.info("Save and Next Button Clicked and Data saved");
      updateCurrentStep(currentStep + 1);
      trackUserInteraction("Form Navigation", {
        action: "next",
        toStep: "Medications",
      });
    }
  }, [submitForm, updateAllergies, updateCurrentStep, currentStep, t]);

  const renderSeverityDropdown = (index: number) => {
    const options = [
      { key: 'mild', label: t('allergies.severity.mild') },
      { key: 'moderate', label: t('allergies.severity.moderate') },
      { key: 'severe', label: t('allergies.severity.severe') }
    ];
    const isOpen = severityDropdowns[index] || false;

    return (
      <View>
        <TouchableOpacity
          style={styles.selectInput}
          onPress={() => toggleSeverityDropdown(index)}
        >
          <Text
            style={
              formData.allergies[index].severity
                ? styles.selectText
                : styles.placeholderText
            }
          >
            {formData.allergies[index].severity || t('allergies.severity.placeholder')}
          </Text>
          {isOpen ? (
            <ChevronDown
              size={16}
              color={colors.gray[400]}
              style={{ transform: [{ rotate: "180deg" }] }}
            />
          ) : (
            <ChevronDown size={16} color={colors.gray[400]} />
          )}
        </TouchableOpacity>

        {isOpen && (
          <View style={styles.dropdownOptionsContainer}>
            <ScrollView
              showsVerticalScrollIndicator={true}
              nestedScrollEnabled={true}
            >
              {options.map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={styles.dropdownOption}
                  onPress={() => {
                    updateAllergy(index, "severity", option.label);
                    toggleSeverityDropdown(index);
                  }}
                >
                  <Text style={styles.dropdownOptionText}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
    );
  };

  if (!isActive) return null;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <AlertTriangle size={moderateScale(32)} color={colors.primary} />
      </View>

      <Text style={styles.title}>{t('allergies.title')}</Text>
      <Text style={styles.subtitle}>
        {t('allergies.subtitle')}
      </Text>

      {formData.allergies.map((allergy, index) => (
        <View key={`allergy-${index}`} style={styles.allergyItem}>
          <View style={styles.allergyHeader}>
            <Text style={styles.label}>{t('allergies.allergyIndex', { index: index + 1 })}</Text>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeAllergy(index)}
              disabled={formData.allergies.length === 1}
            >
              <Minus
                size={moderateScale(16)}
                color={
                  formData.allergies.length === 1
                    ? theme.colors.gray[300]
                    : theme.colors.gray.main
                }
              />
            </TouchableOpacity>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('allergies.name.question')}</Text>
            <TextInput
              style={styles.input}
              placeholder={t('allergies.name.placeholder')}
              value={allergy.name}
              onChangeText={(text) => updateAllergy(index, "name", text)}
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('allergies.severity.question')}</Text>
            {renderSeverityDropdown(index)}
          </View>
        </View>
      ))}

      <TouchableOpacity style={styles.addButton} onPress={addAllergy}>
        <Plus size={moderateScale(20)} color={colors.primary} />
        <Text style={styles.addButtonText}>{t('allergies.addButton')}</Text>
      </TouchableOpacity>

      {(formData.allergies.length === 1 ||
        formData.allergies[0].name === "") && (
        <TouchableOpacity
          style={styles.noAllergiesButton}
          onPress={handleNoAllergies}
        >
          <Text style={styles.noAllergiesText}>{t('allergies.noAllergiesButton')}</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
};

// const styles = StyleSheet.create({
//   container: { flex: 1 },
//   content: { padding: moderateScale(16) },
//   iconContainer: {
//     width: moderateScale(64),
//     height: moderateScale(64),
//     borderRadius: moderateScale(32),
//     backgroundColor: colors.primary + "20",
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: scale(20),
//     fontWeight: "700",
//     color:theme.colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: scale(14),
//     color:theme.colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   allergyItem: {
//     marginBottom: verticalScale(24),
//     backgroundColor:theme.colors.gray[50],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     borderWidth: 1,
//     borderColor:theme.colors.gray[200],
//   },
//   allergyHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginBottom: verticalScale(14),
//   },
//   label: {
//     fontSize: scale(16),
//     fontWeight: "600",
//     color:theme.colors.gray[800],
//   },
//   formField: { marginBottom: verticalScale(14) },
//   fieldLabel: {
//     fontSize: scale(12),
//     fontWeight: "500",
//     marginBottom: verticalScale(8),
//     color:theme.colors.gray[700],
//   },
//   input: {
//     borderWidth: 1,
//     borderColor:theme.colors.gray[300],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     backgroundColor: theme.colors.secondary[50],
//     fontSize: scale(14),
//   },
//   selectInput: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     backgroundColor: colors.white,
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//   },
//   selectText: { fontSize: scale(14), color: colors.black },
//   placeholderText: { fontSize: scale(14), color: colors.gray[400] },
//   dropdownOptionsContainer: {
//     position: "absolute",
//     top: moderateScale(60),
//     left: 0,
//     right: 0,
//     backgroundColor: colors.white,
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(12),
//     zIndex: 9999,
//     maxHeight: moderateScale(116),
//     elevation: 10,
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//   },
//   dropdownOption: {
//     padding: moderateScale(14),
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   dropdownOptionText: { fontSize: scale(14), color: colors.black },
//   removeButton: {
//     width: moderateScale(36),
//     height: moderateScale(36),
//     borderRadius: moderateScale(18),
//     backgroundColor: colors.gray[100],
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   addButton: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     backgroundColor: colors.white,
//     marginBottom: verticalScale(16),
//   },
//   addButtonText: {
//     color: colors.primary,
//     marginLeft: moderateScale(8),
//     fontSize: scale(16),
//     fontWeight: "500",
//   },
//   noAllergiesButton: { padding: moderateScale(14), alignItems: "center" },
//   noAllergiesText: { color: colors.gray[600], fontSize: scale(14) },
// });
