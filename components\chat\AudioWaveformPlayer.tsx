import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Text, Pressable, Platform } from 'react-native';
import { Audio, InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av';
import { Waveform, type IWaveformRef, PlayerState } from '@simform_solutions/react-native-audio-waveform';
import { Play, Pause, PlayIcon } from 'lucide-react-native';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { colors } from '@/constants/colors';
import logger from '@/utils/logger/logger';
import { useAudioStore } from '@/store/audioStore';
import { OperationStatus, trackOperation } from '@/utils/mixpanel/mixpanel-utils';

interface AudioWaveformPlayerProps {
  uri: string;  // Remote URL or local path
  path: string; // Path for waveform rendering
  messageId?: string; // For creating unique filenames
}

const AudioWaveformPlayer: React.FC<AudioWaveformPlayerProps> = ({ 
  uri, 
  path,
  messageId = 'unknown'
}) => {
  //console.log('AudioWaveformPlayer inputs:', { uri, path, messageId });
  
  const waveformRef = useRef<IWaveformRef>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playerState, setPlayerState] = useState<PlayerState>(PlayerState.stopped);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sampleAudioPath, setSampleAudioPath] = useState<string | null>(null);
  const [downloadedPath, setDownloadedPath] = useState<string | null>(null);

  // Check if path is a local file
  const isLocalFile = (filePath: string): boolean => {
    return filePath.startsWith('file://');
  };

  const VOICE_DIR = `${FileSystem.documentDirectory}voices/`;
  const downloadAudioToLocal = async (remoteUri: string, filename: string): Promise<string | null> => {
    try {
      // Ensure the "voices" directory exists
      const dirInfo = await FileSystem.getInfoAsync(VOICE_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(VOICE_DIR, { intermediates: true });
      }

      const fileUri = `${VOICE_DIR}${filename}`;
      const downloadResult = await FileSystem.downloadAsync(remoteUri, fileUri);

      return downloadResult.uri;
    } catch (error) {
      logger.error('Failed to download audio:', error);
      return null;
    }
  };

  // Load sample audio for waveform visualization
  useEffect(() => {
    const loadSampleAudio = async () => {
      try {
        const asset = Asset.fromModule(require('@/assets/audio/sample-audio.m4a'));
        await asset.downloadAsync();
        setSampleAudioPath(asset.localUri || asset.uri);
      } catch (err) {
        //console.error('Failed to load sample audio:', err);
      }
    };

    loadSampleAudio();
  }, []);

  // Load audio for playback
  useEffect(() => {
    let isMounted = true;
    
    const loadSound = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          interruptionModeIOS: InterruptionModeIOS.DoNotMix,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
          playThroughEarpieceAndroid: false,
        });

        // Only load sound if we have a local file path
        const audioPath = downloadedPath || path;
        if (!isLocalFile(audioPath)) {
          // Don't load sound yet - user needs to click play to download
          if (isMounted) setIsLoading(false);
          return;
        }

        const { sound: newSound } = await Audio.Sound.createAsync({ uri: audioPath });
        if (!isMounted) return;

        setSound(newSound);
        newSound.setOnPlaybackStatusUpdate((status) => {
          if (!status.isLoaded) return;
          setIsPlaying(status.isPlaying);

          if (status.didJustFinish) {
            setIsPlaying(false);
            setPlayerState(PlayerState.stopped);
            
        // Clear from store if this was the current sound
        const { currentPlayer, setCurrentPlayer } = useAudioStore.getState();
        if (currentPlayer?.sound === newSound) {
          setCurrentPlayer(null);
        }
          }
        });
      } catch (err) {
        //console.error('Failed to load audio:', err);
        if (isMounted) setError('Failed to load audio');
      } finally {
        if (isMounted) setIsLoading(false);
      }
    };

    loadSound();

    return () => {
      isMounted = false;
      
      // Clean up the sound and remove from store if needed
      if (sound) {
        sound.unloadAsync();
        
        // Clear from store if this was the current sound
        const { currentPlayer, setCurrentPlayer } = useAudioStore.getState();
        if (currentPlayer?.sound === sound) {
          setCurrentPlayer(null);
        }
      }
    };
  }, [path, downloadedPath]);

  function getAndroidWaveformPath(uri: string): string {
    if (Platform.OS === 'android' && uri.startsWith('file://')) {
      return uri.replace('file://', '');
    }
    return uri;
  }

  const handlePlayPress = async () => {
    const audioPath = downloadedPath || path;
    
    // If it's not a local file, download it first
    if (!isLocalFile(audioPath)) {
      setIsDownloading(true);
      try {
        const filename = `voice_${messageId}.m4a`;
        const localPath = await downloadAudioToLocal(uri, filename);
        
        if (localPath) {
          setDownloadedPath(localPath);
          // The useEffect will trigger and load the sound
        } else {
          setError('Failed to download audio');
        }
      } catch (err) {
        //console.error('Download error:', err);
        setError('Failed to download audio');
      } finally {
        setIsDownloading(false);
      }
      return;
    }

    // If we have a loaded sound, toggle playback
    if (sound) {
      toggleAudio();
    }
  };

  const toggleAudio = async () => {
    if (!sound) return;

    try {
      if (isPlaying) {
        await sound.pauseAsync();
        waveformRef.current?.pausePlayer();
      } else {
        // Get the current player from the store
        const { currentPlayer, setCurrentPlayer } = useAudioStore.getState();
        
        // If there's another sound playing and it's not this one, pause both sound and waveform
        if (currentPlayer && currentPlayer.sound !== sound) {
          await currentPlayer.sound.pauseAsync();
          currentPlayer.waveformRef?.current?.pausePlayer();
        }
        
        // Set this as the current player
        setCurrentPlayer({ sound, waveformRef });
        
        if (playerState === PlayerState.stopped) {
          await sound.replayAsync();
          waveformRef.current?.startPlayer();
        } else {
          await sound.playAsync();
          waveformRef.current?.startPlayer();
        }
      }
    } catch (err) {
      //console.error('Audio toggle error:', err);
    }
  };

  const onWaveformStateChange = async (newState: PlayerState) => {
    setPlayerState(newState);
    if (!sound) return;

    try {
      if (newState === PlayerState.playing && !isPlaying) {
        // Get the current player from the store
        const { currentPlayer, setCurrentPlayer } = useAudioStore.getState();
        
        // If there's another sound playing and it's not this one, pause both sound and waveform
        if (currentPlayer && currentPlayer.sound !== sound) {
          await currentPlayer.sound.pauseAsync();
          currentPlayer.waveformRef?.current?.pausePlayer();
        }
        
        // Set this as the current player
        setCurrentPlayer({ sound, waveformRef });
        
        await sound.playAsync();
      } else if (newState === PlayerState.paused && isPlaying) {
        await sound.pauseAsync();
      } else if (newState === PlayerState.stopped) {
        await sound.stopAsync();
      }
    } catch (err) {
      //console.error('Waveform sync error:', err);
    }
  };

  // Determine which path to use for waveform
  const getWaveformPath = (): string => {
    const audioPath = downloadedPath || path;
    
    // If we have a local file, use it
    if (isLocalFile(audioPath)) {
      return audioPath;
    }
    
    // Otherwise, use sample audio for waveform visualization
    return sampleAudioPath || audioPath;
  };

  // Show loading state
  if (isLoading || isDownloading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingText}>
          {isDownloading ? 'Downloading audio...' : 'Loading audio...'}
        </Text>
      </View>
    );
  }

  // Show error state
  if (error) {
    return <Text style={styles.errorText}>{error}</Text>;
  }

  const waveformPath = getWaveformPath();
  const audioPath = downloadedPath || path;
  const hasLocalAudio = isLocalFile(audioPath);

  return (
    <View style={styles.row}>
      <Pressable onPress={handlePlayPress} style={styles.iconButton}>
        <View style={styles.playButtonContainer}>
          {hasLocalAudio && isPlaying ? (
            <Pause size={24} color={colors.primary} />
          ) : (
            <Play color={colors.primary} size={24} />
          )}
        </View>
      </Pressable>
      <Waveform
        ref={waveformRef}
        mode="static"
        path={getAndroidWaveformPath(waveformPath)}
        candleSpace={2}
        candleWidth={2}
        waveColor="#B0B0B0"
        scrubColor="#075E54"
        containerStyle={styles.waveform}
        onPlayerStateChange={onWaveformStateChange}
        //@ts-ignore
        autoPlay={false}
        onError={(error) => { //console.error('Waveform error:', error, waveformPath)
          trackOperation('Waveform Playback', OperationStatus.FAILURE)
        }}
        onReady={() => {
          //console.log('Waveform is ready!', waveformRef.current);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  iconButton: {
    marginRight: 4,
    padding: 8,
  },
  playButtonContainer: {
    backgroundColor: '#fff',
    borderRadius: 24,
    padding: 6,
  },
  waveform: {
    flex: 1,
    height: 60,
    borderRadius: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    padding: 10,
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.primary,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    padding: 10,
  },
});

export default AudioWaveformPlayer;