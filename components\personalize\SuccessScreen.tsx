import type React from "react";
import { useEffect, useCallback, useMemo } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { CheckCircle2, ArrowRight } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import LottieView from "lottie-react-native";
import { useFormDataStore } from "@/store/formDataStore";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from "react-i18next";


const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
      padding: moderateScale(16),
      justifyContent: "center",
    },
    content: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    animationContainer: {
      width: moderateScale(200),
      height: moderateScale(200),
      marginBottom: verticalScale(8),
      flex: 1,
      alignItems: "center",
    },
    animation: {
      width: "120%",
      height: "120%",
    },
    title: {
      fontSize: scale(theme.fontSize['2xl']),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(12),
      textAlign: "center",
    },
    message: {
      fontSize: scale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
      lineHeight: scale(24),
    },
    benefitsContainer: {
      width: "100%",
      marginVertical: moderateScale(12),
    },
    benefitItem: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: verticalScale(12),
    },
    benefitText: {
      fontSize: scale(theme.fontSize.sm),
      color: theme.colors.gray[700],
      marginLeft: moderateScale(12),
    },
    continueButton: {
      backgroundColor: theme.colors.primary.main,
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      gap: moderateScale(8),
    },
    continueButtonText: {
      color: theme.colors.secondary[50],
      fontSize: scale(theme.fontSize.md),
      fontWeight: "600",
    },
  });


export const SuccessScreen: React.FC = () => {
  const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  const router = useRouter();
  const { updateCurrentStep } = useFormDataStore();

  useEffect(() => {
    trackUserInteraction("Success Screen Viewed");
  }, []);

  const handleContinue = useCallback(() => {
    trackUserInteraction("Success Screen Continue");
    updateCurrentStep(6);
    router.replace("/chat");
  }, [router, updateCurrentStep]);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.animationContainer}>
          <LottieView
            source={require("@/assets/animations/success.json")}
            autoPlay
            loop={false}
            style={styles.animation}
          />
        </View>

        <Text style={styles.title}>{t('success.title')}</Text>
        <Text style={styles.message}>
          {t('success.message')}
        </Text>

        <View style={styles.benefitsContainer}>
          {[
            t('success.benefits.insights'),
            t('success.benefits.reminders'),
            t('success.benefits.recommendations')
          ].map((benefit, index) => (
            <View key={index} style={styles.benefitItem}>
              <CheckCircle2 size={moderateScale(20)} color={colors.primary} />
              <Text style={styles.benefitText}>{benefit}</Text>
            </View>
          ))}
        </View>
      </View>

      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>{t('success.continueButton')}</Text>
        <ArrowRight size={moderateScale(20)} color={colors.white} />
      </TouchableOpacity>
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//     padding: moderateScale(16),
//     justifyContent: "center",
//   },
//   content: {
//     flex: 1,
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   animationContainer: {
//     width: moderateScale(200),
//     height: moderateScale(200),
//     marginBottom: verticalScale(8),
//     flex:1,
//     alignItems:"center",
//   },
//   animation: {
//     width: "120%",
//     height: "120%",
//   },
//   title: {
//     fontSize: scale(26),
//     fontWeight: "700",
//     color: colors.gray[800],
//     marginBottom: verticalScale(12),
//     textAlign: "center",
//   },
//   message: {
//     fontSize: scale(14),
//     color: colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//     lineHeight: scale(24),
//   },
//   benefitsContainer: {
//     width: "100%",
//     marginVertical:moderateScale(12),
//   },
//   benefitItem: {
//     flexDirection: "row",
//     alignItems: "center",
//     marginBottom: verticalScale(12),
//   },
//   benefitText: {
//     fontSize: scale(14),
//     color: colors.gray[700],
//     marginLeft: moderateScale(12),
//   },
//   continueButton: {
//     backgroundColor: colors.primary,
//     borderRadius: moderateScale(12),
//     padding: moderateScale(14),
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     gap: moderateScale(8),
//   },
//   continueButtonText: {
//     color: colors.white,
//     fontSize: scale(16),
//     fontWeight: "600",
//   },
// });
