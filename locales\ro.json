{"common": {"error": "Eroare", "yes": "Da", "no": "<PERSON>u", "sometimes": "Uneori", "close": "<PERSON><PERSON><PERSON>", "cancel": "Anulează", "save": "Salvează", "next": "Următorul", "loading": "Se încarcă...", "version": "v0.0.1.7"}, "welcome": "Conectează-te pentru a începe să discuți cu August", "notFound": {"title": "Ups!", "message": "Acest ecran nu există.", "goHome": "Mergi la ecranul de start!"}, "library": {"title": "Biblioteca de sănătate"}, "specialists": {"title": "Specialiști", "description": "Consultați specialiști din domeniul sănătății pentru probleme de sănătate specifice. Alegeți un specialist de mai jos:", "generalPhysician": {"title": "Medic generalist", "description": "Pentru probleme generale de sănătate și îngrijire primară."}, "nutritionist": {"title": "Nutriționist", "description": "Pentru sfaturi despre dietă, nutriție și managementul greutății."}, "cardiologist": {"title": "Cardiolog", "description": "Pentru probleme legate de inimă și sănătate cardiovasculară."}, "neurologist": {"title": "Neurolog", "description": "Pentru probleme legate de cre<PERSON>, măduva spinării și sistemul nervos."}, "oncologist": {"title": "Oncolog", "description": "Pentru probleme legate de cancer și tratamente."}, "endocrinologist": {"title": "Endocrinolog", "description": "Pentru tulburări hormonale și managementul diabetului."}, "dermatologist": {"title": "Dermatolog", "description": "Pentru afecțiuni ale pielii, părului și unghiilor."}, "psychiatrist": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Pentru probleme de sănătate mintală și bunăstare psihologică."}}, "profile": {"title": "Profil", "defaultName": "Invitat", "namePlaceholder": "Introduceți numele dumneavoastră", "saving": "Se salvează...", "noPhoneNumber": "Făr<PERSON> număr de telefon", "loggingOut": "Deconectare...", "about": {"title": "<PERSON><PERSON><PERSON>", "description": "Află mai multe despre August"}, "whatsapp": {"title": "WhatsApp", "description": "Discută cu August pe WhatsApp"}, "refer": {"title": "Recomandă", "description": "Ți-a plăcut August? Distribuie cu prietenii tăi"}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON> contul", "description": "Ne pare rău să te vedem plecând"}, "logout": {"title": "Deconectare", "description": "Revină curând. Ne vei lipsi"}, "shareMessage": "👋Hei, Uite această aplicație grozavă pe care o folosesc!\n\n\n\n➡️Folosesc August pentru a obține informații și îndrumare rapidă și fiabilă despre sănătate. Este ca și cum ai avea un doctor în buzunar! Verifică aici:", "error": {"loadFailed": "Eșec la încărcarea datelor utilizatorului", "fetchError": "A apărut o eroare la preluarea datelor utilizatorului", "updateNameFailed": "Eșec la actualizarea numelui", "updateNameError": "A apărut o eroare la actualizarea numelui", "loadFoodData": "Eșec la încărcarea datelor alimentare", "logoutError": "Eroare la deconectare:", "shareError": "Eroare la partajarea mesajelor:"}}, "error": {"title": "Ceva a mers greșit", "checkLogs": "Verificați jurnalele dispozitivului pentru mai multe detalii.", "unknown": "<PERSON><PERSON><PERSON>", "unknownFile": "<PERSON><PERSON>ier ne<PERSON>", "unknownLine": "<PERSON><PERSON>", "unknownColumn": "Coloană necunoscută"}, "auth": {"phone": {"selectCountry": "Selectează țara", "searchCountries": "<PERSON><PERSON><PERSON>", "validation": {"invalidPhone": "Te rugăm să introduci un număr de telefon valid", "invalidDigits": "Te rugăm să introduci un număr de telefon valid (7-15 cifre)"}}, "header": {"title": "Obține claritate instantaneu și privat asupra problemelor tale de sănătate", "subtitle": "Indrumare atentă. Fără grabă. Fără confuzie.", "emphasis": "<PERSON><PERSON> claritate."}, "greeting": "Salut 👋", "phoneNumber": "<PERSON><PERSON><PERSON><PERSON> telefon", "requestOTP": "Solicită OTP", "otp": {"title": "<PERSON><PERSON><PERSON>", "verify": "Verifică OTP", "sending": "Se trimite...", "countdown": "Retrimite OTP în {{countdown}}s", "resend": "Retrimite OTP", "sentTo": "OTP trimis către ", "whatsappSuffix": " pe WhatsApp"}, "disclaimer": {"prefix": "Înregistrându-te, ești de acord cu ", "continuePrefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ești de acord cu ", "termsOfService": "Termenii și condițiile", "and": " și ", "privacyPolicy": "Politica de confidențialitate", "whatsappConsent": ", și consimți să primești actualizări și mementouri de la noi prin WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Bun venit la August!", "buttonText": "Hai să începem"}}, "postAuth": {"step1": {"title": "Hei!", "subtitle": "<PERSON><PERSON> sunt August 👋", "description": "Gândește-te la mine ca la colțul confortabil de pe dispozitivul tău unde explorezi toate curiozitățile tale despre sănătate.", "subdescription": "Simte-te liber să întrebi orice îți trece prin minte.\nFără judecată, făr<PERSON> limite!", "placeholder": "Cum să te numesc?"}, "step2": {"title": "Salut {{userName}},", "subtitle": "Iată ce pot face:", "features": {"health": {"title": "Răspunde la", "subtitle": "Întrebările tale despre sănătate"}, "nutrition": {"title": "Urmărește-ți", "subtitle": "<PERSON><PERSON><PERSON><PERSON>"}, "reports": {"title": "Analizează", "subtitle": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "pills": {"thoughtful": "G<PERSON><PERSON><PERSON>", "careful": "<PERSON><PERSON>", "accurate": "Precis"}, "features": {"symptoms": {"title": "Verifică-<PERSON><PERSON> simpt<PERSON>le", "description": "Am greață de o săptămână. Ce mi se întâmplă?"}, "prescriptions": {"title": "Analizează-ți reț<PERSON>", "description": "Încarcă și înțelege rețetele ca un doctor."}, "medicine": {"title": "Cunoaște-ți medicamentele", "description": "Interacționează Metforminul pentru PCOS-ul meu cu pastilele mele pentru ADHD?"}, "plans": {"title": "<PERSON><PERSON><PERSON><PERSON> planuri personalizate", "description": "Poți să-mi dai un plan de nutriție și fitness pentru reducerea nivelului meu de HbA1c?"}}, "buttons": {"getStarted": "Începe", "next": "Următorul"}, "errors": {"nameRequired": "Te rugăm să introduci numele tău"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Descoperă", "nutrition": "Nutriție", "personalize": "Personalizează"}, "chat": {"nav": {"title": "August"}, "me": "Eu", "augustName": "August", "input": {"placeholder": "Întreabă pe August...", "disclaimer": "August poate face <PERSON><PERSON><PERSON><PERSON>. Consultă un medic pentru confirmare"}, "list": {"loadingMessages": "Se încarcă mesajele...", "noMessages": "Nu există încă mesaje. Începe o conversație!"}, "connection": {"offlineMessage": "Se pare că ești offline. Reconnectează-te pentru a trimite mesaje.", "connecting": "Conectare...", "tryAgain": "Încearcă din nou"}, "prompts": {"uploadReport": "Încarcă raportul", "speakInHindi": "Vorbește în hindi", "notFeelingWell": "Nu mă simt bine", "whatIsMyBMI": "Care este IMC-ul meu?", "nutritionAdvice": "Sfaturi nutriționale", "sleepBetter": "<PERSON><PERSON><PERSON> mai bine"}, "citations": {"referenceText": "Pentru mai multe detalii despre această conversație, te rugăm să consulti:"}, "actions": {"copiedToClipboard": "Copiat în clipboard", "copied": "Copiat"}, "share": {"introText": "👋 Salut, uite conversația pe care am avut-o cu August:\n\n", "downloadText": "\n\n➡️ Descarcă August pentru a discuta cu asistentul tău AI prietenos pentru sănătate:\n"}}, "discover": {"nav": {"title": "Descoperă"}, "categories": {"all": "Toate", "heartHealth": "Sănătatea inimii", "nutrition": "Nutriție", "mentalHealth": "Sănătate mintală", "fitness": "Fitness", "wellness": "Wellness"}, "cards": {"empty": "Nu există carduri disponibile pentru această categorie"}, "sections": {"features": "Funcții"}, "features": {"healthLibrary": {"title": "Bibliotecă de sănătate", "description": "Acces la informații medicale de încredere, fiabile și actualizate, complet gratuit."}, "nutritionTracker": {"title": "Urmărire nutrițională", "description": "Te-ai întrebat vreodată dacă ai putea încărca o fotografie a mâncării tale și să urmărești toate obiectivele tale nutriționale? August poate face asta!"}, "multilingualSupport": {"title": "Suport multilingv", "description": "Poți comunica cu August în orice limbă te simți confortabil! August este mereu aici pentru a te asculta, a te sprijini și a-ți răspunde oricând ai nevoie.", "samplePrompt": "Vorbește în hindi"}, "labReportAnalysis": {"title": "Analiză rapoarte de laborator", "description": "Când vorbești cu August despre rapoartele tale de laborator, obții o precizie extremă. August a procesat peste 4,7 milioane de rapoarte cu o acuratețe de extragere a biomarkerilor de 98,4%."}}}, "nutrition": {"nav": {"title": "Nutriție"}, "meals": {"title": "M<PERSON><PERSON><PERSON><PERSON><PERSON>e tale", "subtitle": "Atinge pentru a vedea macronutrienții din fiecare masă"}, "upload": {"loading": "Se încarcă imaginea..."}, "defaultFoodName": "Ali<PERSON>", "today": "<PERSON><PERSON><PERSON><PERSON>", "unknownTime": "Oră necunoscută", "calories": "🔥 Calorii", "proteins": "🥩 Proteine", "carbs": "🍞 Carbohi<PERSON><PERSON>i", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 <PERSON><PERSON><PERSON><PERSON><PERSON>", "caloriesLabel": "Calorii", "proteinLabel": "Proteine", "carbohydratesLabel": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fatLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Sfaturi:", "macroBreakdown": "Defalcare <PERSON>", "noMacroData": "Nu sunt disponibile date despre macronutrienți pentru acest aliment.", "disclaimer": "Doar pentru uz educațional. Află mai multe", "disclaimerLink": "aici", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Care este sexul tău?", "subtitle": "Această informație va fi folosită pentru a calibra planul tău personalizat.", "male": "Bărbat", "female": "<PERSON><PERSON><PERSON>", "other": "Altele"}, "age": {"title": "Câți ani ai?", "subtitle": "Această informație va fi folosită pentru a calcula necesarul tău zilnic."}, "measurements": {"title": "Înălțime & Greutate", "subtitle": "Te rugăm să introduci înălțimea în centimetri și greutatea în kilograme."}, "activity": {"title": "Nivel de activitate", "subtitle": "Cât de des faci exerciții fizice?", "none": "Fără exerciții", "moderate": "<PERSON>rat", "high": "Intens"}, "goal": {"title": "Obiectiv greutate", "subtitle": "Ce ți-ai dori să obții?", "increase": "Creștere", "maintain": "Menținere", "decrease": "Scădere"}, "targetWeight": {"title": "Greutate țintă", "subtitle": "Care este greutatea ta țintă în kilograme?"}, "setup": {"title": "Setarea planului tău", "subtitle": "Te rugăm să aștepți în timp ce pregătim planul tău nutrițional."}, "review": {"title": "Verifică-ți planul", "subtitle": "Verifică și personalizează-ți planul nutrițional."}, "height": {"label": "Înălțime (cm)"}, "weight": {"label": "Greutate (kg)"}}, "error": {"updateFailed": "Actualizarea datelor nutriționale a eșuat. Te rugăm să încerci din nou.", "parsingError": "Eroare la parsarea datelor alimentare:", "fetchReportsFailed": "Adunarea datelor rapoartelor a eșuat. Te rugăm să încerci din nou.", "missingReportId": "ID-ul raportului lipsește"}}, "personalize": {"nav": {"title": "Personalizare"}, "button": {"saving": "Se salvează", "review": "Verifică", "saveNext": "Salvează & Următorul"}}, "basicInfo": {"title": "Hai să te cunoaștem mai bine", "subtitle": "Aceste informații ne ajută să personalizăm recomandările tale de sănătate", "age": {"question": "Câți ani ai?", "placeholder": "Introdu vârsta ta"}, "sex": {"question": "Care este sexul tău?", "placeholder": "Selectează sexul tău", "male": "Bărbat", "female": "<PERSON><PERSON><PERSON>", "other": "Altele"}, "height": {"question": "Care este înălțimea ta? (cm)", "placeholder": "Introdu înălțimea ta"}, "weight": {"question": "Care este greutatea ta? (kg)", "placeholder": "Introdu greutatea ta"}}, "lifestyle": {"title": "Obiceiurile Dvs. de Viață", "subtitle": "Înțelegerea obiceiurilor zilnice ne ajută să vă oferim recomandări mai bune", "diet": {"question": "Ce tip de dietă urmați?", "placeholder": "Selectați dieta", "vegetarian": "Vegetarian", "nonVegetarian": "Non-Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Faceți exerciții fizice în mod regulat?"}, "drinking": {"question": "Consumați alcool?"}, "smoking": {"question": "Fumați?"}, "sleep": {"question": "Câte ore dormiți pe noapte?", "value": "{{sleep}} ore"}, "hydration": {"question": "<PERSON><PERSON><PERSON> căni de apă beți zilnic?", "value": "{{hydration}} <PERSON><PERSON><PERSON> ({{liters}}L)"}}, "allergies": {"title": "Aveți alergii?", "subtitle": "Cunoașterea alergiilor vă ajută să oferim recomandări mai sigure", "allergyIndex": "Alergie {{index}}", "name": {"question": "La ce sunteți alergic?", "placeholder": "Introduceți alergia (ex: Arahide, Praf)"}, "severity": {"question": "Cât de severă este această alergie?", "placeholder": "Selectați severitatea", "mild": "Ușoară", "moderate": "<PERSON><PERSON><PERSON>", "severe": "Severă"}, "addButton": "Adăugați o altă alergie", "noAllergiesButton": "Nu am alergii"}, "medications": {"title": "Medicamente și Suplimente", "subtitle": "Spuneți-ne despre orice medicamente sau suplimente pe care le luați în prezent", "medicationIndex": "Medicament {{index}}", "name": {"label": "Numele medicamentului", "placeholder": "Introduceți numele medicamentului"}, "startDate": {"question": "Când ați început să îl luați?", "placeholder": "Selectați data"}, "type": {"label": "Tipul de medicament", "shortTerm": "Pe termen scurt", "longTerm": "Pe termen lung"}, "dose": {"label": "Doză", "placeholder": "Cantitate"}, "unit": {"label": "Unitate"}, "frequency": {"label": "<PERSON>ec<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "perDay": "pe zi", "perWeek": "pe săptămână", "perMonth": "pe lună", "perYear": "pe an"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsp": "tsp", "tbsp": "tbsp", "cups": "<PERSON><PERSON><PERSON>"}, "addButton": "Adăugați un alt medicament", "noMedicationsButton": "Nu iau medicamente", "calendar": {"title": "Selectați data de început"}}, "conditions": {"title": "Afecțiuni Medicale", "subtitle": "Spuneți-ne despre orice afecțiuni medicale pe care le aveți sau le-ați avut în trecut", "conditionIndex": "Afecțiune {{index}}", "name": {"label": "Numele afecțiunii", "placeholder": "Introduceți afecțiunea (ex: Astm, etc)"}, "since": {"question": "De când aveți această afecțiune?", "placeholder": "Selectați data"}, "current": {"question": "Vă mai deranjează în prezent?"}, "medicated": {"question": "Luați medicamente pentru aceasta?"}, "addButton": "Adăugați o altă afecțiune", "noConditionsButton": "Nu am afecțiuni medicale", "calendar": {"title": "Selectați data"}}, "reproductive": {"title": "Sănătate Reproductivă", "subtitle": "Aceste informații ne ajută să vă oferim recomandări de sănătate mai personalizate", "menstruation": {"question": "Ați avut vreodată menstruație?", "detailsTitle": "Detalii Menstruație", "regularity": {"question": "Cât de regulat este ciclul dumneavoastră?", "regular": "<PERSON><PERSON><PERSON>", "irregular": "Irregular", "notSure": "<PERSON><PERSON> ș<PERSON>u"}, "cycleLength": {"label": "Lungimea medie a ciclului (zile)", "placeholder": "Introduceți lungimea ciclului"}, "flowDays": {"label": "Zile de flux: {{flowDays}}", "min": "1 zi", "max": "15 zile"}, "padsPerDay": {"label": "Absorbanți/tamponi pe zi: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Aveți simptome în timpul menstruației?", "placeholder": "Introduceți simptomele (de exemplu, crampe, dureri de cap)"}}, "childbirth": {"question": "Ați născut vreodată?", "detailsTitle": "<PERSON><PERSON><PERSON>", "children": {"label": "<PERSON><PERSON><PERSON><PERSON>ii"}, "pregnancies": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "complications": {"question": "Au existat complicații în timpul sarcinii sau nașterii?", "placeholder": "Introduceți complicațiile (dacă există)"}}}, "review": {"title": "Revizuiește-ți informațiile", "subtitle": "Te rugăm să revizuiești informațiile pe care le-ai furnizat înainte de a le trimite", "sections": {"basicInfo": "Informații de bază", "lifestyle": "Stil de viață", "allergies": "Alergi<PERSON>", "medications": "Medicamente și suplimente", "conditions": "Afecțiuni medicale", "reproductive": "Sănătate Reproductivă", "menstruationDetails": "Detalii Menstruație", "childbirthDetails": "<PERSON><PERSON><PERSON>"}, "fields": {"age": "Vârsta:", "sex": "Sexul:", "height": "Înălțimea:", "weight": "Greutatea:", "diet": "Dieta:", "exercise": "Exerciții fizice:", "drinking": "Consum de alcool:", "smoking": "Fumat:", "sleep": "Somn:", "hydration": "Hidratare:", "allergyIndex": "Alergie {{index}}:", "dose": "Doză:", "frequency": "Frecvență:", "type": "Tip:", "since": "<PERSON>:", "currentlyActive": "Activ în prezent:", "takingMedication": "Luați medicamente:", "hasMenstruated": "A avut menstruație:", "regularity": "Regularitate:", "cycleLength": "Lungimea ciclului:", "flowDays": "Zile de flux:", "padsPerDay": "Absorbanți/tamponi pe zi:", "hasChildbirth": "A născut:", "children": "Copii:", "pregnancies": "Sarcini:"}, "notProvided": "Neprovisionat", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} ore pe zi", "hydration": "{{hydration}} <PERSON><PERSON><PERSON> ({{liters}}L) pe zi", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} zile"}, "noData": {"allergies": "Nu există alergii furnizate", "medications": "Nu există medicamente furnizate", "conditions": "Nu există afecțiuni medicale furnizate"}, "submitButton": "Trimite informațiile"}, "success": {"title": "Informațiile au fost actualizate!", "message": "Vă mulțumim pentru informațiile despre sănătatea dumneavoastră. Le vom folosi pentru a vă personaliza experiența și pentru a vă oferi recomandări mai bune.", "benefits": {"insights": "Informații personalizate despre sănătate", "reminders": "Amintiri mai bune pentru medicamente", "recommendations": "Recomandări personalizate de sănătate"}, "continueButton": "Continuă la tabloul de bord"}, "permissions": {"microphonePermissionDenied": "Accesul la microfon a fost refuzat", "microphoneAccessDescription": "August necesită acces la microfonul dvs. pentru a înregistra audio și a trimite note vocale", "permissionDenied": "Acces refuzat", "cameraPermissionRequired": "Avem nevoie de permisiuni pentru cameră pentru a funcționa!", "mediaLibraryPermissionRequired": "Avem nevoie de permisiuni pentru biblioteca media pentru a funcționa!"}, "voiceRecording": {"recordingTooLong": "Înregistrare prea lungă", "recordingTooLongMessage": "Înregistrările vocale trebuie să fie mai scurte de 5 minute. Vă rugăm să înregistrați un mesaj mai scurt."}, "errors": {"uploadFailed": "Încărcare <PERSON>", "voiceUploadFailed": "Nu s-a putut încărca înregistrarea vocală.", "voiceRecordingFailed": "Eroare la trimiterea înregistrării vocale", "failedToStopRecording": "Eroare la oprirea înregistrării", "photoUploadFailed": "Nu s-a putut încărca fotografia.", "failedToTakePhoto": "Nu s-a putut face fotografia", "imageUploadFailed": "Nu s-a putut încărca imaginea: {{fileName}}", "failedToPickImage": "Nu s-a putut selecta imaginea", "documentUploadFailed": "Nu s-a putut încărca documentul: {{fileName}}", "failedToPickDocument": "Nu s-a putut selecta documentul"}, "audioPlayer": {"downloadingAudio": "Se descarcă audio...", "loadingAudio": "Se încarcă audio..."}, "mediaProcessing": {"processingFile": "Se procesează fișierul dvs.", "uploadingSecuring": "Se încarcă și se securizează fișierul...", "analyzingContent": "Se analizează conținutul documentului...", "extractingInfo": "Se extrag informațiile cheie...", "processingInsights": "Se procesează informațiile...", "preparingResponse": "Se pregătește răspunsul detaliat...", "finalizingResponse": "Se finalizează răspunsul..."}, "attachments": {"voiceMessage": "<PERSON><PERSON> vocal", "image": "[IMAGINE]", "pdf": "[PDF]", "voice": "[NOTĂ VOCALĂ]"}, "pdf": {"loadingPdf": "Se încarcă PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "selectate"}, "mediaUpload": {"photoLibrary": "Galerie foto", "takePhoto": "Fă o poză", "chooseFile": "Alege<PERSON><PERSON>"}, "comingSoon": {"title": "<PERSON><PERSON>ând!", "description": " este în curs de dezvoltare. Rămâneți conectați pentru actualizări!", "buttonText": "Am înțeles!"}, "clipboard": {"success": "Legătura a fost copiată în clipboard"}, "mediaPhotos": {"emptyState": "Nu există încă intrări."}, "foodDetail": {"defaultFoodName": "Ali<PERSON>", "nutrition": {"totalCalories": "Calorii totale", "proteins": "Proteine", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>", "fibers": "Fibre"}}, "reports": {"defaultTitle": "Element media", "defaultFoodName": "Ali<PERSON>", "defaultName": "Document", "openButton": "Deschideți în vizualizator extern", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "Valoare", "headerRefRange": "Interval de referință", "headerStatus": "Statut"}, "noData": "Nu sunt disponibile date despre biomarkeri"}, "setup": {"title": "Pregătim totul pentru dumneavoastră", "inProgress": "În progres...", "progressMessages": {"0": "Se calculează caloriile zilnice", "1": "Se optimizează repartizarea macro", "2": "Se creează planul de masă", "3": "Se calculează scorul de sănătate", "4": "Se finalizează configurarea"}, "checklistItems": {"0": "Se analizează datele dvs. de sănătate", "1": "Se calculează planul nutrițional optim", "2": "Se personalizează recomandările dvs.", "3": "Se creează sugestiile dvs. de masă", "4": "Se finalizează configurarea dvs."}}, "foodEntry": {"emptyState": "<PERSON>ci o intrare de mâncare încă. Fă o poză la masa ta pentru a o adăuga!"}, "nutritionReview": {"congratulations": "Felicitări!", "subtitle": "Planul tău nutrițional personalizat este gata.", "submitButton": "Hai să începem!", "dailyTargetsTitle": "Obiectivele tale zilnice de nutriție", "macroLabels": {"calories": "Calorii", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON>tein<PERSON>", "fats": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Cum să-ți atingi obiectivele:", "healthScores": "Folosește scorurile de sănătate pentru a-ți îmbunătăți rutina.", "trackFood": "Înregistrează-ți constant aportul alimentar.", "followCalories": "Urmați recomandarea zilnică de calorii", "balanceMacros": "Echilibrează-ți aportul de carbohidrați, proteine și grăsimi."}}, "editModal": {"titlePrefix": "Editează", "cancelButton": "Anulează", "saveButton": "Următorul"}, "processing": {"stages": {"scanning": "Scanează alimentele...", "identifying": "Identificarea ingredientelor...", "extracting": "Extragerea nutrienților...", "finalizing": "Finalizarea rezultatelor..."}, "error": {"defaultMessage": "Nu s-a detectat mâncare", "subtitle": "Încearcă un alt unghi."}, "retakeButton": "Atingeți pentru a relua fotografia", "notification": "Te vom notifica când vom fi gata!"}, "chart": {"title": "Urmărirea Nutriției în Timp", "selectNutrient": "Selectați nutrientul:", "emptyState": "Nu sunt încă disponibile date nutriționale.", "dropdown": {"calories": "Calorii", "protein": "<PERSON>tein<PERSON>", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "Produs alimentar", "defaultDate": "<PERSON><PERSON>", "defaultTime": "<PERSON><PERSON>", "saveChanges": "Salvează modificările", "error": {"title": "Eroare", "message": "Nu s-a putut actualiza informațiile nutriționale. Vă rugăm să încercați din nou."}, "nutrition": {"calories": "🔥 Calorii", "proteins": "🥩 Proteine", "carbs": "🍞 Carbohi<PERSON><PERSON>i", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "macroBreakdown": {"title": "Defalcarea macronutrienților", "noData": "Nu sunt disponibile date despre macronutrienți pentru acest aliment."}, "macroLabels": {"calories": "Calorii", "protein": "<PERSON>tein<PERSON>", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Informații Detaliate", "edit": "Editează", "save": "Salvează", "saving": "<PERSON><PERSON><PERSON>...", "enterValue": "Introduceți valoarea", "notSet": "Nesetat", "age": "<PERSON><PERSON><PERSON><PERSON>", "heightCm": "Înălțime (cm)", "weightKg": "Greutate (kg)", "targetWeight": "Greutate țintă", "nutritionTargets": "Obiective Nutriționale", "protein": "<PERSON>tein<PERSON>", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Introduceți valori sau lăsați necompletat pentru calcul automat.", "autoCalculateNote": "Macro-u<PERSON><PERSON> sunt calculate automat pe baza datelor dumneavoastră.", "validation": {"ageMin": "Vârsta trebuie să fie de cel puțin 18 ani.", "ageMax": "Vârsta trebuie să fie sub 125", "heightMin": "Înălțimea trebuie să fie de cel puțin 50 cm", "heightMax": "Înălțimea trebuie să fie sub 250 cm", "weightMin": "Greutatea trebuie să fie de cel puțin 30 kg", "weightMax": "Greutatea trebuie să fie sub 500 kg", "targetWeightMin": "Greutatea țintă trebuie să fie de cel puțin 30 kg", "targetWeightMax": "Greutatea țintă trebuie să fie sub 500 kg", "proteinMin": "Proteina trebuie să fie 0 sau mai mult.", "carbsMin": "Carbohidrații trebuie să fie 0 sau mai mult.", "fatsMin": "Grăsimile trebuie să fie 0 sau mai mult."}}, "tracker": {"calories": "Calorii", "protein": "<PERSON>tein<PERSON>", "carbs": "Carbo<PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "excess": "exces", "remaining": "restante"}, "specialistConstants": {"nutritionist": {"name": "Nutriționist", "description": "Sfaturi experte despre dietă, nutriție și obiceiuri alimentare sănătoase", "featureName": "Specialist <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cardiologist": {"name": "Cardiolog", "description": "Specializat în sănătatea inimii și afecțiunile cardiovasculare", "featureName": "Specialist Card<PERSON>ogie"}, "neurologist": {"name": "Neurolog", "description": "Specializat în afecțiuni ale creierului, măduvei spinării și sistemului nervos", "featureName": "Specialist Neurologie"}, "oncologist": {"name": "Oncolog", "description": "Specializat în diagnosticarea și opțiunile de tratament pentru cancer", "featureName": "Specialist Oncologie"}, "endocrinologist": {"name": "Endocrinolog", "description": "Expert în afecțiuni hormonale și tulburări metabolice", "featureName": "Specialist Endocrinologie"}}, "discoverCards": {"categories": {"nutrition": "Nutriție", "heartHealth": "Sănătatea inimii", "mentalHealth": "Sănătate mintală", "fitness": "Fitness", "wellness": "Wellness"}, "titles": {"vitaminB12Recovery": "<PERSON><PERSON><PERSON> timp durează recuperarea după o deficiență de vitamina B12", "vitaminDeficiencyGanglion": "Care deficiență de vitamine provoacă chisturi ganglionare", "vitaminDeficiencyHairFall": "Care deficiență de vitamine provoacă căderea părului", "vitaminWaters": "Sunt apele vitaminizate bune pentru dumneavoastră", "cholesterolHeadaches": "Poate colesterolul ridicat provoca dureri de cap", "cholesterolEyes": "Care sunt simptomele colesterolului ridicat care pot fi observate la ochi", "diabetesHeadaches": "Poate diabetul provoca dureri de cap", "chestPainDrinking": "De ce mă doare pieptul după ce beau", "stressDizziness": "Poate stresul provoca amețeli", "bulimiaFace": "Ce este fața de bulimie", "kneeTwitch": "De ce îmi tresare genunchiul", "noseTwitching": "De ce apare tresărarea nasului", "piriformisVsSciatica": "Care sunt diferențele dintre sindromul piriformis și sciatică", "shoulderBladePinched": "Cum să eliberați un nerv prins în omoplat", "shoulderPinched": "Cum să eliberați un nerv prins în umăr", "meniscusTorn": "Cum să vindecați natural o ruptură de menisc", "hydrateQuickly": "Cum să vă hidratați rapid", "periodConstipation": "Este normal să aveți constipație în timpul menstruației", "acneScars": "Cum să scăpați de cicatricile de acnee în mod natural într-o săptămână", "perimenopausePregnancy": "Puteți rămâne gravidă în timpul perimenopauzei"}, "descriptions": {"vitaminB12Recovery": "Descoperă cronologia recuperării în cazul deficienței de vitamina B12 și remedii eficiente pentru a-ți crește nivelul de energie.", "vitaminDeficiencyGanglion": "Explorează legătura dintre deficiențele de vitamine și dezvoltarea chisturilor ganglionare în corp.", "vitaminDeficiencyHairFall": "Află cum lipsa vitaminelor esențiale poate duce la căderea părului și ce poți face pentru a preveni acest lucru.", "vitaminWaters": "Descoperă beneficiile și potențialele dezavantaje ale apelor vitaminate ca parte a nutriției tale zilnice.", "cholesterolHeadaches": "Examinează posibila legătură dintre nivelurile ridicate de colesterol și apariția durerilor de cap.", "cholesterolEyes": "Află cum se poate manifesta colesterolul ridicat în ochi și ce simptome trebuie să urmărești.", "diabetesHeadaches": "Investighează relația dintre diabet și apariția durerilor de cap în viața de zi cu zi.", "chestPainDrinking": "Explorează motivele din spatele durerilor în piept după consumul anumitor b<PERSON>uturi.", "stressDizziness": "Află cum stresul îți poate afecta echilibrul și starea generală de bine, ducând la amețeli.", "bulimiaFace": "Înțelege semnele fizice ale bulimiei, inclusiv efectele asupra aspectului facial.", "kneeTwitch": "Investighează potențialele cauze ale contracțiilor involuntare ale genunchiului și relația acestora cu stresul sau oboseala.", "noseTwitching": "Află despre posibilele motive ale tresăririi nasului și legătura acestuia cu anxietatea sau alți factori.", "piriformisVsSciatica": "Compară simptomele sindromului piriformis și ale sciaticii pentru a înțelege mai bine afecțiunea ta.", "shoulderBladePinched": "Descoperă tehnici eficiente pentru a ameliora un nerv prins în omoplat și pentru a-ți restabili mobilitatea.", "shoulderPinched": "Învață exerciții și întinderi simple pentru a ameliora compresia nervoasă în zona umărului.", "meniscusTorn": "Explorează metode naturale și exerciții pentru a susține vindecarea unui menisc rupt.", "hydrateQuickly": "Descoperă modalități rapide și eficiente de a te rehidrata și de a menține o hidratare optimă a corpului.", "periodConstipation": "Înțelege motivele constipației în timpul menstruației și află remedii naturale.", "acneScars": "Descoperă remedii naturale și sfaturi pentru îngrijirea pielii pentru a reduce aspectul cicatricilor de acnee rapid.", "perimenopausePregnancy": "Află despre perimeno<PERSON>uz<PERSON>, considerațiile legate de fertilitate și ce te așteaptă în această etapă a vieții."}}}