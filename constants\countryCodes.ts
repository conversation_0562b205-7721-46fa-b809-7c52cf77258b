export const countryCodes = [
  {
    name: "Argentina",
    dial_code: "+54",
    code: "AR",
    flag: "🇦🇷"
  },
  {
    name: "Australia",
    dial_code: "+61",
    code: "AU",
    flag: "🇦🇺"
  },
  {
    name: "Bahrain",
    dial_code: "+973",
    code: "BH",
    flag: "🇧🇭"
  },
  {
    name: "Belarus",
    dial_code: "+375",
    code: "BY",
    flag: "🇧🇾"
  },
  {
    name: "Belgium",
    dial_code: "+32",
    code: "BE",
    flag: "🇧🇪"
  },
  {
    name: "Brazil",
    dial_code: "+55",
    code: "BR",
    flag: "🇧🇷"
  },
  {
    name: "Cambodia",
    dial_code: "+855",
    code: "KH",
    flag: "🇰🇭"
  },
  {
    name: "Canada",
    dial_code: "+1",
    code: "CA",
    flag: "🇨🇦"
  },
  {
    name: "Chile",
    dial_code: "+56",
    code: "CL",
    flag: "🇨🇱"
  },
  {
    name: "China",
    dial_code: "+86",
    code: "CN",
    flag: "🇨🇳"
  },
  {
    name: "Colombia",
    dial_code: "+57",
    code: "CO",
    flag: "🇨🇴"
  },
  {
    name: "Denmark",
    dial_code: "+45",
    code: "DK",
    flag: "🇩🇰"
  },
  {
    name: "Finland",
    dial_code: "+358",
    code: "FI",
    flag: "🇫🇮"
  },
  {
    name: "France",
    dial_code: "+33",
    code: "FR",
    flag: "🇫🇷"
  },
  {
    name: "Germany",
    dial_code: "+49",
    code: "DE",
    flag: "🇩🇪"
  },
  {
    name: "Greece",
    dial_code: "+30",
    code: "GR",
    flag: "🇬🇷"
  },
  {
    name: "Hong Kong",
    dial_code: "+852",
    code: "HK",
    flag: "🇭🇰"
  },
  {
    name: "Hungary",
    dial_code: "+36",
    code: "HU",
    flag: "🇭🇺"
  },
  {
    name: "India",
    dial_code: "+91",
    code: "IN",
    flag: "🇮🇳"
  },
  {
    name: "Indonesia",
    dial_code: "+62",
    code: "ID",
    flag: "🇮🇩"
  },
  {
    name: "Ireland",
    dial_code: "+353",
    code: "IE",
    flag: "🇮🇪"
  },
  {
    name: "Italy",
    dial_code: "+39",
    code: "IT",
    flag: "🇮🇹"
  },
  {
    name: "Japan",
    dial_code: "+81",
    code: "JP",
    flag: "🇯🇵"
  },
  {
    name: "Korea, Republic of",
    dial_code: "+82",
    code: "KR",
    flag: "🇰🇷"
  },
  {
    name: "Kuwait",
    dial_code: "+965",
    code: "KW",
    flag: "🇰🇼"
  },
  {
    name: "Lithuania",
    dial_code: "+370",
    code: "LT",
    flag: "🇱🇹"
  },
  {
    name: "Luxembourg",
    dial_code: "+352",
    code: "LU",
    flag: "🇱🇺"
  },
  {
    name: "Malaysia",
    dial_code: "+60",
    code: "MY",
    flag: "🇲🇾"
  },
  {
    name: "Mexico",
    dial_code: "+52",
    code: "MX",
    flag: "🇲🇽"
  },
  {
    name: "Monaco",
    dial_code: "+377",
    code: "MC",
    flag: "🇲🇨"
  },
  {
    name: "Netherlands",
    dial_code: "+31",
    code: "NL",
    flag: "🇳🇱"
  },
  {
    name: "New Zealand",
    dial_code: "+64",
    code: "NZ",
    flag: "🇳🇿"
  },
  {
    name: "Norway",
    dial_code: "+47",
    code: "NO",
    flag: "🇳🇴"
  },
  {
    name: "Paraguay",
    dial_code: "+595",
    code: "PY",
    flag: "🇵🇾"
  },
  {
    name: "Peru",
    dial_code: "+51",
    code: "PE",
    flag: "🇵🇪"
  },
  {
    name: "Philippines",
    dial_code: "+63",
    code: "PH",
    flag: "🇵🇭"
  },
  {
    name: "Poland",
    dial_code: "+48",
    code: "PL",
    flag: "🇵🇱"
  },
  {
    name: "Portugal",
    dial_code: "+351",
    code: "PT",
    flag: "🇵🇹"
  },
  {
    name: "Qatar",
    dial_code: "+974",
    code: "QA",
    flag: "🇶🇦"
  },
  {
    name: "Saudi Arabia",
    dial_code: "+966",
    code: "SA",
    flag: "🇸🇦"
  },
  {
    name: "Singapore",
    dial_code: "+65",
    code: "SG",
    flag: "🇸🇬"
  },
  {
    name: "Spain",
    dial_code: "+34",
    code: "ES",
    flag: "🇪🇸"
  },
  {
    name: "Sweden",
    dial_code: "+46",
    code: "SE",
    flag: "🇸🇪"
  },
  {
    name: "Switzerland",
    dial_code: "+41",
    code: "CH",
    flag: "🇨🇭"
  },
  {
    name: "Taiwan",
    dial_code: "+886",
    code: "TW",
    flag: "🇹🇼"
  },
  {
    name: "Thailand",
    dial_code: "+66",
    code: "TH",
    flag: "🇹🇭"
  },
  {
    name: "Turkey",
    dial_code: "+90",
    code: "TR",
    flag: "🇹🇷"
  },
  {
    name: "United Arab Emirates",
    dial_code: "+971",
    code: "AE",
    flag: "🇦🇪"
  },
  {
    name: "United Kingdom",
    dial_code: "+44",
    code: "GB",
    flag: "🇬🇧"
  },
  {
    name: "United States",
    dial_code: "+1",
    code: "US",
    flag: "🇺🇸"
  },
  {
    name: "Vietnam",
    dial_code: "+84",
    code: "VN",
    flag: "🇻🇳"
  },
];