import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { colors } from '@/constants/colors';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';

const WaitingResponseIndicator = () => {
  const [isVisible, setIsVisible] = useState(false); // State to control visibility

  // Create animated values for each dot
  const dot1Animation = useRef(new Animated.Value(0)).current;
  const dot2Animation = useRef(new Animated.Value(0)).current;
  const dot3Animation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Set a timeout to delay showing the component
    setIsVisible(true)
    // Set a timeout to hide the component after 30 seconds
    const hideTimeout = setTimeout(() => setIsVisible(false), 30000);
  
    return () => {
      // Cleanup timeouts on unmount
      clearTimeout(hideTimeout);
    }
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    // Create a single dot animation that can be reused with delays
    const animateDot = (dot: Animated.Value, delay: number) => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(dot, {
            toValue: 1,
            duration: 300,
            delay: delay,
            useNativeDriver: true,
          }),
          Animated.timing(dot, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    // Start the same animation for each dot with staggered delays
    animateDot(dot1Animation, 0);
    animateDot(dot2Animation, 100);
    animateDot(dot3Animation, 200);

    return () => {
      // Clean up animations
      dot1Animation.stopAnimation();
      dot2Animation.stopAnimation();
      dot3Animation.stopAnimation();
    };
  }, [isVisible]);

  if (!isVisible) return null; // Hide component before delay completes

  return (
    <View style={styles.container}>
      <View style={styles.bubble}>
        <View style={styles.dotsContainer}>
          <Animated.View
            style={[
              styles.dot,
              {
                transform: [
                  {
                    translateY: dot1Animation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -6],
                    }),
                  },
                ],
              },
            ]}
          />
          <Animated.View
            style={[
              styles.dot,
              {
                transform: [
                  {
                    translateY: dot2Animation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -6],
                    }),
                  },
                ],
              },
            ]}
          />
          <Animated.View
            style={[
              styles.dot,
              {
                transform: [
                  {
                    translateY: dot3Animation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -6],
                    }),
                  },
                ],
              },
            ]}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(16),
    marginBottom: moderateVerticalScale(16),
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateVerticalScale(12),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: moderateScale(20),
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: moderateVerticalScale(20),
    justifyContent: 'center',
  },
  dot: {
    width: moderateScale(5),
    height: moderateScale(5),
    borderRadius: moderateScale(3.5),
    backgroundColor: colors.gray[500],
    marginHorizontal: moderateScale(3),
  },
});

export default WaitingResponseIndicator;
