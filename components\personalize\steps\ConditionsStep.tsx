import type React from "react"
import { useState, useCallback, useMemo, useRef } from "react"
import { View, Text, TextInput, TouchableOpacity, ScrollView, Modal, StyleSheet, PanResponder, Animated, Dimensions } from "react-native"
import { Plus, Minus, Calendar, X, Stethoscope } from "lucide-react-native"
import { Calendar as CalendarPicker } from "react-native-calendars"
import { useTranslation } from "react-i18next";
import { moderateScale, scale, verticalScale, moderateVerticalScale } from "react-native-size-matters"
import { colors } from "@/constants/colors"
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils"
import { useFormDataStore } from "@/store/formDataStore"
import logger from "@/utils/logger/logger"
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface ConditionsStepProps {
  stepIndex: number
  isActive?: boolean
}
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.secondary[50] },
    content: { padding: scale(16) },
    iconContainer: {
      width: scale(64),
      height: scale(64),
      borderRadius: scale(theme.radii['4xl']),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    conditionItem: {
      marginBottom: verticalScale(24),
      padding: scale(14),
      backgroundColor: theme.colors.gray[50],
      borderRadius: scale(theme.radii.md),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
    },
    conditionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: verticalScale(12),
    },
    label: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: colors.black,
    },
    removeButton: { padding: scale(4) },
    formField: { marginBottom: verticalScale(12) },
    fieldLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      color: theme.colors.gray[700],
      marginBottom: verticalScale(6),
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      padding: scale(14),
      backgroundColor: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
    },
    dateInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      paddingHorizontal: scale(10),
      height: verticalScale(53),
      backgroundColor: theme.colors.secondary[50],
    },
    dateInput: { flex: 1, color: colors.black },
    yesNoButtonGroup: {
      flexDirection: "row",
      borderRadius: scale(theme.radii.md),
      overflow: "hidden",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
    },
    yesNoButton: {
      flex: 1,
      paddingVertical: verticalScale(14),
      alignItems: "center",
      backgroundColor: theme.colors.secondary[50],
    },
    yesNoButtonLeft: { borderRightWidth: 1, borderColor: theme.colors.gray[300] },
    yesNoButtonRight: {},
    activeYesNoButton: { backgroundColor: theme.colors.primary.main },
    yesNoButtonText: {
      color: theme.colors.gray[700],
      fontWeight: "500",
    },
    activeYesNoButtonText: {
      color: theme.colors.secondary[50],
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginVertical: verticalScale(20),
    },
    addButtonText: {
      color: theme.colors.primary.main,
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      marginLeft: scale(8),
    },
    noConditionsButton: { alignSelf: "center", marginTop: verticalScale(8) },
    noConditionsText: { color: theme.colors.gray[600] },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.4)",
      justifyContent: "center",
      alignItems: "center",
      padding: scale(14),
    },
    calendarContainer: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: scale(theme.radii.md),
      padding: scale(14),
      width: "100%",
    },
    calendarHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: verticalScale(12),
    },
    calendarTitle: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: colors.black,
    },
    closeButton: { padding: scale(4) }
  });


export const ConditionsStep: React.FC<ConditionsStepProps> = ({ isActive = false }) => {
  const { t } = useTranslation();
  const { formData, updateConditions, submitForm, currentStep, updateCurrentStep, isLoading } = useFormDataStore()

  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  
  const { height: SCREEN_HEIGHT } = Dimensions.get('window');
  const DISMISS_THRESHOLD = SCREEN_HEIGHT * 0.15; // 15% of screen height
  const VELOCITY_THRESHOLD = 0.7; // Velocity threshold for quick flick

  const [calendarVisible, setCalendarVisible] = useState(false)
  const [activeConditionIndex, setActiveConditionIndex] = useState<number | null>(null)
  const [isDragging, setIsDragging] = useState(false);
  const translateY = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  // Create PanResponder to handle swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to significant vertical movement
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
        translateY.setValue(0);
        translateY.setOffset(0);
      },
      onPanResponderMove: (_, gestureState) => {
        // Only allow downward movement (positive dy)
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
          
          // Calculate progress for scale and opacity
          const progress = Math.min(Math.abs(gestureState.dy) / (SCREEN_HEIGHT * 0.5), 1);
          scaleValue.setValue(1 - (0.1 * progress));
          opacityValue.setValue(1 - (0.5 * progress));
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        setIsDragging(false);
        
        // Determine if we should dismiss based on distance or velocity
        const shouldDismiss =
          gestureState.dy > DISMISS_THRESHOLD ||
          gestureState.vy > VELOCITY_THRESHOLD;

        if (shouldDismiss) {
          // Animate to bottom of screen and close
          Animated.timing(translateY, {
            toValue: SCREEN_HEIGHT,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            closeCalendar();
          });
        } else {
          // Animate back to original position
          Animated.parallel([
            Animated.spring(translateY, {
              toValue: 0,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            }),
            Animated.spring(scaleValue, {
              toValue: 1,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            }),
            Animated.spring(opacityValue, {
              toValue: 1,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            })
          ]).start();
        }
      },
      onPanResponderTerminate: () => {
        setIsDragging(false);
        // Animate back to original position
        Animated.parallel([
          Animated.spring(translateY, {
            toValue: 0,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.spring(scaleValue, {
            toValue: 1,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.spring(opacityValue, {
            toValue: 1,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          })
        ]).start();
      },
    })
  ).current;

  const addCondition = useCallback(() => {
    updateConditions([...formData.conditions, { name: "", since: "", isCurrent: null, isMedicated: null }])
    trackUserInteraction("Form Item Added", {
      section: "conditions",
      itemType: "condition",
      newCount: formData.conditions.length + 1,
    })
  }, [formData.conditions])

  const removeCondition = useCallback(
    (index: number) => {
      if (formData.conditions.length > 1) {
        // If the condition has any data filled in, clear it first
        const condition = formData.conditions[index]
        if (condition.name || condition.since || condition.isCurrent !== null || condition.isMedicated !== null) {
          const newConditions = [...formData.conditions]
          newConditions[index] = { name: "", since: "", isCurrent: null, isMedicated: null }
          updateConditions(newConditions)
          trackUserInteraction("Form Item Cleared", {
            section: "conditions",
            itemType: "condition",
            index,
          })
        } else {
          // If already cleared, remove it
          const newConditions = [...formData.conditions]
          newConditions.splice(index, 1)
          updateConditions(newConditions)
          trackUserInteraction("Form Item Removed", {
            section: "conditions",
            itemType: "condition",
            newCount: formData.conditions.length - 1,
          })
        }
      }
    },
    [formData.conditions],
  )

  const updateCondition = useCallback(
    (index: number, field: string, value: any) => {
      const newConditions = [...formData.conditions]
      newConditions[index] = { ...newConditions[index], [field]: value }
      updateConditions(newConditions)

      if ((field === "name" && value) || field === "isCurrent" || field === "isMedicated") {
        trackUserInteraction("Form Field Update", {
          section: "conditions",
          itemType: "condition",
          field,
          value: typeof value === "boolean" ? value.toString() : value,
          index,
        })
      }
    },
    [formData.conditions],
  )

  const openCalendar = useCallback((index: number) => {
    setActiveConditionIndex(index)
    setCalendarVisible(true)
    // Reset animation values
    translateY.setValue(0);
    scaleValue.setValue(1);
    opacityValue.setValue(1);
    trackUserInteraction("Calendar Opened", {
      section: "conditions",
      index,
    })
  }, [translateY, scaleValue, opacityValue])

  const handleDateSelect = useCallback(
    (date: any) => {
      if (activeConditionIndex === null) return

      const formattedDate = date.dateString
      updateCondition(activeConditionIndex, "since", formattedDate)
      trackUserInteraction("Date Selected", {
        section: "conditions",
        index: activeConditionIndex,
        date: date.dateString,
        formattedDate,
      })

      setCalendarVisible(false)
      setActiveConditionIndex(null)
    },
    [activeConditionIndex, updateCondition],
  )

  const closeCalendar = useCallback(() => {
    trackUserInteraction("Calendar Closed", {
      section: "conditions",
      index: activeConditionIndex,
      withoutSelection: true,
    })
    setCalendarVisible(false)
    setActiveConditionIndex(null)
  }, [activeConditionIndex])

  const handleNoConditions = useCallback(async () => {
    updateConditions([
      {
        name: "No medical conditions",
        since: "",
        isCurrent: false,
        isMedicated: false,
      },
    ])
    trackUserInteraction("No Conditions Selected")

    const success = await submitForm()
    if (success) {
      logger.info("Save and Next Button Clicked and Data saved")
      updateCurrentStep(currentStep + 1)
      trackUserInteraction("Form Navigation", { action: "next", toStep: "Medical Conditions" })
    }
  }, [submitForm, updateConditions, updateCurrentStep, currentStep, t])

  const markedDates = useMemo(() => {
    if (activeConditionIndex !== null && formData.conditions[activeConditionIndex]?.since) {
      return {
        [formData.conditions[activeConditionIndex].since]: {
          selected: true,
          selectedColor: theme.colors.primary.main,
          selectedTextColor: theme.colors.secondary[50],
        },
      }
    }
    return {}
  }, [activeConditionIndex, formData.conditions])

  if (!isActive) return null

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <Stethoscope size={moderateScale(32)} color={theme.colors.primary.main} />
      </View>

      <Text style={styles.title}>{t('conditions.title')}</Text>
      <Text style={styles.subtitle}>{t('conditions.subtitle')}</Text>

      {formData.conditions.map((condition, index) => (
        <View key={`condition-${index}`} style={styles.conditionItem}>
          <View style={styles.conditionHeader}>
            <Text style={styles.label}>{t('conditions.conditionIndex', { index: index + 1 })}</Text>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeCondition(index)}
              disabled={formData.conditions.length === 1}
            >
              {formData.conditions.length > 1 &&
                (formData.conditions[index].name ||
                formData.conditions[index].since ||
                formData.conditions[index].isCurrent !== null ||
                formData.conditions[index].isMedicated !== null ? (
                  <Minus size={moderateScale(16)} color={colors.gray[500]} />
                ) : (
                  <X size={moderateScale(16)} color={colors.gray[500]} />
                ))}
              {formData.conditions.length === 1 && <Minus size={moderateScale(16)} color={colors.gray[300]} />}
            </TouchableOpacity>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('conditions.name.label')}</Text>
            <TextInput
              style={styles.input}
              placeholder={t('conditions.name.placeholder')}
              value={condition.name}
              onChangeText={(text) => updateCondition(index, "name", text)}
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('conditions.since.question')}</Text>
            <TouchableOpacity style={styles.dateInputContainer} onPress={() => openCalendar(index)}>
              <TextInput
                style={styles.dateInput}
                placeholder={t('conditions.since.placeholder')}
                value={condition.since ? new Date(condition.since).toLocaleDateString() : ""}
                editable={false}
              />
              <Calendar size={moderateScale(20)} color={colors.gray[500]} />
            </TouchableOpacity>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('conditions.current.question')}</Text>
            <View style={styles.yesNoButtonGroup}>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  styles.yesNoButtonLeft,
                  condition.isCurrent === true && styles.activeYesNoButton,
                ]}
                onPress={() => updateCondition(index, "isCurrent", true)}
              >
                <Text style={[styles.yesNoButtonText, condition.isCurrent === true && styles.activeYesNoButtonText]}>
                  {t('common.yes')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  styles.yesNoButtonRight,
                  condition.isCurrent === false && styles.activeYesNoButton,
                ]}
                onPress={() => updateCondition(index, "isCurrent", false)}
              >
                <Text style={[styles.yesNoButtonText, condition.isCurrent === false && styles.activeYesNoButtonText]}>
                  {t('common.no')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('conditions.medicated.question')}</Text>
            <View style={styles.yesNoButtonGroup}>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  styles.yesNoButtonLeft,
                  condition.isMedicated === true && styles.activeYesNoButton,
                ]}
                onPress={() => updateCondition(index, "isMedicated", true)}
              >
                <Text style={[styles.yesNoButtonText, condition.isMedicated === true && styles.activeYesNoButtonText]}>
                  {t('common.yes')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  styles.yesNoButtonRight,
                  condition.isMedicated === false && styles.activeYesNoButton,
                ]}
                onPress={() => updateCondition(index, "isMedicated", false)}
              >
                <Text style={[styles.yesNoButtonText, condition.isMedicated === false && styles.activeYesNoButtonText]}>
                  {t('common.no')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ))}

      <TouchableOpacity style={styles.addButton} onPress={addCondition}>
        <Plus size={moderateScale(20)} color={theme.colors.primary.main} />
        <Text style={styles.addButtonText}>{t('conditions.addButton')}</Text>
      </TouchableOpacity>

      {formData.conditions.length === 1 && formData.conditions[0].name.toLowerCase() !== "no medical conditions" && (
        <TouchableOpacity style={styles.noConditionsButton} onPress={handleNoConditions}>
          <Text style={styles.noConditionsText}>{t('conditions.noConditionsButton')}</Text>
        </TouchableOpacity>
      )}

      <Modal visible={calendarVisible} transparent animationType="fade" onRequestClose={closeCalendar}>
        <View style={styles.modalOverlay}>
          <Animated.View
            {...panResponder.panHandlers}
            style={[
              styles.calendarContainer,
              {
                transform: [
                  { translateY: translateY },
                  { scale: scaleValue }
                ],
                opacity: opacityValue
              }
            ]}
          >
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>{t('conditions.calendar.title')}</Text>
              <TouchableOpacity onPress={closeCalendar} style={styles.closeButton}>
                <X size={moderateScale(20)} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>
            <CalendarPicker
              onDayPress={handleDateSelect}
              maxDate={new Date().toISOString().split("T")[0]}
              markedDates={markedDates}
              theme={{
                backgroundColor: theme.colors.secondary[50],
                calendarBackground: theme.colors.secondary[50],
                textSectionTitleColor: theme.colors.gray[600],
                selectedDayBackgroundColor: theme.colors.primary.main,
                selectedDayTextColor: theme.colors.secondary[50],
                todayTextColor: theme.colors.primary.main,
                dayTextColor: colors.black,
                arrowColor: theme.colors.primary.main,
                monthTextColor: theme.colors.primary.main,
                textDayFontSize: theme.fontSize.md,
                textMonthFontSize: theme.fontSize.lg,
                textDayHeaderFontSize: theme.fontSize.sm,
              }}
            />
          </Animated.View>
        </View>
      </Modal>
    </ScrollView>
  )
}

// const styles = StyleSheet.create({
//   container: { flex: 1, backgroundColor: theme.colors.secondary[50] },
//   content: { padding: scale(16) },
//   iconContainer: {
//     width: scale(64),
//     height: scale(64),
//     borderRadius: scale(32),
//     backgroundColor: theme.colors.primary.main + "20",
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: moderateScale(22),
//     fontWeight: "700",
//     color: theme.colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: moderateScale(theme.fontSize.sm),
//     color: colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   conditionItem: {
//     marginBottom: verticalScale(24),
//     padding: scale(14),
//     backgroundColor: colors.gray[50],
//     borderRadius: scale(theme.radii.md),
//     borderWidth: 1,
//     borderColor: colors.gray[200],
//   },
//   conditionHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     marginBottom: verticalScale(12),
//   },
//   label: {
//     fontSize: moderateScale(theme.fontSize.lg),
//     fontWeight: "600",
//     color: colors.black,
//   },
//   removeButton: { padding: scale(4) },
//   formField: { marginBottom: verticalScale(12) },
//   fieldLabel: {
//     fontSize: moderateScale(14),
//     fontWeight: "500",
//     color: colors.gray[700],
//     marginBottom: verticalScale(6),
//   },
//   input: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: scale(12),
//     padding: scale(14),
//     backgroundColor: colors.white,
//     fontSize: moderateScale(14),
//   },
//   dateInputContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: scale(12),
//     paddingHorizontal: scale(10),
//     height: verticalScale(53),
//     backgroundColor: colors.white,
//   },
//   dateInput: { flex: 1, color: colors.black },
//   yesNoButtonGroup: {
//     flexDirection: "row",
//     borderRadius: scale(12),
//     overflow: "hidden",
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//   },
//   yesNoButton: {
//     flex: 1,
//     paddingVertical: verticalScale(14),
//     alignItems: "center",
//     backgroundColor: colors.white,
//   },
//   yesNoButtonLeft: { borderRightWidth: 1, borderColor: colors.gray[300] },
//   yesNoButtonRight: {},
//   activeYesNoButton: { backgroundColor: colors.primary },
//   yesNoButtonText: {
//     color: colors.gray[700],
//     fontWeight: "500",
//   },
//   activeYesNoButtonText: {
//     color: colors.white,
//   },
//   addButton: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     marginVertical: verticalScale(20),
//   },
//   addButtonText: {
//     color: colors.primary,
//     fontSize: moderateScale(16),
//     fontWeight: "600",
//     marginLeft: scale(8),
//   },
//   noConditionsButton: { alignSelf: "center", marginTop: verticalScale(8) },
//   noConditionsText: { color: colors.gray[600] },
//   modalOverlay: {
//     flex: 1,
//     backgroundColor: "rgba(0,0,0,0.4)",
//     justifyContent: "center",
//     alignItems: "center",
//     padding: scale(14),
//   },
//   calendarContainer: {
//     backgroundColor: colors.white,
//     borderRadius: scale(12),
//     padding: scale(14),
//     width: "100%",
//   },
//   calendarHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     marginBottom: verticalScale(12),
//   },
//   calendarTitle: {
//     fontSize: moderateScale(theme.fontSize.lg),
//     fontWeight: "600",
//     color: colors.black,
//   },
//   closeButton: { padding: scale(4) },
// })
