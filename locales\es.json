{"common": {"error": "Error", "yes": "Sí", "no": "No", "sometimes": "A veces", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "next": "Siguient<PERSON>", "loading": "Cargando...", "version": "v0.0.1.7"}, "welcome": "Inicia sesión para empezar a hablar con August", "notFound": {"title": "¡Ups!", "message": "Esta pantalla no existe.", "goHome": "¡Ir a la pantalla de inicio!"}, "library": {"title": "Biblioteca de Salud"}, "specialists": {"title": "Especialistas", "description": "Consulta con profesionales de la salud especializados para problemas de salud más específicos. Elige un especialista a continuación:", "generalPhysician": {"title": "Médico General", "description": "Para problemas de salud generales y atención primaria."}, "nutritionist": {"title": "Nutricionista", "description": "Para consejos sobre dieta, nutrición y control de peso."}, "cardiologist": {"title": "Cardiólogo", "description": "Para problemas relacionados con el corazón y la salud cardiovascular."}, "neurologist": {"title": "Neurólogo", "description": "Para problemas del cerebro, la médula espinal y el sistema nervioso."}, "oncologist": {"title": "Oncólogo", "description": "Para problemas y tratamientos relacionados con el cáncer."}, "endocrinologist": {"title": "Endocrinólogo", "description": "Para trastornos hormonales y control de la diabetes."}, "dermatologist": {"title": "Dermatólogo", "description": "Para afecciones de la piel, el cabello y las uñas."}, "psychiatrist": {"title": "Psiquiatra", "description": "Para problemas de salud mental y bienestar psicológico."}}, "profile": {"title": "Perfil", "defaultName": "<PERSON><PERSON><PERSON><PERSON>", "namePlaceholder": "Introduce tu nombre", "saving": "Guardando...", "noPhoneNumber": "Sin número de teléfono", "loggingOut": "<PERSON><PERSON><PERSON> se<PERSON>...", "about": {"title": "Acerca de", "description": "Conoce más sobre August"}, "whatsapp": {"title": "WhatsApp", "description": "Chatea con August en WhatsApp"}, "refer": {"title": "Recomendar", "description": "¿Te ha gustado August? Compártelo con tus amigos"}, "deleteAccount": {"title": "Eliminar cuenta", "description": "Sentimos verte ir"}, "logout": {"title": "<PERSON><PERSON><PERSON>", "description": "Vuelve pronto. Te echaremos de menos"}, "shareMessage": "👋Hola, ¡Mira esta increíble aplicación que he estado usando!\n\n\n\n➡️He estado usando August para obtener información y orientación de salud rápida y confiable. ¡Es como tener un médico en tu bolsillo! Míralo aquí:", "error": {"loadFailed": "Error al cargar los datos del usuario", "fetchError": "Se produjo un error al obtener los datos del usuario", "updateNameFailed": "Error al actualizar el nombre", "updateNameError": "Se produjo un error al actualizar el nombre", "loadFoodData": "Error al cargar los datos de alimentos", "logoutError": "Error durante el cierre de sesión:", "shareError": "Error al compartir mensajes:"}}, "error": {"title": "Algo salió mal", "checkLogs": "Por favor, revisa los registros de tu dispositivo para más detalles.", "unknown": "Error descon<PERSON>", "unknownFile": "Archivo desconocido", "unknownLine": "Línea desconocida", "unknownColumn": "Columna desconocida"}, "auth": {"phone": {"selectCountry": "Seleccionar país", "searchCountries": "Buscar países", "validation": {"invalidPhone": "Por favor, ingrese un número de teléfono válido", "invalidDigits": "Por favor, ingrese un número de teléfono válido (7-15 dígitos)"}}, "header": {"title": "Obtenga claridad sobre sus preocupaciones de salud de forma instantánea y privada", "subtitle": "Guía reflexiva. Sin prisas. Sin confusiones.", "emphasis": "Solo claridad."}, "greeting": "Hola 👋", "phoneNumber": "Número de teléfono", "requestOTP": "Solicitar OTP", "otp": {"title": "Contraseña de un solo uso", "verify": "Verificar OTP", "sending": "Enviando...", "countdown": "Reenviar OTP en {{countdown}}s", "resend": "Reenviar OTP", "sentTo": "OTP enviado a ", "whatsappSuffix": " en Whatsapp"}, "disclaimer": {"prefix": "Al registrarse, usted acepta nuestros ", "continuePrefix": "<PERSON> continuar, usted acepta nuestros ", "termsOfService": "Términos de servicio", "and": " y ", "privacyPolicy": "Política de privacidad", "whatsappConsent": ", y acepta recibir actualizaciones y recordatorios de nuestra parte a través de WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "¡Bienvenido a August!", "buttonText": "Empecemos"}}, "postAuth": {"step1": {"title": "¡Hola!", "subtitle": "Soy August 👋", "description": "Piensa en mí como el rincón cómodo de tu\ndispositivo donde exploras todas tus\ncuriosidades de salud.", "subdescription": "Siéntete libre de preguntar cualquier cosa que te preocupe.\nSin juicios, ¡sin límites!", "placeholder": "¿Cómo quieres que te llame?"}, "step2": {"title": "<PERSON><PERSON> {{userName}},", "subtitle": "Esto es lo que puedo hacer:", "features": {"health": {"title": "Responde a tus", "subtitle": "Consultas de salud"}, "nutrition": {"title": "Rastrea esos", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Informes"}}}}, "pills": {"thoughtful": "Reflexivo", "careful": "Cuidadoso", "accurate": "Preciso"}, "features": {"symptoms": {"title": "Revisa tus s<PERSON>as", "description": "He tenido náuseas durante una semana. ¿Qué me está pasando?"}, "prescriptions": {"title": "<PERSON><PERSON>za tus recetas", "description": "Sube y comprende las recetas como un médico."}, "medicine": {"title": "Conoce tus medicamentos", "description": "¿La Metformina para mi SOP interactúa con mis pastillas para el TDAH?"}, "plans": {"title": "Obtén planes personalizados", "description": "¿<PERSON>uedes darme un plan de nutrición y ejercicio para reducir mis niveles de HbA1c?"}}, "buttons": {"getStarted": "Empezar", "next": "Siguient<PERSON>"}, "errors": {"nameRequired": "Por favor, ingresa tu nombre"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Descubre", "nutrition": "Nutrición", "personalize": "Personalizar"}, "chat": {"nav": {"title": "Agosto"}, "me": "Yo", "augustName": "Agosto", "input": {"placeholder": "Pregúntale a Agosto...", "disclaimer": "Agosto puede cometer errores. Confirma con un médico"}, "list": {"loadingMessages": "Cargando mensajes...", "noMessages": "Aún no hay mensajes. ¡Comienza una conversación!"}, "connection": {"offlineMessage": "Parece que estás sin conexión. Reconéctate para enviar mensajes.", "connecting": "Conectando...", "tryAgain": "Intentar de nuevo"}, "prompts": {"uploadReport": "Subir informe", "speakInHindi": "Habla en hindi", "notFeelingWell": "No me siento bien", "whatIsMyBMI": "¿Cuál es mi IMC?", "nutritionAdvice": "Consejos de nutrición", "sleepBetter": "<PERSON><PERSON><PERSON> mejor"}, "citations": {"referenceText": "Para más detalles sobre esta conversación, consulta:"}, "actions": {"copiedToClipboard": "Copiado al portapapeles", "copied": "Copiado"}, "share": {"introText": "👋Hola, mira la conversación que tuve con Agosto:\n\n", "downloadText": "\n\n➡️Descarga Agosto para chatear con tu amigable compañero de salud con IA:\n"}}, "discover": {"nav": {"title": "Descubre"}, "categories": {"all": "Todo", "heartHealth": "Salud cardiovascular", "nutrition": "Nutrición", "mentalHealth": "<PERSON><PERSON> mental", "fitness": "Forma física", "wellness": "Bienestar"}, "cards": {"empty": "No hay tarjetas disponibles para esta categoría"}, "sections": {"features": "Características"}, "features": {"healthLibrary": {"title": "Biblioteca de salud", "description": "Acceso a información médica confiable, actualizada y de confianza, totalmente gratuita."}, "nutritionTracker": {"title": "Seguimiento nutricional", "description": "¿Alguna vez te preguntaste si podrías subir una foto de tu comida y hacer un seguimiento de tus objetivos nutricionales? ¡Agosto puede hacerlo!"}, "multilingualSupport": {"title": "Soporte multilingüe", "description": "¡Puedes comunicarte con Agosto en cualquier idioma con el que te sientas cómodo! Agosto siempre está aquí para escucharte, apoyarte y responderte cuando lo necesites.", "samplePrompt": "Habla en hindi"}, "labReportAnalysis": {"title": "Análisis de informes de laboratorio", "description": "<PERSON>uando hablas con Agosto sobre tus informes de laboratorio, obtienes una precisión extrema. Agosto ha procesado más de 4,7 millones de informes con una precisión de extracción de biomarcadores del 98,4%."}}}, "nutrition": {"nav": {"title": "Nutrición"}, "meals": {"title": "<PERSON><PERSON>", "subtitle": "Toca para ver los macros de cada comida"}, "upload": {"loading": "Subiendo imagen..."}, "defaultFoodName": "Alimento", "today": "Hoy", "unknownTime": "Hora desconocida", "calories": "🔥 Calorías", "proteins": "🥩 Proteínas", "carbs": "🍞 Carbohidratos", "sugars": "🍬 Azúcares", "fat": "🥑 Grasa", "caloriesLabel": "Calorías", "proteinLabel": "<PERSON><PERSON><PERSON><PERSON>", "carbohydratesLabel": "Carbohidratos", "fatLabel": "<PERSON><PERSON><PERSON>", "sugarLabel": "<PERSON><PERSON><PERSON><PERSON>", "tips": "Consejos:", "macroBreakdown": "Desglose de Macronutrientes", "noMacroData": "No hay datos de macronutrientes disponibles para este alimento.", "disclaimer": "Solo para fines educativos. Más información", "disclaimerLink": "aquí", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "¿<PERSON>uál es tu género?", "subtitle": "Esto se usará para calibrar tu plan personalizado.", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino", "other": "<PERSON><PERSON>"}, "age": {"title": "¿<PERSON><PERSON><PERSON>l es tu edad?", "subtitle": "Esto se usará para calcular tus necesidades diarias."}, "measurements": {"title": "Altura y Peso", "subtitle": "Por favor, ingresa tu altura en centímetros y tu peso en kilogramos."}, "activity": {"title": "Nivel de Actividad", "subtitle": "¿Con qué frecuencia haces ejercicio?", "none": "Sin ejercicio", "moderate": "Moderado", "high": "Alto"}, "goal": {"title": "Objetivo de Peso", "subtitle": "¿Qué te gustaría lograr?", "increase": "Aumentar", "maintain": "<PERSON><PERSON><PERSON>", "decrease": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Peso Objetivo", "subtitle": "¿Cuál es tu peso objetivo en kilogramos?"}, "setup": {"title": "Configurando tu Plan", "subtitle": "Por favor, espera mientras preparamos tu plan de nutrición."}, "review": {"title": "Revisa tu Plan", "subtitle": "Revisa y personaliza tu plan de nutrición."}, "height": {"label": "Altura (cm)"}, "weight": {"label": "Peso (kg)"}}, "error": {"updateFailed": "No se pudieron actualizar los datos de nutrición. Por favor, inténtalo de nuevo.", "parsingError": "Error al analizar los datos de alimentos:", "fetchReportsFailed": "No se pudieron obtener los datos de los informes. Por favor, inténtalo de nuevo.", "missingReportId": "Falta el ID del informe"}}, "personalize": {"nav": {"title": "Personalizar"}, "button": {"saving": "Guardando", "review": "Rev<PERSON><PERSON>", "saveNext": "Guardar y Siguiente"}}, "basicInfo": {"title": "Conozcámonos mejor", "subtitle": "Esta información nos ayuda a personalizar tus recomendaciones de salud", "age": {"question": "¿Cuántos años tienes?", "placeholder": "Ingresa tu edad"}, "sex": {"question": "¿<PERSON>uál es tu sexo?", "placeholder": "Selecciona tu sexo", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino", "other": "<PERSON><PERSON>"}, "height": {"question": "¿<PERSON>u<PERSON>l es tu altura? (cm)", "placeholder": "Ingresa tu altura"}, "weight": {"question": "¿Cuál es tu peso? (kg)", "placeholder": "Ingresa tu peso"}}, "lifestyle": {"title": "Tus Hábitos de Vida", "subtitle": "Comprender tus hábitos diarios nos ayuda a proporcionar mejores recomendaciones.", "diet": {"question": "¿Qué tipo de dieta sigues?", "placeholder": "Selecciona tu dieta", "vegetarian": "Vegetarian<PERSON>", "nonVegetarian": "No vegetariano", "vegan": "Vegano", "pescatarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keto": "Keto", "paleo": "Paleolítico"}, "exercise": {"question": "¿Haces ejercicio regularmente?"}, "drinking": {"question": "¿Consumes alcohol?"}, "smoking": {"question": "¿Fumas?"}, "sleep": {"question": "¿Cuántas horas duermes por noche?", "value": "{{sleep}} horas"}, "hydration": {"question": "¿Cuántas tazas de agua bebes al día?", "value": "{{hidratación}} tazas ({{litros}}L)"}}, "allergies": {"title": "¿Tiene alguna alergia?", "subtitle": "Conocer sus alergias nos ayuda a proporcionar recomendaciones más seguras.", "allergyIndex": "Alergia {{index}}", "name": {"question": "¿A qué eres alérgico/a?", "placeholder": "Ingrese alergia (p. ej., Cacahuates, Polvo)"}, "severity": {"question": "¿Qué tan grave es esta alergia?", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mild": "Suave", "moderate": "Moderado", "severe": "<PERSON><PERSON><PERSON>"}, "addButton": "Añadir otra alergia", "noAllergiesButton": "No tengo ninguna alergia."}, "medications": {"title": "Medicamentos y Suplementos", "subtitle": "Díganos sobre cualquier medicamento o suplemento que esté tomando actualmente.", "medicationIndex": "Medicamento {{index}}", "name": {"label": "Nombre del medicamento", "placeholder": "Ingrese el nombre del medicamento"}, "startDate": {"question": "¿Cuándo empezaste a tomarlo?", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha"}, "type": {"label": "Tipo de Medicamento", "shortTerm": "<PERSON><PERSON><PERSON>", "longTerm": "<PERSON><PERSON>"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Monto"}, "unit": {"label": "Unidad"}, "frequency": {"label": "Frecuencia", "placeholder": "Tiempos", "perDay": "por día", "perWeek": "por semana", "perMonth": "por mes", "perYear": "por año"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "bocanadas", "drops": "gotas", "tsp": "cucharaditas", "tbsp": "cuchara (sopera)", "cups": "tazas"}, "addButton": "Añadir otro medicamento", "noMedicationsButton": "No tomo ningún medicamento.", "calendar": {"title": "Seleccionar fecha de inicio"}}, "conditions": {"title": "Condiciones Médicas", "subtitle": "Cuéntenos sobre cualquier condición médica que tenga o haya tenido en el pasado.", "conditionIndex": "Condición {{index}}", "name": {"label": "Nombre de la condición", "placeholder": "Ingrese la condición (p. ej., asma, etc.)"}, "since": {"question": "¿Desde cuándo tiene esta condición?", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha"}, "current": {"question": "¿Te está preocupando actualmente?"}, "medicated": {"question": "¿Está tomando algún medicamento para esto?"}, "addButton": "Agregar otra condición", "noConditionsButton": "No tengo ninguna condición médica.", "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "<PERSON><PERSON>roducti<PERSON>", "subtitle": "Esta información nos ayuda a brindar recomendaciones de salud más personalizadas", "menstruación": {"question": "¿Ha menstruado alguna vez?", "detailsTitle": "Detalles de la Menstruación", "regularity": {"question": "¿Qué tan regular es su ciclo?", "regular": "Regular", "irregular": "Irregular", "notSure": "No estoy segura"}, "cycleLength": {"label": "Duración promedio del ciclo (días)", "placeholder": "Ingrese la duración del ciclo"}, "flowDays": {"label": "Días de flujo: {{flowDays}}", "min": "1 día", "max": "15 días"}, "padsPerDay": {"label": "Compresas/tampones por día: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "¿Algún síntoma durante su período?", "placeholder": "Ingrese los síntomas (ej., calambres, dolores de cabeza)"}}, "childbirth": {"question": "¿Ha experimentado un parto?", "detailsTitle": "Detalles del Parto", "children": {"label": "Número de <PERSON>"}, "pregnancies": {"label": "Número de embarazos"}, "complications": {"question": "¿Alguna complicación durante el embarazo o el parto?", "placeholder": "Ingrese las complicaciones (si las hay)"}}}, "review": {"title": "Revise su información", "subtitle": "Por favor, revise la información que ha proporcionado antes de enviar", "sections": {"basicInfo": "Información básica", "lifestyle": "Estilo de vida", "allergies": "Alergias", "medications": "Medicamentos y suplementos", "conditions": "Condiciones médicas", "reproductive": "<PERSON><PERSON> reproductiva", "menstruationDetails": "Detalles de la menstruación", "childbirthDetails": "Detalles del parto"}, "fields": {"age": "Edad:", "sex": "Sexo:", "height": "Altura:", "weight": "Peso:", "diet": "Dieta:", "exercise": "<PERSON><PERSON><PERSON><PERSON>:", "drinking": "Consumo de alcohol:", "smoking": "Tabaquismo:", "sleep": "Sueño:", "hydration": "Hidratación:", "allergyIndex": "Alergia {{index}}:", "dose": "Dosis:", "frequency": "Frecuencia:", "type": "Tipo:", "since": "Desde:", "currentlyActive": "Actualmente activo:", "takingMedication": "Tomando medicamento:", "hasMenstruated": "¿Ha menstruado?:", "regularity": "Regularidad:", "cycleLength": "Duración del ciclo:", "flowDays": "Días de flujo:", "padsPerDay": "Compresas/tampones por día:", "hasChildbirth": "¿Ha experimentado un parto?:", "children": "Hijos:", "pregnancies": "Embarazos:"}, "notProvided": "No proporcionado", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} horas por día", "hydration": "{{hydration}} tazas ({{liters}}L) por día", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} días"}, "noData": {"allergies": "No se han proporcionado alergias", "medications": "No se han proporcionado medicamentos", "conditions": "No se han proporcionado condiciones médicas"}, "submitButton": "Enviar información"}, "success": {"title": "¡Información actualizada!", "message": "G<PERSON><PERSON> por proporcionar su información de salud. La usaremos para personalizar su experiencia y brindar mejores recomendaciones.", "benefits": {"insights": "Información personalizada sobre la salud", "reminders": "Mejores recordatorios de medicamentos", "recommendations": "Recomendaciones de salud personalizadas"}, "continueButton": "Con<PERSON><PERSON>r al panel"}, "permissions": {"microphonePermissionDenied": "Permiso del micrófono denegado", "microphoneAccessDescription": "August necesita acceder a tu micrófono para grabar audio y enviar notas de voz", "permissionDenied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "cameraPermissionRequired": "¡Necesitamos permisos de cámara para que esto funcione!", "mediaLibraryPermissionRequired": "¡Necesitamos permisos de la biblioteca multimedia para que esto funcione!"}, "voiceRecording": {"recordingTooLong": "Grabación demasiado larga", "recordingTooLongMessage": "Las grabaciones de voz deben durar menos de 5 minutos. Graba un mensaje más corto."}, "errors": {"uploadFailed": "Error al subir", "voiceUploadFailed": "No se pudo subir la grabación de voz.", "voiceRecordingFailed": "Error al enviar la grabación de voz", "failedToStopRecording": "Error al detener la grabación", "photoUploadFailed": "No se pudo subir la foto.", "failedToTakePhoto": "Error al tomar la foto", "imageUploadFailed": "No se pudo subir la imagen: {{fileName}}", "failedToPickImage": "Error al seleccionar la imagen", "documentUploadFailed": "No se pudo subir el documento: {{fileName}}", "failedToPickDocument": "Error al seleccionar el documento"}, "audioPlayer": {"downloadingAudio": "Descargando audio...", "loadingAudio": "Cargando audio..."}, "mediaProcessing": {"processingFile": "Procesando tu archivo", "uploadingSecuring": "Subiendo y asegurando el archivo...", "analyzingContent": "Analizando el contenido del documento...", "extractingInfo": "Extrayendo información clave...", "processingInsights": "Procesando información...", "preparingResponse": "Preparando una respuesta detallada...", "finalizingResponse": "Finalizando la respuesta..."}, "attachments": {"voiceMessage": "Mensaje de voz", "image": "[IMAGEN]", "pdf": "[PDF]", "voice": "[NOTA DE VOZ]"}, "pdf": {"loadingPdf": "Cargando PDF..."}, "dateTime": {"yesterday": "Ayer, "}, "navbar": {"defaultTitle": "august", "selectedCount": "seleccionados"}, "mediaUpload": {"photoLibrary": "Librería de fotos", "takePhoto": "Tomar foto", "chooseFile": "Elegir archivo"}, "comingSoon": {"title": "¡Próximamente!", "description": " está actualmente en desarrollo. ¡Mantente atento a las actualizaciones!", "buttonText": "¡Entendido!"}, "clipboard": {"success": "Enlace copiado al portapapeles"}, "mediaPhotos": {"emptyState": "Aún no hay entradas."}, "foodDetail": {"defaultFoodName": "Alimento", "nutrition": {"totalCalories": "Calorías totales", "proteins": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON><PERSON>", "sugars": "Azúcares", "fibers": "Fi<PERSON><PERSON>"}}, "reports": {"defaultTitle": "Elemento multimedia", "defaultFoodName": "Alimento", "defaultName": "Documento", "openButton": "Abrir en visor externo", "biomarker": {"headerBiomarker": "Biomarcador", "headerValue": "Valor", "headerRefRange": "<PERSON><PERSON> de <PERSON>", "headerStatus": "Estado"}, "noData": "No hay datos de biomarcadores disponibles"}, "setup": {"title": "Estamos configurando todo para ti", "inProgress": "En progreso...", "progressMessages": {"0": "Calculando calorías diarias", "1": "Optimizando la distribución de macros", "2": "Creando plan de comidas", "3": "Calculando el índice de salud", "4": "Finalizando la configuración"}, "checklistItems": {"0": "<PERSON><PERSON><PERSON><PERSON> tus datos de salud", "1": "Calculando el plan de nutrición óptimo", "2": "Personalizando tus recomendaciones", "3": "Creando tus sugerencias de comidas", "4": "Finalizando tu configuración"}}, "foodEntry": {"emptyState": "Aún no hay entradas de comida. ¡Toma una foto de tu comida para añadirla!"}, "nutritionReview": {"congratulations": "¡Felicidades!", "subtitle": "Tu plan de nutrición personalizado está listo", "submitButton": "¡Empecemos!", "dailyTargetsTitle": "Tus objetivos diarios de nutrición", "macroLabels": {"calories": "Calorías", "carbs": "Carbohidratos", "protein": "<PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON>"}, "recommendations": {"title": "<PERSON><PERSON><PERSON> al<PERSON>zar tus metas:", "healthScores": "Usa los puntajes de salud para mejorar tu rutina", "trackFood": "Registra tu ingesta de alimentos de forma consistente", "followCalories": "Sigue tu recomendación diaria de calorías", "balanceMacros": "Equilibra tu consumo de carbohidratos, proteínas y grasas"}}, "editModal": {"titlePrefix": "<PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Siguient<PERSON>"}, "processing": {"stages": {"scanning": "Escaneando comida...", "identifying": "Identificando ingredientes...", "extracting": "Extrayendo nutrientes...", "finalizing": "Finalizando resultados..."}, "error": {"defaultMessage": "No se detectó comida", "subtitle": "Prueba un ángulo diferente"}, "retakeButton": "Toca para tomar la foto de nuevo", "notification": "¡Te notificaremos cuando termine!"}, "chart": {"title": "Seguimiento de la Nutrición a lo Largo del Tiempo", "selectNutrient": "Seleccionar nutriente:", "emptyState": "Aún no hay datos nutricionales disponibles", "dropdown": {"calories": "Calorías", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON>", "sugars": "Azúcares"}}, "foodModal": {"defaultName": "Artículo de comida", "defaultDate": "Hoy", "defaultTime": "Tiempo desconocido", "saveChanges": "Guardar cambios", "error": {"title": "Error", "message": "No se pudieron actualizar los datos de nutrición. Inténtelo de nuevo."}, "nutrition": {"calories": "🔥 Calorías", "proteins": "🥩 Proteínas", "carbs": "🍞 Carbohidratos", "sugars": "🍬 Azúcares", "fat": "🥑 Grasa"}, "macroBreakdown": {"title": "Desglose de Macronutrientes", "noData": "No hay datos disponibles sobre macronutrientes para este alimento."}, "macroLabels": {"calories": "Calorías", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Información detallada", "edit": "<PERSON><PERSON>", "save": "Guardar", "saving": "Guardando...", "enterValue": "Introducir valor", "notSet": "No establecido", "age": "Edad", "heightCm": "Altura (cm)", "weightKg": "Peso (kg)", "targetWeight": "Peso objetivo", "nutritionTargets": "Objetivos Nutricionales", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fats": "<PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Ingrese valores o deje en blanco para cálculo automático.", "autoCalculateNote": "Las macros se calculan automáticamente en función de sus datos.", "validation": {"ageMin": "La edad debe ser de al menos 18 años", "ageMax": "La edad debe ser menor de 125", "heightMin": "La altura debe ser de al menos 50 cm", "heightMax": "La altura debe ser inferior a 250 cm", "weightMin": "El peso debe ser de al menos 30 kg", "weightMax": "El peso debe ser inferior a 500 kg", "targetWeightMin": "El peso objetivo debe ser de al menos 30 kg", "targetWeightMax": "El peso objetivo debe ser inferior a 500 kg", "proteinMin": "La proteína debe ser 0 o más.", "carbsMin": "Los carbohidratos deben ser 0 o más", "fatsMin": "Las grasas deben ser 0 o más"}}, "tracker": {"calories": "Calorías", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON>", "excess": "exceso", "remaining": "restante"}, "specialistConstants": {"nutritionist": {"name": "Nutricionista", "description": "Asesoramiento experto en dieta, nutrición y hábitos alimenticios saludables", "featureName": "Especialista en Nutrición"}, "cardiologist": {"name": "Cardiólogo", "description": "Especializado en salud cardíaca y afecciones cardiovasculares", "featureName": "Especialista en Cardiología"}, "neurologist": {"name": "Neurólogo", "description": "Enfocado en trastornos del cerebro, la médula espinal y el sistema nervioso", "featureName": "Especialista en Neurología"}, "oncologist": {"name": "Oncólogo", "description": "Especializado en diagnóstico y opciones de tratamiento del cáncer", "featureName": "Especialista en Oncología"}, "endocrinologist": {"name": "Endocrinólogo", "description": "Experto en afecciones hormonales y trastornos metabólicos", "featureName": "Especialista en Endocrinología"}}, "discoverCards": {"categories": {"nutrition": "Nutrición", "heartHealth": "Salud del corazón", "mentalHealth": "<PERSON><PERSON> mental", "fitness": "Fitness", "wellness": "Bienestar"}, "titles": {"vitaminB12Recovery": "¿Cuánto tiempo se tarda en recuperarse de una deficiencia de vitamina B12?", "vitaminDeficiencyGanglion": "¿Qué deficiencia de vitaminas causa quistes de ganglio?", "vitaminDeficiencyHairFall": "¿Qué deficiencia de vitaminas causa la caída del cabello?", "vitaminWaters": "¿Son buenas para ti las aguas vitaminadas?", "cholesterolHeadaches": "¿El colesterol alto causa dolores de cabeza?", "cholesterolEyes": "¿<PERSON>uáles son los síntomas del colesterol alto que se pueden ver en los ojos?", "diabetesHeadaches": "¿Puede la diabetes causar dolores de cabeza?", "chestPainDrinking": "¿Por qué duele el pecho después de beber?", "stressDizziness": "¿Puede el estrés causar mareos?", "bulimiaFace": "¿Qué es la cara de bulimia?", "kneeTwitch": "¿Por qué me tiembla la rodilla?", "noseTwitching": "¿Por qué ocurre el tic en la nariz?", "piriformisVsSciatica": "¿<PERSON>u<PERSON>les son las diferencias entre el síndrome del piriforme y la ciática?", "shoulderBladePinched": "¿Cómo liberar un nervio pellizcado en el omóplato?", "shoulderPinched": "¿Cómo liberar un nervio pellizcado en el hombro?", "meniscusTorn": "¿Cómo curar un menisco roto de forma natural?", "hydrateQuickly": "¿Cómo hidratarse rápidamente?", "periodConstipation": "¿Es normal tener estreñimiento durante la menstruación?", "acneScars": "¿Cómo deshacerse de las cicatrices del acné de forma natural en una semana?", "perimenopausePregnancy": "¿<PERSON>ued<PERSON> quedar embarazada durante la perimenopausia?"}, "descriptions": {"vitaminB12Recovery": "Descubre la línea de tiempo de recuperación para la deficiencia de vitamina B12 y remedios efectivos para aumentar tus niveles de energía.", "vitaminDeficiencyGanglion": "Explora la relación entre las deficiencias de vitaminas y el desarrollo de quistes de ganglios en el cuerpo.", "vitaminDeficiencyHairFall": "Aprende cómo la falta de vitaminas esenciales puede provocar la caída del cabello y qué puedes hacer para prevenirlo.", "vitaminWaters": "Descubre los beneficios y los posibles inconvenientes de las aguas vitaminadas como parte de tu nutrición diaria.", "cholesterolHeadaches": "Examina la posible conexión entre los niveles altos de colesterol y la aparición de dolores de cabeza.", "cholesterolEyes": "Aprende cómo el colesterol alto puede manifestarse en tus ojos y qué síntomas debes observar.", "diabetesHeadaches": "Investiga la relación entre la diabetes y la aparición de dolores de cabeza en la vida diaria.", "chestPainDrinking": "Explora las razones detrás del dolor en el pecho después del consumo de ciertas bebidas.", "stressDizziness": "Profundiza en cómo el estrés puede afectar tu equilibrio y bienestar general, provocando mareos.", "bulimiaFace": "Comprende los signos físicos de la bulimia, incluidos los efectos en la apariencia facial.", "kneeTwitch": "Investiga las posibles causas detrás de los espasmos involuntarios de la rodilla y su relación con el estrés o la fatiga.", "noseTwitching": "Aprende sobre las posibles razones para el tic en la nariz y su vínculo con la ansiedad u otros factores.", "piriformisVsSciatica": "Compara los síntomas del síndrome del piriforme y la ciática para comprender mejor tu condición.", "shoulderBladePinched": "Descubre técnicas efectivas para aliviar un nervio pellizcado en el omóplato y restaurar la movilidad.", "shoulderPinched": "Aprende ejercicios y estiramientos sencillos para aliviar la compresión nerviosa en el área del hombro.", "meniscusTorn": "Explora métodos naturales y ejercicios para ayudar en la curación de un menisco roto.", "hydrateQuickly": "Descubre formas rápidas y efectivas de rehidratarte y mantener una hidratación corporal óptima.", "periodConstipation": "Comprende las razones detrás del estreñimiento durante la menstruación y aprende remedios naturales.", "acneScars": "Descubre remedios naturales y consejos para el cuidado de la piel para reducir la apariencia de las cicatrices del acné rápidamente.", "perimenopausePregnancy": "Aprende sobre la perimenopausia, las consideraciones de fertilidad y qué esperar durante esta etapa de la vida."}}}