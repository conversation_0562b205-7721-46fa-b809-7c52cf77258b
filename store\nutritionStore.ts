import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger/logger';
import { useMemo } from 'react';

export type NutritionProfile = {
  sex: 'male' | 'female' | 'other';
  age: number;
  height: number;
  weight: number;
  exerciseLevel: 'no exercise' | 'moderate' | 'high';
  goal: 'increase' | 'maintain' | 'decrease';
  desiredWeight: number;
};

export type NutritionEntry = {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  sugars?: number;
  fibers?: number;
  date: string;
  imageUri?: string;
};

type NutritionState = {
  profile: NutritionProfile | null;
  entries: NutritionEntry[];
  dailyGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  nutritionHistory: {
    date: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    sugars?: number;
    fibers?: number;
  }[];
  setProfile: (profile: NutritionProfile | null) => void;
  addEntry: (entry: Omit<NutritionEntry, 'id' | 'date'>) => void;
  removeEntry: (id: string) => void;
  calculateDailyGoals: (profile: NutritionProfile | null) => void;
  updateNutritionHistory: () => void;
  setDailyGoals: (goals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }) => void;
};

// Create a memoization cache for calculateDailyGoals
const dailyGoalsCache = new Map<string, any>();
const MAX_CACHE_SIZE = 20;

// Create a memoization cache for updateNutritionHistory
const historyCache = new Map<string, any>();

export const useNutritionStore = create<NutritionState>()(
  persist(
    (set, get) => ({
  profile: null,
  entries: [],
  dailyGoals: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
  },
      nutritionHistory: [],
      setProfile: (profile) => {
        set({ profile });
        get().calculateDailyGoals(profile);
      },
      addEntry: (entry) => {
        const newEntry = {
          ...entry,
          id: Math.random().toString(36).substring(2, 9),
          date: new Date().toISOString().split('T')[0],
        };
        
        set((state) => ({
          entries: [newEntry, ...state.entries],
        }));
        
        get().updateNutritionHistory();
      },
      removeEntry: (id) => {
        set((state) => ({
          entries: state.entries.filter((entry) => entry.id !== id),
        }));
        
        get().updateNutritionHistory();
      },
      calculateDailyGoals: (profile) => {
        if (!profile) return;
        
        // Create a cache key from the profile data
        const cacheKey = JSON.stringify(profile);
        
        // Check if we have a cached result
        if (dailyGoalsCache.has(cacheKey)) {
          const cachedGoals = dailyGoalsCache.get(cacheKey);
          set({ dailyGoals: cachedGoals });
          return;
        }
        
        // Calculate BMR using Mifflin-St Jeor Equation (same as getMacroPerDay.ts)
        let bmr = 0;
        if (profile.sex === 'male') {
          bmr = 10 * profile.weight + 6.25 * profile.height - 5 * profile.age + 5;
        } else {
          bmr = 10 * profile.weight + 6.25 * profile.height - 5 * profile.age - 161;
        }
        // Activity multiplier based on form options (same as getMacroPerDay.ts)
        const activityMultipliers = {
          'no exercise': 1.2,      // Sedentary
          'moderate': 1.55,        // Moderate exercise
          'high': 1.725           // Very active
        };
        
        // Calculate TDEE (Total Daily Energy Expenditure)
        const tdee = bmr * activityMultipliers[profile.exerciseLevel];
        
        // Calculate weight difference and adjust calories accordingly (same as getMacroPerDay.ts)
        const weightDiff = Math.abs(profile.desiredWeight - profile.weight);
        const weeklyRate = 0.5; // Target 0.5kg per week change
        const caloriesPerKg = 7700; // Approximate calories per kg of body weight
        const dailyCalorieAdjustment = (caloriesPerKg * weeklyRate) / 7;
        
        // Adjust calories based on goal and desired weight
        let calories;
        switch (profile.goal) {
          case 'increase':
            calories = tdee + dailyCalorieAdjustment;
            break;
          case 'decrease':
            calories = tdee - dailyCalorieAdjustment;
            break;
          case 'maintain':
            calories = tdee;
            break;
          default:
            calories = tdee;
        }
        const macroSplit = {
          protein: 0.25,  // ~25% of calories
          fat: 0.26,      // ~26%
          carbs: 0.49     // ~49%
        };
        
        const protein = Math.round((calories * macroSplit.protein) / 4);
        const fat = Math.round((calories * macroSplit.fat) / 9);
        const carbs = Math.round((calories * macroSplit.carbs) / 4);
        
        const newDailyGoals = {
          calories: Math.round(calories),
          protein,
          carbs,
          fat,
        };
        
        // Store in cache (limit cache size to prevent memory leaks)
        if (dailyGoalsCache.size > MAX_CACHE_SIZE) {
          const firstKey = dailyGoalsCache.keys().next().value;
          if (firstKey !== undefined) {
            dailyGoalsCache.delete(firstKey);
          }
        }
        dailyGoalsCache.set(cacheKey, newDailyGoals);
        
        set({ dailyGoals: newDailyGoals });
      },
      setDailyGoals: (goals) => {
        set({ dailyGoals: goals });
      },
      updateNutritionHistory: () => {
        const { entries } = get();
        
        // Create a cache key from the entries data
        // We'll use the length and the first/last entry IDs as a simple cache key
        const cacheKey = entries.length > 0 
          ? `${entries.length}-${entries[0].id}-${entries[entries.length-1].id}`
          : 'empty';
        
        // Check if we have a cached result
        if (historyCache.has(cacheKey)) {
          const cachedHistory = historyCache.get(cacheKey);
          set({ nutritionHistory: cachedHistory });
          return;
        }
        
        // Define the type for our grouped data
        type NutritionHistoryItem = {
          date: string;
          calories: number;
          protein: number;
          carbs: number;
          fat: number;
          sugars: number;
          fibers: number;
        };
        
        type GroupedData = {
          [key: string]: NutritionHistoryItem;
        };
        
        // Group entries by date
        const groupedByDate = entries.reduce<GroupedData>((acc, entry) => {
          if (!acc[entry.date]) {
            acc[entry.date] = {
              date: entry.date,
              calories: 0,
              protein: 0,
              carbs: 0,
              fat: 0,
              sugars: 0,
              fibers: 0,
            };
          }
          
          acc[entry.date].calories += entry.calories || 0;
          acc[entry.date].protein += entry.protein || 0;
          acc[entry.date].carbs += entry.carbs || 0;
          acc[entry.date].fat += entry.fat || 0;
          acc[entry.date].sugars += entry.sugars || 0;
          acc[entry.date].fibers += entry.fibers || 0;
          
          return acc;
        }, {});
        
        // Convert to array and sort by date
        const history = Object.values(groupedByDate).sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        // Store in cache (limit cache size to prevent memory leaks)
        if (historyCache.size > MAX_CACHE_SIZE) {
          const firstKey = historyCache.keys().next().value;
          if (firstKey !== undefined) {
            historyCache.delete(firstKey);
          }
        }
        historyCache.set(cacheKey, history);
        
        set({ nutritionHistory: history });
      },
    }),
    {
      name: 'nutrition-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

// Custom hooks for memoized selectors

// Hook to get daily goals with memoization
export const useDailyGoals = () => {
  const dailyGoals = useNutritionStore(state => state.dailyGoals);
  return useMemo(() => dailyGoals, [
    dailyGoals.calories,
    dailyGoals.protein,
    dailyGoals.carbs,
    dailyGoals.fat
  ]);
};

// Hook to get nutrition history with memoization
export const useNutritionHistory = () => {
  const nutritionHistory = useNutritionStore(state => state.nutritionHistory);
  return useMemo(() => nutritionHistory, [JSON.stringify(nutritionHistory)]);
};

// Hook to get entries for a specific date with memoization
export const useEntriesForDate = (date: string) => {
  const entries = useNutritionStore(state => state.entries);
  
  return useMemo(() => {
    return entries.filter(entry => entry.date === date);
  }, [entries, date]);
};

// Hook to get total nutrition for today with memoization
export const useTodayNutrition = () => {
  const entries = useNutritionStore(state => state.entries);
  
  return useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const todayEntries = entries.filter(entry => entry.date === today);
    
    return todayEntries.reduce((acc, entry) => {
      return {
        calories: acc.calories + (entry.calories || 0),
        protein: acc.protein + (entry.protein || 0),
        carbs: acc.carbs + (entry.carbs || 0),
        fat: acc.fat + (entry.fat || 0),
        sugars: acc.sugars + (entry.sugars || 0),
        fibers: acc.fibers + (entry.fibers || 0),
      };
    }, { calories: 0, protein: 0, carbs: 0, fat: 0, sugars: 0, fibers: 0 });
  }, [entries]);
};

// Hook to get progress towards daily goals with memoization
export const useNutritionProgress = () => {
  const dailyGoals = useDailyGoals();
  const todayNutrition = useTodayNutrition();
  
  return useMemo(() => {
    return {
      calories: {
        current: todayNutrition.calories,
        goal: dailyGoals.calories,
        percentage: Math.min(100, Math.round((todayNutrition.calories / dailyGoals.calories) * 100) || 0)
      },
      protein: {
        current: todayNutrition.protein,
        goal: dailyGoals.protein,
        percentage: Math.min(100, Math.round((todayNutrition.protein / dailyGoals.protein) * 100) || 0)
      },
      carbs: {
        current: todayNutrition.carbs,
        goal: dailyGoals.carbs,
        percentage: Math.min(100, Math.round((todayNutrition.carbs / dailyGoals.carbs) * 100) || 0)
      },
      fat: {
        current: todayNutrition.fat,
        goal: dailyGoals.fat,
        percentage: Math.min(100, Math.round((todayNutrition.fat / dailyGoals.fat) * 100) || 0)
      }
    };
  }, [dailyGoals, todayNutrition]);
};
