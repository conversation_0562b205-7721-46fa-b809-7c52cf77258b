import React, { useMemo, useState } from "react"
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from "react-native"
import { Activity, ChevronDown, Droplets, Moon } from "lucide-react-native"
import { colors } from "@/constants/colors"
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils"
import Slider from "@react-native-community/slider"
import { useFormDataStore } from "@/store/formDataStore"
import { moderateScale, scale, verticalScale } from "react-native-size-matters"
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from "react-i18next";

interface LifestyleStepProps {
  stepIndex: number
  isActive?: boolean
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    content: { padding: scale(20) },
    iconContainer: {
      width: scale(64),
      height: scale(64),
      borderRadius: scale(32),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    formField: { marginBottom: verticalScale(24) },
    label: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      marginBottom: verticalScale(8),
      color: theme.colors.gray[700],
    },
    selectInput: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    selectText: { fontSize: moderateScale(14), color: colors.black },
    placeholderText: { fontSize: moderateScale(14), color: colors.gray[400] },
    dropdownOptions: {
      position: "absolute",
      top: verticalScale(50),
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      zIndex: 9999,
      maxHeight: 170,
      elevation: 10,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    dropdownOption: {
      padding: moderateScale(14),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    dropdownOptionText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
    buttonGroup: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: theme.colors.primary.main,
      borderRadius: scale(theme.radii.md),
      overflow: "hidden",
    },
    button: {
      flex: 1,
      padding: moderateScale(16),
      alignItems: "center",
      justifyContent: "center",
    },
    activeButton: { backgroundColor: theme.colors.primary.main },
    buttonText: {
      color: theme.colors.primary.main,
      fontSize: moderateScale(theme.fontSize.xs),
    },
    activeButtonText: { color: theme.colors.secondary[50] },
    sliderHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: verticalScale(8),
    },
    sliderLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      color: theme.colors.gray[700],
      marginLeft: scale(8),
    },
    sliderValue: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: theme.colors.primary.main,
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    sliderContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: verticalScale(8),
    },
    slider: { flex: 1, height: verticalScale(40) },
    sliderEmoji: {
      fontSize: moderateScale(theme.fontSize.xl),
      marginHorizontal: scale(8),
    },
  });


export const LifestyleStep: React.FC<LifestyleStepProps> = ({ stepIndex, isActive = false }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { formData, updateLifestyle } = useFormDataStore()
  const [sleep, setSleep] = useState(formData.basicInfo.sleep || 8)
  const [hydration, setHydration] = useState(formData.basicInfo.hydration || 8)
  const [showDietOptions, setShowDietOptions] = useState(false)

  const handleDietChange = (value: string) => {
    updateLifestyle({ diet: value })
    setShowDietOptions(false)
    trackUserInteraction("Form Field Update", {
      section: "lifestyle",
      field: "diet",
      value,
    })
  }

  const handleLifestyleChange = (field: string, value: string) => {
    updateLifestyle({ [field]: value })
    trackUserInteraction("Form Field Update", {
      section: "lifestyle",
      field,
      value,
    })
  }

  if (!isActive) return null

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <Activity size={32} color={colors.primary} />
      </View>

      <Text style={styles.title}>{t('lifestyle.title')}</Text>
      <Text style={styles.subtitle}>{t('lifestyle.subtitle')}</Text>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('lifestyle.diet.question')}</Text>
        
        <View>
          <TouchableOpacity style={styles.selectInput} onPress={() => setShowDietOptions(!showDietOptions)}>
            <Text style={formData.basicInfo.diet ? styles.selectText : styles.placeholderText}>
              {formData.basicInfo.diet || t('lifestyle.diet.placeholder')}
            </Text>
            { showDietOptions ? (
              <ChevronDown size={16} color={colors.gray[400]} style={{transform: [{rotate: '180deg'}]}} />
            ) : (
              <ChevronDown size={16} color={colors.gray[400]} />
            )}
            </TouchableOpacity>

          {showDietOptions && (
            <View style={styles.dropdownOptions}>
               <ScrollView
                                      showsVerticalScrollIndicator={true}
                                      nestedScrollEnabled={true}>
              {[t('lifestyle.diet.vegetarian'), t('lifestyle.diet.nonVegetarian'), t('lifestyle.diet.vegan'), t('lifestyle.diet.pescatarian'), t('lifestyle.diet.keto'), t('lifestyle.diet.paleo')].map((option) => (
                <TouchableOpacity key={option} style={styles.dropdownOption} onPress={() => handleDietChange(option)}>
                  <Text style={styles.dropdownOptionText}>{option}</Text>
                </TouchableOpacity>
              ))}
              </ScrollView>
            </View>
          )}
        </View>
      </View>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('lifestyle.exercise.question')}</Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.exercise === "Yes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("exercise", "Yes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.exercise === "Yes" && styles.activeButtonText]}>
              {t('common.yes')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.exercise === "No" && styles.activeButton]}
            onPress={() => handleLifestyleChange("exercise", "No")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.exercise === "No" && styles.activeButtonText]}>{t('common.no')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.exercise === "Sometimes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("exercise", "Sometimes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.exercise === "Sometimes" && styles.activeButtonText]}>
              {t('common.sometimes')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('lifestyle.drinking.question')}</Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.drinking === "Yes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("drinking", "Yes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.drinking === "Yes" && styles.activeButtonText]}>
              {t('common.yes')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.drinking === "No" && styles.activeButton]}
            onPress={() => handleLifestyleChange("drinking", "No")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.drinking === "No" && styles.activeButtonText]}>{t('common.no')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.drinking === "Sometimes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("drinking", "Sometimes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.drinking === "Sometimes" && styles.activeButtonText]}>
              {t('common.sometimes')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('lifestyle.smoking.question')}</Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.smoking === "Yes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("smoking", "Yes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.smoking === "Yes" && styles.activeButtonText]}>
              {t('common.yes')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.smoking === "No" && styles.activeButton]}
            onPress={() => handleLifestyleChange("smoking", "No")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.smoking === "No" && styles.activeButtonText]}>{t('common.no')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, formData.basicInfo.smoking === "Sometimes" && styles.activeButton]}
            onPress={() => handleLifestyleChange("smoking", "Sometimes")}
          >
            <Text style={[styles.buttonText, formData.basicInfo.smoking === "Sometimes" && styles.activeButtonText]}>
              {t('common.sometimes')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formField}>
        <View style={styles.sliderHeader}>
          <Moon size={20} color={colors.primary} />
          <Text style={styles.sliderLabel}>{t('lifestyle.sleep.question')}</Text>
        </View>
        <Text style={styles.sliderValue}>{t('lifestyle.sleep.value', { sleep })}</Text>
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderEmoji}>😴</Text>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={15}
            step={0.5}
            value={formData.basicInfo.sleep}
            onValueChange={(v) => {setSleep(v)}} 
            onSlidingComplete={(value) => updateLifestyle({ sleep: value })}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.gray[300]}
            thumbTintColor={colors.primary}
          />
          <Text style={styles.sliderEmoji}>😊</Text>
        </View>
      </View>

      <View style={styles.formField}>
        <View style={styles.sliderHeader}>
          <Droplets size={20} color={colors.primary} />
          <Text style={styles.sliderLabel}>{t('lifestyle.hydration.question')}</Text>
        </View>
        <Text style={styles.sliderValue}>
          {t('lifestyle.hydration.value', { hydration, liters: (hydration * 0.25).toFixed(1) })}
        </Text>
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderEmoji}>💧</Text>
          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={20}
            step={1}
            value={formData.basicInfo.hydration}
            onValueChange={(v) => {setHydration(v)}} 
            onSlidingComplete={(value) => updateLifestyle({ hydration: value })}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.gray[300]}
            thumbTintColor={colors.primary}
          />
          <Text style={styles.sliderEmoji}>🌊</Text>
        </View>
      </View>
    </ScrollView>
  )
}

// const styles = StyleSheet.create({
//   container: { flex: 1 },
//   content: { padding: scale(20) },
//   iconContainer: {
//     width: scale(64),
//     height: scale(64),
//     borderRadius: scale(32),
//     backgroundColor: colors.primary + "20",
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: moderateScale(22),
//     fontWeight: "700",
//     color: colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   formField: { marginBottom: verticalScale(24) },
//   label: {
//     fontSize: moderateScale(14),
//     fontWeight: "500",
//     marginBottom: verticalScale(8),
//     color: colors.gray[700],
//   },
//   selectInput: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: scale(12),
//     padding: moderateScale(14),
//     backgroundColor: theme.colors.secondary[50],
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//   },
//   selectText: { fontSize: moderateScale(14), color: colors.black },
//   placeholderText: { fontSize: moderateScale(14), color: colors.gray[400] },
//   dropdownOptions: {
//     position: "absolute",
//     top: verticalScale(50),
//     left: 0,
//     right: 0,
//     backgroundColor: theme.colors.secondary[50],
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: scale(12),
//     zIndex: 9999,
//     maxHeight:170,
//     elevation: 10,
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//   },
//   dropdownOption: {
//     padding: moderateScale(14),
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   dropdownOptionText: { fontSize: moderateScale(14), color: colors.black },
//   buttonGroup: {
//     flexDirection: "row",
//     borderWidth: 1,
//     borderColor: colors.primary,
//     borderRadius: scale(12),
//     overflow: "hidden",
//   },
//   button: {
//     flex: 1,
//     padding: moderateScale(16),
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   activeButton: { backgroundColor: colors.primary },
//   buttonText: { color: colors.primary, fontSize: moderateScale(12) },
//   activeButtonText: { color: theme.colors.secondary[50] },
//   sliderHeader: {
//     flexDirection: "row",
//     alignItems: "center",
//     marginBottom: verticalScale(8),
//   },
//   sliderLabel: {
//     fontSize: moderateScale(14),
//     fontWeight: "500",
//     color: colors.gray[700],
//     marginLeft: scale(8),
//   },
//   sliderValue: {
//     fontSize: moderateScale(14),
//     fontWeight: "600",
//     color: colors.primary,
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   sliderContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     marginTop: verticalScale(8),
//   },
//   slider: { flex: 1, height: verticalScale(40) },
//   sliderEmoji: {
//     fontSize: moderateScale(20),
//     marginHorizontal: scale(8),
//   },
// })
