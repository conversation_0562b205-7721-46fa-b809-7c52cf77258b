import React from "react";
import {
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  FlexAlignType,
  Platform,
} from "react-native";
import Markdown from "react-native-markdown-display";
import logger from "@/utils/logger/logger";
import { colors } from "@/constants/colors";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";

interface WhatsAppStyleMarkdownBubbleProps {
  content?: string;
  style?: ViewStyle;
  bubbleStyle?: ViewStyle | ViewStyle[];
  textStyle?: TextStyle;
}

const WhatsAppStyleMarkdownBubble: React.FC<
  WhatsAppStyleMarkdownBubbleProps
> = ({ content = "", style, bubbleStyle, textStyle }) => {
  // Detect if content has structured elements like lists, tables, or multiple lines
  const hasStructuredContent = (text: string): boolean => {
    return (
      text.includes("- ") || text.includes("|") || text.split("\n").length > 2
    );
  };

  // Determine if the content is structured
  const isStructured = hasStructuredContent(content);


  // Create base markdown styles
  const baseMarkdownStyles = StyleSheet.create({
    // Body and general text
    body: {
      fontSize: moderateScale(15),
      lineHeight: moderateScale(21),
      marginBottom: 0,
      marginTop: 0,
    },

    // Paragraph
    paragraph: {
      fontSize: moderateScale(15),
      lineHeight: moderateScale(21),
      marginBottom: 0,
      marginTop: 0,
      padding: 0,
    },

    // Headings
    heading1: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },
    heading2: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },
    heading3: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },
    heading4: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },
    heading5: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },
    heading6: {
      fontSize: moderateScale(15),
      fontWeight: "bold",
      marginVertical: verticalScale(8),
    },

    // Lists
    bullet_list: {
      marginVertical: verticalScale(2),
    },
    ordered_list: {
      marginVertical: verticalScale(8),
    },
    list_item: {
      flexDirection: "row",
      marginBottom: verticalScale(8),
      alignItems: "flex-start",
    },
    bullet: {
      fontSize: moderateScale(15),
      lineHeight: moderateScale(21),
      marginRight: scale(8),
    },

    // Strong/Bold text
    strong: {
      fontWeight: "bold",
      fontSize: moderateScale(15),
    },

    // Tables
    table: {
      marginVertical: verticalScale(10),
      borderWidth: scale(1),
      borderColor: "#ddd",
      borderRadius: moderateScale(4),
      overflow: "hidden",
    },
    thead: {
      backgroundColor: "#f9f9f9",
      borderBottomWidth: scale(1),
      borderBottomColor: "#ddd",
    },
    tbody: {},
    th: {
      flex: 1,
      padding: scale(8),
      justifyContent: "center",
      borderRightWidth: scale(1),
      borderRightColor: "#ddd",
    },
    tr: {
      flexDirection: "row",
      borderBottomWidth: scale(1),
      borderBottomColor: "#ddd",
    },
    td: {
      flex: 1,
      padding: scale(8),
      justifyContent: "center",
      borderRightWidth: scale(1),
      borderRightColor: "#ddd",
    },

    // Table text
    table_header_cell: {
      fontWeight: "bold",
      fontSize: moderateScale(16),
    },
    table_cell: {
      fontSize: moderateScale(16),
    },

    // Links
    link: {
      color: "#007bff",
      textDecorationLine: "underline",
    },

    // Code
    code_inline: {
      fontFamily: "monospace",
      backgroundColor: "rgba(0, 0, 0, 0.05)",
      padding: scale(2),
      borderRadius: moderateScale(3),
      fontSize: moderateScale(16),
    },
    code_block: {
      fontFamily: "monospace",
      backgroundColor: "rgba(0, 0, 0, 0.05)",
      padding: scale(8),
      borderRadius: moderateScale(4),
      fontSize: moderateScale(16),
    },

    // Blockquote
    blockquote: {
      borderLeftWidth: scale(4),
      borderLeftColor: "#ddd",
      paddingLeft: scale(8),
      marginLeft: scale(8),
      marginVertical: verticalScale(8),
    },
  });

  // Apply text color to all text styles
  const markdownStyles = Object.entries(baseMarkdownStyles).reduce(
    (acc, [key, style]) => {
      // Add text color to text-based styles
      if (
        [
          "body",
          "paragraph",
          "heading1",
          "heading2",
          "heading3",
          "heading4",
          "heading5",
          "heading6",
          "bullet",
          "strong",
          "table_header_cell",
          "table_cell",
          "link",
          "code_inline",
          "code_block",
        ].includes(key)
      ) {
        acc[key] = { ...style, color: textStyle?.color };
      } else {
        acc[key] = style;
      }
      return acc;
    },
    {} as Record<string, any>
  );

  return (
    <View style={style}>
      <View
        style={[
          bubbleStyle,
          isStructured ? styles.structuredBubble : styles.adaptiveBubble,
        ]}
      >
        <Markdown style={markdownStyles}>{content}</Markdown>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: scale(8),
    borderWidth: 1,
    borderColor: colors.primary,
  },
  bubble: {
    backgroundColor: "#E8E8E8",
    borderRadius: moderateScale(18),
    padding: scale(12),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: scale(1) },
    shadowOpacity: 0.2,
    shadowRadius: scale(1),
    elevation: 2,
  },
  // For structured content like lists and tables
  structuredBubble: {
    width: "100%",
    maxWidth: "100%",
    minWidth: Platform.OS === "android" ? scale(200) : scale(239),
  },
  // For simple text messages
  adaptiveBubble: {
    alignSelf: "flex-start",
    minWidth: scale(200),
    maxWidth: "100%",
  },
  contentContainer: {
    width: "100%",
  },
});
export default WhatsAppStyleMarkdownBubble;
