import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { useLanguage } from '@/hooks/useLanguage';
import { Globe } from 'lucide-react-native';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';

interface LanguageButtonProps {
  onPress: () => void;
}

export const LanguageButton: React.FC<LanguageButtonProps> = ({ onPress }) => {
  const { currentLanguageInfo, isManuallySelected } = useLanguage();

  // Get language code for display (first 2 characters, uppercase)
  const getLanguageCode = () => {
    if (!currentLanguageInfo) return 'EN';
    
    // For codes like 'pt-BR', take first part
    const code = currentLanguageInfo.code.split('-')[0];
    return code.toUpperCase();
  };

  // Get display text for current language
  const getDisplayText = () => {
    if (!currentLanguageInfo) return 'English';
    
    // Show native name for better recognition
    return currentLanguageInfo.nativeName;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Globe size={16} color={colors.primary} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.languageCode}>{getLanguageCode()}</Text>
          {isManuallySelected && <View style={styles.indicator} />}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateVerticalScale(6),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: moderateScale(20),
    paddingHorizontal: moderateScale(10),
    paddingVertical: moderateVerticalScale(6),
    minWidth: moderateScale(50),
  },
  iconContainer: {
    marginRight: moderateScale(4),
  },
  textContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  languageCode: {
    fontSize: moderateScale(12),
    fontWeight: '600',
    color: colors.gray[700],
  },
  indicator: {
    position: 'absolute',
    top: moderateVerticalScale(-2),
    right: moderateScale(-2),
    width: moderateScale(6),
    height: moderateScale(6),
    borderRadius: moderateScale(3),
    backgroundColor: colors.primary,
  },
});
