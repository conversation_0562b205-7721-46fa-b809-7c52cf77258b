{"common": {"error": "<PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "sometimes": "Bazen", "close": "Ka<PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "version": "sürüm *******"}, "welcome": "August ile konuşmaya başlamak için giriş yapın", "notFound": {"title": "Ups!", "message": "<PERSON>u ekran mevcut değil.", "goHome": "Ana ekrana git!"}, "library": {"title": "Sağlık Kütüphanesi"}, "specialists": {"title": "U<PERSON><PERSON>", "description": "Daha spesifik sağlık sorunları için uzman sağlık profesyonelleriyle görüşün. Aşağıdan bir uzman seçin:", "generalPhysician": {"title": "<PERSON><PERSON>", "description": "Genel sağlık sorunları ve birincil bakım için."}, "nutritionist": {"title": "Beslenme Uzmanı", "description": "<PERSON><PERSON><PERSON>, beslenme ve kilo yönetimi tavsiyeleri için."}, "cardiologist": {"title": "Kardiyolog", "description": "<PERSON><PERSON><PERSON> ilgili sorunlar ve kardiyovasküler sağlık için."}, "neurologist": {"title": "Nörolog", "description": "<PERSON><PERSON>, omurilik ve sinir sistemi sorunları için."}, "oncologist": {"title": "Onkolog", "description": "<PERSON><PERSON><PERSON> ilgili sorunlar ve teda<PERSON>er i<PERSON>."}, "endocrinologist": {"title": "Endokrinolog", "description": "<PERSON><PERSON><PERSON> il<PERSON> boz<PERSON> ve diyabet yönetimi i<PERSON>."}, "dermatologist": {"title": "Dermatolog", "description": "Cilt, saç ve tırnak rahatsızlıkları için."}, "psychiatrist": {"title": "Psikiyatrist", "description": "Ak<PERSON>l sağlığı sorunları ve psikolojik iyilik için."}}, "profile": {"title": "Profil", "defaultName": "<PERSON><PERSON><PERSON><PERSON>", "namePlaceholder": "Adınızı girin", "saving": "Kay<PERSON>ili<PERSON>r...", "noPhoneNumber": "Telefon numarası yok", "loggingOut": "<PERSON><PERSON><PERSON> kapatılıyor...", "about": {"title": "Hakkında", "description": "August hakkında daha fazla bilgi edinin"}, "whatsapp": {"title": "WhatsApp", "description": "WhatsApp'tan August ile sohbet edin"}, "refer": {"title": "Tavsiye Et", "description": "August'u sevdiniz mi? Arkadaşlarınızla paylaşın"}, "deleteAccount": {"title": "Hesabı sil", "description": "Gittiğinize üzüldük"}, "logout": {"title": "Çıkış Yap", "description": "Yakında geri dö<PERSON>ün. <PERSON>zi <PERSON>ğ<PERSON>"}, "shareMessage": "👋Hey, kullandığım bu harika uygulamayı inceleyin!\n\n\n\n➡️Hızlı ve güvenilir sağlık bilgileri ve rehberliği almak için August'u kullanıyorum. Cebinizde bir doktor gibi! Buradan inceleyin:", "error": {"loadFailed": "Kullanıcı verileri yüklenirken hata oluştu", "fetchError": "Kullanıcı verileri alınırken bir hata oluştu", "updateNameFailed": "Ad gü<PERSON>llenemedi", "updateNameError": "Ad güncellenirken bir hata oluştu", "loadFoodData": "Yiyecek verileri yüklenirken hata oluştu", "logoutError": "O<PERSON><PERSON> kapatma sırasında hata:", "shareError": "<PERSON><PERSON> hata:"}}, "error": {"title": "<PERSON><PERSON> so<PERSON> o<PERSON>", "checkLogs": "Daha fazla ayrıntı için lütfen cihaz günlüklerinizi kontrol edin.", "unknown": "Bilinmeyen hata", "unknownFile": "Bilinmeyen dosya", "unknownLine": "Bilinmeyen satır", "unknownColumn": "Bilinmeyen sütun"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON>", "searchCountries": "Ülkeleri ara", "validation": {"invalidPhone": "Lütfen geçerli bir telefon numarası girin", "invalidDigits": "Lütfen geçerli bir telefon numarası girin (7-15 hane)"}}, "header": {"title": "Sağlık endişeleriniz hakkında anında ve gizli bir şekilde bilgi edinin", "subtitle": "Düşünceli rehberlik. Acele yok. Karışıklık yok.", "emphasis": "<PERSON><PERSON><PERSON> netlik."}, "greeting": "<PERSON><PERSON><PERSON><PERSON> 👋", "phoneNumber": "Telefon Numarası", "requestOTP": "OTP İste", "otp": {"title": "Tek Kullanımlık Şifre", "verify": "OTP Doğrula", "sending": "Gönderiliyor...", "countdown": "{{countdown}} sn içinde OTP yeniden gönderilecek", "resend": "OTP'yi <PERSON>", "sentTo": "OTP şu numaraya g<PERSON>ildi: ", "whatsappSuffix": " WhatsApp üzerinden"}, "disclaimer": {"prefix": "Kaydolarak, bizim ", "continuePrefix": "<PERSON><PERSON>, bizim ", "termsOfService": "Hizmet Şartlarımızı", "and": " ve ", "privacyPolicy": "Gizlilik Politikamızı", "whatsappConsent": "kabul ediyorsunuz ve WhatsApp üzerinden güncellemeler ve hatırlatıcılar almayı kabul ediyorsunuz."}}, "onboarding": {"preAuth": {"welcome": {"title": "Ağustos'a Hoş Geldiniz!", "buttonText": "Başlayalım"}}, "postAuth": {"step1": {"title": "Hey!", "subtitle": "<PERSON> 👋", "description": "<PERSON><PERSON>, cihazınızda tüm sağlık meraklarınızı keşfedeceğiniz rahat bir köşe olarak düşünün.", "subdescription": "Aklındaki her şeyi sormakta özgürsün. Yarg<PERSON> yok, sınır yok!", "placeholder": "Sana ne diyeyim?"}, "step2": {"title": "<PERSON><PERSON><PERSON><PERSON> {{userName}},", "subtitle": "İşte yapabileceklerim:", "features": {"health": {"title": "Sağlık", "subtitle": "sorularınıza cevap verin"}, "nutrition": {"title": "Takip edin", "subtitle": "Makrolarınızı"}, "reports": {"title": "<PERSON><PERSON><PERSON> edin", "subtitle": "Raporlarınızı"}}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "careful": "Dikkatli", "accurate": "Do<PERSON><PERSON>"}, "features": {"symptoms": {"title": "Belirtilerinizi kontrol edin", "description": "Bir haftadır midem bulantılı. Bana ne oluyor?"}, "prescriptions": {"title": "Reçetenizi analiz edin", "description": "Reçetenizi yü<PERSON>in ve bir doktor gibi anlayın."}, "medicine": {"title": "İlacınızı tanıyın", "description": "PCOS'um i<PERSON><PERSON>, ADHD haplarımla etkileşime giriyor mu?"}, "plans": {"title": "Kişiselleştirilmiş planlar edinin", "description": "HbA1c seviyemi düşürmek için beslenme ve fitness planı verebilir misin?"}}, "buttons": {"getStarted": "Başlayın", "next": "<PERSON><PERSON><PERSON>"}, "errors": {"nameRequired": "Lütfen adınızı girin"}}, "tabs": {"chat": "<PERSON><PERSON><PERSON>", "discover": "Keşfet", "nutrition": "Beslenme", "personalize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chat": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "me": "<PERSON>", "augustName": "<PERSON><PERSON><PERSON><PERSON>", "input": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>'a sorun...", "disclaimer": "Ağustos yanlış yapabilir. Bir doktorla doğrulayın"}, "list": {"loadingMessages": "Mesajlar yükleniyor...", "noMessages": "<PERSON><PERSON><PERSON>z mesaj yok. <PERSON><PERSON><PERSON>e başlayın!"}, "connection": {"offlineMessage": "Görünüşe göre çevrimdışısınız. Mesaj göndermek için tekrar bağlanın.", "connecting": "Bağlanılıyor...", "tryAgain": "<PERSON><PERSON><PERSON>"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON>", "speakInHindi": "Hintçe konuş", "notFeelingWell": "İyi hissetmiyorum", "whatIsMyBMI": "<PERSON><PERSON><PERSON> kitle indekslerim ne?", "nutritionAdvice": "Beslenme ta<PERSON>yesi", "sleepBetter": "<PERSON><PERSON> iyi uyuma"}, "citations": {"referenceText": "Bu konuşma ile ilgili daha fazla ayrıntı için lütfen şuraya bakın:"}, "actions": {"copiedToClipboard": "Panoya kopyalandı", "copied": "Kopyalandı"}, "share": {"introText": "👋Hey, <PERSON><PERSON><PERSON><PERSON> ile yaptığım sohbete bak:\n\n", "downloadText": "\n\n➡️Dost canlısı yapay zeka sağlık arkadaşınız Ağustos ile sohbet etmek için Ağustos'u indirin:\n"}}, "discover": {"nav": {"title": "Keşfet"}, "categories": {"all": "Tümü", "heartHealth": "<PERSON><PERSON><PERSON>lığı", "nutrition": "Beslenme", "mentalHealth": "<PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "Sağlıklı Yaşam"}, "cards": {"empty": "<PERSON>u kategori için kart mevcut değil"}, "sections": {"features": "<PERSON><PERSON><PERSON><PERSON>"}, "features": {"healthLibrary": {"title": "Sağlık Kütüphanesi", "description": "Tamamen ücretsiz gü<PERSON>r, sağlam ve güncel tıbbi bilgilere erişim."}, "nutritionTracker": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>z ye<PERSON>ğin fotoğrafını yükleyip tüm beslenme hedeflerinizi takip edebileceğinizi hiç düşündünüz mü? Ağustos bunu yapabilir!"}, "multilingualSupport": {"title": "Çok Dilli Destek", "description": "Ağustos ile rahat ettiğiniz herhangi bir dilde iletişim kurabilirsiniz! Ağustos her zaman dinlemek, desteklemek ve ihtiyacınız olduğunda size yanıt vermek için burada.", "samplePrompt": "Hintçe konuş"}, "labReportAnalysis": {"title": "Laborat<PERSON><PERSON>", "description": "Laboratuvar raporlarınız hakkında Ağustos ile konuştuğunuzda, son derece hassas sonuçlar elde edersiniz. Ağustos, %98,4'lük bir biyobelirteç çıkarma doğruluğuyla 4,7 milyondan fazla rapor işledi."}}}, "nutrition": {"nav": {"title": "Beslenme"}, "meals": {"title": "Öğ<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Her öğünün makrolarına göz atmak için dokunun"}, "upload": {"loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>..."}, "defaultFoodName": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON><PERSON>", "unknownTime": "Bilinmeyen zaman", "calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON><PERSON>", "carbs": "🍞 Karbonhidratlar", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 Yağ", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "<PERSON><PERSON>", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "İpuçları:", "macroBreakdown": "<PERSON><PERSON><PERSON> Öğesi Ayrıntısı", "noMacroData": "Bu gıda öğesi için makro besin öğesi verisi mevcut değil.", "disclaimer": "Sadece eğitim amaçlıdır. Daha fazla bilgi edinin", "disclaimerLink": "bura<PERSON>", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Cinsiyetiniz nedir?", "subtitle": "B<PERSON>, özel planınızı kalibre etmek için kullanılacaktır.", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın", "other": "<PERSON><PERSON><PERSON>"}, "age": {"title": "Ya<PERSON>ı<PERSON><PERSON>z kaç?", "subtitle": "<PERSON><PERSON>, günlük ihtiyaçlarınızı hesaplamak için kullanılacaktır."}, "measurements": {"title": "<PERSON> ve <PERSON>", "subtitle": "Lütfen boyunuzu santimetre ve kilonuzu kilogram cinsinden girin."}, "activity": {"title": "Aktivite Seviyesi", "subtitle": "Ne sıklıkla egzersiz yapıyorsunuz?", "none": "Egzersiz <PERSON>", "moderate": "Orta", "high": "<PERSON><PERSON><PERSON><PERSON>"}, "goal": {"title": "<PERSON><PERSON>", "subtitle": "Ne elde etmek istersiniz?", "increase": "Arttır", "maintain": "<PERSON><PERSON><PERSON>", "decrease": "Azalt"}, "targetWeight": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "He<PERSON><PERSON> kilonuz kaç kilogram?"}, "setup": {"title": "Planınızı Kurma", "subtitle": "Lütfen beslenme planınızı hazırlarken bekleyin."}, "review": {"title": "Planınızı Gözden Geçirin", "subtitle": "Beslenme planınızı gözden geçirin ve özelleştirin."}, "height": {"label": "Boy (cm)"}, "weight": {"label": "Kilo (kg)"}}, "error": {"updateFailed": "Beslenme verileri güncellenemedi. Lütfen tekrar deneyin.", "parsingError": "Gıda verileri ayrıştırılırken hata oluştu:", "fetchReportsFailed": "Rapor verileri alınırken hata oluştu. Lütfen tekrar deneyin.", "missingReportId": "<PERSON><PERSON> kimliği e<PERSON>"}}, "personalize": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "button": {"saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "review": "Gözden Geçir", "saveNext": "<PERSON><PERSON>"}}, "basicInfo": {"title": "<PERSON><PERSON> daha iyi tanıyalım", "subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, sağlık önerilerinizi kişiselleştirmemize yardımcı olur", "age": {"question": "Ya<PERSON>ı<PERSON><PERSON>z kaç?", "placeholder": "Yaşınızı girin"}, "sex": {"question": "Cinsiyetiniz nedir?", "placeholder": "Cinsiyetinizi seçin", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın", "other": "<PERSON><PERSON><PERSON>"}, "height": {"question": "Boyunuz kaç cm?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> girin"}, "weight": {"question": "Kilonuz kaç kg?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> girin"}}, "lifestyle": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Günlük alışkanlıklarınızı anlamak, size daha iyi öneriler sunmamıza yardımcı olur", "diet": {"question": "Ne tür bir diyet uyguluyorsunuz?", "placeholder": "Diyetinizi seçin", "vegetarian": "Vejetaryen", "nonVegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "Vegan", "pescatarian": "Pescataryen", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "D<PERSON><PERSON>li olarak egzersiz yapıyor musunuz?"}, "drinking": {"question": "Alkol tüketiyor musunuz?"}, "smoking": {"question": "Sigara içiyor musunuz?"}, "sleep": {"question": "Gece kaç saat uyuyorsunuz?", "value": "{{sleep}} saat"}, "hydration": {"question": "Günlük kaç bardak su içiyorsunuz?", "value": "{{hydration}} bar<PERSON> ({{liters}}L)"}}, "allergies": {"title": "Alerjiniz var mı?", "subtitle": "Al<PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON> gü<PERSON>li öneriler sunmamıza yardımcı olur", "allergyIndex": "<PERSON><PERSON><PERSON> {{index}}", "name": {"question": "Neye alerjiniz var?", "placeholder": "Alerjiyi girin (örneğin, Fındık, Toz)"}, "severity": {"question": "Bu ale<PERSON>ji ne kadar <PERSON>?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "mild": "<PERSON><PERSON><PERSON>", "moderate": "Orta", "severe": "<PERSON><PERSON><PERSON><PERSON>"}, "addButton": "Başka Alerji Ekle", "noAllergiesButton": "Alerjim yok"}, "medications": {"title": "İlaçlar ve Takviyeler", "subtitle": "<PERSON><PERSON> anda k<PERSON>ınız ilaçlar veya takviyeler hakkında bilgi verin", "medicationIndex": "İlaç {{index}}", "name": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> adını girin"}, "startDate": {"question": "Ne zaman almaya başladınız?", "placeholder": "<PERSON><PERSON><PERSON>"}, "type": {"label": "<PERSON><PERSON>ç <PERSON>ü<PERSON>ü", "shortTerm": "<PERSON><PERSON><PERSON>", "longTerm": "<PERSON><PERSON><PERSON>"}, "dose": {"label": "Doz", "placeholder": "<PERSON><PERSON><PERSON>"}, "unit": {"label": "<PERSON><PERSON><PERSON>"}, "frequency": {"label": "Sıklık", "placeholder": "<PERSON><PERSON>", "perDay": "günde", "perWeek": "haftada", "perMonth": "ayda", "perYear": "yılda"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puf", "drops": "<PERSON><PERSON>", "tsp": "ta<PERSON>ı kaşığı", "tbsp": "yemek ka<PERSON>", "cups": "bardak"}, "addButton": "Başka İlaç Ekle", "noMedicationsButton": "<PERSON><PERSON><PERSON>mıyorum", "calendar": {"title": "Başlangıç Tarihini Seçin"}}, "conditions": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Geçmişte veya şu anda sahip olduğunuz tıbbi durumlar hakkında bilgi verin", "conditionIndex": "Durum {{index}}", "name": {"label": "Durum Adı", "placeholder": "<PERSON><PERSON><PERSON> giri<PERSON> (<PERSON><PERSON><PERSON><PERSON>, vb.)"}, "since": {"question": "Bu durum ne zamandan beri sizde var?", "placeholder": "<PERSON><PERSON><PERSON>"}, "current": {"question": "Şu anda sizi rahatsız ediyor mu?"}, "medicated": {"question": "Bu durum için ilaç kullanıyor musunuz?"}, "addButton": "Başka Durum Ekle", "noConditionsButton": "<PERSON>ı<PERSON>i durumum yok", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "Üreme Sağlığı", "subtitle": "<PERSON><PERSON> bilgi<PERSON>, size daha ki<PERSON><PERSON>ş<PERSON>rilmiş sağlık önerileri sunmamıza yardımcı olur", "menstruation": {"question": "Daha önce adet gördü<PERSON>z mü?", "detailsTitle": "<PERSON><PERSON>", "regularity": {"question": "<PERSON><PERSON> dö<PERSON> ne kadar düzen<PERSON>?", "regular": "<PERSON><PERSON><PERSON><PERSON>", "irregular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notSure": "<PERSON><PERSON>"}, "cycleLength": {"label": "Ortalama adet döngü<PERSON>ü <PERSON> (gün)", "placeholder": "Döngü uzunluğ<PERSON>u girin"}, "flowDays": {"label": "<PERSON><PERSON><PERSON> s<PERSON>: {{flowDays}}", "min": "1 gün", "max": "15 gün"}, "padsPerDay": {"label": "Günlük ped/tampon sayısı: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "<PERSON>et döneminizde herhangi bir belirti yaşıyor musunuz?", "placeholder": "Belirt<PERSON><PERSON> girin (<PERSON><PERSON><PERSON><PERSON>, kramplar, baş ağrıları)"}}, "childbirth": {"question": "Doğum yaşadınız mı?", "detailsTitle": "Doğum Detayları", "children": {"label": "Çocuk Sayısı"}, "pregnancies": {"label": "Gebelik <PERSON>ı"}, "complications": {"question": "Gebelik veya doğum sırasında herhangi bir komplikasyon yaşadınız mı?", "placeholder": "Komplikasyonları girin (varsa)"}}}, "review": {"title": "Bilgilerinizi Gözden Geçirin", "subtitle": "Göndermeden önce verdiğiniz bilgileri gözden geçirin", "sections": {"basicInfo": "<PERSON><PERSON>", "lifestyle": "<PERSON><PERSON><PERSON>", "allergies": "<PERSON><PERSON><PERSON><PERSON>", "medications": "İlaçlar ve Takviyeler", "conditions": "<PERSON><PERSON><PERSON><PERSON>", "reproductive": "Üreme Sağlığı", "menstruationDetails": "<PERSON><PERSON>", "childbirthDetails": "Doğum Detayları"}, "fields": {"age": "Yaş:", "sex": "Cinsiyet:", "height": "Boy:", "weight": "Kilo:", "diet": "Diyet:", "exercise": "Egzersiz:", "drinking": "Alkol Kullanımı:", "smoking": "Sigara Kullanımı:", "sleep": "Uyku:", "hydration": "Hidrasyon:", "allergyIndex": "{{index}}. <PERSON><PERSON><PERSON>:", "dose": "Doz:", "frequency": "Sıklık:", "type": "Tip:", "since": "Ne zamandan beri:", "currentlyActive": "Şu anda aktif:", "takingMedication": "<PERSON><PERSON><PERSON> kullanıyor musunuz:", "hasMenstruated": "<PERSON><PERSON> gördünüz mü:", "regularity": "Düzenlilik:", "cycleLength": "Döngü Uzunluğu:", "flowDays": "<PERSON><PERSON><PERSON>:", "padsPerDay": "Günlük Ped/Tampon Sayısı:", "hasChildbirth": "Doğum yaşadınız mı:", "children": "Çocuklar:", "pregnancies": "Gebelikler:"}, "notProvided": "Sağlanmadı", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "Günlük {{sleep}} saat uyku", "hydration": "Günlük {{hydration}} bardak ({{liters}}L) sıvı tüketimi", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} gün"}, "noData": {"allergies": "<PERSON><PERSON><PERSON> bi<PERSON> giri<PERSON>i", "medications": "İlaç bilgisi girilmedi", "conditions": "Tıbbi durum bilgisi girilmedi"}, "submitButton": "Bilgileri Gönder"}, "success": {"title": "Bilgiler Güncellendi!", "message": "Sağlık bilgilerinizi sağladığınız için teşekkür ederiz. Bunu deneyiminizi kişiselleştirmek ve daha iyi öneriler sunmak için kullanacağız.", "benefits": {"insights": "Kişiselleştirilmiş sağlık içgörüleri", "reminders": "Daha iyi ilaç <PERSON>ı<PERSON>ı<PERSON>ıları", "recommendations": "Kişiye özel sağlık önerileri"}, "continueButton": "<PERSON><PERSON><PERSON>"}, "permissions": {"microphonePermissionDenied": "Mikrofon izni reddedildi", "microphoneAccessDescription": "August, ses kaydı yapmak ve sesli notlar göndermek için mikrofonunuza erişmesi gerekiyor", "permissionDenied": "<PERSON><PERSON>dedildi", "cameraPermissionRequired": "<PERSON><PERSON>un çalışması için kamera izinlerine ihtiyacımız var!", "mediaLibraryPermissionRequired": "<PERSON><PERSON>un çalışması için medya kitaplığı izinlerine ihtiyacımız var!"}, "voiceRecording": {"recordingTooLong": "Kayıt Çok Uzun", "recordingTooLongMessage": "Ses kayıtları 5 dakikadan kısa olmalıdır. Lütfen daha kısa bir mesaj kaydedin."}, "errors": {"uploadFailed": "Yükleme Başarısız Oldu", "voiceUploadFailed": "<PERSON>s kaydı yüklenemedi.", "voiceRecordingFailed": "<PERSON>s kaydı gönder<PERSON>medi", "failedToStopRecording": "Kaydı durdurma başarısız oldu", "photoUploadFailed": "Foto<PERSON><PERSON><PERSON>.", "failedToTakePhoto": "Fotoğ<PERSON><PERSON>", "imageUploadFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {{fileName}}", "failedToPickImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentUploadFailed": "Belge yüklenemedi: {{fileName}}", "failedToPickDocument": "<PERSON><PERSON> seç<PERSON>medi"}, "audioPlayer": {"downloadingAudio": "Ses indiriliyor...", "loadingAudio": "Ses yü<PERSON>yor..."}, "mediaProcessing": {"processingFile": "Dosyanız İşleniyor", "uploadingSecuring": "<PERSON><PERSON><PERSON>r ve gü<PERSON>li hale getiriliyor...", "analyzingContent": "Belge içeriği analiz ediliyor...", "extractingInfo": "Önemli bilgiler çıkarılıyor...", "processingInsights": "Bilgiler işleniyor...", "preparingResponse": "Detaylı yanıt hazırlanıyor...", "finalizingResponse": "<PERSON><PERSON>t sonlandırılıyor..."}, "attachments": {"voiceMessage": "<PERSON><PERSON><PERSON>", "image": "[RESİM]", "pdf": "[PDF]", "voice": "[SES KAYDI]"}, "pdf": {"loadingPdf": "PDF yükleniyor..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "seç<PERSON>"}, "mediaUpload": {"photoLibrary": "<PERSON>oto<PERSON><PERSON><PERSON>", "takePhoto": "Fotoğ<PERSON><PERSON>", "chooseFile": "<PERSON><PERSON><PERSON>"}, "comingSoon": {"title": "Yakında!", "description": " <PERSON>u anda geliştiril<PERSON> aşamasında. Güncellemeler için bizi takip edin!", "buttonText": "<PERSON><PERSON><PERSON><PERSON>!"}, "clipboard": {"success": "Bağlantı panoya kopyalandı"}, "mediaPhotos": {"emptyState": "Henüz kayıt yok."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON><PERSON>", "nutrition": {"totalCalories": "<PERSON><PERSON>lor<PERSON>", "proteins": "<PERSON><PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fat": "<PERSON><PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>", "fibers": "<PERSON><PERSON><PERSON>"}}, "reports": {"defaultTitle": "<PERSON><PERSON><PERSON>", "defaultFoodName": "<PERSON><PERSON><PERSON>", "defaultName": "Belge", "openButton": "<PERSON><PERSON>", "biomarker": {"headerBiomarker": "Biyobelirteç", "headerValue": "<PERSON><PERSON><PERSON>", "headerRefRange": "Referans <PERSON>lığı", "headerStatus": "Durum"}, "noData": "Mevcut biyobelirteç verisi yok"}, "setup": {"title": "Her şeyi sizin için ha<PERSON>ı<PERSON>ıyoruz", "inProgress": "Devam ediyor...", "progressMessages": {"0": "Günlük Kalori Hesaplanıyor", "1": "Makro Oranı Optimize Ediliyor", "2": "Yemek Planı Oluşturuluyor", "3": "Sağlık Puanı Hesaplanıyor", "4": "<PERSON><PERSON><PERSON>rılıyor"}, "checklistItems": {"0": "Sağlık verileriniz analiz ediliyor", "1": "Optimal beslenme planı hesaplanıyor", "2": "Önerileriniz kişiselleştiriliyor", "3": "Yemek önerileriniz oluşturuluyor", "4": "Kurulumunuz sonlandırılıyor"}}, "foodEntry": {"emptyState": "Henüz yemek girişi yok. Eklemek için yemeğinizin fotoğrafını çekin!"}, "nutritionReview": {"congratulations": "Tebrikler!", "subtitle": "<PERSON><PERSON> beslenme planınız hazır.", "submitButton": "<PERSON>i ba<PERSON>alım!", "dailyTargetsTitle": "Günlük Beslenme Hedefleriniz", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "protein": "<PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Hedeflerinize nasıl ulaşabilirsiniz:", "healthScores": "Sağlık puanlarınızı rutinizi iyileştirmek için kullanın", "trackFood": "<PERSON><PERSON> alımınızı sürekli takip edin", "followCalories": "Günlük kalori önerinize uyun", "balanceMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, protein ve yağ alımınızı dengeleyin"}}, "editModal": {"titlePrefix": "<PERSON><PERSON><PERSON><PERSON>", "cancelButton": "İptal", "saveButton": "<PERSON><PERSON><PERSON>"}, "processing": {"stages": {"scanning": "Yiyecek taranıyor...", "identifying": "Malzemeler tespit ediliyor...", "extracting": "<PERSON><PERSON> maddel<PERSON>karılıyor...", "finalizing": "Sonuçlar tamamlanıyor..."}, "error": {"defaultMessage": "Yiyecek tespit edilmedi", "subtitle": "Farklı bir a<PERSON><PERSON><PERSON>"}, "retakeButton": "<PERSON><PERSON><PERSON> ye<PERSON>den çekmek için dokunun", "notification": "Bittiğinde sizi bilgilendireceğiz!"}, "chart": {"title": "Zaman İçinde Beslenme Ta<PERSON>bi", "selectNutrient": "<PERSON><PERSON>:", "emptyState": "<PERSON><PERSON><PERSON><PERSON> beslenme bilgisi mevcut değil.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fat": "<PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON>", "defaultDate": "<PERSON><PERSON><PERSON><PERSON>", "defaultTime": "Bilinmeyen zaman", "saveChanges": "Değişiklikleri kaydet", "error": {"title": "<PERSON><PERSON>", "message": "Beslenme verileri güncellenemedi. Lütfen tekrar deneyin."}, "nutrition": {"calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON><PERSON>", "carbs": "Ekmek 🍞 Karbonhidratlar", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 Yağ"}, "macroBreakdown": {"title": "<PERSON><PERSON><PERSON>ı<PERSON>", "noData": "<PERSON>u gıda maddesi için mak<PERSON>sin verisi mevcut değil."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fat": "<PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Ayrıntılı Bilgi", "edit": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "enterValue": "<PERSON><PERSON><PERSON> giri<PERSON>", "notSet": "Belirtilmemiş", "age": "Yaş", "heightCm": "Boy (cm)", "weightKg": "Ağırlık (kg)", "targetWeight": "<PERSON><PERSON><PERSON>", "nutritionTargets": "<PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fats": "<PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Değerleri girin veya otomatik hesaplama için boş bırakın.", "autoCalculateNote": "Makrolar verilerinize göre otomatik olarak hesaplanır.", "validation": {"ageMin": "Yaş en az 18 olmalıdır.", "ageMax": "Yaş 125'in altında olmalıdır.", "heightMin": "Yükseklik en az 50 cm olmalıdır.", "heightMax": "Boy 250 cm'nin altında olmalıdır.", "weightMin": "Ağırlık en az 30kg olmalıdır.", "weightMax": "Ağırlık 500 kg'ın altında olmalıdır.", "targetWeightMin": "<PERSON><PERSON><PERSON> en az 30 kg olmalıdır.", "targetWeightMax": "<PERSON><PERSON><PERSON> 500 kg'ın altında olmalıdır.", "proteinMin": "Protein 0 veya daha fazla olmalı", "carbsMin": "Karbonhidratlar 0 veya daha fazla olmalıdır.", "fatsMin": "Yağlar 0 veya daha fazla olmalıdır."}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Ka<PERSON>on<PERSON>dratl<PERSON>", "fat": "<PERSON><PERSON>", "excess": "<PERSON>azla", "remaining": "kalan"}, "specialistConstants": {"nutritionist": {"name": "Beslenme Uzmanı", "description": "<PERSON><PERSON><PERSON>, beslenme ve sağlıklı beslenme alışkanlıkları konusunda uzman tavsiyesi", "featureName": "Beslenme Uzmanı"}, "cardiologist": {"name": "Kardiyolog", "description": "<PERSON><PERSON><PERSON>ğlığı ve kardiyovasküler rahatsızlıklarında uzman", "featureName": "Kardiyoloji <PERSON>"}, "neurologist": {"name": "Nörolog", "description": "<PERSON><PERSON>, omuril<PERSON> ve sinir sistemi bozukluk<PERSON><PERSON>na odaklanmıştır", "featureName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "oncologist": {"name": "Onkolog", "description": "<PERSON><PERSON><PERSON> teşhis<PERSON> ve tedavi seçeneklerinde uzman", "featureName": "Onkoloji <PERSON>"}, "endocrinologist": {"name": "Endokrinolog", "description": "Hormonal durumlar ve metabolik bozukluklar konusunda uzman", "featureName": "Endokrinoloji Uzmanı"}}, "discoverCards": {"categories": {"nutrition": "Beslenme", "heartHealth": "<PERSON><PERSON><PERSON>lığı", "mentalHealth": "Zihinsel Sağlık", "fitness": "Fitness", "wellness": "Sağlık"}, "titles": {"vitaminB12Recovery": "B12 Vitamini Eksikliğinden Kurtulmak Ne Kadar Sürer", "vitaminDeficiencyGanglion": "<PERSON><PERSON>i Ganglion Kistlerine Neden Olur", "vitaminDeficiencyHairFall": "Hangi Vitamin Eksikliği Saç Dökülmesine Neden Olur", "vitaminWaters": "Vitamin Suları Faydalı mıdır", "cholesterolHeadaches": "Yüksek Kolesterol Baş Ağrısına Neden Olur mu", "cholesterolEyes": "Gözlerde Görülebilen Yüksek Kolesterol Belirtileri Nelerdir", "diabetesHeadaches": "Diyabet Baş Ağrısına Neden Olabilir mi", "chestPainDrinking": "İçtikten Sonra Neden Göğüs Ağrısı Olur", "stressDizziness": "Stres Baş Dönmesine Neden Olabilir mi", "bulimiaFace": "Bulimia Yüzü Nedir", "kneeTwitch": "<PERSON>zi<PERSON> Neden <PERSON>", "noseTwitching": "<PERSON><PERSON>n Seğirmesi Neden <PERSON>şur", "piriformisVsSciatica": "Piriformis Sendromu ile Siyatik Arasındaki Farklar Nelerdir", "shoulderBladePinched": "<PERSON><PERSON><PERSON>inde Sıkışan Sinir Nasıl Serbest Bırakılır", "shoulderPinched": "Omuzda Sıkışan Sinir Nasıl Serbest Bırakılır", "meniscusTorn": "Yırtık Menisküs Nasıl Doğal Yoldan İyileştirilir", "hydrateQuickly": "Hızlıca Nasıl Hidrate Olunur", "periodConstipation": "Adet Döneminde Kabızlık Yaşamak Normal midir", "acneScars": "Sivilce İzlerinden Bir Hafta İçinde Doğal Yoldan Nasıl Kurtulunur", "perimenopausePregnancy": "Perimenopozda Hamile Ka<PERSON>ınır mı"}, "descriptions": {"vitaminB12Recovery": "B12 vitamini eksikliğinin iyileşme sürecini ve enerji seviyenizi artırmak için etkili çözüm yollarını keşfedin.", "vitaminDeficiencyGanglion": "Vücutta ganglion kistlerinin oluşumu ile vitamin eksiklikleri arasındaki bağlantıyı inceleyin.", "vitaminDeficiencyHairFall": "Temel vitamin eksikliğinin saç dökülmesine nasıl yol açabileceğini ve bunu önlemek için neler yapabileceğinizi öğrenin.", "vitaminWaters": "Günlük beslenmenizin bir parçası olarak vitamin sularının faydalarını ve olası dezavantajlarını ortaya çıkarın.", "cholesterolHeadaches": "Yüksek kolesterol seviyeleri ile baş ağrılarının başlaması arasındaki olası bağlantıyı inceleyin.", "cholesterolEyes": "Yüksek kolesterolün gözlerinizde nasıl ortaya çıkabileceğini ve nelere dikkat etmeniz gereken belirtileri öğrenin.", "diabetesHeadaches": "Diyabet ile günlük hayatta baş ağrısı oluşumu arasındaki ilişkiyi araştırın.", "chestPainDrinking": "Belirli içeceklerin tüketilmesinden sonra göğüs ağrısının ardındaki nedenleri keşfedin.", "stressDizziness": "Stresin dengenizi ve genel sağlığınızı nasıl etkileyerek baş dönmesine yol açabileceğini inceleyin.", "bulimiaFace": "Y<PERSON>z görünümündeki etkiler de dahil olmak üzere buliminin fiziksel belirtilerini anlayın.", "kneeTwitch": "İstemsiz diz seğirmesinin ardındaki olası nedenleri ve stres veya yorgunlukla ilişkisini araştırın.", "noseTwitching": "Burun seğirmesinin olası nedenlerini ve anksiyete veya diğer faktörlerle bağlantısını öğrenin.", "piriformisVsSciatica": "Piriformis sendromu ve siyatik sinir ağrısının semptomlarını karşılaştırarak durumunuzu daha iyi anlayın.", "shoulderBladePinched": "<PERSON><PERSON><PERSON> kemiğinizdeki sıkışmış siniri rahatlatmak ve hareket kabiliyetini geri kazanmak için etkili teknikleri keşfedin.", "shoulderPinched": "<PERSON><PERSON>z b<PERSON><PERSON>sindeki sinir sıkışıklığını hafifletmek için basit egzersizler ve germe hareketleri öğrenin.", "meniscusTorn": "Yırtık menisküsün iyileşmesini desteklemek için doğal yöntemleri ve egzersizleri keşfedin.", "hydrateQuickly": "Hızlı ve etkili bir şekilde yeniden hidrate olmak ve optimal vücut hidrasyonunu korumak için yollar bulun.", "periodConstipation": "Adet döneminde kabızlığın ardındaki nedenleri anlayın ve doğal çözüm yollarını öğrenin.", "acneScars": "Sivilce izlerinin görünümünü hızlı bir şekilde azaltmak için doğal çözüm yollarını ve cilt bakım ipuçlarını keşfedin.", "perimenopozPregnancy": "<PERSON><PERSON><PERSON><PERSON><PERSON>, doğurganlık hususları ve yaşamın bu döneminde neler bekleyebileceğiniz hakkında bilgi edinin."}}}