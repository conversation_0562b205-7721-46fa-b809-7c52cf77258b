import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Linking,
  Platform,
  Pressable,
  Modal,
  Animated,
  Button,
} from "react-native";
import * as Clipboard from "expo-clipboard";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import { colors, theme } from "@/constants/colors";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { Message } from "@/store/chatStore";
import { FileText, Circle, CheckCircle, Info, Globe, X, Play, Pause, Volume2, PlayIcon } from "lucide-react-native";
import MediaViewerModal from "./MediaViewerModal";
import logger from "@/utils/logger/logger";
import { WebView } from "react-native-webview";
import WhatsAppStyleMarkdownBubble from "./MarkdownBubble";
import {
  moderateScale,
  moderateVerticalScale,
} from "react-native-size-matters";
import DummyWaveform from './DummyWaveform';
import AudioWaveformPlayer from "./AudioWaveformPlayer";
import AudioWaveformPlayerAndroid from "./AudioWaveformPlayerAndroid";
import { downloadIfRecentVoice, deleteFile } from '@/utils/chat/downloadAudioToLocal';
import Toast from 'react-native-root-toast';
import { mediumHaptic } from "@/utils/haptics/haptics";
import { formatTimestamp } from "@/utils/formatTime/formatTimeStamp";

const TEXT_FONT_SIZE = Platform.OS == "android" ? 15 : 16;


interface MessageBubbleProps {
  message: Message;
  selectedMessages?: string[];
  handlePress: (messageId: string) => void;
  selectionState: {
    isInSelectionMode: boolean;
    isSelected: boolean;
    isFirstSelected: boolean;
  };
}

const MessageBubbleComponent: React.FC<MessageBubbleProps> = ({
  message,
  selectedMessages,
  handlePress,
  selectionState,
}) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<{
    uri: string;
    type: "image" | "pdf";
    signedUrl?: string;
    serverUrl?: string;
  } | null>(null);
  const [showCopyFeedback, setShowCopyFeedback] = useState(false);
  const [isLongPressing, setIsLongPressing] = useState(false);
  // Add animation value for entry animation
  const entryAnimation = useRef(new Animated.Value(0)).current;

  const { isInSelectionMode, isSelected, isFirstSelected } = selectionState;

  const [sound, setSound] = useState();
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState<string | null>(null);
  const [localPath, setLocalPath] = useState<string | null>(null);
  const [isLoadingVoice, setIsLoadingVoice] = useState(false);
  useEffect(() => {
    let isMounted = true;
    if (
      message.attachments &&
      //@ts-ignore
      (message.attachments[0]?.type === 'voice' || message.attachments["0"]?.type === 'voice')
    ) {
      const fetchPath = async () => {
        //@ts-ignore
        if (message.isRecent && !localPath) {
          setIsLoadingVoice(true); // Start loading
          try {
            const path = await downloadIfRecentVoice(message);
            if (isMounted) {
              setLocalPath(path);
            }
          } catch (error) {
            logger.error('Error downloading voice file:', error);
          } finally {
            if (isMounted) {
              setIsLoadingVoice(false); // Stop loading
            }
          }
          //@ts-ignore
        } else if (!message.isRecent) {
          // If it's not a recent message, we don't need to download
          setIsLoadingVoice(false);
        }
      };
      fetchPath();
    }
    return () => {
      isMounted = false;
    };
  }, [message]);

  const [showCitationsModal, setShowCitationsModal] = useState(false);
  const [modalAnimation] = useState(new Animated.Value(0));
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  const infoIconRef = useRef(null);

  // Run entry animation when component mounts
  useEffect(() => {
    // Start the animation with slower timing parameters
    Animated.spring(entryAnimation, {
      toValue: 1,
      useNativeDriver: true,
      friction: 12, // Increased friction to slow down the animation
      tension: 30,  // Reduced tension for a more gentle motion
      restDisplacementThreshold: 0.01, // More precise ending
      restSpeedThreshold: 0.01, // More precise ending
    }).start();
  }, []);

  const openCitationsModal = () => {
    if (infoIconRef.current) {
      //@ts-ignore
      infoIconRef.current.measure((x, y, width, height, pageX, pageY) => {
        setModalPosition({ x: pageX, y: pageY });
        setShowCitationsModal(true);

        // Reset and start animation
        modalAnimation.setValue(0);
        Animated.timing(modalAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    } else {
      // Fallback if ref doesn't work
      setShowCitationsModal(true);
    }
  };

  const DEMO_CITATIONS = [
    { title: "Mayo Clinic", url: "https://www.mayoclinic.org" },
    { title: "U.S. National Library of Medicine (PubMed)", url: "https://pubmed.ncbi.nlm.nih.gov" },
    { title: "National Institutes of Health (NIH)", url: "https://www.nih.gov" },
    { title: "August Health Library", url: "https://www.meetaugust.ai/en/library" },
  ];
  // Handle message press and long press
  const handleMessagePress = useCallback(() => {
    if (isInSelectionMode) {
      handlePress(message.id);
    }
  }, [isInSelectionMode, handlePress, message.id]);

  const handlePressIn = useCallback(() => {
    if (!isInSelectionMode) {
      setIsLongPressing(true);
    }
  }, [isInSelectionMode]);

  const handlePressOut = useCallback(() => {
    setIsLongPressing(false);
  }, []);

  const handleMessageLongPress = () => {
    if (!isInSelectionMode) {
      // Trigger haptic feedback
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        mediumHaptic()
      }
      // Call the handler to enter selection mode
      handlePress(message.id);
    }
  };

  // Hide copy feedback after 2 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showCopyFeedback) {
      timer = setTimeout(() => {
        setShowCopyFeedback(false);
      }, 2000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showCopyFeedback]);

  const handleCopyText = useCallback(async (text: string) => {
    if (!text) return;

    try {
      await Clipboard.setStringAsync(text);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      if (Platform.OS === 'ios') {
        Toast.show(t('chat.actions.copied'), {
          duration: Toast.durations.SHORT,
          position: Toast.positions.BOTTOM,
          shadow: true,
          animation: true,
          hideOnPress: true,
          delay: 0,
        });
      }
    } catch (error) {
      logger.error("Error copying text to clipboard:", error);
    }
  }, []);

  const isUser = useMemo(() => message.sender === "user", [message.sender]);

  const isImageOnly = useMemo(() => {
    return (
      !message.text &&
      message.attachments &&
      message.attachments.some((att) => att.type === "image")
    );
  }, [message.text, message.attachments]);

  const isPdfOnly = useMemo(() => {
    return (
      !message.text &&
      message.attachments &&
      message.attachments.some((att) => att.type === "pdf")
    );
  }, [message.text, message.attachments]);

  const isVoiceOnly = useMemo(() => {
    return (
      !message.text &&
      message.attachments &&
      message.attachments.some((att) => att.type === "voice")
    );
  }, [message.text, message.attachments]);

  const handleOpenMedia = useCallback(
    (
      uri: string | undefined,
      type: "image" | "pdf",
      signedUrl?: string,
      serverUrl?: string
    ) => {
      // Don't open media if in selection mode
      if (isInSelectionMode) {
        handlePress(message.id);
        return;
      }
      // For PDFs, open directly in external app instead of showing in modal
      if (type === "pdf") {
        // Use the best available URL (signedUrl, serverUrl, or uri)
        const pdfUrl = signedUrl || serverUrl || uri;

        // Make sure we have a valid URL before trying to open it
        if (!pdfUrl) {
          logger.error("No valid URL found for PDF");
          return;
        }

        // Open the PDF in an external app
        Linking.openURL(`https://docs.google.com/gview?url=${encodeURIComponent(pdfUrl)}`).catch((err) => {
          logger.error("Error opening PDF in external app:", err);
        });

        // Linking.openURL(pdfUrl).catch((err) => {
        //   logger.error("Error opening PDF in external app:", err);
        // });
        return;
      }

      // For other media types (images), continue using the modal
      if (!uri) {
        logger.error("No valid URI found for media");
        return;
      }

      setSelectedMedia({ uri, type, signedUrl, serverUrl });
      setModalVisible(true);
    },
    [isInSelectionMode]
  );

  const renderSelectionRadio = () => {
    if (!isInSelectionMode && !isSelected) return null;

    return (
      <TouchableOpacity
        style={styles.radioButton}
        onPress={() => handlePress(message.id)}
      >
        {isSelected ? (
          <View>
            <CheckCircle size={20} color={colors.primary} />
          </View>
        ) : (
          <Circle size={20} color={colors.gray[400]} />
        )}
      </TouchableOpacity>
    );
  };

  const detectLinks = (text: string) => {
    // URL regex pattern
    const urlPattern = /(https?:\/\/[^\s]+)/g;
    const parts = text.split(urlPattern);

    return parts.map((part, index) => {
      if (part.match(urlPattern)) {
        return (
          <Text
            key={index}
            style={[
              styles.text,
              isUser ? styles.userText : styles.botText,
              styles.link
            ]}
            onPress={() => {
              if (!isInSelectionMode) {
                Linking.openURL(part).catch((err) => {
                  logger.error("Error opening URL:", err);
                });
              }
            }}
          >
            {part}
          </Text>
        );
      }
      return (
        <Text
          key={index}
          style={[styles.text, isUser ? styles.userText : styles.botText]}
        >
          {part}
        </Text>
      );
    });
  };

  // Helper function to determine if we should show the waveform
  const shouldShowWaveform = () => {
    // For recent messages, show when localPath is ready
    //@ts-ignore
    if (message.isRecent) {
      return localPath !== null;
    }
    // For non-recent messages, always show (component handles everything internally)
    return true;
  };

  if (isVoiceOnly && message.attachments) {
    const voiceAttachment = message.attachments.find(
      (att) => att.type === "voice"
    );
    if (voiceAttachment) {
      return (
        <View style={styles.container}>
          <View style={styles.messageWrapper}>
            {!isUser && renderSelectionRadio()}
            <Pressable
              onPress={handleMessagePress}
              onLongPress={handleMessageLongPress}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              style={[
                styles.messageContainer,
                isUser ? styles.userContainer : styles.botContainer,
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.voiceBubbleContainer,
                  isUser ? styles.userVoiceBubble : styles.botVoiceBubble,
                  isLongPressing &&
                    (isUser
                      ? styles.userPressHighlight
                      : styles.botPressHighlight),
                ]}
                activeOpacity={0.8}
              >
                {isLoadingVoice ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={styles.loadingText}>{t('audioPlayer.loadingAudio')}</Text>
                  </View>
                ) : (
                  <View style={styles.waveformRow}>
                    {Platform.OS === 'ios' ? (
                      <AudioWaveformPlayer
                        uri={voiceAttachment.uri}
                        path={localPath || voiceAttachment.uri}
                        messageId={message.id}
                      />
                    ) : (
                      <AudioWaveformPlayerAndroid
                        uri={voiceAttachment.uri}
                        path={localPath || voiceAttachment.uri}
                        messageId={message.id}
                        currentlyPlayingId={currentlyPlayingId}
                        setCurrentlyPlayingId={setCurrentlyPlayingId}
                      />
                    )}
                  </View>
                )}
                
              </TouchableOpacity>
            </Pressable>
            {isUser && renderSelectionRadio()}
          </View>
        </View>
      );
    }
  }
  // For image-only messages, render a different layout
  if (isImageOnly && message.attachments) {
    const imageAttachment = message.attachments.find(
      (att) => att.type === "image"
    );

    if (imageAttachment) {
      return (
        <Animated.View style={[
          styles.container,
          {
            opacity: entryAnimation,
            transform: [
              {
                translateY: entryAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [isUser ? 20 : -20, 0],
                }),
              },
              {
                scale: entryAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
            ],
          }
        ]}>
          <View style={styles.messageWrapper}>
            {!isUser && renderSelectionRadio()}
            <Pressable
              onPress={handleMessagePress}
              onLongPress={handleMessageLongPress}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              style={[
                styles.messageContainer,
                isUser ? styles.userContainer : styles.botContainer,
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.imageOnlyContainer,
                  isLongPressing && (isUser ? styles.userPressHighlight : styles.botPressHighlight),
                ]}
                onPress={() => handleOpenMedia(imageAttachment.uri, "image")}
                onLongPress={(e) => {
                  // Prevent the default behavior and trigger selection
                  e.preventDefault();
                  handleMessageLongPress();
                }}
                delayLongPress={200}
              >
                <Image
                  source={{ uri: imageAttachment.uri }}
                  placeholder={require('@/assets/images/favicon.png')}
                  style={isUser ? styles.imageOnly : styles.botImageOnly}
                  resizeMode="cover"
                />
              </TouchableOpacity>

              {modalVisible && selectedMedia && (
                <MediaViewerModal
                  visible={modalVisible}
                  uri={selectedMedia.uri}
                  type={selectedMedia.type}
                  signedUrl={selectedMedia.signedUrl}
                  serverUrl={selectedMedia.serverUrl}
                  onClose={() => setModalVisible(false)}
                />

              )}
            </Pressable>
            {isUser && renderSelectionRadio()}
          </View>
        </Animated.View>
      );
    }
  }

  // For PDF-only messages, render a PDF preview
  if (isPdfOnly && message.attachments) {
    const pdfAttachment = message.attachments.find((att) => att.type === "pdf");

    if (pdfAttachment) {
      const [isWebViewLoading, setIsWebViewLoading] = useState(true);
      const pdfUrl =
        pdfAttachment.signedUrl || pdfAttachment.serverUrl || pdfAttachment.uri;

      return (
        <Animated.View style={[
          styles.container,
          {
            opacity: entryAnimation,
            transform: [
              {
                translateY: entryAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [isUser ? 20 : -20, 0],
                }),
              },
              {
                scale: entryAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
            ],
          }
        ]}>
          <View style={styles.messageWrapper}>
            {renderSelectionRadio()}
            <Pressable
              onPress={handleMessagePress}
              onLongPress={handleMessageLongPress}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              style={[
                styles.messageContainer,
                isUser ? styles.userContainer : styles.botContainer,
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.pdfPreviewContainer,
                  styles.pdfPreviewContainer,
                  isLongPressing && (isUser ? styles.userPressHighlight : styles.botPressHighlight),
                ]}
                onPress={() =>
                  handleOpenMedia(
                    pdfAttachment.uri,
                    "pdf",
                    pdfAttachment.signedUrl,
                    pdfAttachment.serverUrl
                  )
                }
                activeOpacity={0.9}
              >
                <View style={styles.pdfPreview}>
                  <WebView
                    source={{
                      uri: `https://docs.google.com/viewer?url=${encodeURIComponent(
                        pdfUrl
                      )}&embedded=true`,
                    }}
                    style={styles.pdfPreviewContent}
                    onLoadStart={() => setIsWebViewLoading(true)}
                    onLoadEnd={() => setIsWebViewLoading(false)}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    startInLoadingState={true}
                    scalesPageToFit={true}
                  />

                  {isWebViewLoading && (
                    <View style={styles.pdfLoadingContainer}>
                      <FileText size={24} color={colors.primary} />
                      <ActivityIndicator
                        size="small"
                        color={colors.primary}
                        style={styles.loadingIndicator}
                      />
                      <Text style={styles.pdfLabel}>{t('pdf.loadingPdf')}</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </Pressable>
          </View>
        </Animated.View>
      );
    }
  }
  // For regular messages (with or without attachments)
  return (
    <Animated.View style={[
      styles.container,
      {
        opacity: entryAnimation,
        transform: [
          {
            translateY: entryAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [isUser ? 20 : -20, 0],
            }),
          },
          {
            scale: entryAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1],
            }),
          },
        ],
      }
    ]}>
      <View style={styles.messageWrapper}>
        {!isUser && renderSelectionRadio()}
        <Pressable
          onPress={handleMessagePress}
          onLongPress={handleMessageLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={[
            styles.messageContainer,
            isUser ? styles.userContainer : styles.botContainer,
          ]}
        >
          <View
            style={[
              styles.bubble,
              isUser ? styles.userBubble : styles.botBubble,
              isInSelectionMode || isSelected
                ? styles.bubbleWithCheckbox
                : styles.bubbleWithoutCheckbox,
              isLongPressing && (isUser ? styles.userPressHighlight : styles.botPressHighlight),
            ]}
          >
            {!isUser && Platform.OS === 'ios' && (
              <TouchableOpacity
                ref={infoIconRef}
                style={styles.infoIconContainer}
                onPress={openCitationsModal}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <View style={styles.infoIconInner}>
                  <Text style={styles.infoText}>i</Text>
                </View>
              </TouchableOpacity>
            )}

            {message.text && (
              <View style={styles.textContainer}>
                {isUser ? (
                  <Text
                    suppressHighlighting={true}
                    style={[styles.text, styles.userText]}
                    onPress={() => {
                      if (isInSelectionMode) {
                        handleMessagePress();
                      }
                    }}
                    onLongPress={() => {
                      if (isInSelectionMode) {
                        handleMessagePress();
                      } else {
                        handleCopyText(message.text);
                      }
                    }}
                  >
                    {detectLinks(message.text)}
                  </Text>
                ) : (
                  <WhatsAppStyleMarkdownBubble
                    content={message.text}
                    textStyle={styles.botText}
                  />
                )}
            
                    <Text
                      style={[
                        styles.timestamp,
                        isUser ? styles.userTimestamp : styles.botTimestamp,
                      ]}
                    >
                      {formatTimestamp(message.timestamp)}
                    </Text>
                {showCopyFeedback && (
                  <View
                    style={[
                      styles.copyFeedback,
                      isUser ? styles.userCopyFeedback : styles.botCopyFeedback,
                    ]}
                  >
                    <Text style={styles.copyFeedbackText}>
                      {t('chat.actions.copiedToClipboard')}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {message.attachments &&
              message.attachments.map((attachment, index: number) => (
                <View
                  key={`${message.id}-${attachment.uri || ""}-${index}`}
                  style={styles.attachmentContainer}
                >
                  {attachment.type === "image" && (
                    <TouchableOpacity
                      onPress={() => handleOpenMedia(attachment.uri, "image")}
                    >
                      <Image
                        source={{ uri: attachment.uri }}
                        style={isUser ? styles.image : styles.botImage}
                        resizeMode="cover"
                      />
                    </TouchableOpacity>

                  )}
                  {attachment.type === "file" && (
                    <TouchableOpacity
                      style={styles.fileContainer}
                      onPress={() => handleOpenMedia(attachment.uri, "pdf")}
                    >
                      <View style={styles.fileIconContainer}>
                        <FileText
                          size={20}
                          color={isUser ? colors.white : colors.primary}
                        />
                      </View>
                      <Text
                        style={[styles.fileName, isUser && styles.userFileName]}
                      >
                        {attachment.name || "Document"}
                      </Text>
                    </TouchableOpacity>
                  )}
                  {attachment.type === "pdf" && (
                    <TouchableOpacity
                      style={styles.inlinePdfContainer}
                      onPress={() =>
                        handleOpenMedia(
                          attachment.uri,
                          "pdf",
                          attachment.signedUrl,
                          attachment.serverUrl
                        )
                      }
                      activeOpacity={0.9}
                    >
                      <View style={styles.inlinePdfPreview}>
                        <WebView
                          source={{
                            uri: `https://docs.google.com/viewer?url=${encodeURIComponent(
                              attachment.signedUrl ||
                              attachment.serverUrl ||
                              attachment.uri
                            )}&embedded=true`,
                          }}
                          onError={(error) => {
                            logger.error("WebView error:", error.nativeEvent);
                          }}
                          style={styles.inlinePdfContent}
                          javaScriptEnabled={true}
                          domStorageEnabled={true}
                          startInLoadingState={true}
                          scalesPageToFit={true}
                          injectedJavaScript={`
                          document.querySelector('body').style.zoom = '1.5';
                          document.querySelector('body').style.overflow = 'hidden';
                          true;
                        `}
                          renderLoading={() => (
                            <View style={styles.inlinePdfLoading}>
                              <FileText
                                size={20}
                                color={isUser ? colors.white : colors.primary}
                              />
                              <ActivityIndicator
                                size="small"
                                color={isUser ? colors.white : colors.primary}
                                style={styles.loadingIndicator}
                              />
                            </View>
                          )}
                        />
                      </View>
                    </TouchableOpacity>
                  )}
                  {attachment.type === "voice" && (
                    <View style={styles.audioContainer}>
                      <Text style={styles.audioText}>{t('attachments.voiceMessage')}</Text>
                    </View>
                  )}
                </View>
              ))}
          </View>
        </Pressable>
        {isUser && renderSelectionRadio()}
      </View>

      {showCitationsModal && (
        <Modal
          visible={showCitationsModal}
          transparent={true}
          animationType="none" // We'll handle animation ourselves
          onRequestClose={() => setShowCitationsModal(false)}
        >
          <TouchableOpacity
            style={styles.citationsModalOverlay}
            activeOpacity={1}
            onPress={() => setShowCitationsModal(false)}
          >
            <Animated.View
              style={[
                styles.citationsModalContent,
                {
                  opacity: modalAnimation,
                  transform: [
                    {
                      scale: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.5, 1],
                      }),
                    },
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [modalPosition.y - 100, 0],
                      }),
                    },
                    {
                      translateX: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [modalPosition.x - 150, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={styles.citationsModalTitle}>{t('chat.citations.referenceText')}</Text>
                <TouchableOpacity
                  style={styles.closeIconButton}
                  onPress={() => setShowCitationsModal(false)}
                >
                  <X size={20} color={colors.gray[600]} />
                </TouchableOpacity>
              </View>

              <View style={styles.citationsList}>
                {DEMO_CITATIONS.map((citation, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.citationItem}
                    onPress={() => Linking.openURL(citation.url)}
                  >
                    <FileText size={16} color={colors.primary} style={styles.citationIcon} />
                    <Text style={styles.citationTitle}>{citation.title}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </TouchableOpacity>
        </Modal>
      )}


      {modalVisible && selectedMedia && (
        <MediaViewerModal
          visible={modalVisible}
          uri={selectedMedia.uri}
          type={selectedMedia.type}
          signedUrl={selectedMedia.signedUrl}
          serverUrl={selectedMedia.serverUrl}
          onClose={() => setModalVisible(false)}
        />
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  timestamp: {
    fontSize: moderateScale(11),
    color: colors.gray[500],
    marginTop: moderateVerticalScale(4),
    alignSelf: 'flex-end',
  },
  userTimestamp: {
    color: colors.gray[100],
    alignSelf: 'flex-end',
  },
  botTimestamp: {
    color: colors.gray[500],
    // alignSelf: 'flex-start',
  },
  textContainer: {
    position: "relative",
  },
  copyFeedback: {
    position: "absolute",
    top: -moderateVerticalScale(40),
    left: 0,
    right: 0,
    padding: moderateScale(8),
    borderRadius: moderateScale(8),
    alignItems: "center",
    justifyContent: "center",
    zIndex: 10,
  },
  markdownBubble: {
    backgroundColor: "transparent",
    padding: 0,
    shadowOpacity: 0,
    elevation: 0,
  },
  userCopyFeedback: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  botCopyFeedback: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  copyFeedbackText: {
    color: colors.white,
    fontSize: moderateScale(12),
    fontWeight: "bold",
  },
  pdfPreviewContainer: {
    maxWidth: "100%",
    borderRadius: moderateScale(12),
    overflow: "hidden",
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  pdfPreview: {
    width: moderateScale(240),
    borderRadius: moderateScale(12),
  },
  pdfPreviewContent: {
    width: moderateScale(240),
    height: moderateVerticalScale(200),
    borderTopLeftRadius: moderateScale(12),
    borderTopRightRadius: moderateScale(12),
  },
  pdfLoadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.gray[200],
  },
  loadingIndicator: {
    marginTop: moderateVerticalScale(8),
  },
  pdfLabel: {
    color: colors.primary,
    fontSize: moderateScale(12),
    fontWeight: "bold",
    marginTop: moderateVerticalScale(4),
  },
  inlinePdfContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: moderateScale(8),
    overflow: "hidden",
    flexDirection: "column",
    marginTop: moderateVerticalScale(8),
    borderWidth: 1,
    borderColor: colors.primary,
  },
  inlinePdfPreview: {
    width: "100%",
    height: moderateVerticalScale(150),
    borderRadius: moderateScale(8),
    overflow: "hidden",
  },
  inlinePdfContent: {
    width: "100%",
    height: moderateVerticalScale(150),
  },
  inlinePdfLoading: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  fileIconContainer: {
    marginRight: moderateScale(8),
  },
  userFileName: {
    color: colors.white,
  },
  container: {
    paddingHorizontal: moderateScale(16),
    marginBottom: moderateVerticalScale(16),
  },
  messageWrapper: {
    flexDirection: "row",
    alignItems: "center",
  },
  messageContainer: {
    flex: 1,
    flexDirection: "row",
    position: "relative",
  },
  userContainer: {
    justifyContent: "flex-end",
  },
  botContainer: {
    justifyContent: "flex-start",
  },
  bubble: {
    padding: moderateScale(12),
    borderRadius: moderateScale(20),
  },
  bubbleWithCheckbox: {
    maxWidth: "90%", // Smaller max width when checkbox is visible
  },
  bubbleWithoutCheckbox: {
    maxWidth: "85%",
  },
  userBubble: {
    position: "relative",
    backgroundColor: colors.primary,
    borderTopLeftRadius: moderateScale(20),
    borderTopRightRadius: moderateScale(20),
    borderBottomLeftRadius: moderateScale(20),
    borderBottomRightRadius: 0,
  },
  link: {
    textDecorationLine: 'underline',
    color: Platform.OS === 'ios' ? '#007AFF' : '#2196F3',
  },
  voiceButton: {

  },
  botBubble: {
    backgroundColor: colors.gray[100],
  },
  text: {
    fontSize: TEXT_FONT_SIZE,
    lineHeight: moderateVerticalScale(22),
    // flexShrink: 1, // Allow text to shrink
    // flexWrap: "wrap", // Wrap long text
  },
  userText: {
    color: colors.white,
  },
  botText: {
    fontSize: moderateScale(16),
    lineHeight: moderateVerticalScale(22),
    color: colors.gray[800],
  },
  attachmentContainer: {
    marginTop: moderateVerticalScale(8),
  },
  image: {
    width: "100%",
    height: moderateVerticalScale(200),
    borderRadius: moderateScale(8),
  },
  botImage: {
    width: "100%",
    minHeight: moderateVerticalScale(100),
    height: undefined,
    borderRadius: moderateScale(8),
  },
  imageOnlyContainer: {
    maxWidth: "80%",
    borderRadius: moderateScale(12),
    overflow: "hidden",
  },
  imageOnly: {
    width: moderateScale(240),
    height: moderateVerticalScale(240),
    borderRadius: moderateScale(12),
  },
  botImageOnly: {
    width: moderateScale(240),
    minHeight: moderateVerticalScale(100),
    height: undefined,
    borderRadius: moderateScale(12),
  },
  fileContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    padding: moderateScale(8),
    borderRadius: moderateScale(8),
  },
  fileName: {
    color: colors.white,
    fontSize: moderateScale(14),
  },
  audioContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    padding: moderateScale(8),
    borderRadius: moderateScale(8),
    alignItems: "center",
  },
  audioText: {
    color: colors.white,
    fontSize: moderateScale(14),
  },
  radioButton: {
    marginHorizontal: moderateScale(5),
    width: moderateScale(24),
    height: moderateScale(24),
    justifyContent: "center",
    alignItems: "center",
  },
  highlightOverlay: {
    borderRadius: moderateScale(20),
    zIndex: 1,
  },
  userHighlight: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  botHighlight: {
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  userPressHighlight: {
    backgroundColor: colors.primary + 'DD', // Slightly more transparent version of the primary color
  },
  botPressHighlight: {
    backgroundColor: colors.gray[200], // Slightly darker gray for bot messages
  },
  citationsModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(16),
  },
  citationsList: {
    marginBottom: moderateVerticalScale(16),
    borderTopWidth: moderateVerticalScale(1),
    borderTopColor: theme.colors.gray[200]
  },
  citationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: moderateVerticalScale(10),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  citationIcon: {
    marginRight: moderateScale(8),
  },
  citationTitle: {
    fontSize: moderateScale(14),
    color: "#0069c9",
    flex: 1,
  },
  closeButton: {
    backgroundColor: colors.primary,
    borderRadius: moderateScale(8),
    padding: moderateScale(12),
    alignItems: 'center',
    marginTop: moderateVerticalScale(8),
  },
  closeButtonText: {
    color: colors.white,
    fontSize: moderateScale(16),
    fontWeight: 'bold',
  },
  infoIconContainer: {
    position: 'absolute',
    top: '50%', // Align with top of bubble
    right: moderateScale(-30), // Position to the right of the bubble
    zIndex: 10,
    borderWidth: 0.8,
    borderColor: theme.colors.gray[300],
    borderRadius: moderateScale(10),
    width: moderateScale(20),
    height: moderateScale(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoIconInner: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    color: theme.colors.gray[400], // Subtle gray color
    fontWeight: 'bold',
    fontSize: moderateScale(11),
    textAlign: 'center',
    padding: moderateScale(2)
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateVerticalScale(16),
    paddingBottom: moderateVerticalScale(8),
  },
  citationsModalTitle: {
    fontSize: moderateScale(16),
    lineHeight: moderateVerticalScale(21),
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeIconButton: {
    padding: moderateScale(4),
  },
  citationsModalContent: {
    backgroundColor: colors.white,
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    width: '90%',
    maxWidth: moderateScale(320),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  voiceBubbleContainer: {
    maxWidth: '80%',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginVertical: 4,
  },
  userVoiceBubble: {
    backgroundColor: '#eaeaea',
    alignSelf: 'flex-end',
  },
  botVoiceBubble: {
    backgroundColor: '#ffffff',
    alignSelf: 'flex-start',
  },
  waveformRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  loadingText: {
    marginLeft: 8,
    color: '#888',
    fontSize: 14,
  },
});

export default React.memo(MessageBubbleComponent, (prevProps, nextProps) => {
  // Only re-render if these specific props changed
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.selectionState.isSelected === nextProps.selectionState.isSelected &&
    prevProps.selectionState.isInSelectionMode === nextProps.selectionState.isInSelectionMode
  );
});