import React, { useEffect, useMemo, useRef } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
} from "react-native";
import {
  User,
  Activity,
  AlertTriangle,
  Pill,
  Heart,
  Stethoscope,
  CheckCircle2,
} from "lucide-react-native";
import { colors } from "@/constants/colors";
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";


type Step = {
  icon: React.ReactElement;
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      marginTop: Platform.OS === 'ios' ? verticalScale(16) : verticalScale(8),
      paddingVertical: verticalScale(6),
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: scale(32),
      justifyContent: "center",
      flexWrap: "nowrap",
      backgroundColor: theme.colors.secondary[50],
    },
    iconWrapper: {
      padding: moderateScale(4),
      justifyContent: "center",
      alignItems: "center",
    },
    active: {
      padding: moderateScale(8),
      borderRadius: scale(30),
      borderWidth: 2,
      margin: 1,
      borderColor: theme.colors.primary.main,
      backgroundColor: theme.colors.gray[100],
    },
    connectorWrapper: {
      position: "relative",
      height: verticalScale(2),
      width: `${90 / (steps.length * 2)}%`,
      margin: -1,
    },
    connector: {
      position: "absolute",
      height: verticalScale(2),
      width: "100%",
      backgroundColor: theme.colors.gray[300],
      borderRadius: 1,
    },
    connectorOverlay: {
      position: "absolute",
      height: verticalScale(2),
      backgroundColor: theme.colors.primary.main,
      borderRadius: 1,
    },
  });

const steps: Step[] = [
  { icon: <User /> },
  { icon: <Activity /> },
  { icon: <AlertTriangle /> },
  { icon: <Pill /> },
  { icon: <Stethoscope /> },
  { icon: <Heart /> },
  { icon : <CheckCircle2/>}
];

type FormProgressProps = {
  currentStep: number;
  onStepPress: (step: number) => void;
};

export const FormProgress: React.FC<FormProgressProps> = ({ currentStep, onStepPress }) => {
  const connectorWidths = useRef(steps.map(() => new Animated.Value(0))).current;
 
   const { theme } = useTheme();
   const styles = useMemo(() => createStyles(theme), [theme]);
  useEffect(() => {
    steps.forEach((_, index) => {
      Animated.timing(connectorWidths[index], {
        toValue: index < currentStep ? 1 : 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    });
  }, [currentStep]);

  return (
    <View style={styles.container}>
      {steps.map((step, index) => {
        const isCompleted = index <= currentStep;

        return (
          <React.Fragment key={index}>
            <View style={[(index === currentStep) ? {shadowColor : colors.gray[800] , shadowRadius : 30} : {elevation : 0}]}>
            <TouchableOpacity onPress={() => onStepPress(index)} style={[styles.iconWrapper, (index === currentStep) && styles.active]}>
              {React.cloneElement(step.icon, {
                color: (index === currentStep) ? colors.primary : isCompleted ? colors.primary : colors.gray[400],
                size : 26,
              })}
            </TouchableOpacity>
            </View>

            {index !== steps.length - 1 && (
              <View style={styles.connectorWrapper}>
                <View style={styles.connector} />
                <Animated.View
                  style={[
                    styles.connectorOverlay,
                    {
                      width: connectorWidths[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0%", "100%"],
                      }),
                    },
                  ]}
                />
              </View>
            )}
          </React.Fragment>
        );
      })}
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     marginTop : verticalScale(6),
//     paddingVertical: verticalScale(6),
//     flexDirection: "row",
//     alignItems: "center",
//     paddingHorizontal: scale(32),
//     justifyContent: "center",
//     flexWrap: "nowrap",
//     backgroundColor: "#fff",
//   },
//   iconWrapper: {
//     padding: moderateScale(4),
//     justifyContent: "center",
//     alignItems: "center",
//   },
//   active: {
//     padding: moderateScale(8),
//     borderRadius: scale(30),
//     borderWidth: 2,
//     margin: 1,
//     borderColor: colors.primary,
//     backgroundColor: colors.gray[100],
//   },
//   connectorWrapper: {
//     position: "relative",
//     height: verticalScale(2),
//     width: `${90 / (steps.length * 2)}%`, 
//     margin: -1,
//   },
//   connector: {
//     position: "absolute",
//     height: verticalScale(2),
//     width: "100%",
//     backgroundColor: colors.gray[300],
//     borderRadius: 1,
//   },
//   connectorOverlay: {
//     position: "absolute",
//     height: verticalScale(2),
//     backgroundColor: colors.primary,
//     borderRadius: 1,
//   },
// });

