import React, { useState, useEffect, useRef } from 'react';
import { 
  Modal, 
  View, 
  Text, 
  TextInput, 
  StyleSheet, 
  TouchableOpacity, 
  KeyboardAvoidingView, 
  Platform,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, theme } from '@/constants/colors';
import logger from '@/utils/logger/logger';

interface EditValueModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (value: string) => void;
  initialValue: string;
  label: string;
  unit?: string;
}

export default function EditValueModal({ 
  visible, 
  onClose, 
  onSave, 
  initialValue, 
  label,
  unit,
}: EditValueModalProps) {
  const { t } = useTranslation();
  const [value, setValue] = useState('');
  const inputRef = useRef<TextInput>(null)
  
  // Extract numeric part from initialValue
  useEffect(() => {
    if (visible) {
      const numericMatch = initialValue.match(/(\d+(\.\d+)?)/);
      setValue(numericMatch ? numericMatch[0] : initialValue);
    }

    // auto focus input when the component mounts
    const timeout = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);

    return () => clearTimeout(timeout);

  }, [visible, initialValue]);

  const handleSave = () => {
    const formattedValue = unit ? `${value}${unit}` : value;
    onSave(formattedValue);
    
    onClose();
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.centeredView}
        >
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>{t('editModal.titlePrefix')} {label}</Text>
            
            <View style={styles.inputContainer}>
              <TextInput
                ref={inputRef}
                style={styles.input}
                value={value}
                onChangeText={setValue}
                keyboardType={unit ? "numeric" : "default"}
                autoFocus
              />
              {unit && <Text style={styles.unitText}>{unit}</Text>}
            </View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onClose}
              >
                <Text style={styles.cancelButtonText}>{t('editModal.cancelButton')}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSave}
              >
                <Text style={styles.saveButtonText}>{t('editModal.saveButton')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    width: '80%',
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black,
    marginBottom: 16,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 24,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: 16,
  },
  unitText: {
    fontSize: 16,
    color: colors.gray[500],
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: theme.radii.lg,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.gray[100],
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    marginLeft: 8,
  },
  cancelButtonText: {
    color: colors.gray[700],
    fontWeight: '600',
  },
  saveButtonText: {
    color: colors.white,
    fontWeight: '600',
  },
});
