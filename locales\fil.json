{"common": {"error": "Error", "yes": "Oo", "no": "Hindi", "sometimes": "<PERSON><PERSON>", "close": "Isara", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "save": "I-save", "next": "<PERSON><PERSON><PERSON>", "loading": "Naglo-load...", "version": "v0.0.1.7"}, "welcome": "Mag-log in para makapagsimula ng makipag-usap kay August", "notFound": {"title": "Oops!", "message": "Ang screen na ito ay wala.", "goHome": "Bumalik sa home screen!"}, "library": {"title": "Health Library"}, "specialists": {"title": "Mga Espesyalista", "description": "Kumonsulta sa mga dalubhasang propesyonal sa kalusugan para sa mas tiyak na mga alalahanin sa kalusugan. Pumili ng espesyalista sa ibaba:", "generalPhysician": {"title": "Pangkalahatang Doktor", "description": "Para sa pangkalahatang mga alalahanin sa kalusugan at pangunahing pangangalaga."}, "nutritionist": {"title": "Nutritionist", "description": "Para sa payo sa diyeta, nutrisyon, at pamamahala ng timbang."}, "cardiologist": {"title": "Cardiologist", "description": "Para sa mga alalahanin na may kaugnayan sa puso at kalusugan ng cardiovascular."}, "neurologist": {"title": "Neurologist", "description": "Para sa mga isyu sa utak, spinal cord, at nervous system."}, "oncologist": {"title": "Oncologist", "description": "Para sa mga alalahanin at paggamot na may kaugnayan sa kanser."}, "endocrinologist": {"title": "Endocrinologist", "description": "Para sa mga karamdaman na may kaugnayan sa hormone at pamamahala ng diabetes."}, "dermatologist": {"title": "Dermatologist", "description": "Para sa mga kondisyon ng balat, buhok, at kuko."}, "psychiatrist": {"title": "Psychiatrist", "description": "Para sa mga alalahanin sa kalusugan ng pag-iisip at psychological well-being."}}, "profile": {"title": "Profile", "defaultName": "Guest", "namePlaceholder": "<PERSON><PERSON><PERSON> ang iyong pangalan", "saving": "Nagse-save...", "noPhoneNumber": "Walang numero ng telepono", "loggingOut": "Naglo-log out...", "about": {"title": "Tungkol", "description": "<PERSON>amin ang higit pa tungkol kay August"}, "whatsapp": {"title": "WhatsApp", "description": "Makipag-chat kay August sa WhatsApp"}, "refer": {"title": "I-refer", "description": "Gustong-gusto si August? Ibahagi sa iyong mga kaibigan"}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON> ang account", "description": "Ikinalulungkot naming makita kang umalis"}, "logout": {"title": "Mag-log out", "description": "<PERSON><PERSON><PERSON> kaagad. <PERSON><PERSON><PERSON><PERSON> ka namin"}, "shareMessage": "👋Hey, Tingnan mo itong napakagandang app na ginagamit ko!\n\n\n\n➡️Ginagamit ko si August para makakuha ng mabilis at maaasahang impormasyon at gabay sa kalusugan. Para itong may doktor ka sa bulsa! Tingnan ito dito:", "error": {"loadFailed": "Nabigo ang paglo-load ng data ng user", "fetchError": "May naganap na error habang kinukuha ang data ng user", "updateNameFailed": "Nabigo ang pag-update ng pangalan", "updateNameError": "May naganap na error habang ina-update ang pangalan", "loadFoodData": "Nabigo ang paglo-load ng data ng pagkain", "logoutError": "Error habang naglo-log out:", "shareError": "Error sa pagbabahagi ng mga mensahe:"}}, "error": {"title": "May mali", "checkLogs": "Mangyaring tingnan ang mga logs ng iyong device para sa higit pang detalye.", "unknown": "Hindi kilalang error", "unknownFile": "Hindi kilalang file", "unknownLine": "Hindi kilalang linya", "unknownColumn": "Hindi kilalang column"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON>", "searchCountries": "Maghanap ng mga bansa", "validation": {"invalidPhone": "Mangyaring maglagay ng wastong numero ng telepono", "invalidDigits": "Mangyaring maglagay ng wastong numero ng telepono (7-15 digits)"}}, "header": {"title": "Makakuha ng linaw sa iyong mga alalahanin sa kalusugan agad at pribado", "subtitle": "Maingat na gabay. Walang pagmamadali. Walang kalituhan.", "emphasis": "<PERSON><PERSON> lang."}, "greeting": "Hi 👋", "phoneNumber": "Numero ng Telepono", "requestOTP": "<PERSON><PERSON>in ang OTP", "otp": {"title": "One-Time Password", "verify": "I-verify ang <PERSON>", "sending": "Pagpapadala...", "countdown": "Muling ipadala ang OTP sa {{countdown}}s", "resend": "Muling ipadala ang OTP", "sentTo": "Ang OTP ay ipinadala sa ", "whatsappSuffix": " sa Whatsapp"}, "disclaimer": {"prefix": "Sa pamamagitan ng pag-sign up, sumasang-ayon ka sa aming ", "continuePrefix": "Sa pamamagitan ng pagpapatuloy, sumasang-ayon ka sa aming ", "termsOfService": "Mga Tuntunin ng Serbisyo", "and": " at ", "privacyPolicy": "Patakaran sa Privacy", "whatsappConsent": ", at pahintulot na makatanggap ng mga update at paalala mula sa amin sa pamamagitan ng WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Maligayang pagdating sa August!", "buttonText": "Simulan na natin"}}, "postAuth": {"step1": {"title": "Hoy!", "subtitle": "<PERSON><PERSON> si <PERSON> 👋", "description": "Isipin mo ako bilang ang komportableng sulok sa iyong\napparato kung saan mo masusuri ang lahat ng iyong mga kuryosidad sa kalusugan.", "subdescription": "Huwag mag-atubiling magtanong ng anumang nasa isip mo.\n<PERSON><PERSON>g pagh<PERSON>, Walang limitasyon!", "placeholder": "Ano ang itatawag ko sa iyo?"}, "step2": {"title": "Hi {{userName}},", "subtitle": "Narito ang mga magagawa ko:", "features": {"health": {"title": "<PERSON><PERSON><PERSON> ang iyong", "subtitle": "Mga katanungan sa kalusugan"}, "nutrition": {"title": "Subaybayan ang mga", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON>n ang", "subtitle": "<PERSON><PERSON>"}}}, "pills": {"thoughtful": "Mapag-isip", "careful": "Maingat", "accurate": "Tumpak"}, "features": {"symptoms": {"title": "Suriin ang iyong mga sintomas", "description": "<PERSON><PERSON><PERSON><PERSON> ang aking sikmura sa loob ng isang linggo. Ano ang nangyayari sa akin?"}, "prescriptions": {"title": "Suriin ang iyong mga reseta", "description": "I-upload at maunawaan ang mga reseta tulad ng isang doktor."}, "medicine": {"title": "<PERSON><PERSON><PERSON> ang iyong gamot", "description": "Ang Metformin para sa aking PCOS ba ay nakikipag-ugnayan sa aking mga gamot sa ADHD?"}, "plans": {"title": "Kumuha ng mga personalized na plano", "description": "<PERSON><PERSON><PERSON> mo bang bigyan ako ng plano sa nutrisyon at fitness para sa pagbaba ng aking HbA1c levels?"}}, "buttons": {"getStarted": "Simulan na", "next": "<PERSON><PERSON><PERSON>"}, "errors": {"nameRequired": "<PERSON><PERSON><PERSON> ilagay ang iyong pangalan"}}}, "tabs": {"chat": "Cha<PERSON>", "discover": "<PERSON><PERSON><PERSON>", "nutrition": "Nutrisyon", "personalize": "I-personalize"}, "chat": {"nav": {"title": "Agosto"}, "me": "<PERSON><PERSON>", "augustName": "Agosto", "input": {"placeholder": "Tanong kay Agosto...", "disclaimer": "Ma<PERSON>ing magkamali si Agosto. Kumpirmahin sa doktor."}, "list": {"loadingMessages": "Naglo-load ng mga mensahe...", "noMessages": "Walang mensahe pa. Magsimula ng usapan!"}, "connection": {"offlineMessage": "Mukhang offline ka. Mag-reconnect para makapagpadala ng mensahe.", "connecting": "Nagko-connect...", "tryAgain": "<PERSON><PERSON><PERSON> muli"}, "prompts": {"uploadReport": "Mag-upload ng report", "speakInHindi": "Magsalita sa Tagalog", "notFeelingWell": "Hindi ako maganda ang pakiramdam", "whatIsMyBMI": "Ano ang BMI ko?", "nutritionAdvice": "Payo sa nutrisyon", "sleepBetter": "Maging mas maayos ang tulog"}, "citations": {"referenceText": "Para sa karagdagang detalye tungkol sa usapang ito, mangyaring sumangguni sa:"}, "actions": {"copiedToClipboard": "Nakopya sa clipboard", "copied": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"introText": "👋Hoy, tingnan mo ang usapan ko kay Agosto:\n\n", "downloadText": "\n\n➡️I-download ang Agosto para maka-chat sa iyong palakaibigang AI health companion:\n"}}, "discover": {"nav": {"title": "<PERSON><PERSON><PERSON>"}, "categories": {"all": "Lahat", "heartHealth": "Kalusugan ng Puso", "nutrition": "Nutrisyon", "mentalHealth": "Kalusugan ng Pag-iisip", "fitness": "Fitness", "wellness": "Wellness"}, "cards": {"empty": "Walang available na cards para sa kategoryang ito"}, "sections": {"features": "Mga Tampok"}, "features": {"healthLibrary": {"title": "Health Library", "description": "Access sa maaasahan, mapagkakatiwalaan, at updated na impormasyon medikal ng libre."}, "nutritionTracker": {"title": "Nutrition Tracker", "description": "Naisip mo na ba kung pwede mo lang i-upload ang picture ng iyong pagkain at i-track lahat ng iyong nutritional goals? Kaya ni Agosto iyon!"}, "multilingualSupport": {"title": "Multilingual Support", "description": "Maaari kang makipag-usap kay Agosto sa anumang wika na komportable ka! Si Agosto ay laging nandito para makinig, suportahan, at tumugon sa iyo anumang oras na kailangan mo.", "samplePrompt": "Magsalita sa Tagalog"}, "labReportAnalysis": {"title": "Lab Report Analysis", "description": "Kapag kinausap mo si Agosto tungkol sa iyong lab reports, makakakuha ka ng mataas na precision.  Nakapagproseso na si Agosto ng mahigit 4.7 milyong reports na may biomarker extraction accuracy na 98.4%."}}}, "nutrition": {"nav": {"title": "Nutrisyon"}, "meals": {"title": "Ang <PERSON>", "subtitle": "<PERSON><PERSON>kin para makita ang macros sa bawat pagkain"}, "upload": {"loading": "Ina-upload ang larawan..."}, "defaultFoodName": "<PERSON>g<PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>", "unknownTime": "Hindi alam ang oras", "calories": "🔥 Kaloriya", "proteins": "🥩 <PERSON>tina", "carbs": "🍞 Carbohydrates", "sugars": "🍬 <PERSON><PERSON>l", "fat": "🥑 Taba", "caloriesLabel": "Kaloriya", "proteinLabel": "Protina", "carbohydratesLabel": "Carbohydrates", "fatLabel": "Taba", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Mga Tip:", "macroBreakdown": "Pagkasira ng Macronutrient", "noMacroData": "Walang available na macronutrient data para sa pagkaing ito.", "disclaimer": "Para sa pang-edukasyon na paggamit lamang. <PERSON><PERSON> pa", "disclaimerLink": "dito", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "<PERSON>o ang i<PERSON>?", "subtitle": "Ito ay gagamitin upang i-calibrate ang iyong custom na plano.", "male": "<PERSON><PERSON>", "female": "<PERSON><PERSON>", "other": "Iba pa"}, "age": {"title": "Ano ang iyong <PERSON>?", "subtitle": "Ito ay gagamitin upang kalkulahin ang iyong pang-araw-araw na pangangailangan."}, "measurements": {"title": "Taas at Timbang", "subtitle": "<PERSON><PERSON><PERSON>t ang iyong taas sa sentimetro at timbang sa kilogramo."}, "activity": {"title": "Antas ng Aktibidad", "subtitle": "Gaano kadalas ka mag-ehersisyo?", "none": "<PERSON><PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON>"}, "goal": {"title": "<PERSON><PERSON><PERSON> sa Timbang", "subtitle": "Ano ang gusto mong makamit?", "increase": "Dagdagan", "maintain": "Panatilihin", "decrease": "<PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Target na Timbang", "subtitle": "Ano ang iyong target na timbang sa kilogramo?"}, "setup": {"title": "Pagse-set up ng Iyong Plano", "subtitle": "Pakisuyong maghintay habang inihahanda namin ang iyong plano sa nutrisyon."}, "review": {"title": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> at i-customize ang iyong plano sa nutrisyon."}, "height": {"label": "Taas (cm)"}, "weight": {"label": "Timbang (kg)"}}, "error": {"updateFailed": "Nabigo ang pag-update ng data ng nutrisyon. Pakisubukan muli.", "parsingError": "Error sa pag-parse ng data ng pagkain:", "fetchReportsFailed": "Nabigo ang pagkuha ng data ng mga ulat. Pakisubukan muli.", "missingReportId": "Nawawala ang Report ID"}}, "personalize": {"nav": {"title": "Personalize"}, "button": {"saving": "Nagse-save", "review": "<PERSON><PERSON><PERSON><PERSON>", "saveNext": "I-save at Susunod"}}, "basicInfo": {"title": "Mas makilala ka namin", "subtitle": "Ang impormasyong ito ay tumutulong sa amin na i-personalize ang iyong mga rekomendasyon sa kalusugan", "age": {"question": "<PERSON><PERSON> taon ka na?", "placeholder": "Ilagay ang iyong edad"}, "sex": {"question": "Ano ang iyong kasarian?", "placeholder": "<PERSON><PERSON><PERSON> ang iyong kasarian", "male": "<PERSON><PERSON>", "female": "<PERSON><PERSON>", "other": "Iba pa"}, "height": {"question": "<PERSON>o ang iyong taas? (cm)", "placeholder": "Ilagay ang iyong taas"}, "weight": {"question": "<PERSON><PERSON> ang iyong timbang? (kg)", "placeholder": "Il<PERSON>y ang iyong timbang"}}, "lifestyle": {"title": "Ang Iyong Mga Gawain sa <PERSON>ay", "subtitle": "Ang pag-unawa sa iyong mga pang-araw-araw na gawain ay nakakatulong sa amin upang makapagbigay ng mas magagandang rekomendasyon", "diet": {"question": "<PERSON><PERSON> uri ng diyeta ang iyong sinusunod?", "placeholder": "<PERSON><PERSON><PERSON> ang iyong diyeta", "vegetarian": "Vegetarian", "nonVegetarian": "Non-Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Regular ka bang nag-eehersisyo?"}, "drinking": {"question": "Umiinom ka ba ng alak?"}, "smoking": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka ba?"}, "sleep": {"question": "<PERSON>ang oras ka natutulog kada gabi?", "value": "{{sleep}} oras"}, "hydration": {"question": "<PERSON>ang baso ng tubig ang iyong iniinom araw-araw?", "value": "{{hydration}} baso ({{liters}}L)"}}, "allergies": {"title": "<PERSON><PERSON> ka bang anumang allergy?", "subtitle": "Ang pag-alam sa iyong mga allergy ay nakakatulong sa amin upang makapagbigay ng mas ligtas na mga rekomendasyon", "allergyIndex": "Allergy {{index}}", "name": {"question": "Ano ang iyong allergy?", "placeholder": "Ilagay ang allergy (hal., Peanuts, Dust)"}, "severity": {"question": "Gaano kalubha ang allergy na ito?", "placeholder": "<PERSON><PERSON><PERSON> ang ka<PERSON>", "mild": "Banayad", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "severe": "<PERSON><PERSON><PERSON>"}, "addButton": "Magdagdag ng Isa Pang Allergy", "noAllergiesButton": "Wala akong allergy"}, "medications": {"title": "Mga Gamot at Supplements", "subtitle": "Sabihin sa amin ang tungkol sa anumang gamot o supplement na iyong iniinom sa kasalukuyan", "medicationIndex": "Gamot {{index}}", "name": {"label": "Pangalan ng Gamot", "placeholder": "Ilagay ang pangalan ng gamot"}, "startDate": {"question": "<PERSON>lan mo ito sinimulang inumin?", "placeholder": "<PERSON><PERSON><PERSON> ang <PERSON>a"}, "type": {"label": "Uri ng Gamot", "shortTerm": "<PERSON><PERSON><PERSON>", "longTerm": "Pangmatagalan"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "unit": {"label": "<PERSON><PERSON>"}, "frequency": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "perDay": "kada araw", "perWeek": "kada linggo", "perMonth": "kada buwan", "perYear": "kada taon"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "Magdagdag ng Isa Pang Gamot", "noMedicationsButton": "Wala akong iniinom na gamot", "calendar": {"title": "<PERSON><PERSON><PERSON> ang Petsa ng Pagsisimula"}}, "conditions": {"title": "Mga Kondisyon sa <PERSON>ugan", "subtitle": "Sabihin sa amin ang tungkol sa anumang kondisyon sa kalusugan na mayroon ka o nagkaroon noon", "conditionIndex": "Kondisyon {{index}}", "name": {"label": "Pangalan ng Kondisyon", "placeholder": "<PERSON><PERSON><PERSON> ang kond<PERSON>yon (hal. <PERSON><PERSON><PERSON>, atbp.)"}, "since": {"question": "Simula kailan mo naranasan ang kondisyong ito?", "placeholder": "<PERSON><PERSON><PERSON> ang <PERSON>a"}, "current": {"question": "Nagtutuloy pa ba ito sa iyo?"}, "medicated": {"question": "May iniinom ka bang gamot para dito?"}, "addButton": "Magdagdag ng Isa Pang Kondisyon", "noConditionsButton": "Wala akong kondisyon sa kalusugan", "calendar": {"title": "<PERSON><PERSON><PERSON> an<PERSON>"}}, "reproductive": {"title": "Kalusugan sa Pag-aanak", "subtitle": "Ang impormasyong ito ay tutulong sa amin upang makapagbigay ng mas personalized na mga rekomendasyon sa kalusugan", "menstruation": {"question": "Nakaranas ka na ba ng regla?", "detailsTitle": "Mga Detalye ng Regla", "regularity": {"question": "Gaano ka-regular ang iyong siklo?", "regular": "Regular", "irregular": "Hindi Regular", "notSure": "Hindi Sigurado"}, "cycleLength": {"label": "Average na haba ng siklo (araw)", "placeholder": "Ilagay ang haba ng siklo"}, "flowDays": {"label": "Mga araw ng pagdaloy: {{flowDays}}", "min": "1 araw", "max": "15 araw"}, "padsPerDay": {"label": "Pads/tampons kada araw: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "May mga sintomas ka ba habang may regla?", "placeholder": "Ilagay ang mga sintomas (hal., pananakit ng tiyan, sakit ng ulo)"}}, "childbirth": {"question": "Nakaranas ka na ba ng panganganak?", "detailsTitle": "Mga Detalye ng Panganganak", "children": {"label": "Bilang ng mga Anak"}, "pregnancies": {"label": "Bilang ng mga Pagbubuntis"}, "complications": {"question": "May mga komplikasyon ba sa panahon ng pagbubuntis o panganganak?", "placeholder": "Ilagay ang mga komplikasyon (kung mayroon man)"}}}, "review": {"title": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>", "subtitle": "Pakirepaso ang impormasyong iyong ibinigay bago isumite", "sections": {"basicInfo": "Pangunahing Impormasyon", "lifestyle": "<PERSON><PERSON><PERSON><PERSON>", "allergies": "Mga Allergy", "medications": "Mga Gamot at Supplements", "conditions": "Mga Kondisyon sa <PERSON>ugan", "reproductive": "Kalusugan sa Pag-aanak", "menstruationDetails": "Mga Detalye ng Regla", "childbirthDetails": "Mga Detalye ng Panganganak"}, "fields": {"age": "Edad:", "sex": "Kasarian:", "height": "Taas:", "weight": "Timbang:", "diet": "Diyeta:", "exercise": "Ehersisyo:", "drinking": "Pag-inom:", "smoking": "Paninigarilyo:", "sleep": "Tulog:", "hydration": "Hydration:", "allergyIndex": "Allergy {{index}}:", "dose": "Dosis:", "frequency": "Dalas:", "type": "Uri:", "since": "Simula:", "currentlyActive": "Kasalukuyang Aktibo:", "takingMedication": "Umiinom ng Gamot:", "hasMenstruated": "Nakaranas ng Regla:", "regularity": "Regularidad:", "cycleLength": "Haba ng Siklo:", "flowDays": "Mga Araw ng Pagdaloy:", "padsPerDay": "Pads/Tampons kada Araw:", "hasChildbirth": "Nakaranas ng Panganganak:", "children": "Mga Anak:", "pregnancies": "Mga Pagbubuntis:"}, "notProvided": "Hindi ibinigay", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} oras kada araw", "hydration": "{{hydration}} tasa ({{liters}}L) kada araw", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} araw"}, "noData": {"allergies": "Walang ibinigay na allergies", "medications": "Walang ibinigay na gamot", "conditions": "Walang ibinigay na kondisyon sa ka<PERSON>ugan"}, "submitButton": "Isumite ang Imporma<PERSON>on"}, "success": {"title": "Na-update na ang Impormasyon!", "message": "Salamat sa pagbibigay ng iyong impormasyon sa kalusugan. <PERSON><PERSON><PERSON> namin ito upang i-personalize ang iyong karanasan at makapagbigay ng mas magagandang rekomendasyon.", "benefits": {"insights": "Personalized na mga pananaw sa kalusugan", "reminders": "Mas magagandang paalala sa gamot", "recommendations": "Mga rekomendasyon sa kalusugan na angkop sa iyo"}, "continueButton": "Magpatuloy sa Dashboard"}, "permissions": {"microphonePermissionDenied": "Hindi pinapayagan ang mikropono", "microphoneAccessDescription": "Kailangan ng August ng access sa iyong mikropono para makapag-record ng audio at makapagpadala ng voice notes", "permissionDenied": "Hindi Pinapayagan", "cameraPermissionRequired": "Kailangan namin ng permission sa camera para gumana ito!", "mediaLibraryPermissionRequired": "Kailangan namin ng permission sa media library para gumana ito!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON><PERSON>ng Mahab<PERSON> ang <PERSON>", "recordingTooLongMessage": "<PERSON> voice recording ay dapat na mas maikli sa 5 minuto. Mangyaring mag-record ng mas maikling mensahe."}, "errors": {"uploadFailed": "Nabigo ang Pag-upload", "voiceUploadFailed": "Hindi na-upload ang voice recording.", "voiceRecordingFailed": "Nabigo ang pagpa<PERSON>ala ng voice recording", "failedToStopRecording": "Nabigo ang pagtigil sa pag-record", "photoUploadFailed": "Hindi na-upload ang larawan.", "failedToTakePhoto": "Nabigo ang pagkuha ng larawan", "imageUploadFailed": "Hindi na-upload ang larawan: {{fileName}}", "failedToPickImage": "Nabigo ang pagpili ng larawan", "documentUploadFailed": "Hindi na-upload ang dokumento: {{fileName}}", "failedToPickDocument": "Nabigo ang pagpili ng dokumento"}, "audioPlayer": {"downloadingAudio": "Downloading audio...", "loadingAudio": "Naglo-load ng audio..."}, "mediaProcessing": {"processingFile": "Pinoproseso ang <PERSON>", "uploadingSecuring": "Ina-upload at sinisiguro ang file...", "analyzingContent": "Ina-analyze ang nilalaman ng dokumento...", "extractingInfo": "<PERSON><PERSON><PERSON><PERSON> ang mahahalagang impormasyon...", "processingInsights": "Pinoproseso ang mga insights...", "preparingResponse": "Inihahanda ang detalyadong tugon...", "finalizingResponse": "Pinapa-finalize ang tugon..."}, "attachments": {"voiceMessage": "Voice Message", "image": "[LARAWAN]", "pdf": "[PDF]", "voice": "[VOICE NOTE]"}, "pdf": {"loadingPdf": "Naglo-load ng PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "napili"}, "mediaUpload": {"photoLibrary": "Photo Library", "takePhoto": "<PERSON><PERSON><PERSON> ng <PERSON>wan", "chooseFile": "Pumili ng File"}, "comingSoon": {"title": "Malapit na!", "description": " ay ka<PERSON><PERSON>yang binubuo pa. Abangan ang mga update!", "buttonText": "Na<PERSON><PERSON><PERSON><PERSON> ko!"}, "clipboard": {"success": "Nakopya ang link sa clipboard"}, "mediaPhotos": {"emptyState": "Walang entry pa."}, "foodDetail": {"defaultFoodName": "<PERSON>g<PERSON><PERSON>", "nutrition": {"totalCalories": "Total Calories", "proteins": "<PERSON>teins", "carbs": "<PERSON><PERSON>", "fat": "Fat", "sugars": "Sugars", "fibers": "Fibers"}}, "reports": {"defaultTitle": "Media Item", "defaultFoodName": "<PERSON>g<PERSON><PERSON>", "defaultName": "Doku<PERSON>", "openButton": "<PERSON><PERSON><PERSON> sa <PERSON> Viewer", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "Value", "headerRefRange": "Ref Range", "headerStatus": "Status"}, "noData": "Walang available na biomarker data"}, "setup": {"title": "In<PERSON><PERSON><PERSON> namin ang lahat para sa iyo", "inProgress": "Nasa progreso...", "progressMessages": {"0": "Kinakalkula ang Daily Calories", "1": "Ino-optimize ang Macro Split", "2": "Gumagawa ng Meal Plan", "3": "Kinukuwenta ang Health Score", "4": "Pinapa-finalize ang <PERSON>up"}, "checklistItems": {"0": "Ina-analyze ang iyong health data", "1": "Kinakalkula ang optimal nutrition plan", "2": "Pina-personalize ang iyong mga rekomendasyon", "3": "Gumagawa ng iyong mga mungkahi sa pagkain", "4": "Pinapa-finalize ang iyong setup"}}, "foodEntry": {"emptyState": "Walang mga entry ng pagkain pa. Kumuha ng larawan ng iyong pagkain para idagdag ito!"}, "nutritionReview": {"congratulations": "Congratulations!", "subtitle": "Handa na ang iyong custom na plano sa nutrisyon", "submitButton": "Simulan na natin!", "dailyTargetsTitle": "<PERSON><PERSON><PERSON>a<PERSON>-araw na Target sa Nutrisyon", "macroLabels": {"calories": "Calories", "carbs": "Carbohydrates", "protein": "<PERSON><PERSON>", "fats": "Mga taba"}}, "editModal": {"titlePrefix": "I-edit ang ", "cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON>"}, "processing": {"stages": {"scanning": "Ini-scan ang pagkain...", "identifying": "Tinutukoy ang mga sangkap...", "extracting": "Kinukuha ang mga sustansya...", "finalizing": "Finalizing ang mga resulta..."}, "error": {"defaultMessage": "Walang nakitang pagkain", "subtitle": "<PERSON><PERSON><PERSON> ang ibang anggulo"}, "retakeButton": "Tapikin para kumuha ulit ng larawan", "notification": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka namin kapag tapos na!"}, "chart": {"title": "Pagsubaybay sa Nutrisyon sa Paglipas ng Panahon", "selectNutrient": "Pi<PERSON>in ang Nutrient:", "emptyState": "Walang available na data sa nutrisyon.", "dropdown": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fat": "Taba", "sugars": "<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON>g<PERSON><PERSON>", "defaultDate": "<PERSON><PERSON><PERSON>", "defaultTime": "Hindi alam ang oras", "saveChanges": "I-save ang mga pagbabago", "error": {"title": "Error", "message": "Nabigo na i-update ang data ng nutrisyon. Subukang muli."}, "nutrition": {"calories": "🔥 Calories", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 Carbohydrates", "sugars": "🍬 <PERSON><PERSON>l", "fat": "🥑 Taba"}, "macroBreakdown": {"title": "Pagkasira ng Macronutrient", "noData": "Walang available na data ng macronutrient para sa pagkaing ito."}, "macroLabels": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fat": "Taba", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Detalyadong Impormasyon", "edit": "I-edit", "save": "I-save", "saving": "Nagse-save...", "enterValue": "Ilagay ang halaga", "notSet": "Hindi pa nakatakda", "age": "Edad", "heightCm": "Taas (cm)", "weightKg": "Timbang (kg)", "targetWeight": "Target na Timbang", "nutritionTargets": "Mga Target sa Nutrisyon", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fats": "Mga taba", "gm": "gm", "editNote": "Ilagay ang mga halaga o iwanan na blangko para sa awtomatikong pagkalkula.", "autoCalculateNote": "Ang mga Macros ay awtomatikong kinakalkula batay sa iyong data.", "validation": {"ageMin": "Ang edad ay dapat na 18 pataas", "ageMax": "Ang edad ay dapat na mas mababa sa 125", "heightMin": "<PERSON> taas ay dapat na 50cm pataas", "heightMax": "Ang taas ay dapat na mas mababa sa 250cm", "weightMin": "Ang timbang ay dapat na 30kg pataas", "weightMax": "Ang timbang ay dapat na mas mababa sa 500kg", "targetWeightMin": "Ang target na timbang ay dapat na 30kg pataas", "targetWeightMax": "Ang target na timbang ay dapat na mas mababa sa 500kg", "proteinMin": "Ang protein ay dapat na 0 o higit pa", "carbsMin": "Ang Carbohydrates ay dapat na 0 o higit pa", "fatsMin": "Ang taba ay dapat na 0 o higit pa"}}, "tracker": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fat": "Taba", "excess": "sobra", "remaining": "natitira"}, "specialistConstants": {"nutritionist": {"name": "Nutritionist", "description": "Ekspertong payo sa diet, nutrisyon, at malusog na gawi sa pagkain", "featureName": "Espesyalista sa Nutrisyon"}, "cardiologist": {"name": "Cardiologist", "description": "Dalubhasa sa kalusugan ng puso at mga kondisyon ng cardiovascular", "featureName": "Espesyalista sa Cardiology"}, "neurologist": {"name": "Neurologist", "description": "Nakatuon sa mga karamdaman ng utak, spinal cord, at nervous system", "featureName": "Espesyalista sa Neurology"}, "oncologist": {"name": "Oncologist", "description": "Dalubhasa sa diagnosis ng kanser at mga opsyon sa paggamot", "featureName": "Espesyalista sa Oncology"}, "endocrinologist": {"name": "Endocrinologist", "description": "Eksperto sa mga kondisyon ng hormonal at metabolic disorder", "featureName": "Espesyalista sa Endocrinology"}}, "discoverCards": {"categories": {"nutrition": "Nutrisyon", "heartHealth": "Kalusugan ng Puso", "mentalHealth": "Kalusugang <PERSON>", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "titles": {"vitaminB12Recovery": "Gaano Katagal ang Paggaling sa Kakulangan ng Vitamin B12", "vitaminDeficiencyGanglion": "<PERSON><PERSON> sa Bitamina ang Nagdudulot ng Ganglion Cyst", "vitaminDeficiencyHairFall": "Anong <PERSON>an sa Bitamina ang Nagdudulot ng Paglalagas ng Buhok", "vitaminWaters": "Mabuti ba sa iyo ang mga Vitamin Water?", "cholesterolHeadaches": "Nag<PERSON><PERSON><PERSON> ba ng pananakit ng ulo ang mataas na kolesterol?", "cholesterolEyes": "Ano ang mga sintomas ng mataas na kolesterol na makikita sa mga mata?", "diabetesHeadaches": "Maaari bang Magdulot ng Pananakit ng Ulo ang Diabetes", "chestPainDrinking": "Bakit Masakit Ang Dibdib Pagkatapos Uminom", "stressDizziness": "Ma<PERSON><PERSON> bang Magdulot ng Pagkahilo ang Stress", "bulimiaFace": "Ano ang Mukha ng Bulimia", "kneeTwitch": "Bakit Nangangatal ang Tuho<PERSON> Ko", "noseTwitching": "Bakit Nangyayari ang Pag-ikot ng Ilong", "piriformisVsSciatica": "Ano ang mga Pagkakaiba ng Piriformis Syndrome at Sciatica?", "shoulderBladePinched": "Paano Magpagaling ng Napipisil na Ugat sa Likod ng Balikat", "shoulderPinched": "Paano Magpagaling ng Napipisil na Ugat sa Balikat", "meniscusTorn": "Paano Magpagaling ng Napunit na Meniskus nang Natural", "hydrateQuickly": "Paano Mabilis na Mag-hydrate", "periodConstipation": "Normal lang bang magkaroon ng constipation kapag may regla?", "acneScars": "<PERSON><PERSON> ang mga Peklat ng Tigyawat ng Natural sa loob ng Isang Linggo", "perimenopausePregnancy": "<PERSON><PERSON><PERSON> Nagpeperimenopo<PERSON>?"}, "descriptions": {"vitaminB12Recovery": "Alamin ang timeline ng paggaling mula sa kakulangan ng bitamina B12 at mga epektibong lunas para mapalakas ang iyong enerhiya.", "vitaminDeficiencyGanglion": "Suriin ang koneksyon sa pagitan ng kakulangan ng bitamina at pag-develop ng ganglion cyst sa katawan.", "vitaminDeficiencyHairFall": "<PERSON><PERSON> kung paano ang kakulangan ng mahahalagang bitamina ay maaaring humantong sa pagkawala ng buhok at kung ano ang maaari mong gawin upang maiwasan ito.", "vitaminWaters": "Tu<PERSON>sin ang mga benepisyo at posibleng disbentaha ng vitamin waters bilang bahagi ng iyong pang-araw-araw na nutrisyon.", "cholesterolHeadaches": "Siyasatin ang posibleng koneksyon sa pagitan ng mataas na antas ng kolesterol at pagsisimula ng pananakit ng ulo.", "cholesterolEyes": "Alamin kung paano maaaring mahayag ang mataas na kolesterol sa iyong mga mata at kung anong mga sintomas ang dapat bantayan.", "diabetesHeadaches": "<PERSON>amin ang ugnayan sa pagitan ng diabetes at pagsakit ng ulo sa pang-araw-araw na buhay.", "chestPainDrinking": "Suriin ang mga dahilan sa likod ng pananakit ng dibdib pagkatapos uminom ng ilang inumin.", "stressDizziness": "Siyasatin kung paano nakakaapekto ang stress sa iyong balanse at pangkalahatang kagalingan, na humahantong sa pagkahilo.", "bulimiaFace": "Unawain ang mga pisikal na senyales ng bulimia, kasama na ang mga epekto sa hitsura ng mukha.", "kneeTwitch": "Siyasatin ang mga posibleng dahilan sa likod ng hindi sinasadyang pag-twitch ng tuhod at ang kaugnayan nito sa stress o pagkapagod.", "noseTwitching": "Alamin ang mga posibleng dahilan ng pag-twitch ng ilong at ang kaugnayan nito sa pagkabalisa o iba pang mga salik.", "piriformisVsSciatica": "Ihambing ang mga sintomas ng piriformis syndrome at sciatica upang mas maunawaan ang iyong kondisyon.", "shoulderBladePinched": "<PERSON><PERSON><PERSON> ang mga epektibong pamamaraan upang mapawi ang isang pinched nerve sa iyong shoulder blade at maibalik ang kadaliang kumilos.", "shoulderPinched": "Matuto ng simpleng ehersisyo at pag-uunat upang mapagaan ang compression ng nerbiyos sa lugar ng balikat.", "meniscusTorn": "Suriin ang mga natural na paraan at ehersisyo upang suportahan ang paggaling ng isang napunit na meniscus.", "hydrateQuickly": "<PERSON><PERSON><PERSON> ang mabilis at epektibong paraan upang mag-rehydrate at mapanatili ang pinakamainam na hydration ng katawan.", "periodConstipation": "Unawain ang mga dahilan sa likod ng paninigas ng dumi sa panahon ng regla at matuto ng mga natural na lunas.", "acneScars": "Tu<PERSON>sin ang mga natural na lunas at mga tip sa pangangalaga sa balat upang mabawasan ang hitsura ng mga peklat ng acne nang mabilis.", "perimenopausePregnancy": "<PERSON>uto tungkol sa perimenopause, mga konsiderasyon sa pagkamayabong, at kung ano ang aasahan sa yugtong ito ng buhay."}}}