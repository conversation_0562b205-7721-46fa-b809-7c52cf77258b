{"common": {"error": "Помилка", "yes": "Так", "no": "Ні", "sometimes": "Іноді", "close": "Закрити", "cancel": "Скасувати", "save": "Зберегти", "next": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Завантаження...", "version": "v0.0.1.7"}, "welcome": "Увійдіть, щоб почати спілкуватися з Августом", "notFound": {"title": "Ой!", "message": "Цей екран не існує.", "goHome": "Перейти на головний екран!"}, "library": {"title": "Бібліотека здоров'я"}, "specialists": {"title": "Спеціалісти", "description": "Зверніться до спеціалізованих медичних працівників для отримання більш конкретної інформації щодо проблем зі здоров'ям. Виберіть спеціаліста нижче:", "generalPhysician": {"title": "Загальнопрактикуючий лікар", "description": "Для загальних проблем зі здоров'ям та первинної медичної допомоги."}, "nutritionist": {"title": "Дієтолог", "description": "Для отримання порад щодо дієти, харчування та контролю ваги."}, "cardiologist": {"title": "Кардіолог", "description": "Для проблем, пов'язаних із серцем, та серцево-судинним здоров'ям."}, "neurologist": {"title": "Невролог", "description": "Для проблем із мозком, спинним мозком та нервовою системою."}, "oncologist": {"title": "Онколог", "description": "Для проблем, пов'язаних з раком, та лікуванням."}, "endocrinologist": {"title": "Ендокринолог", "description": "Для гормональних розладів та лікування діабету."}, "dermatologist": {"title": "Дерматолог", "description": "Для станів шкіри, волосся та нігтів."}, "psychiatrist": {"title": "Психіатр", "description": "Для проблем психічного здоров'я та психологічного благополуччя."}}, "profile": {"title": "Профіль", "defaultName": "Гість", "namePlaceholder": "Введіть ваше ім'я", "saving": "Збереження...", "noPhoneNumber": "Номер телефону відсутній", "loggingOut": "Вихід з системи...", "about": {"title": "Про нас", "description": "Дізнайтеся більше про Августа"}, "whatsapp": {"title": "WhatsApp", "description": "Спілкуйтеся з Августом у WhatsApp"}, "refer": {"title": "Рекомендації", "description": "Сподобався Август? Поділіться з друзями"}, "deleteAccount": {"title": "Видалити обліковий запис", "description": "Нам шкода, що ви йдете"}, "logout": {"title": "Вийти", "description": "Повертайтеся скоро. Ми будемо сумувати за вами"}, "shareMessage": "👋Привіт, перегляньте цей чудовий додаток, який я використовую!\n\n\n\n➡️Я використовую Августа, щоб отримувати швидку та надійну інформацію та поради щодо здоров'я. Це як мати лікаря у кишені! Перегляньте його тут:", "error": {"loadFailed": "Не вдалося завантажити дані користувача", "fetchError": "Виникла помилка під час отримання даних користувача", "updateNameFailed": "Не вдалося оновити ім'я", "updateNameError": "Виникла помилка під час оновлення імені", "loadFoodData": "Не вдалося завантажити дані про їжу", "logoutError": "Помилка під час виходу з системи:", "shareError": "Помилка під час обміну повідомленнями:"}}, "error": {"title": "Щось пішло не так", "checkLogs": "Будь ласка, перевірте журнали вашого пристрою для отримання додаткової інформації.", "unknown": "Невідома помилка", "unknownFile": "Невідомий файл", "unknownLine": "Невідома рядок", "unknownColumn": "Невідомий стовпець"}, "auth": {"phone": {"selectCountry": "Виберіть країну", "searchCountries": "Шукати країни", "validation": {"invalidPhone": "Будь ласка, введіть дійсний номер телефону", "invalidDigits": "Будь ласка, введіть дійсний номер телефону (7-15 цифр)"}}, "header": {"title": "Отримайте відповіді на ваші запитання про здоров'я миттєво та конфіденційно", "subtitle": "Продуманий підхід. Без поспіху. Без плутанини.", "emphasis": "Тільки ясність."}, "greeting": "Привіт 👋", "phoneNumber": "Номер телефону", "requestOTP": "Запитати OTP", "otp": {"title": "Одноразовий пароль", "verify": "Перевірити OTP", "sending": "Відправлення...", "countdown": "Повторна відправка OTP через {{countdown}}с", "resend": "Повторно відправити OTP", "sentTo": "OTP відправлено на ", "whatsappSuffix": " в WhatsApp"}, "disclaimer": {"prefix": "Реєструючись, ви погоджуєтеся з нашими ", "continuePrefix": "Продовжуючи, ви погоджуєтеся з нашими ", "termsOfService": "Умовами обслуговування", "and": " та ", "privacyPolicy": "Політикою конфіденційності", "whatsappConsent": ", а також погоджуєтеся отримувати оновлення та нагадування від нас через WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Ласкаво просимо до August!", "buttonText": "Почнемо!"}}, "postAuth": {"step1": {"title": "Привіт!", "subtitle": "Я — August 👋", "description": "Уявляйте мене як затишний куточок на вашому\nпристрої, де ви можете досліджувати всі ваші\nзапитання про здоров'я.", "subdescription": "Не соромтеся запитувати все, що вас цікавить.\nБез осуду, без обмежень!", "placeholder": "Як мене звати?"}, "step2": {"title": "Привіт, {{userName}},", "subtitle": "Ось що я можу зробити:", "features": {"health": {"title": "Відповідати на ваші", "subtitle": "Запитання про здоров'я"}, "nutrition": {"title": "Відстежувати ваші", "subtitle": "Макронутрієнти"}, "reports": {"title": "Аналізувати", "subtitle": "Звіти"}}}}, "pills": {"thoughtful": "Продуманий", "careful": "Обережний", "accurate": "Точний"}, "features": {"symptoms": {"title": "Перевірте свої симптоми", "description": "Мене нудить вже тиждень. Що зі мною відбувається?"}, "prescriptions": {"title": "Проаналізуйте свої рецепти", "description": "Завантажте та зрозумійте рецепти, як лікар."}, "medicine": {"title": "Дізнайтеся про свої ліки", "description": "Чи взаємодіє Метформін для мого СПКЯ з моїми таблетками від СДУГ?"}, "plans": {"title": "Отримайте персоналізовані плани", "description": "Чи можете ви скласти для мене план харчування та фітнесу для зниження рівня HbA1c?"}}, "buttons": {"getStarted": "Почати", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"nameRequired": "Будь ласка, введіть своє ім'я"}}, "tabs": {"chat": "Чат", "discover": "Відкрити", "nutrition": "Хар<PERSON>ування", "personalize": "Персоналізувати"}, "chat": {"nav": {"title": "Серпень"}, "me": "Я", "augustName": "Серпень", "input": {"placeholder": "Запитайте Серпня...", "disclaimer": "Серпень може помилятися. Перевірте у лікаря"}, "list": {"loadingMessages": "Завантаження повідомлень...", "noMessages": "Поки що немає повідомлень. Розпочніть розмову!"}, "connection": {"offlineMessage": "Виглядає на те, що ви не в мережі. Підключіться, щоб відправити повідомлення.", "connecting": "Підключаємось...", "tryAgain": "Спробуйте ще раз"}, "prompts": {"uploadReport": "Заван<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> звіт", "speakInHindi": "Розмовляти хінді", "notFeelingWell": "Мені нездужається", "whatIsMyBMI": "Який мій ІМТ?", "nutritionAdvice": "Рекомендації щодо харчування", "sleepBetter": "Краще спати"}, "citations": {"referenceText": "Для отримання додаткової інформації щодо цієї розмови, будь ласка, зверніться до:"}, "actions": {"copiedToClipboard": "Скопійовано до буфера обміну", "copied": "Скопійовано"}, "share": {"introText": "👋 Привіт, подивись на розмову, яку я мав з Серпнем:\n\n", "downloadText": "\n\n➡️Завантажте Серпень, щоб поспілкуватися зі своїм дружнім AI-компаньйоном зі здоров'я:\n"}}, "discover": {"nav": {"title": "Відкрити"}, "categories": {"all": "Усі", "heartHealth": "Здоров'я серця", "nutrition": "Хар<PERSON>ування", "mentalHealth": "Психічне здоров'я", "fitness": "Фіт<PERSON><PERSON>с", "wellness": "Здоров'я"}, "cards": {"empty": "Карток для цієї категорії немає"}, "sections": {"features": "Особливості"}, "features": {"healthLibrary": {"title": "Мед<PERSON>чна бібліотека", "description": "Доступ до надійної та актуально оновленої медичної інформації абсолютно безкоштовно."}, "nutritionTracker": {"title": "Трекер харчування", "description": "Завжди цікавило, чи можна просто завантажити фотографію своєї їжі та відстежувати всі свої цілі щодо харчування? Серпень може це зробити!"}, "multilingualSupport": {"title": "Бага<PERSON><PERSON>мовна підтримка", "description": "Ви можете спілкуватися з Серпнем будь-якою мовою, якою вам зручно! Серпень завжди тут, щоб вислухати, підтримати та відповісти вам у будь-який час.", "samplePrompt": "Розмовляти хінді"}, "labReportAnalysis": {"title": "Аналіз лабораторних результатів", "description": "Коли ви розмовляєте з Серпнем про свої лабораторні результати, ви отримуєте надзвичайну точність. Серпень обробив понад 4,7 мільйона звітів з точністю вилучення біомаркерів 98,4%."}}}, "nutrition": {"nav": {"title": "Хар<PERSON>ування"}, "meals": {"title": "Ваші прийоми їжі", "subtitle": "Торкніться, щоб переглянути макроелементи в кожному прийомі їжі"}, "upload": {"loading": "Завантаження зображення..."}, "defaultFoodName": "Продукт харчування", "today": "Сьогодні", "unknownTime": "Невідомий час", "calories": "🔥 Калорії", "proteins": "🥩 Білки", "carbs": "🍞 Вуглеводи", "sugars": "🍬 Цукри", "fat": "🥑 Жири", "caloriesLabel": "Калорії", "proteinLabel": "Білок", "carbohydratesLabel": "Вуглеводи", "fatLabel": "<PERSON><PERSON><PERSON>", "sugarLabel": "Цукор", "tips": "Поради:", "macroBreakdown": "Розподіл макроелементів", "noMacroData": "Для цього продукту харчування немає даних про макроелементи.", "disclaimer": "Тільки для освітніх цілей. Дізнатися більше", "disclaimerLink": "тут", "unit": {"kcal": "ккал", "g": "г"}, "form": {"gender": {"title": "Яка ваша стать?", "subtitle": "Це буде використано для калібрування вашого персонального плану.", "male": "Чоловіча", "female": "Жіноча", "other": "Інша"}, "age": {"title": "Скільки вам років?", "subtitle": "Це буде використано для розрахунку ваших щоденних потреб."}, "measurements": {"title": "Зріст та вага", "subtitle": "Будь ласка, введіть свій зріст у сантиметрах та вагу у кілограмах."}, "activity": {"title": "Рівень активності", "subtitle": "Як часто ви займаєтесь спортом?", "none": "Без фізичних вправ", "moderate": "Помірний", "high": "Високий"}, "goal": {"title": "Мета щодо ваги", "subtitle": "Чого б ви хотіли досягти?", "increase": "Зб<PERSON>льшити", "maintain": "Підтримувати", "decrease": "Зменшити"}, "targetWeight": {"title": "Баж<PERSON><PERSON> вага", "subtitle": "Яка ваша бажана вага у кілограмах?"}, "setup": {"title": "Налаштування вашого плану", "subtitle": "Будь ласка, зачекайте, поки ми підготуємо ваш план харчування."}, "review": {"title": "Перегляньте свій план", "subtitle": "Перегляньте та налаштуйте свій план харчування."}, "height": {"label": "Зріст (см)"}, "weight": {"label": "Вага (кг)"}}, "error": {"updateFailed": "Не вдалося оновити дані про харчування. Будь ласка, спробуйте ще раз.", "parsingError": "Помилка розбору даних про продукти харчування:", "fetchReportsFailed": "Не вдалося отримати дані звітів. Будь ласка, спробуйте ще раз.", "missingReportId": "ID звіту відсутній"}}, "personalize": {"nav": {"title": "Персоналізація"}, "button": {"saving": "Збереження", "review": "Перегляд", "saveNext": "Зберегти та Далі"}}, "basicInfo": {"title": "Давайте краще познайомимось", "subtitle": "Ця інформація допоможе нам персоналізувати ваші рекомендації щодо здоров'я", "age": {"question": "Скільки вам років?", "placeholder": "Введіть ваш вік"}, "sex": {"question": "Яка ваша стать?", "placeholder": "Оберіть вашу стать", "male": "Чоловіча", "female": "Жіноча", "other": "Інша"}, "height": {"question": "Я<PERSON><PERSON> ваш зріст? (см)", "placeholder": "Введіть ваш зріст"}, "weight": {"question": "Яка ваша вага? (кг)", "placeholder": "Введіть вашу вагу"}}, "lifestyle": {"title": "Ваші звички", "subtitle": "Розуміння ваших щоденних звичок допомагає нам надавати кращі рекомендації", "diet": {"question": "Якого типу дієту ви дотримуєтеся?", "placeholder": "Оберіть вашу дієту", "vegetarian": "Вегетаріанська", "nonVegetarian": "Не вегетаріанська", "vegan": "Веганська", "pescatarian": "Пескатаріанська", "keto": "Кето", "paleo": "Палео"}, "exercise": {"question": "Чи займаєтеся ви регулярно спортом?"}, "drinking": {"question": "Чи вживаєте ви алкоголь?"}, "smoking": {"question": "Чи палите ви?"}, "sleep": {"question": "Скільки годин ви спите за ніч?", "value": "{{sleep}} годин"}, "hydration": {"question": "Скільки склянок води ви випиваєте щодня?", "value": "{{hydration}} склянок ({{liters}}л)"}}, "allergies": {"title": "Чи маєте ви якісь алергії?", "subtitle": "Знання ваших алергій допомагає нам надавати безпечніші рекомендації", "allergyIndex": "А<PERSON>е<PERSON><PERSON><PERSON><PERSON> {{index}}", "name": {"question": "На що у вас алергія?", "placeholder": "Введіть алерген (наприклад, арах<PERSON><PERSON>, пил)"}, "severity": {"question": "Наскільки серйозна ця алергія?", "placeholder": "Оберіть ступінь тяжкості", "mild": "Легка", "moderate": "Помірна", "severe": "Важка"}, "addButton": "Додати ще одну алергію", "noAllergiesButton": "У мене немає алергій"}, "medications": {"title": "Ліки та добавки", "subtitle": "Розкажіть нам про будь-які ліки або добавки, які ви зараз приймаєте", "medicationIndex": "<PERSON><PERSON><PERSON><PERSON> {{index}}", "name": {"label": "Назва ліків", "placeholder": "Введіть назву ліків"}, "startDate": {"question": "Коли ви почали їх приймати?", "placeholder": "Оберіть дату"}, "type": {"label": "<PERSON>и<PERSON> л<PERSON>", "shortTerm": "Короткострокові", "longTerm": "Довгострокові"}, "dose": {"label": "Доза", "placeholder": "Кількість"}, "unit": {"label": "Одиниця виміру"}, "frequency": {"label": "Періодичність", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perDay": "на день", "perWeek": "на тиждень", "perMonth": "на місяць", "perYear": "на рік"}, "units": {"mg": "мг", "ml": "мл", "iu": "од", "puffs": "вдихів", "drops": "крапель", "tsp": "ч.л.", "tbsp": "ст.л.", "cups": "склянок"}, "addButton": "Додати ще одні ліки", "noMedicationsButton": "Я не приймаю жодних ліків", "calendar": {"title": "Оберіть дату початку"}}, "conditions": {"title": "Медичні стани", "subtitle": "Розкажіть нам про будь-які медичні стани, які у вас є або були в минулому", "conditionIndex": "Стан {{index}}", "name": {"label": "Назва стану", "placeholder": "Введіть стан (наприклад, астма тощо)"}, "since": {"question": "З якого часу у вас є цей стан?", "placeholder": "Оберіть дату"}, "current": {"question": "Чи турбує він вас зараз?"}, "medicated": {"question": "Чи приймаєте ви якісь ліки від цього?"}, "addButton": "Додати ще один стан", "noConditionsButton": "У мене немає медичних станів", "calendar": {"title": "Оберіть дату"}}, "reproductive": {"title": "Репродуктивне здоров'я", "subtitle": "Ця інформація допоможе нам надати більш персоналізовані рекомендації щодо здоров'я", "menstruation": {"question": "Чи мала ви менструацію?", "detailsTitle": "Деталі менструації", "regularity": {"question": "Наскільки регулярний ваш цикл?", "regular": "Регулярний", "irregular": "Нерегулярний", "notSure": "Не впевнена"}, "cycleLength": {"label": "Середня тривалість циклу (дні)", "placeholder": "Введіть тривалість циклу"}, "flowDays": {"label": "Дн<PERSON> кровотечі: {{flowDays}}", "min": "1 день", "max": "15 днів"}, "padsPerDay": {"label": "Прокладок/тампонів на день: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Які симптоми ви відчуваєте під час менструації?", "placeholder": "Введіть симптоми (наприклад, спазми, головний біль)"}}, "childbirth": {"question": "Чи народжували ви дитину?", "detailsTitle": "Деталі пологів", "children": {"label": "Кількість дітей"}, "pregnancies": {"label": "Кількість вагітностей"}, "complications": {"question": "Чи були якісь ускладнення під час вагітності або пологів?", "placeholder": "Введіть ускладнення (якщо такі були)"}}}, "review": {"title": "Перегляньте вашу інформацію", "subtitle": "Будь ласка, перегляньте введену вами інформацію перед відправкою", "sections": {"basicInfo": "Основна інформація", "lifestyle": "Спосіб життя", "allergies": "Алергії", "medications": "Ліки та добавки", "conditions": "Медичні стани", "reproductive": "Репродуктивне здоров'я", "menstruationDetails": "Деталі менструації", "childbirthDetails": "Деталі пологів"}, "fields": {"age": "Вік:", "sex": "Стать:", "height": "Зріст:", "weight": "Вага:", "diet": "Дієта:", "exercise": "Фізичні вправи:", "drinking": "Алкоголь:", "smoking": "Куріння:", "sleep": "Сон:", "hydration": "Гідратація:", "allergyIndex": "Але<PERSON>г<PERSON>я {{index}}:", "dose": "Доза:", "frequency": "Частота:", "type": "Тип:", "since": "З:", "currentlyActive": "Активно зараз:", "takingMedication": "Приймає ліки:", "hasMenstruated": "Мала менструацію:", "regularity": "Регулярність:", "cycleLength": "Тривалість циклу:", "flowDays": "Дні кровотечі:", "padsPerDay": "Прокладок/тампонів на день:", "hasChildbirth": "Народжувала:", "children": "Діти:", "pregnancies": "Вагітності:"}, "notProvided": "Не вказано", "units": {"cm": "{{height}} см", "kg": "{{weight}} кг"}, "values": {"sleepHours": "{{sleep}} годин на день", "hydration": "{{hydration}} чашок ({{liters}} л) на день", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} дн<PERSON>в"}, "noData": {"allergies": "Алерг<PERSON>ї не вказані", "medications": "Ліки не вказані", "conditions": "Медичні стани не вказані"}, "submitButton": "Надіслати інформацію"}, "success": {"title": "Інформацію оновлено!", "message": "Дякуємо за надання вашої інформації про здоров'я. Ми використаємо її для персоналізації вашого досвіду та надання кращих рекомендацій.", "benefits": {"insights": "Персоналізовані поради щодо здоров'я", "reminders": "Кращі нагадування про ліки", "recommendations": "Індивідуальні рекомендації щодо здоров'я"}, "continueButton": "Перейти до панелі"}, "permissions": {"microphonePermissionDenied": "Доступ до мікрофону заборонено", "microphoneAccessDescription": "August потребує доступу до вашого мікрофону для запису аудіо та відправлення голосових повідомлень", "permissionDenied": "Доступ заборонено", "cameraPermissionRequired": "Нам потрібен доступ до камери, щоб це працювало!", "mediaLibraryPermissionRequired": "Нам потрібен доступ до бібліотеки медіа, щоб це працювало!"}, "voiceRecording": {"recordingTooLong": "За<PERSON>ис занадто довгий", "recordingTooLongMessage": "Голосові записи повинні бути менше 5 хвилин. Будь ласка, запишіть коротше повідомлення."}, "errors": {"uploadFailed": "Завантаження не вдалося", "voiceUploadFailed": "Не вдалося завантажити голосовий запис.", "voiceRecordingFailed": "Не вдалося відправити голосовий запис", "failedToStopRecording": "Не вдалося зупинити запис", "photoUploadFailed": "Не вдалося завантажити фото.", "failedToTakePhoto": "Не вдалося зробити фото", "imageUploadFailed": "Не вдалося завантажити зображення: {{fileName}}", "failedToPickImage": "Не вдалося вибрати зображення", "documentUploadFailed": "Не вдалося завантажити документ: {{fileName}}", "failedToPickDocument": "Не вдалося вибрати документ"}, "audioPlayer": {"downloadingAudio": "Завантаження аудіо...", "loadingAudio": "Завантаження аудіо..."}, "mediaProcessing": {"processingFile": "Обробка вашого файлу", "uploadingSecuring": "Завантаження та захист файлу...", "analyzingContent": "Аналіз вмісту документа...", "extractingInfo": "Вилучення ключової інформації...", "processingInsights": "Обробка результатів...", "preparingResponse": "Підготовка детальної відповіді...", "finalizingResponse": "Завершення відповіді..."}, "attachments": {"voiceMessage": "Голосове повідомлення", "image": "[ЗОБРАЖЕННЯ]", "pdf": "[PDF]", "voice": "[ГОЛОСОВЕ ПОВІДОМЛЕННЯ]"}, "pdf": {"loadingPdf": "Завантаження PDF..."}, "dateTime": {"yesterday": "Вчора, "}, "navbar": {"defaultTitle": "august", "selectedCount": "обрано"}, "mediaUpload": {"photoLibrary": "Фотогалерея", "takePhoto": "Зробити фото", "chooseFile": "Вибрати файл"}, "comingSoon": {"title": "Зараз у розробці!", "description": " знаходиться на стадії розробки. Слідкуйте за оновленнями!", "buttonText": "Зрозуміло!"}, "clipboard": {"success": "Посилання скопійовано в буфер обміну"}, "mediaPhotos": {"emptyState": "Ще немає записів."}, "foodDetail": {"defaultFoodName": "Продукт харчування", "nutrition": {"totalCalories": "Загальна калорійність", "proteins": "Білки", "carbs": "Вуглеводи", "fat": "<PERSON><PERSON><PERSON><PERSON>", "sugars": "Цукри", "fibers": "Клітковина"}}, "reports": {"defaultTitle": "Медіа-елемент", "defaultFoodName": "Продукт харчування", "defaultName": "Документ", "openButton": "Відкрити у зовнішньому переглядачі", "biomarker": {"headerBiomarker": "Біомаркер", "headerValue": "Значення", "headerRefRange": "Діапазон значень", "headerStatus": "Статус"}, "noData": "Дані про біомаркери відсутні"}, "setup": {"title": "Ми налаштовуємо все для вас", "inProgress": "Триває...", "progressMessages": {"0": "Розрахунок денної калорійності", "1": "Оптимізація розподілу макроелементів", "2": "Створення плану харчування", "3": "Розрахунок показника здоров'я", "4": "Завершення налаштування"}, "checklistItems": {"0": "Аналіз ваших даних про здоров'я", "1": "Розрахунок оптимального плану харчування", "2": "Персоналізація ваших рекомендацій", "3": "Створення ваших пропозицій щодо харчування", "4": "Завершення вашого налаштування"}}, "foodEntry": {"emptyState": "Ще немає записів про їжу. Зробіть фото свого прийому їжі, щоб додати його!"}, "nutritionReview": {"congratulations": "Вітаємо!", "subtitle": "Ваш персональний план харчування готовий", "submitButton": "Починаємо!", "dailyTargetsTitle": "Ваші щоденні цілі щодо харчування", "macroLabels": {"calories": "Калорії", "carbs": "Вуглеводи", "protein": "Білок", "fats": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Як досягти своїх цілей:", "healthScores": "Використовуйте показники здоров’я для покращення своєї рутини", "trackFood": "Регулярно відстежуйте споживання їжі", "followCalories": "Дотримуйтесь рекомендованої денної норми калорій", "balanceMacros": "Збалансуйте споживання вуглеводів, білків та жирів"}}, "editModal": {"titlePrefix": "Редагувати", "cancelButton": "Скасувати", "saveButton": "<PERSON><PERSON><PERSON><PERSON>"}, "processing": {"stages": {"scanning": "Сканую їжу...", "identifying": "Визначаю інгредієнти...", "extracting": "Видобування поживних речовин...", "finalizing": "Завершення обробки результатів..."}, "error": {"defaultMessage": "Їжі не виявлено", "subtitle": "Спробуйте інший кут"}, "retakeButton": "Торкніться, щоб перезняти фото", "notification": "Ми повідомимо вас, коли буде зроблено!"}, "chart": {"title": "Відстеження харчування з часом", "selectNutrient": "Оберіть поживну речовину:", "emptyState": "Ще немає даних про харчову цінність.", "dropdown": {"calories": "Калорії", "protein": "Білок", "carbs": "Вуглеводи", "fat": "<PERSON><PERSON><PERSON>", "sugars": "Цукри"}}, "foodModal": {"defaultName": "Продукт харчування", "defaultDate": "Сьогодні", "defaultTime": "Невідомий час", "saveChanges": "Зберегти зміни", "error": {"title": "Помилка", "message": "Не вдалося оновити дані про харчування. Спробуйте ще раз."}, "nutrition": {"calories": "🔥 Калорії", "proteins": "🥩 Білки", "carbs": "🍞 Вуглеводи", "sugars": "🍬 Цукор", "fat": "🥑 Жир"}, "macroBreakdown": {"title": "Розподіл макронутрієнтів", "noData": "Немає доступних даних про макронутрієнти для цього продукту."}, "macroLabels": {"calories": "Калорії", "protein": "Білок", "carbs": "Вуглеводи", "fat": "<PERSON><PERSON><PERSON>", "sugar": "Цукор"}}, "infoModal": {"title": "Докладна інформація", "edit": "Редагувати", "save": "Зберегти", "saving": "Збереження...", "enterValue": "Введіть значення", "notSet": "Не встановлено", "age": "<PERSON><PERSON><PERSON>", "heightCm": "Зріст (см)", "weightKg": "Вага (кг)", "targetWeight": "Цільова вага", "nutritionTargets": "Цілі харчування", "protein": "Білок", "carbs": "Вуглеводи", "fats": "<PERSON><PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Введіть значення або залиште поля порожніми для автоматичного розрахунку.", "autoCalculateNote": "Макроси розраховуються автоматично на основі ваших даних.", "validation": {"ageMin": "Вік має бути не менше 18 років", "ageMax": "Вік має бути менше 125", "heightMin": "Висота має бути не менше 50 см", "heightMax": "Висота має бути менше 250 см", "weightMin": "Вага має бути не менше 30 кг", "weightMax": "Вага має бути менше 500 кг", "targetWeightMin": "Цільова вага має бути не менше 30 кг", "targetWeightMax": "Цільова вага має бути менше 500 кг", "proteinMin": "Білок має бути 0 або більше", "carbsMin": "Вуглеводів має бути 0 або більше", "fatsMin": "Жири мають бути 0 або більше"}}, "tracker": {"calories": "Калорії", "protein": "Білок", "carbs": "Вуглеводи", "fat": "<PERSON><PERSON><PERSON>", "excess": "надлишок", "remaining": "залишок"}, "specialistConstants": {"nutritionist": {"name": "Дієтолог", "description": "Експертні поради щодо дієти, харчування та здорових харчових звичок", "featureName": "Спеціаліст з харчування"}, "cardiologist": {"name": "Кардіолог", "description": "Спеціалізується на здоров'ї серця та серцево-судинних захворюваннях", "featureName": "Спеціаліст з кардіології"}, "neurologist": {"name": "Невролог", "description": "Спеціалізується на захворюваннях головного мозку, спинного мозку та нервової системи", "featureName": "Спеціаліст з неврології"}, "oncologist": {"name": "Онколог", "description": "Спеціалізується на діагностиці раку та варіантах лікування", "featureName": "Спеціаліст з онкології"}, "endocrinologist": {"name": "Ендокринолог", "description": "Експерт з гормональних станів та метаболічних розладів", "featureName": "Спеціаліст з ендокринології"}}, "discoverCards": {"categories": {"nutrition": "Хар<PERSON>ування", "heartHealth": "Здоров'я серця", "mentalHealth": "Ментальне здоров'я", "fitness": "Фіт<PERSON><PERSON>с", "wellness": "Вел<PERSON><PERSON>с"}, "titles": {"vitaminB12Recovery": "Скільки часу потрібно, щоб відновитися після дефіциту вітаміну В12", "vitaminDeficiencyGanglion": "Який дефіцит вітамінів викликає кісти гангліїв", "vitaminDeficiencyHairFall": "Який дефіцит вітамінів викликає випадіння волосся", "vitaminWaters": "Чи корисні вітамінні води", "cholesterolHeadaches": "Чи викликає високий рівень холестерину головний біль", "cholesterolEyes": "Які симптоми високого рівня холестерину можна побачити в очах", "diabetesHeadaches": "Чи може діабет викликати головний біль", "chestPainDrinking": "Чому болить груди після алкоголю", "stressDizziness": "Чи може стрес викликати запаморочення", "bulimiaFace": "Що таке обличчя булімії", "kneeTwitch": "Чому у мене тремтить коліно", "noseTwitching": "Чому відбувається тремтіння носа", "piriformisVsSciatica": "У чому різниця між синдромом грушоподібного м'яза та ішіасом", "shoulderBladePinched": "Як зняти защемлення нерва в лопатки", "shoulderPinched": "Як зняти защемлення нерва в плечі", "meniscusTorn": "Як вилікувати розрив меніска природним шляхом", "hydrateQuickly": "Як швидко відновити водний баланс", "periodConstipation": "Чи нормально мати запор під час менструації", "acneScars": "Як позбутися слідів від прищів природним шляхом за тиждень", "perimenopausePregnancy": "Чи можна завагітніти під час перименопаузи"}, "descriptions": {"vitaminB12Recovery": "Дізнайтеся про терміни відновлення при дефіциті вітаміну B12 та ефективні засоби для підвищення рівня енергії.", "vitaminDeficiencyGanglion": "Дослідіть зв'язок між дефіцитом вітамінів та розвитком ганглієвих кіст у тілі.", "vitaminDeficiencyHairFall": "Дізнайтеся, як нестача важливих вітамінів може призвести до випадіння волосся та що ви можете зробити, щоб запобігти цьому.", "vitaminWaters": "Відкрийте для себе переваги та потенційні недоліки вітамінних вод як частини вашого щоденного харчування.", "cholesterolHeadaches": "Розгляньте можливий зв'язок між високим рівнем холестерину та виникненням головного болю.", "cholesterolEyes": "Дізнайтеся, як високий рівень холестерину може проявлятися у ваших очах та на які симптоми слід звернути увагу.", "diabetesHeadaches": "Дослідіть взаємозв'язок між діабетом та виникненням головного болю в повсякденному житті.", "chestPainDrinking": "Дослідіть причини болю в грудях після вживання певних напоїв.", "stressDizziness": "Заглибтеся в те, як стрес може впливати на ваш баланс та загальне самопочуття, призводячи до запаморочення.", "bulimiaFace": "Зрозумійте фізичні ознаки булімії, включаючи вплив на зовнішність обличчя.", "kneeTwitch": "Дослідіть потенційні причини мимовільного посмикування коліна та його зв'язок зі стресом або втомою.", "noseTwitching": "Дізнайтеся про можливі причини посмикування носа та його зв'язок з тривогою або іншими факторами.", "piriformisVsSciatica": "Порівняйте симптоми синдрому грушоподібного м'яза та ішіасу, щоб краще зрозуміти свій стан.", "shoulderBladePinched": "Відкрийте для себе ефективні методи зняття защемлення нерва в лопатці та відновлення рухливості.", "shoulderPinched": "Дізнайтеся про прості вправи та розтяжки для полегшення стиснення нерва в області плеча.", "meniscusTorn": "Дослідіть природні методи та вправи для підтримки загоєння розриву меніска.", "hydrateQuickly": "Дізнайтеся про швидкі та ефективні способи регідратації та підтримки оптимального водного балансу організму.", "periodConstipation": "Зрозумійте причини запорів під час менструації та дізнайтеся про природні засоби.", "acneScars": "Відкрийте для себе природні засоби та поради щодо догляду за шкірою, щоб швидко зменшити появу рубців від вугрів.", "perimenopausePregnancy": "Дізнайтеся про перименопаузу, питання фертильності та чого очікувати на цьому етапі життя."}}}