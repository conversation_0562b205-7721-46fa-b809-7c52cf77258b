import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { ChevronRight } from "lucide-react-native";
import Logo from '@/components/common/logos/Logo';
import { scale, verticalScale } from 'react-native-size-matters';
import { SvgUri } from 'react-native-svg';
import { setPreAuthOnboardingComplete } from '@/utils/onboarding/onboarding';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');


const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: "#1d7764",
    },
    contentContainer: {
      flex: 1,
      // paddingHorizontal: 20,
      // borderWidth: 1,
      // borderColor: "red",
    },
    image: {
      width: Platform.OS === "ios" ? width * 0.95 : width * 0.95,
      position: "absolute",
      top: 123.25,
      left: Platform.OS === "ios" ? -6 : -10,
      right: 20,
      height: Platform.OS === "ios" ? height * 0.75 : height * 0.78,
    },
    textContainer: {
      position: "absolute",
      top: Platform.OS === "ios" ? 64 : 20,
      left:  Platform.OS === "ios" ? 24 : 24,
    },
    title: {
      fontSize: Platform.OS === "ios" ? 32 : 22,
      fontWeight: "600",
      color: theme.colors.secondary[50],
      // textAlign: "center",
      marginTop: 8,
      alignItems: "center",
      justifyContent: "center",
    },
    description: {
      fontSize: 16,
      color: colors.gray[700],
      textAlign: "center",
      lineHeight: 24,
    },
    buttonContainer: {
      paddingHorizontal: 20,
      paddingBottom: 95,
      alignItems: "center",
    },
    button: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: theme.radii["4xl"],
      // width: Platform.OS === "ios" ? 192 : 192,
      paddingHorizontal: 10,
      // height: 56,
      paddingVertical:8,
      justifyContent: "center", // Center content vertically
      alignItems: "center",
      // borderWidth:1,
      // borderColor:'red'
    },
    buttonContent: {
      flexDirection: "row", // Arrange items horizontally
      alignItems: "center", // Center items vertically
      justifyContent: "space-between", // Space between text and icon
    },
    buttonText: {
      color: colors.black,
      fontSize: Platform.OS === "ios" ? 16 : 14,
      fontWeight: "400",
    },
    iconContainer: {
      width: 40, // Increased size for better visibility
      height: 40, // Increased size for better visibility
      borderRadius: 20, // Half of width/height for perfect circle
      backgroundColor: "#206E55",
      alignItems: "center",
      justifyContent: "center",
      marginLeft: 12, // Space between text and icon
    },

    topRightLogo: {
      width: scale(100),
      height: verticalScale(40),
      maxWidth: 100,
      maxHeight: 40,
    },
    iconLogo: {
      width: 40,
      height: 40, // Changed to match width for perfect square
      backgroundColor: theme.colors.secondary[50],
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 8,
    },
    iconText: {
      textAlign: "center",
      color: "#206E55",
      fontSize: 32,
      fontWeight: "bold",
      lineHeight: 32, // Added to match fontSize for vertical alignment
      includeFontPadding: false, // Removes default Android padding
      padding: 0, // Removes any padding
      margin: 0, // Removes any margin
    },
  });

  const MemoizedSvgLogo = React.memo(() => (
    <Image
      width={40}
      height={40}
      source={{
        uri:"https://augustbuckets.blob.core.windows.net/mobile-app-assets/favicon.png",
      }}
      style={{ borderRadius: 12 }}
    />
  ));
  

export default function PreAuthOnboarding() {
  const { t } = useTranslation();
  const handleContinue = async () => {
    // Mark pre-auth onboarding as complete
    await setPreAuthOnboardingComplete(true);
    // Navigate to authentication screen
    router.replace('/auth/phone');
  };

  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />

      <View style={styles.textContainer}>
        <MemoizedSvgLogo/>
        <Text style={styles.title}>{t('onboarding.preAuth.welcome.title')}</Text>
      </View>

      <View style={styles.contentContainer}>
        <Image
          source={require("@/assets/images/welcome.png")}
          style={styles.image}
          resizeMode="contain"
        />
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={handleContinue}
          activeOpacity={0.7}
        >
          <View style={styles.buttonContent}>
            <Text style={styles.buttonText}>{t('onboarding.preAuth.welcome.buttonText')}</Text>
            <TouchableOpacity
              onPress={handleContinue}
              style={styles.iconContainer}
            >
              <ChevronRight size={20} color={theme.colors.secondary[50]} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}













