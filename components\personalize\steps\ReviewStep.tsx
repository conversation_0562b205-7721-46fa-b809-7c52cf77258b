import type React from "react"
import { useState, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Animated } from "react-native"
import { CheckCircle2, ChevronDown, ChevronRight, PenLine } from "lucide-react-native"
import { useTranslation } from "react-i18next";
import { colors } from "@/constants/colors"
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils"
import { useFormDataStore } from "@/store/formDataStore"
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface ReviewStepProps {
  stepIndex: number
  isActive?: boolean
  onSubmitSuccess: () => void
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: scale(16),
      paddingBottom: verticalScale(40),
    },
    iconContainer: {
      width: scale(64),
      height: scale(64),
      borderRadius: scale(theme.radii["4xl"]),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    reviewSection: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: scale(theme.radii.md),
      marginBottom: verticalScale(12),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
      overflow: "hidden",
    },
    reviewHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: scale(10),
    },
    reviewTitle: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      color: theme.colors.gray[800],
    },
    reviewDetails: {
      padding: scale(16),
      paddingTop: verticalScale(8),
      borderTopWidth: 1,
      borderTopColor: theme.colors.gray[200],
    },
    reviewItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: verticalScale(2),
    },
    reviewItemGroup: {
      marginBottom: verticalScale(16),
      paddingBottom: verticalScale(8),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    reviewGroupTitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
    },
    reviewLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
    },
    reviewValue: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      color: theme.colors.gray[800],
      maxWidth: "60%",
      textAlign: "right",
    },
    noDataText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray.main,
      fontStyle: "italic",
    },
    errorContainer: {
      padding: scale(16),
      backgroundColor: "#FFEBEE",
      borderRadius: scale(theme.radii.md),
      marginBottom: verticalScale(16),
    },
    errorText: {
      color: "#D32F2F",
      fontSize: moderateScale(theme.fontSize.sm),
    },
    submitButton: {
      backgroundColor: theme.colors.primary.main,
      borderRadius: scale(theme.radii.md),
      padding: scale(16),
      alignItems: "center",
      marginTop: verticalScale(16),
    },
    submitButtonText: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
    },
    progress: {
      height: verticalScale(4),
      width: "90%",
      backgroundColor: theme.colors.primary.main,
      borderRadius: scale(theme.radii.xs),
    },
    penIcon: {
      width: scale(35),
      height: scale(35),
      backgroundColor: theme.colors.gray[100],
      borderRadius: scale(theme.radii["3xl"]),
      justifyContent: "center",
      alignItems: "center",
    },
  });

export const ReviewStep: React.FC<ReviewStepProps> = ({ stepIndex, isActive = false, onSubmitSuccess }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { formData, submitForm, isLoading, error, getSectionProgress, updateCurrentStep } = useFormDataStore()
  const [expandedSection, setExpandedSection] = useState<string | null>(null)
  const fields = ["basicInfo", "lifestyle","allergies","medications","conditions","reproHealth"];

  const toggleSection = useCallback((section: string) => {
    setExpandedSection(prev => prev === section ? null : section);
    trackUserInteraction("Review Section Toggle", { section });
  }, [trackUserInteraction]);
  
  const getTotalProgress = () => {
    let score = 0;
    fields.forEach((field) => {
      score += getSectionProgress(field);
    });
    return score;
  }
  
  const handleSubmit = useCallback(async () => {
    trackUserInteraction("Final Submit Button Pressed");
    const success = await submitForm();
    if (success) {
      onSubmitSuccess();
    }
  }, [trackUserInteraction, submitForm, onSubmitSuccess]);
  

  if (!isActive) return null

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <CheckCircle2 size={32} color={colors.primary} />
      </View>

      <Text style={styles.title}>{t('review.title')}</Text>
      <Text style={styles.subtitle}>{t('review.subtitle')}</Text>

      {/* Basic Info Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("basic")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("basicInfo") * 100}%` }]} />
        <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row",gap:2}}>
            {expandedSection==="basic" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.basicInfo')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(0)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "basic" && (
          <View style={styles.reviewDetails}>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.age')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.age || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.sex')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.sex || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.height')}</Text>
              <Text style={styles.reviewValue}>
                {formData.basicInfo.height ? t('review.units.cm', { height: formData.basicInfo.height }) : t('review.notProvided')}
              </Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.weight')}</Text>
              <Text style={styles.reviewValue}>
                {formData.basicInfo.weight ? t('review.units.kg', { weight: formData.basicInfo.weight }) : t('review.notProvided')}
              </Text>
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Lifestyle Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("lifestyle")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("lifestyle") * 100}%` }]} />
        <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row"}}>
          {expandedSection==="lifestyle" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.lifestyle')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(1)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "lifestyle" && (
          <View style={styles.reviewDetails}>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.diet')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.diet || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.exercise')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.exercise || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.drinking')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.drinking || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.smoking')}</Text>
              <Text style={styles.reviewValue}>{formData.basicInfo.smoking || t('review.notProvided')}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.sleep')}</Text>
              <Text style={styles.reviewValue}>{t('review.values.sleepHours', { sleep: formData.basicInfo.sleep })}</Text>
            </View>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.hydration')}</Text>
              <Text
                style={styles.reviewValue}
              >{t('review.values.hydration', { hydration: formData.basicInfo.hydration, liters: (formData.basicInfo.hydration * 0.25).toFixed(1) })}</Text>
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Allergies Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("allergies")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("allergies") * 100}%` }]} />
      <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row"}}>
          {expandedSection==="allergies" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.allergies')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(2)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "allergies" && (
          <View style={styles.reviewDetails}>
            {formData.allergies.length > 0 && formData.allergies[0].name ? (
              formData.allergies.map((allergy, index) => (
                <View key={`allergy-${index}`} style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>{t('review.fields.allergyIndex', { index: index + 1 })}</Text>
                  <Text
                    style={styles.reviewValue}
                  >{`${allergy.name}${allergy.severity ? ` (${allergy.severity})` : ""}`}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.noDataText}>{t('review.noData.allergies')}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>

      {/* Medications Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("medications")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("medications") * 100}%` }]} />
        <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row"}}>
          {expandedSection==="medications" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.medications')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(3)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "medications" && (
          <View style={styles.reviewDetails}>
            {formData.medications.length > 0 && formData.medications[0].name ? (
              formData.medications.map((medication, index) => (
                <View key={`medication-${index}`} style={styles.reviewItemGroup}>
                  <Text style={styles.reviewGroupTitle}>{medication.name}</Text>
                  {medication.dose && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.dose')}</Text>
                      <Text style={styles.reviewValue}>{`${medication.dose} ${medication.doseUnit}`}</Text>
                    </View>
                  )}
                  {medication.frequency && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.frequency')}</Text>
                      <Text style={styles.reviewValue}>{`${medication.frequency} ${medication.frequencyUnit}`}</Text>
                    </View>
                  )}
                  {medication.type && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.type')}</Text>
                      <Text style={styles.reviewValue}>{medication.type}</Text>
                    </View>
                  )}
                </View>
              ))
            ) : (
              <Text style={styles.noDataText}>{t('review.noData.medications')}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>

      {/* Conditions Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("conditions")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("conditions") * 100}%` }]} />
        <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row"}}>
          {expandedSection==="conditions" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.conditions')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(4)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "conditions" && (
          <View style={styles.reviewDetails}>
            {formData.conditions.length > 0 && formData.conditions[0].name ? (
              formData.conditions.map((condition, index) => (
                <View key={`condition-${index}`} style={styles.reviewItemGroup}>
                  <Text style={styles.reviewGroupTitle}>{condition.name}</Text>
                  {condition.since && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.since')}</Text>
                      <Text style={styles.reviewValue}>{condition.since ? new Date(condition.since).toLocaleDateString() : ""}</Text>
                    </View>
                  )}
                  {condition.isCurrent !== null && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.currentlyActive')}</Text>
                      <Text style={styles.reviewValue}>{condition.isCurrent ? t('common.yes') : t('common.no')}</Text>
                    </View>
                  )}
                  {condition.isMedicated !== null && (
                    <View style={styles.reviewItem}>
                      <Text style={styles.reviewLabel}>{t('review.fields.takingMedication')}</Text>
                      <Text style={styles.reviewValue}>{condition.isMedicated ? t('common.yes') : t('common.no')}</Text>
                    </View>
                  )}
                </View>
              ))
            ) : (
              <Text style={styles.noDataText}>{t('review.noData.conditions')}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>

      {/* Reproductive Health Section */}
      <TouchableOpacity style={styles.reviewSection} onPress={() => toggleSection("reproductive")}>
      <Animated.View style={[styles.progress, { width: `${getSectionProgress("reproHealth") * 100}%` }]} />
        <View style={styles.reviewHeader}>
          <View style={{display:"flex",flexDirection:"row"}}>
          {expandedSection==="reproductive" ?
            <ChevronDown size={26} color={colors.gray[900 ] } />
            :
            <ChevronRight size={26} color={colors.gray[900 ] } />
            }
            <Text style={styles.reviewTitle}>{t('review.sections.reproductive')}</Text>
          </View>
          <TouchableOpacity onPress={()=>{updateCurrentStep(5)}} style={styles.penIcon}>
            <PenLine size={20} color={colors.gray[900]} />
          </TouchableOpacity>
        </View>

        {expandedSection === "reproductive" && (
          <View style={styles.reviewDetails}>
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.hasMenstruated')}</Text>
              <Text style={styles.reviewValue}>{formData.reproHealth.hasMenstruated ? t('common.yes') : t('common.no')}</Text>
            </View>

            {formData.reproHealth.hasMenstruated && (
              <View style={styles.reviewItemGroup}>
                <Text style={styles.reviewGroupTitle}>{t('review.sections.menstruationDetails')}</Text>
                {formData.reproHealth.menstruation.regularity && (
                  <View style={styles.reviewItem}>
                    <Text style={styles.reviewLabel}>{t('review.fields.regularity')}</Text>
                    <Text style={styles.reviewValue}>{formData.reproHealth.menstruation.regularity}</Text>
                  </View>
                )}
                {formData.reproHealth.menstruation.cycleLength && (
                  <View style={styles.reviewItem}>
                    <Text style={styles.reviewLabel}>{t('review.fields.cycleLength')}</Text>
                    <Text style={styles.reviewValue}>{t('review.values.cycleDays', { cycleLength: formData.reproHealth.menstruation.cycleLength })}</Text>
                  </View>
                )}
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>{t('review.fields.flowDays')}</Text>
                  <Text style={styles.reviewValue}>{formData.reproHealth.menstruation.flowDays}</Text>
                </View>
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>{t('review.fields.padsPerDay')}</Text>
                  <Text style={styles.reviewValue}>{formData.reproHealth.menstruation.padsPerDay}</Text>
                </View>
              </View>
            )}

            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>{t('review.fields.hasChildbirth')}</Text>
              <Text style={styles.reviewValue}>{formData.reproHealth.hasChildbirth ? t('common.yes') : t('common.no')}</Text>
            </View>

            {formData.reproHealth.hasChildbirth && (
              <View style={styles.reviewItemGroup}>
                <Text style={styles.reviewGroupTitle}>{t('review.sections.childbirthDetails')}</Text>
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>{t('review.fields.children')}</Text>
                  <Text style={styles.reviewValue}>{formData.reproHealth.childbirth.children}</Text>
                </View>
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>{t('review.fields.pregnancies')}</Text>
                  <Text style={styles.reviewValue}>{formData.reproHealth.childbirth.pregnancies}</Text>
                </View>
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {(getTotalProgress()===(fields.length))&&<TouchableOpacity style={styles.submitButton} onPress={handleSubmit} disabled={isLoading}>
        {isLoading ? (
          <ActivityIndicator size="small" color={colors.white} />
        ) : (
          <Text style={styles.submitButtonText}>{t('review.submitButton')}</Text>
        )}
      </TouchableOpacity>}
    </ScrollView>
  )
}

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   content: {
//     padding: scale(16),
//     paddingBottom: verticalScale(40),
//   },
//   iconContainer: {
//     width: scale(64),
//     height: scale(64),
//     borderRadius: scale(32),
//     backgroundColor: theme.colors.primary.main + "20",
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: moderateScale(22),
//     fontWeight: "700",
//     color: theme.colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: moderateScale(theme.fontSize.sm),
//     color: theme.colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   reviewSection: {
//     backgroundColor: theme.colors.secondary[50],
//     borderRadius: scale(theme.radii.md),
//     marginBottom: verticalScale(12),
//     borderWidth: 1,
//     borderColor: colors.gray[200],
//     overflow: "hidden",
//   },
//   reviewHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//     padding: scale(10),
//   },
//   reviewTitle: {
//     fontSize: moderateScale(16),
//     fontWeight: "600",
//     color: colors.gray[800],
//   },
//   reviewDetails: {
//     padding: scale(16),
//     paddingTop: verticalScale(8),
//     borderTopWidth: 1,
//     borderTopColor: colors.gray[200],
//   },
//   reviewItem: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     marginBottom: verticalScale(2),
//   },
//   reviewItemGroup: {
//     marginBottom: verticalScale(16),
//     paddingBottom: verticalScale(8),
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   reviewGroupTitle: {
//     fontSize: moderateScale(15),
//     fontWeight: "600",
//     color: colors.gray[800],
//     marginBottom: verticalScale(8),
//   },
//   reviewLabel: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//   },
//   reviewValue: {
//     fontSize: moderateScale(14),
//     fontWeight: "500",
//     color: colors.gray[800],
//     maxWidth: "60%",
//     textAlign: "right",
//   },
//   noDataText: {
//     fontSize: moderateScale(14),
//     color: colors.gray[500],
//     fontStyle: "italic",
//   },
//   errorContainer: {
//     padding: scale(16),
//     backgroundColor: "#FFEBEE",
//     borderRadius: scale(12),
//     marginBottom: verticalScale(16),
//   },
//   errorText: {
//     color: "#D32F2F",
//     fontSize: moderateScale(14),
//   },
//   submitButton: {
//     backgroundColor: colors.primary,
//     borderRadius: scale(12),
//     padding: scale(16),
//     alignItems: "center",
//     marginTop: verticalScale(16),
//   },
//   submitButtonText: {
//     color: theme.colors.secondary[50],
//     fontSize: moderateScale(16),
//     fontWeight: "600",
//   },
//   progress: {
//     height: verticalScale(4),
//     width: "90%",
//     backgroundColor: colors.primary,
//     borderRadius: scale(3),
//   },
//   penIcon: {
//     width: scale(35),
//     height: scale(35),
//     backgroundColor: colors.gray[100],
//     borderRadius: scale(30),
//     justifyContent: "center",
//     alignItems: "center",
//   },
// });