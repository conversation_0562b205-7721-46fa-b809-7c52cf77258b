import React from "react"; 
import MediaPhotos from "./MediaPhotos"; 
import { Text,StyleSheet,View } from "react-native";
import { colors } from "@/constants/colors";


function MediaGalleryScreen({
  reportsData,
  isLoadingReports,
  reportsError,
  title,
  subtitle,
  onFoodPress,
  type,
}: any) {
  const reports = reportsData.filter((report: any) => report.type === type);
  
  return (
    <>
      <View style={styles.mealsContainer}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <Text style={styles.sectionSubtitle}>{subtitle}</Text>
        {!type || type === "" || type === "ALL" ? (
          <MediaPhotos
            onFoodPress={onFoodPress}
            reportsData={reportsData}
            isLoadingReports={isLoadingReports}
            reportsError={reportsError}
          />
        ) : (
          <MediaPhotos
            onFoodPress={onFoodPress}
            reportsData={reports}
            isLoadingReports={isLoadingReports}
            reportsError={reportsError}
          />
        )}
      </View>
    </>
  );
}
const styles = StyleSheet.create({
  mealsContainer: {
    marginVertical: 16,
    width: "100%", // Ensure it takes full width
    justifyContent: "center",

    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
    // marginLeft: 4,
    marginTop: 8,
    color: colors.black,
    // borderWidth: 1,
    // borderColor: "red",
    textAlign: "center",
  },
  sectionSubtitle: {
    fontSize: 16,
    textAlign: "center",
    color: colors.gray[600],
    marginBottom: 26,
    // marginLeft: 4,
  },
});

export default MediaGalleryScreen;
