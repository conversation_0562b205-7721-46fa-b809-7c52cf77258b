import logger from './logger';

// Navigation event type
type NavigationEvent = {
  route: string;
  action: 'enter' | 'leave';
  timestamp: number;
};

// Configuration options
interface NavigationLoggerConfig {
  enabled: boolean;
  maxHistoryLength: number;
  logFullPathOnEveryNavigation: boolean;
}

class NavigationLogger {
  private history: NavigationEvent[] = [];
  private currentRoute: string | null = null;
  private config: NavigationLoggerConfig;

  constructor(config?: Partial<NavigationLoggerConfig>) {
    this.config = {
      enabled: true,
      maxHistoryLength: 100,
      logFullPathOnEveryNavigation: true,
      ...config
    };
  }

  // Log entering a route
  logEnterRoute(route: string) {
    if (!this.config.enabled) return;
    
    const timestamp = Date.now();
    const event: NavigationEvent = { route, action: 'enter', timestamp };
    
    this.currentRoute = route;
    this.addToHistory(event);
    
    logger.info(`NAVIGATION: Entered ${route} at ${new Date(timestamp).toLocaleTimeString()}`);
    
    if (this.config.logFullPathOnEveryNavigation) {
      this.logCurrentPath();
    }
  }

  // Log leaving a route
  logLeaveRoute(route: string) {
    if (!this.config.enabled) return;
    
    const timestamp = Date.now();
    const event: NavigationEvent = { route, action: 'leave', timestamp };
    
    this.addToHistory(event);
    
    logger.info(`NAVIGATION: Left ${route} at ${new Date(timestamp).toLocaleTimeString()}`);
  }

  // Log the current navigation path
  logCurrentPath() {
    if (!this.config.enabled) return;
    
    const path = this.getNavigationPath();
    logger.info(`NAVIGATION PATH: ${path}`);
  }

  // Get the current navigation path as a string
  getNavigationPath(): string {
    const enterEvents = this.history
      .filter(event => event.action === 'enter')
      .map(event => event.route);
    
    return enterEvents.join(' → ');
  }

  // Add an event to history and maintain max length
  private addToHistory(event: NavigationEvent) {
    this.history.push(event);
    
    // Trim history if it exceeds max length
    if (this.history.length > this.config.maxHistoryLength) {
      this.history = this.history.slice(this.history.length - this.config.maxHistoryLength);
    }
  }

  // Get the current navigation history
  getHistory() {
    return [...this.history];
  }

  // Get the current route
  getCurrentRoute() {
    return this.currentRoute;
  }

  // Clear the navigation history
  clearHistory() {
    this.history = [];
    logger.info('NAVIGATION: History cleared');
  }

  // Enable or disable logging
  setEnabled(enabled: boolean) {
    this.config.enabled = enabled;
    logger.info(`NAVIGATION: Logging ${enabled ? 'enabled' : 'disabled'}`);
  }
}

// Export a singleton instance
export const navigationLogger = new NavigationLogger();
