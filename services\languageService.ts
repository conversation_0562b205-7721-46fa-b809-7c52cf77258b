import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';
import i18n from '@/app/i18n';
import logger from '@/utils/logger/logger';

const LANGUAGE_KEY = 'user_selected_language';

export interface Language {
  code: string;
  name: string;
  nativeName: string;
}

// Supported languages with their native names
export const SUPPORTED_LANGUAGES: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिंदी' },
  { code: 'mr', name: 'Marathi', nativeName: 'मराठी' },
  { code: 'pa', name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ' },
  { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்' },
  { code: 'te', name: 'Telugu', nativeName: 'తెలుగు' },
  { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ' },
  { code: 'gu', name: 'Gujarati', nativeName: 'ગુજરાતી' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা' },
  { code: 'ur', name: 'Urdu', nativeName: 'اردو' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'pt-BR', name: 'Portuguese (Brazil)', nativeName: 'Português (Brasil)' },
  { code: 'pt-PT', name: 'Portuguese (Portugal)', nativeName: 'Português (Portugal)' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
  { code: 'zh-TW', name: 'Chinese (Traditional)', nativeName: '繁體中文' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
  { code: 'id', name: 'Indonesian', nativeName: 'Bahasa Indonesia' },
  { code: 'fil', name: 'Filipino', nativeName: 'Filipino' },
  { code: 'tr', name: 'Turkish', nativeName: 'Türkçe' },
  { code: 'ms', name: 'Malay', nativeName: 'Bahasa Melayu' },
  { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  { code: 'th', name: 'Thai', nativeName: 'ไทย' },
  { code: 'he', name: 'Hebrew', nativeName: 'עברית' },
  { code: 'pl', name: 'Polish', nativeName: 'Polski' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  { code: 'uk', name: 'Ukrainian', nativeName: 'Українська' },
  { code: 'el', name: 'Greek', nativeName: 'Ελληνικά' },
  { code: 'sv', name: 'Swedish', nativeName: 'Svenska' },
  { code: 'cs', name: 'Czech', nativeName: 'Čeština' },
  { code: 'hu', name: 'Hungarian', nativeName: 'Magyar' },
  { code: 'ro', name: 'Romanian', nativeName: 'Română' },
];

/**
 * Language Service
 * Handles language preference storage and management
 */
export const languageService = {
  /**
   * Get the device's preferred language
   */
  getDeviceLanguage: (): string => {
    const locales = Localization.getLocales();
    const deviceLanguage = locales?.[0]?.languageCode || 'en';
    
    // Check if device language is supported, otherwise fallback to English
    const isSupported = SUPPORTED_LANGUAGES.some(lang => lang.code === deviceLanguage);
    return isSupported ? deviceLanguage : 'en';
  },

  /**
   * Get stored language preference
   */
  getStoredLanguage: async (): Promise<string | null> => {
    try {
      return await AsyncStorage.getItem(LANGUAGE_KEY);
    } catch (error) {
      logger.error('Error retrieving stored language:', error);
      return null;
    }
  },

  /**
   * Store language preference
   */
  setStoredLanguage: async (languageCode: string): Promise<void> => {
    try {
      await AsyncStorage.setItem(LANGUAGE_KEY, languageCode);
    } catch (error) {
      logger.error('Error storing language preference:', error);
      throw error;
    }
  },

  /**
   * Remove stored language preference (revert to device language)
   */
  removeStoredLanguage: async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(LANGUAGE_KEY);
    } catch (error) {
      logger.error('Error removing stored language:', error);
      throw error;
    }
  },

  /**
   * Get the current active language (stored > device > fallback)
   */
  getCurrentLanguage: async (): Promise<string> => {
    const storedLanguage = await languageService.getStoredLanguage();
    if (storedLanguage) {
      return storedLanguage;
    }
    return languageService.getDeviceLanguage();
  },

  /**
   * Change the app language
   */
  changeLanguage: async (languageCode: string): Promise<void> => {
    try {
      // Store the language preference
      await languageService.setStoredLanguage(languageCode);
      
      // Change i18n language
      await i18n.changeLanguage(languageCode);
      
      logger.info(`Language changed to: ${languageCode}`);
    } catch (error) {
      logger.error('Error changing language:', error);
      throw error;
    }
  },

  /**
   * Reset to device language
   */
  resetToDeviceLanguage: async (): Promise<void> => {
    try {
      // Remove stored preference
      await languageService.removeStoredLanguage();
      
      // Get device language and apply it
      const deviceLanguage = languageService.getDeviceLanguage();
      await i18n.changeLanguage(deviceLanguage);
      
      logger.info(`Language reset to device language: ${deviceLanguage}`);
    } catch (error) {
      logger.error('Error resetting to device language:', error);
      throw error;
    }
  },

  /**
   * Get language info by code
   */
  getLanguageInfo: (code: string): Language | undefined => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === code);
  },

  /**
   * Check if user has manually selected a language
   */
  isLanguageManuallySelected: async (): Promise<boolean> => {
    const storedLanguage = await languageService.getStoredLanguage();
    return storedLanguage !== null;
  },
};
