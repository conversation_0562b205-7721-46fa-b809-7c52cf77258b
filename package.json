{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "bun i && bun expo start", "start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@azure/web-pubsub-client": "^1.0.2", "@expo/vector-icons": "^14.0.2", "@gluestack-ui/themed": "^1.1.71", "@microsoft/react-native-clarity": "^4.2.0", "@quidone/react-native-wheel-picker": "^1.4.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "^4.5.5", "@react-native-picker/picker": "2.9.0", "@react-navigation/native": "^7.0.0", "@react-navigation/stack": "^7.2.3", "@simform_solutions/react-native-audio-waveform": "^2.1.5", "@types/uuid": "^10.0.0", "axios": "^1.8.3", "expo": "~52.0.36", "expo-audio": "~0.3.5", "expo-av": "~15.0.2", "expo-blur": "~14.0.1", "expo-build-properties": "~0.13.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.2", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.0", "expo-image": "~2.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-localization": "~16.0.1", "expo-location": "~18.0.7", "expo-modules-core": "~2.2.3", "expo-navigation-bar": "~4.0.9", "expo-network-security-config": "^1.0.2", "expo-notifications": "~0.29.14", "expo-router": "~4.0.17", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.1", "i18next": "^25.2.1", "i18next-http-backend": "^3.0.2", "libphonenumber-js": "^1.12.8", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.475.0", "mixpanel-react-native": "^3.0.8", "nativewind": "^4.1.23", "node-fetch": "^3.3.2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.5.3", "react-native": "0.76.7", "react-native-appsflyer": "^6.16.2", "react-native-calendars": "^1.1310.0", "react-native-chart-kit": "^6.12.0", "react-native-deck-swiper": "^2.0.17", "react-native-device-info": "^14.0.4", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-logs": "^5.3.0", "react-native-markdown-display": "^7.0.2", "react-native-normalize": "^1.0.1", "react-native-pdf": "^6.7.7", "react-native-remote-svg": "^2.0.11", "react-native-responsive-fontsize": "^0.5.1", "react-native-root-toast": "^4.0.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-size-matters": "^0.4.2", "react-native-skeleton-content": "^1.0.28", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "15.8.0", "react-native-view-shot": "~4.0.3", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~18.3.12", "typescript": "~5.3.3"}, "private": true}