import { create } from "zustand"
import { trackOperation, OperationStatus } from "@/utils/mixpanel/mixpanel-utils"
import { useUserDataStore } from "@/store/userDataStore"
import logger from '@/utils/logger/logger';
import { getMedicalData } from '@/services/medicalService';

// Define the form data structure
interface FormData {
  basicInfo: {
    age: string
    sex: string
    height: string
    weight: string
    diet: string
    exercise: string
    drinking: string
    smoking: string
    sleep: number
    hydration: number
  }
  allergies: Array<{
    name: string
    severity: string
  }>
  medications: Array<{
    name: string
    dose: string
    doseUnit: string
    frequency: string
    frequencyUnit: string
    startDate: string
    type: string
  }>
  conditions: Array<{
    name: string
    since: string
    isCurrent: boolean | null
    isMedicated: boolean | null
  }>
  reproHealth: {
    hasMenstruated: boolean
    hasChildbirth: boolean
    menstruation: {
      regularity: string
      cycleLength: string
      flowDays: number
      padsPerDay: number
      symptoms: string
    }
    childbirth: {
      children: number
      pregnancies: number
      complications: string
    }
  }
}

// Default form data
export const defaultFormData: FormData = {
  basicInfo: {
    age: "",
    sex: "",
    height: "",
    weight: "",
    diet: "",
    exercise: "",
    drinking: "",
    smoking: "",
    sleep: 8,
    hydration: 8,
  },
  allergies: [{ name: "", severity: "" }],
  medications: [
    { name: "", dose: "", doseUnit: "mg", frequency: "", frequencyUnit: "per day", startDate: "", type: "" },
  ],
  conditions: [{ name: "", since: "", isCurrent: null, isMedicated: null }],
  reproHealth: {
    hasMenstruated: false,
    hasChildbirth: false,
    menstruation: {
      regularity: "",
      cycleLength: "",
      flowDays: 5,
      padsPerDay: 4,
      symptoms: "",
    },
    childbirth: {
      children: 0,
      pregnancies: 0,
      complications: "",
    },
  },
}

type FormDataState = {
  currentStep: number
  formData: FormData
  originalData: any
  isLoading: boolean
  error: string | null

  // Actions
  updateCurrentStep: (step: number) => void
  updateBasicInfo: (data: Partial<FormData["basicInfo"]>) => void
  updateLifestyle: (data: Partial<FormData["basicInfo"]>) => void
  updateAllergies: (allergies: FormData["allergies"]) => void
  updateMedications: (medications: FormData["medications"]) => void
  updateConditions: (conditions: FormData["conditions"]) => void
  updateReproHealth: (data: Partial<FormData["reproHealth"]>) => void
  submitForm: () => Promise<boolean>
  getSectionProgress: (sectionName: string) => number
  setOriginalData: (data: any) => void
  resetForm: () => void
  loadFormData: () => void
}

// Remove the persist wrapper - just use create directly
export const useFormDataStore = create<FormDataState>((set, get) => ({
  currentStep: 0,
  formData: defaultFormData,
  originalData: null,
  isLoading: false,
  error: null,

  updateCurrentStep: (step) => set({ currentStep: step }),

  updateBasicInfo: (data) =>
    set((state) => ({
      formData: {
        ...state.formData,
        basicInfo: { ...state.formData.basicInfo, ...data },
      },
    })),

  updateLifestyle: (data) =>
    {
      set((state) => ({
      formData: {
        ...state.formData,
        basicInfo: { ...state.formData.basicInfo, ...data },
      },
    }))
  },

  updateAllergies: (allergies) =>
    set((state) => ({
      formData: {
        ...state.formData,
        allergies,
      },
    })),

  updateMedications: (medications) =>
    set((state) => ({
      formData: {
        ...state.formData,
        medications,
      },
    })),

  updateConditions: (conditions) =>
    set((state) => ({
      formData: {
        ...state.formData,
        conditions,
      },
    })),

  updateReproHealth: (data) =>
    set((state) => ({
      formData: {
        ...state.formData,
        reproHealth: {
          ...state.formData.reproHealth,
          ...data,
          menstruation: {
            ...state.formData.reproHealth.menstruation,
            ...(data.menstruation || {}),
          },
          childbirth: {
            ...state.formData.reproHealth.childbirth,
            ...(data.childbirth || {}),
          },
        },
      },
    })),

  resetForm: () => set({
    currentStep: 0,
    formData: defaultFormData,
    originalData: null,
    isLoading: false,
    error: null,
  }),

  submitForm: async () => {
    const state = get()
    set({ isLoading: true, error: null })
    trackOperation("Medical Form Submit", OperationStatus.INITIATED)

    try {
      // Format the current form data for API
      const currentFormData = {
        patient_profile: {
          age: state.formData.basicInfo.age,
          sex: state.formData.basicInfo.sex ? state.formData.basicInfo.sex.toLowerCase() : "",
          height_cm: state.formData.basicInfo.height,
          weight_kg: state.formData.basicInfo.weight,
        },
        social_history: {
          diet: state.formData.basicInfo.diet,
          exercise:
            state.formData.basicInfo.exercise === "Yes"
              ? "yes"
              : state.formData.basicInfo.exercise === "No"
                ? "no"
                : state.formData.basicInfo.exercise === "Sometimes"
                  ? "sometimes"
                  : "",
          drinking:
            state.formData.basicInfo.drinking === "Yes"
              ? "yes"
              : state.formData.basicInfo.drinking === "No"
                ? "no"
                : state.formData.basicInfo.drinking === "Sometimes"
                  ? "sometimes"
                  : "",
          smoking:
            state.formData.basicInfo.smoking === "Yes"
              ? "yes"
              : state.formData.basicInfo.smoking === "No"
                ? "no"
                : state.formData.basicInfo.smoking === "Sometimes"
                  ? "sometimes"
                  : "",
          sleep: state.formData.basicInfo.sleep.toString(),
          hydration: state.formData.basicInfo.hydration.toString(),
        },
        allergies: state.formData.allergies
          .filter((a) => a.name)
          .map((allergy) => ({
            name: allergy.name,
            severity: allergy.severity ? allergy.severity.toLowerCase() : "",
          })),
        medications: state.formData.medications
          .filter((m) => m.name)
          .map((medication) => ({
            name: medication.name,
            dosage_amount: medication.dose,
            dosage_unit: medication.doseUnit,
            dosage_frequency: medication.frequency,
            dosage_frequency_unit: medication.frequencyUnit,
            started_on_timestamp: medication.startDate,
            medication_type:
              medication.type === "Short Term" ? "short_term" : medication.type === "Long Term" ? "long_term" : "",
          })),
        condition_details: state.formData.conditions
          .filter((c) => c.name)
          .map((condition) => ({
            name: condition.name,
            condition_since_timestamp: condition.since,
            current_concern: condition.isCurrent,
            taking_medications: condition.isMedicated,
          })),
        menstrual_history: 
        // state.formData.reproHealth.hasMenstruated
          // ? 
          {
              regularity: state.formData.reproHealth.menstruation.regularity
                ? state.formData.reproHealth.menstruation.regularity.toLowerCase()
                : "",
              duration: state.formData.reproHealth.menstruation.cycleLength,
              volume: state.formData.reproHealth.menstruation.flowDays,
              frequency: state.formData.reproHealth.menstruation.padsPerDay,
              associated_symptoms: state.formData.reproHealth.menstruation.symptoms,
              status : state.formData.reproHealth.hasMenstruated
            },
          // : null,
        obstetric_history: 
        // state.formData.reproHealth.hasChildbirth
        //   ?
           {
              number_of_children: state.formData.reproHealth.childbirth.children,
              number_of_pregnancies: state.formData.reproHealth.childbirth.pregnancies,
              complications: state.formData.reproHealth.childbirth.complications,
              status : state.formData.reproHealth.hasChildbirth
            },
          // : null,
        dietary_preferences: {
          diet_type: state.formData.basicInfo.diet ? state.formData.basicInfo.diet.toLowerCase() : "",
        },
        supplements: state.formData.medications
          .filter((m) => m.name)
          .map((medication) => ({
            name: medication.name,
            dosage: `${medication.dose} ${medication.doseUnit}`,
            frequency: medication.frequency
              ? medication.frequency === "1"
                ? "daily"
                : medication.frequency === "2"
                  ? "twice daily"
                  : `${medication.frequency} times ${medication.frequencyUnit}`
              : "",
          })),
      }

      // logger.info("currentFormData --------------------------------------------------------------------",currentFormData);
      

      // Identify which tables have changes
      const changedTables: string[] = []
      const apiFormData: Record<string, any> = {}
      const originalData = state.originalData

      // Compare patient_profile
      if (
        originalData?.patient_profile &&
        JSON.stringify(currentFormData.patient_profile) !== JSON.stringify(originalData.patient_profile)
      ) {
        changedTables.push("patient_profile")
        apiFormData.patient_profile = currentFormData.patient_profile
      }

      // Compare social_history
      if (
        JSON.stringify(currentFormData?.social_history) !== JSON.stringify(originalData?.social_history)
      ) {
        changedTables.push("social_history")
        apiFormData.social_history = currentFormData.social_history
      }

      // Compare allergies
      if (
        originalData?.allergies &&
        JSON.stringify(currentFormData.allergies) !== JSON.stringify(originalData.allergies)
      ) {
        changedTables.push("allergies")
        apiFormData.allergies = currentFormData.allergies
      }

      // Compare medications
      if (
        originalData?.medications &&
        JSON.stringify(currentFormData.medications) !== JSON.stringify(originalData.medications)
      ) {
        changedTables.push("medications")
        apiFormData.medications = currentFormData.medications
      }

      // Compare condition_details
      if (
        originalData?.condition_details &&
        JSON.stringify(currentFormData.condition_details) !== JSON.stringify(originalData.condition_details)
      ) {
        changedTables.push("condition_details")
        apiFormData.condition_details = currentFormData.condition_details
      }

      // Compare menstrual_history
      if (
        (originalData?.menstrual_history || currentFormData.menstrual_history) &&
        JSON.stringify(currentFormData.menstrual_history) !== JSON.stringify(originalData?.menstrual_history)
      ) {
        changedTables.push("menstrual_history")
        apiFormData.menstrual_history = currentFormData.menstrual_history
      }

      // Compare obstetric_history
      if (
        (originalData?.obstetric_history || currentFormData.obstetric_history) &&
        JSON.stringify(currentFormData.obstetric_history) !== JSON.stringify(originalData?.obstetric_history)
      ) {
        changedTables.push("obstetric_history")
        apiFormData.obstetric_history = currentFormData.obstetric_history
      }

      // Add dietary_preferences and supplements if they exist in the form
      if (currentFormData.dietary_preferences && Object.keys(currentFormData.dietary_preferences).length > 0) {
        changedTables.push("dietary_preferences")
        apiFormData.dietary_preferences = currentFormData.dietary_preferences
      }

      if (currentFormData.supplements && currentFormData.supplements.length > 0) {
        changedTables.push("supplements")
        apiFormData.supplements = currentFormData.supplements
      }

      // Create the request payload
      const requestPayload = {
        formData: apiFormData,
        updateType: "all",
        tablesToUpdate: changedTables,
      }
      // logger.info("apiFormDAta ==========================================================",apiFormData);

      logger.info("Sending update with changed tables:", changedTables)

      // Update via global store
      await useUserDataStore.getState().updateMedical(requestPayload)

      trackOperation("Medical Form Submit", OperationStatus.SUCCESS, {
        changedTables,
        tableCount: changedTables.length,
      })

      set({ isLoading: false })
      return true
    } catch (err : any) {
      if ( err?.response?.status !== 401) {
        logger.error("Error updating medical data:", err)
      
      }
      set({ isLoading: false, error: "Failed to update medical data. Please try again." })

      trackOperation("Medical Form Submit", OperationStatus.FAILURE, {
        error: err instanceof Error ? err.message : String(err),
      })

      return false
    }
  },

  getSectionProgress: (sectionName) => {
    const state = get()
    const clamp = (val: number) => Math.max(0.1, Math.min(1, val)) // ensures the return value is between 0 and 1

    switch (sectionName) {
      case "basicInfo": {
        const fields = ["age", "sex", "height", "weight"]
        const filled = fields.filter((field) =>
          state.formData.basicInfo[field as keyof typeof state.formData.basicInfo]?.toString().trim(),
        ).length
        return clamp(filled / fields.length)
      }

      case "lifestyle": {
        const { diet, exercise, drinking, smoking, sleep, hydration } = state.formData.basicInfo
        const fields = [diet, exercise, drinking, smoking, sleep, hydration]
        const filled = fields.filter((val) => val !== "" && val !== null && val !== undefined).length
        return clamp(filled / fields.length)
      }

      case "allergies": {
        const filled = state.formData.allergies.filter(
          (allergy) => allergy.name.trim() && allergy.severity.trim(),
        ).length
        const total = state.formData.allergies.length || 1
        return clamp(filled / total)
      }

      case "medications": {
        if (state.formData.medications[0]?.name.toLowerCase() === "no medications") {
          return 1
        }
        // return 1;
        const filled = state.formData.medications.filter(
          (med) =>
            med.name.trim() &&
            med.dose.trim() &&
            med.doseUnit.trim() &&
            med.frequency.trim() &&
            med.frequencyUnit.trim() &&
            med.startDate &&
            med.type.trim(),
        ).length
        const total = state.formData.medications.length || 1
        return clamp(filled / total)
      }

      case "conditions": {
        if (state.formData.conditions[0]?.name.toLowerCase() === "no medical conditions") {
          return 1
        }
        const filled = state.formData.conditions.filter(
          (cond) => cond.name.trim() && cond.since && (cond.isCurrent !== null) && (cond.isMedicated !== null),
        ).length
        const total = state.formData.conditions.length || 1
        return clamp(filled / total)
      }

      case "reproHealth": {
        const { hasMenstruated, hasChildbirth, menstruation, childbirth } = state.formData.reproHealth

        const menstruationFields = [
          menstruation.regularity,
          menstruation.cycleLength,
          menstruation.flowDays,
          menstruation.padsPerDay,
        ]
        const childbirthFields = [childbirth.children, childbirth.pregnancies]

        let filled = 0
        let total = 2 // hasMenstruated, hasChildbirth
        if (typeof hasMenstruated === "boolean") filled++
        if (typeof hasChildbirth === "boolean") filled++

        // Only count menstruation fields if menstruated
        if (hasMenstruated) {
          total += menstruationFields.length
          filled += menstruationFields.filter((val) => val !== "" && val !== null && val !== undefined).length
        }

        // Only count childbirth fields if given birth
        if (hasChildbirth) {
          total += childbirthFields.length
          filled += childbirthFields.filter((val) => val !== null && val !== undefined).length
        }

        return clamp(filled / total)
      }

      default:
        return 0
    }
  },

  setOriginalData: (data) => {
    set({ originalData: data })
    let mappedData: Partial<FormData> = {}

    if ((data) && (data === defaultFormData)){
      mappedData = defaultFormData;
    }
    
    // Map the initialData to our formData structure
    else if (data) {
      // Map basic info
      if (data.patient_profile) {
        mappedData.basicInfo = {
          ...get().formData.basicInfo,
          age: data.patient_profile.age || "",
          sex: data.patient_profile.sex
            ? data.patient_profile.sex.charAt(0).toUpperCase() + data.patient_profile.sex.slice(1)
            : "",
          height: data.patient_profile.height_cm || "",
          weight: data.patient_profile.weight_kg || "",
        }
      }

      // Map social history
      if (data.social_history) {
        const social = data.social_history
        mappedData.basicInfo = {
          ...(mappedData.basicInfo || get().formData.basicInfo),
          diet: social.diet || "",
          exercise:
            social.exercise === "no"
              ? "No"
              : social.exercise === "yes"
                ? "Yes"
                : social.exercise === "sometimes"
                  ? "Sometimes"
                  : "",
          drinking:
            social.drinking === "no"
              ? "No"
              : social.drinking === "yes"
                ? "Yes"
                : social.drinking === "sometimes"
                  ? "Sometimes"
                  : "",
          smoking:
            social.smoking === "no"
              ? "No"
              : social.smoking === "yes"
                ? "Yes"
                : social.smoking === "sometimes"
                  ? "Sometimes"
                  : "",
          sleep: social.sleep ? Number.parseFloat(social.sleep) : 8,
          hydration: social.hydration ? Number.parseFloat(social.hydration) : 8,
        }
      }

      // Map allergies
      if (data.allergies && data.allergies.length > 0) {
        mappedData.allergies = data.allergies.map((allergy: any) => ({
          name: allergy.name || "",
          severity: allergy.severity ? allergy.severity.charAt(0).toUpperCase() + allergy.severity.slice(1) : "",
        }))
      }
      // Map medications
      if (data.medications && data.medications.length > 0) {
        mappedData.medications = data.medications.map((medication: any) => ({
          name: medication.name || "",
          dose: medication.dosage_amount || "",
          doseUnit: medication.dosage_unit || "mg",
          frequency: medication.dosage_frequency || "",
          frequencyUnit: medication.dosage_frequency_unit || "per day",
          startDate: medication.started_on_timestamp ? new Date(medication.started_on_timestamp) : "",
          type:
            medication.medication_type === "short_term" ? "Short Term" : medication.medication_type === "long_term" ? "Long Term" : "",
        }))
      }

      // Map conditions
      if (data.condition_details && data.condition_details.length > 0) {
        mappedData.conditions = data.condition_details.map((condition: any) => ({
          name: condition.name || "",
          since: condition.condition_since_timestamp || "",
          isCurrent: condition.current_concern === 'true' ? true : condition.current_concern === 'false' ? false : null,
          isMedicated: condition.taking_medications === 'true' ? true : condition.taking_medications === 'false' ? false : null,
        }))
      }

      // Map reproductive health
      if (data.menstrual_history || data.obstetric_history) {
        mappedData.reproHealth = {
          hasMenstruated: data.menstrual_history?.status ? true : false,
          hasChildbirth: data.obstetric_history?.status ? true : false,
          menstruation: data.menstrual_history
            ? {
                regularity: data.menstrual_history.regularity
                  ? data.menstrual_history.regularity.charAt(0).toUpperCase() +
                    data.menstrual_history.regularity.slice(1)
                  : "",
                cycleLength: data.menstrual_history.duration || "",
                flowDays: Number.parseInt(data.menstrual_history.volume) || 5,
                padsPerDay: Number.parseInt(data.menstrual_history.frequency) || 4,
                symptoms: data.menstrual_history.associated_symptoms || "",
              }
            : defaultFormData.reproHealth.menstruation,
          childbirth: data.obstetric_history
            ? {
                children: Number.parseInt(data.obstetric_history.number_of_children) || 0,
                pregnancies: Number.parseInt(data.obstetric_history.number_of_pregnancies) || 0,
                complications: data.obstetric_history.complications|| "",
              }
            : defaultFormData.reproHealth.childbirth,
        }
      }

     
    }

    // Update form data with mapped data
    set((state) => ({
      formData: {
        ...state.formData,
        ...mappedData,
        basicInfo: { ...state.formData.basicInfo, ...mappedData.basicInfo },
        allergies: mappedData.allergies || state.formData.allergies,
        medications: mappedData.medications || state.formData.medications,
        conditions: mappedData.conditions || state.formData.conditions,
        reproHealth: { ...state.formData.reproHealth, ...mappedData.reproHealth },
      },
    }))
  },

  loadFormData: async () => {
    set({ isLoading: true, error: null });
    try {
      const medicalResponse = await getMedicalData();
      const medicalData = medicalResponse?.success ? medicalResponse.medicalData : null;
      if (medicalData) {
        get().setOriginalData(medicalData);
      } else {
        set({ error: "No medical data found." });
      }
      set({ isLoading: false });
    } catch (error: any) {
      set({ isLoading: false, error: "Failed to load medical data. Please try again." });
      if (error?.response?.status !== 401) {
        logger.error("Error loading medical data:", error);
      }
    }
  },
}))