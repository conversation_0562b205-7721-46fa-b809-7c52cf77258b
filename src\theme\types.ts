export type ColorShades = {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  main: string;
  600: string;
  700: string;
  800: string;
  900: string;
};

export type ThemeColors = {
  primary: ColorShades;
  secondary: ColorShades;
  gray: ColorShades;
  errorLight:string;
  error: string;
  warning: string;
  success: string;
  info: string;
  background: {
    primary: string;
    secondary: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  specialistColors: string[];
  cardGradients: [string, string][];
};

export type ThemeSpacing = {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
  '3xl': number;
};

export type ThemeFontSizes = {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  "2xl": number;
  "3xl": number;
  "4xl": number;
  "5xl":number;
};

export type ThemeRadii = {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  "2xl":number;
  "3xl":number
  '4xl':number;
  full: number;
};

export type Theme = {
  colors: ThemeColors;
  spacing: ThemeSpacing;
  fontSize: ThemeFontSizes;
  radii: ThemeRadii;
  fonts: {
    heading: string;
    body: string;
  };
};

