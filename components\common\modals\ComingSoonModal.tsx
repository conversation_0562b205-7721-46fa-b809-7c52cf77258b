import React, { useMemo } from 'react';
import { StyleSheet, View, Text, Modal, TouchableOpacity, Dimensions, TouchableWithoutFeedback } from 'react-native';
import { X } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from 'react-i18next';

const SCREEN_WIDTH = Dimensions.get('window').width;
const MODAL_WIDTH = SCREEN_WIDTH * 0.7;

interface ComingSoonModalProps {
  isVisible: boolean;
  onClose: () => void;
  featureName: string;
}
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContainer: {
      width: MODAL_WIDTH,
      aspectRatio: 1,
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.xl),
      padding: moderateScale(24),
      position: "relative",
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
      marginBottom: moderateVerticalScale(20),
    },
    contentContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "600",
      color: theme.colors.gray[800],
    },
    closeButton: {
      position: "absolute",
      right: 0,
      padding: moderateScale(4),
    },
    button: {
      backgroundColor: theme.colors.primary.main,
      paddingVertical: moderateVerticalScale(12),
      paddingHorizontal: moderateScale(24),
      borderRadius: moderateScale(theme.radii.sm),
      alignItems: "center",
      justifyContent: "center",
      marginTop: moderateVerticalScale(20),
    },
    buttonText: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      textAlign: "center",
      lineHeight: moderateVerticalScale(20),
    },
    featureName: {
      color: theme.colors.primary.main,
    },
  });

const ComingSoonModal: React.FC<ComingSoonModalProps> = ({ isVisible, onClose, featureName }) => {
   const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const { t } = useTranslation();
  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
          <View style={styles.modalContainer}>
              <View style={styles.header}>
                <Text style={styles.title}>{t('comingSoon.title')}</Text>
              </View>
              
              <View style={styles.contentContainer}>
                <Text style={styles.subtitle}>
                  <Text style={styles.featureName}>{featureName}</Text>
                  {t('comingSoon.description')}
                </Text>
              </View>

              <TouchableOpacity style={styles.button} onPress={onClose}>
                <Text style={styles.buttonText}>{t('comingSoon.buttonText')}</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

// const styles = StyleSheet.create({
//   overlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   modalContainer: {
//     width: MODAL_WIDTH,
//     aspectRatio: 1,
//     backgroundColor: colors.white,
//     borderRadius: moderateScale(20),
//     padding: moderateScale(24),
//     position: 'relative',
//   },
//   header: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'center',
//     width: '100%',
//     marginBottom: moderateVerticalScale(20),
//   },
//   contentContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   title: {
//     fontSize: moderateScale(20),
//     fontWeight: '600',
//     color: colors.gray[800],
//   },
//   closeButton: {
//     position: 'absolute',
//     right: 0,
//     padding: moderateScale(4),
//   },
//   button: {
//     backgroundColor: colors.primary,
//     paddingVertical: moderateVerticalScale(12),
//     paddingHorizontal: moderateScale(24),
//     borderRadius: moderateScale(8),
//     alignItems: 'center',
//     justifyContent: 'center',
//     marginTop: moderateVerticalScale(20),
//   },
//   buttonText: {
//     color: colors.white,
//     fontSize: moderateScale(15),
//     fontWeight: '600',
//   },
//   subtitle: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//     textAlign: 'center',
//     lineHeight: moderateVerticalScale(20),
//   },
//   featureName: {
//     color: colors.primary,
//   },
// });

export default ComingSoonModal;