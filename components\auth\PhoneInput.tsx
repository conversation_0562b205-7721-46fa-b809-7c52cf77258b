import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, TextInput, TouchableWithoutFeedback, Keyboard, KeyboardAvoidingView, Platform } from 'react-native';
import { countryCodes } from '@/constants/countryCodes';
import { colors } from '@/constants/colors';
import { ChevronDown, Search } from 'lucide-react-native';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';

interface PhoneInputProps {
  value: string;
  onChangeText: (text: string) => void;
  countryCode: string;
  countryIsoCode?: string;
  onChangeCountry: (dialCode: string, isoCode: string) => void;
  error?: string | null;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChangeText,
  countryCode,
  countryIsoCode = 'IN', // Default to India if not provided
  onChangeCountry,
  error,
}) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Find country by both dial code and ISO code if available
  const selectedCountry = countryIsoCode 
    ? countryCodes.find(country => country.code === countryIsoCode)
    : countryCodes.find(country => country.dial_code === countryCode);

  const filteredCountries = countryCodes.filter(country => {
    const query = searchQuery.toLowerCase();
    return (
      country.name.toLowerCase().includes(query) ||
      country.dial_code.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query)
    );
  });

  const handleSelectCountry = (dialCode: string, isoCode: string) => {
    onChangeCountry(dialCode, isoCode);
    setModalVisible(false);
  };

  return (
  
    <View style={styles.container}>
      <View style={[styles.inputContainer, error && styles.inputError]}>
        <TouchableOpacity
          style={styles.countrySelector}
          onPress={() => setModalVisible(true)}
        >
          <Text style={styles.countryFlag}>
            {selectedCountry?.flag}
          </Text>
          <ChevronDown size={16} color={colors.gray[500]} />
        </TouchableOpacity>
        
        <View style={styles.inputWrapper}>
          <Text style={styles.countryCode}>{countryCode}</Text>
          <TextInput
            style={styles.input}
            value={value}
            onChangeText={onChangeText}
            keyboardType="phone-pad"
            maxLength={15}
            defaultValue=""
          />
        </View>
      </View>
      
      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{t('auth.phone.selectCountry')}</Text>
                <TouchableOpacity
                  onPress={() => setModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>{t('common.close')}</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <Search size={20} color={colors.gray[500]} style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder={t('auth.phone.searchCountries')}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                />
              </View>

              <FlatList
                data={filteredCountries}
                keyExtractor={(item) => item.code}
                keyboardShouldPersistTaps="handled"
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.countryItem}
                    onPress={() => handleSelectCountry(item.dial_code, item.code)}
                  >
                    <Text style={styles.countryFlag}>{item.flag}</Text>
                    <Text style={styles.countryName}>{item.name}</Text>
                    <Text style={styles.countryDialCode}>{item.dial_code}</Text>
                  </TouchableOpacity>
                )}
              />
          </View>
        </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </Modal>
    </View>
    
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: moderateVerticalScale(16),
  },
  inputContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: moderateScale(12),
    overflow: 'hidden',
  },
  inputError: {
    borderColor: colors.error,
  },
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(10),
    paddingVertical: moderateVerticalScale(14),
    borderRightWidth: 1,
    borderRightColor: colors.gray[300],
    backgroundColor: colors.white,
  },
  countryFlag: {
    fontSize: moderateScale(16),
    marginRight: moderateScale(4),
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryCode: {
    paddingLeft: moderateScale(12),
    fontSize: moderateScale(16),
    color: colors.gray[700],
  },
  input: {
    flex: 1,
    paddingHorizontal: moderateScale(4),
    paddingVertical: moderateVerticalScale(16),
    fontSize: moderateScale(16),
  },
  errorText: {
    color: colors.error,
   textAlign:'center',
    marginTop: moderateVerticalScale(4),
    fontSize: moderateScale(14),
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: moderateScale(16),
    borderTopRightRadius: moderateScale(16),
    paddingTop: moderateVerticalScale(16),
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingBottom: moderateVerticalScale(16),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
  },
  closeButton: {
    padding: moderateScale(8),
  },
  closeButtonText: {
    color: colors.primary,
    fontSize: moderateScale(16),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateVerticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  searchIcon: {
    marginRight: moderateScale(8),
  },
  searchInput: {
    flex: 1,
    fontSize: moderateScale(16),
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateVerticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  countryName: {
    flex: 1,
    fontSize: moderateScale(16),
  },
  countryDialCode: {
    fontSize: moderateScale(16),
    color: colors.gray[500],
  },
});

export default PhoneInput;
