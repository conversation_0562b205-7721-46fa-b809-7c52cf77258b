{"common": {"error": "Fout", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "sometimes": "Soms", "close": "Sluiten", "cancel": "<PERSON><PERSON><PERSON>", "save": "Opsla<PERSON>", "next": "Volgende", "loading": "Bezig met laden...", "version": "v0.0.1.7"}, "welcome": "Log in om te beginnen met chatten met August", "notFound": {"title": "Oeps!", "message": "<PERSON>t scherm bestaat niet.", "goHome": "Ga naar het startscherm!"}, "library": {"title": "Gezondheidsbibliotheek"}, "specialists": {"title": "<PERSON><PERSON>", "description": "Raadpleeg gespecialiseerde gezondheidsprofessionals voor specifiekere gezondheidsproblemen. <PERSON>es hieronder een specialist:", "generalPhysician": {"title": "Huisarts", "description": "Voor algemene gezondheidsklachten en eerstelijnszorg."}, "nutritionist": {"title": "Voedingsdeskundige", "description": "Voor advies over dieet, voeding en gewichtsbeheersing."}, "cardiologist": {"title": "Cardioloog", "description": "Voor hartgerelateerde problemen en cardiovasculaire gezondheid."}, "neurologist": {"title": "Neuroloog", "description": "<PERSON><PERSON> <PERSON>en met de <PERSON><PERSON><PERSON>, het ruggenmerg en het zenuwstelsel."}, "oncologist": {"title": "Oncoloog", "description": "Voor kankergerelateerde problemen en behandelingen."}, "endocrinologist": {"title": "Endocrinoloog", "description": "Voor hormoon gerelateerde aandoeningen en diabetes management."}, "dermatologist": {"title": "Dermatoloog", "description": "<PERSON>oor huid-, haar- en nagelproblemen."}, "psychiatrist": {"title": "Psychiater", "description": "Voor geestelijke gezondheidsproblemen en psychisch welzijn."}}, "profile": {"title": "<PERSON><PERSON>", "defaultName": "Gas<PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON> je naam in", "saving": "<PERSON><PERSON> met op<PERSON><PERSON>...", "noPhoneNumber": "<PERSON>n telefoonnummer", "loggingOut": "Aan het uitloggen...", "about": {"title": "Over", "description": "Kom meer te weten over August"}, "whatsapp": {"title": "WhatsApp", "description": "<PERSON><PERSON> met August op WhatsApp"}, "refer": {"title": "Verwi<PERSON>zen", "description": "August leuk gevonden? <PERSON><PERSON> het met je vrienden"}, "deleteAccount": {"title": "Account verwij<PERSON>en", "description": "Het spijt ons dat je gaat"}, "logout": {"title": "Uitloggen", "description": "Tot snel. We zullen je missen"}, "shareMessage": "👋Hey, <PERSON><PERSON> eens naar deze geweldige app die ik gebruik!\n\n\n\n➡️Ik gebruik August om snel betrouwbare gezondheidsinformatie en begeleiding te krijgen. Het is alsof je een dokter op zak hebt! Bekijk het hier:", "error": {"loadFailed": "Gegevens laden mislukt", "fetchError": "Er is een fout opgetreden tijdens het ophalen van gebruikersgegevens", "updateNameFailed": "Naam bij<PERSON> mislukt", "updateNameError": "Er is een fout opgetreden tijdens het bijwerken van de <PERSON>am", "loadFoodData": "Gegevens laden mislukt", "logoutError": "Fout tijdens uitloggen:", "shareError": "Fout bij het delen van be<PERSON>:"}}, "error": {"title": "Er is iets misgegaan", "checkLogs": "Controleer de logboeken van je apparaat voor meer details.", "unknown": "Onbekende fout", "unknownFile": "<PERSON><PERSON><PERSON> bestand", "unknownLine": "<PERSON><PERSON><PERSON><PERSON> regel", "unknownColumn": "Onbekende kolom"}, "auth": {"phone": {"selectCountry": "Selecteer land", "searchCountries": "<PERSON><PERSON> landen", "validation": {"invalidPhone": "Voer een geldig telefoonnummer in", "invalidDigits": "<PERSON><PERSON>r een geldig telefoonnummer in (7-15 cijfers)"}}, "header": {"title": "Krijg direct en privé duidelijkheid over uw gezondheidskwesties", "subtitle": "Doordachte begeleiding. <PERSON><PERSON> haast. <PERSON>n verwarring.", "emphasis": "G<PERSON><PERSON> duidelijkheid."}, "greeting": "Hallo 👋", "phoneNumber": "Telefoonnummer", "requestOTP": "Vraag OTP aan", "otp": {"title": "<PERSON><PERSON><PERSON><PERSON> wachtwoord", "verify": "Verifieer OTP", "sending": "Verzenden...", "countdown": "OTP opnieuw verzenden over {{countdown}}s", "resend": "OTP opnieuw verzenden", "sentTo": "OTP verzonden naar ", "whatsappSuffix": " op Whatsapp"}, "disclaimer": {"prefix": "Door u aan te melden, gaat u ak<PERSON><PERSON> met onze ", "continuePrefix": "Door verder te gaan, gaat u ak<PERSON><PERSON> met onze ", "termsOfService": "Servicevoorwaarden", "and": " en ", "privacyPolicy": "Privacybeleid", "whatsappConsent": ", en geeft u toestemming om updates en herinneringen van ons via WhatsApp te ontvangen."}}, "onboarding": {"preAuth": {"welcome": {"title": "Welkom bij August!", "buttonText": "Laten we beginnen"}}, "postAuth": {"step1": {"title": "Hé!", "subtitle": "<PERSON><PERSON> ben August 👋", "description": "Beschouw me als de comfortabele hoek op uw\napparaat waar u al uw gezondheids-\nnieuwsgierigheid kunt verkennen.", "subdescription": "Voel je vrij om alles te vragen wat je dwarszit.\n<PERSON><PERSON>, geen lim<PERSON>en!", "placeholder": "Hoe mag ik je noemen?"}, "step2": {"title": "<PERSON><PERSON> {{userName}},", "subtitle": "Dit kan ik doen:", "features": {"health": {"title": "Beantwoord uw", "subtitle": "Gezondheidsvragen"}, "nutrition": {"title": "Volg die", "subtitle": "<PERSON><PERSON>'s"}, "reports": {"title": "Analyseer", "subtitle": "Rapporten"}}}}, "pills": {"thoughtful": "Doordacht", "careful": "Voorzichtig", "accurate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "features": {"symptoms": {"title": "Controleer uw symptomen", "description": "Ik ben al een week misselijk. Wat gebeurt er met me?"}, "prescriptions": {"title": "<PERSON><PERSON><PERSON>r uw recepten", "description": "Upload en begrijp recepten zoals een arts."}, "medicine": {"title": "<PERSON> uw medici<PERSON>en", "description": "Interageert Metformine voor mijn PCOS met mijn ADHD-pillen?"}, "plans": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> plannen", "description": "Kun je me een voedings- en fitnessplan geven om mijn HbA1c-waarden te verlagen?"}}, "buttons": {"getStarted": "<PERSON><PERSON>", "next": "Volgende"}, "errors": {"nameRequired": "<PERSON><PERSON><PERSON> uw naam in"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Ontdek", "nutrition": "Voeding", "personalize": "<PERSON><PERSON>er"}, "chat": {"nav": {"title": "<PERSON>"}, "me": "Ik", "augustName": "<PERSON>", "input": {"placeholder": "Vraag het Augustus...", "disclaimer": "<PERSON> kan fouten maken. Bevestig bij een arts"}, "list": {"loadingMessages": "Berich<PERSON> laden...", "noMessages": "Nog geen berichten. Begin een gesprek!"}, "connection": {"offlineMessage": "Het lijkt erop dat je offline bent. Maak opnieuw verbinding om berichten te verzenden.", "connecting": "Verbinding maken...", "tryAgain": "Opnieuw proberen"}, "prompts": {"uploadReport": "Rapport uploaden", "speakInHindi": "Praat in het Hindi", "notFeelingWell": "Ik voel me niet goed", "whatIsMyBMI": "Wat is mijn BMI?", "nutritionAdvice": "Voedingsadvies", "sleepBetter": "<PERSON><PERSON> slapen"}, "citations": {"referenceText": "<PERSON>oor meer details over dit gesprek, raad<PERSON><PERSON>:"}, "actions": {"copiedToClipboard": "Gekopieerd naar klembord", "copied": "Gekopieerd"}, "share": {"introText": "👋Hé, kijk eens naar het gesprek dat ik met <PERSON> had:\n\n", "downloadText": "\n\n➡️Download <PERSON> om te chatten met je vriendelijke AI-gezondheidsassistent:\n"}}, "discover": {"nav": {"title": "Ontdek"}, "categories": {"all": "Alles", "heartHealth": "Hartgezondheid", "nutrition": "Voeding", "mentalHealth": "Geestelijke gezondheid", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "cards": {"empty": "<PERSON><PERSON> kaarten beschik<PERSON>ar voor deze categorie"}, "sections": {"features": "Functies"}, "features": {"healthLibrary": {"title": "Gezondheidsbibliotheek", "description": "Toegang tot betrouwbare en actuele medische informatie, volledig gratis."}, "nutritionTracker": {"title": "<PERSON><PERSON>dingstracker", "description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> of je gewoon een foto van je eten kon uploaden en al je voedingsdoelen kon bijhouden? Augustus kan dat!"}, "multilingualSupport": {"title": "Meertalige ondersteuning", "description": "Je kunt met <PERSON> communiceren in elke taal waarin je je prettig voelt! <PERSON> is er altijd om te luisteren, te ondersteunen en te reageren wanneer je maar wilt.", "samplePrompt": "Praat in het Hindi"}, "labReportAnalysis": {"title": "<PERSON><PERSON><PERSON> van laboratoriumrapporten", "description": "<PERSON><PERSON> je met <PERSON> over je laborator<PERSON><PERSON><PERSON><PERSON> praat, krijg je extreme precisie. <PERSON> heeft meer dan 4,7 miljoen rapporten verwerkt met een na<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van 98,4% bij het extraheren van biomarkers."}}}, "nutrition": {"nav": {"title": "Voeding"}, "meals": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Tik om de macro's in elke maaltijd te bekijken"}, "upload": {"loading": "Afbeelding uploaden..."}, "defaultFoodName": "Voedingsmiddel", "today": "Vandaag", "unknownTime": "Onbekend tijdstip", "calories": "🔥 Calorieën", "proteins": "🥩 <PERSON><PERSON><PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON>ten", "sugars": "🍬 Suikers", "fat": "🥑 Vet", "caloriesLabel": "Calorieën", "proteinLabel": "<PERSON><PERSON><PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "Vet", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Tips:", "macroBreakdown": "Macronutriënten verdeling", "noMacroData": "Geen macronutriënten data beschikbaar voor dit voedingsmiddel.", "disclaimer": "Alleen voor educatief gebruik. Meer informatie", "disclaimerLink": "hier", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Wat is uw geslacht?", "subtitle": "Dit wordt gebruikt om uw persoonlijke plan te kalibreren.", "male": "Man", "female": "Vrouw", "other": "<PERSON>"}, "age": {"title": "Wat is uw leeftijd?", "subtitle": "Dit wordt gebruikt om uw dagelijkse behoeften te berekenen."}, "measurements": {"title": "Lengte & Gewicht", "subtitle": "<PERSON><PERSON><PERSON> uw lengte in centimeters en gewicht in kilogrammen in."}, "activity": {"title": "Activiteitenniveau", "subtitle": "Hoe vaak sport u?", "none": "Geen sport", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON>"}, "goal": {"title": "Gewichtsdoel", "subtitle": "Wat wilt u bereiken?", "increase": "<PERSON><PERSON><PERSON>", "maintain": "Handhaven", "decrease": "Afname"}, "targetWeight": {"title": "Doelgewicht", "subtitle": "Wat is uw doelgewicht in kilogrammen?"}, "setup": {"title": "Uw plan instellen", "subtitle": "Wacht even terwijl we uw voedingsplan voorbereiden."}, "review": {"title": "Uw plan bekijken", "subtitle": "Bekijk en pas uw voedingsplan aan."}, "height": {"label": "Lengte (cm)"}, "weight": {"label": "Gewicht (kg)"}}, "error": {"updateFailed": "Het bijwer<PERSON> van voedingsgegevens is mislukt. Probeer het opnieuw.", "parsingError": "Fout bij het parseren van voedingsgegevens:", "fetchReportsFailed": "<PERSON><PERSON> op<PERSON><PERSON> van rap<PERSON> is mislukt. Probeer het opnieuw.", "missingReportId": "Rapport-ID ontbreekt"}}, "personalize": {"nav": {"title": "<PERSON><PERSON>er"}, "button": {"saving": "Aan het opslaan", "review": "Beoordelen", "saveNext": "Opslaan & Volgende"}}, "basicInfo": {"title": "Laten we u beter leren kennen", "subtitle": "Deze informatie helpt ons uw gezondheidsaanbevelingen te personaliseren", "age": {"question": "Hoe oud bent u?", "placeholder": "<PERSON><PERSON><PERSON> uw leefti<PERSON>d in"}, "sex": {"question": "Wat is uw geslacht?", "placeholder": "Selecteer uw g<PERSON>cht", "male": "Man", "female": "Vrouw", "other": "<PERSON>"}, "height": {"question": "Wat is uw lengte? (cm)", "placeholder": "<PERSON><PERSON><PERSON> uw lengte in"}, "weight": {"question": "Wat is uw gewicht? (kg)", "placeholder": "<PERSON><PERSON><PERSON> uw gewicht in"}}, "lifestyle": {"title": "Uw Lifestyle Gewoontes", "subtitle": "Inzicht in uw dagelijkse gewoontes helpt ons betere aanbevelingen te geven", "diet": {"question": "Welk type dieet volgt u?", "placeholder": "Selecteer uw dieet", "vegetarian": "Vegetarisch", "nonVegetarian": "<PERSON><PERSON>-veget<PERSON><PERSON>", "vegan": "Veganistisch", "pescatarian": "Pescotarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Beweegt u regelmatig?"}, "drinking": {"question": "Gebruikt u alcohol?"}, "smoking": {"question": "Rookt u?"}, "sleep": {"question": "Ho<PERSON><PERSON> uur slaapt u per nacht?", "value": "{{sleep}} uur"}, "hydration": {"question": "Hoeveel glazen water drinkt u dagelijks?", "value": "{{hydration}} glazen ({{liters}}L)"}}, "allergies": {"title": "Heeft u allergieën?", "subtitle": "Het kennen van uw allergieën helpt ons veiligere aanbevelingen te geven", "allergyIndex": "Allergie {{index}}", "name": {"question": "Waar bent u allergisch voor?", "placeholder": "Voer allergie in (bijv. pinda's, stof)"}, "severity": {"question": "Hoe ernstig is deze allergie?", "placeholder": "Selecteer ernst", "mild": "Mild", "moderate": "<PERSON><PERSON>", "severe": "<PERSON><PERSON>"}, "addButton": "Voeg nog een allergie toe", "noAllergiesButton": "<PERSON>k heb geen allergieën"}, "medications": {"title": "Medicijnen & Supplementen", "subtitle": "Vertel ons over alle medicijnen of supplementen die u momenteel neemt", "medicationIndex": "<PERSON>jn {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> medic<PERSON> in"}, "startDate": {"question": "Wanneer bent u ermee begonnen?", "placeholder": "Selecteer datum"}, "type": {"label": "Type medicijn", "shortTerm": "<PERSON><PERSON>", "longTerm": "<PERSON>"}, "dose": {"label": "Dosering", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "frequency": {"label": "Frequentie", "placeholder": "<PERSON><PERSON><PERSON> keer", "perDay": "per dag", "perWeek": "per week", "perMonth": "per maand", "perYear": "per jaar"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "druppels", "tsp": "theelepels", "tbsp": "e<PERSON>lepel", "cups": "kopjes"}, "addButton": "Voeg nog een medicijn toe", "noMedicationsButton": "Ik neem geen medicijnen", "calendar": {"title": "Selecteer startdatum"}}, "conditions": {"title": "Medische aandoeningen", "subtitle": "Vertel ons over eventuele medische aandoeningen die u heeft of in het verleden heeft gehad", "conditionIndex": "Aandoening {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> a<PERSON> in (bijv. astma, etc.)"}, "since": {"question": "Sinds wanneer heeft u deze aandoening?", "placeholder": "Selecteer datum"}, "current": {"question": "Heeft u er momenteel last van?"}, "medicated": {"question": "Neemt u medicijnen hiervoor?"}, "addButton": "Voeg nog een aandoening toe", "noConditionsButton": "Ik heb geen medische aandoeningen", "calendar": {"title": "Selecteer datum"}}, "reproductive": {"title": "<PERSON><PERSON>uc<PERSON><PERSON>", "subtitle": "Deze informatie helpt ons om meer persoonlijke gezondheidsadviezen te geven", "menstruation": {"question": "Heeft u ooit menstruatie gehad?", "detailsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "regularity": {"question": "Hoe regelmatig is uw cyclus?", "regular": "Regelmatig", "irregular": "Onregelmatig", "notSure": "Weet ik niet"}, "cycleLength": {"label": "Gemiddelde cycluslengte (dagen)", "placeholder": "<PERSON><PERSON><PERSON> cyc<PERSON> in"}, "flowDays": {"label": "Menstruatiedagen: {{flowDays}}", "min": "1 dag", "max": "15 dagen"}, "padsPerDay": {"label": "Inlegkruisjes/tampons per dag: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Eventuele symptomen tijdens uw menstruatie?", "placeholder": "<PERSON><PERSON>r symptomen in (bijv. krampen, hoofdpijn)"}}, "childbirth": {"question": "Heeft u een bevalling me<PERSON>t?", "detailsTitle": "Bevalling Details", "children": {"label": "Aantal Kinderen"}, "pregnancies": {"label": "Aantal Zwangerschappen"}, "complications": {"question": "Eventuele complicaties tijdens de zwangerschap of bevalling?", "placeholder": "<PERSON><PERSON><PERSON> complicaties in (<PERSON><PERSON> van <PERSON>)"}}}, "review": {"title": "Controleer Uw Informatie", "subtitle": "Controleer de informatie die u heeft verstrekt voordat u deze verzendt", "sections": {"basicInfo": "Basisgegevens", "lifestyle": "Lifestyle", "allergies": "Allergieën", "medications": "Medicijnen & Supplementen", "conditions": "Medische Aandoeningen", "reproductive": "<PERSON><PERSON>uc<PERSON><PERSON>", "menstruationDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childbirthDetails": "Bevalling Details"}, "fields": {"age": "Leeftijd:", "sex": "Geslacht:", "height": "Lengte:", "weight": "Gewicht:", "diet": "Dieet:", "exercise": "Beweging:", "drinking": "Alcoholgebruik:", "smoking": "Roken:", "sleep": "Slaap:", "hydration": "Hydratatie:", "allergyIndex": "Allergie {{index}}:", "dose": "Dosering:", "frequency": "Frequentie:", "type": "Type:", "since": "Sinds:", "currentlyActive": "Momenteel actief:", "takingMedication": "Gebruikt medicatie:", "hasMenstruated": "<PERSON><PERSON><PERSON> menstruatie gehad:", "regularity": "Regelmaat:", "cycleLength": "Cycluslengte:", "flowDays": "Menstruatiedagen:", "padsPerDay": "Inlegkruisjes/tampons per dag:", "hasChildbirth": "He<PERSON>t een bevalling me<PERSON>:", "children": "Kinderen:", "pregnancies": "Zwangerschappen:"}, "notProvided": "<PERSON><PERSON> ve<PERSON>", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} uur per dag", "hydration": "{{hydration}} bekers ({{liters}}L) per dag", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} dagen"}, "noData": {"allergies": "<PERSON>n allergieën opgegeven", "medications": "<PERSON>n medicijnen opgegeven", "conditions": "Geen medische aandoeningen opgegeven"}, "submitButton": "Verzend Informatie"}, "success": {"title": "Informatie Bijgewerkt!", "message": "Bedankt voor het verstrekken van uw gezondheidsinformatie. We gebruiken dit om uw ervaring te personaliseren en betere aanbevelingen te geven.", "benefits": {"insights": "Gepersonaliseerde gezondheidsinzichten", "reminders": "Betere medicijnherinneringen", "recommendations": "Op maat gemaakte gezondheidsadviezen"}, "continueButton": "Ga verder naar Dashboard"}, "permissions": {"microphonePermissionDenied": "Microfoon toestemming geweigerd", "microphoneAccessDescription": "August heeft toegang tot je microfoon nodig om audio op te nemen en spraakberichten te verzenden", "permissionDenied": "Toestemming geweigerd", "cameraPermissionRequired": "We hebben camera toestemming nodig om dit te laten werken!", "mediaLibraryPermissionRequired": "We hebben mediabibliotheek toestemming nodig om dit te laten werken!"}, "voiceRecording": {"recordingTooLong": "Opname te lang", "recordingTooLongMessage": "Spraakopnames moeten korter dan 5 minuten zijn. Neem een korter bericht op."}, "errors": {"uploadFailed": "Upload mislukt", "voiceUploadFailed": "<PERSON>n de spraakopname niet uploaden.", "voiceRecordingFailed": "<PERSON><PERSON> verz<PERSON><PERSON> van de s<PERSON> is mislukt", "failedToStopRecording": "Opname kon niet worden gestopt", "photoUploadFailed": "Kon de foto niet uploaden.", "failedToTakePhoto": "Kon geen foto maken", "imageUploadFailed": "Kon het beeld niet uploaden: {{fileName}}", "failedToPickImage": "Kon geen beeld selecteren", "documentUploadFailed": "Kon het document niet uploaden: {{fileName}}", "failedToPickDocument": "Kon geen document selecteren"}, "audioPlayer": {"downloadingAudio": "Audio wordt gedownload...", "loadingAudio": "Audio wordt geladen..."}, "mediaProcessing": {"processingFile": "Uw bestand wordt verwerkt", "uploadingSecuring": "Bestand uploaden en beveiligen...", "analyzingContent": "Documentinhoud analyseren...", "extractingInfo": "Belangrijkste informatie extraheren...", "processingInsights": "Inzichten verwerken...", "preparingResponse": "G<PERSON>tailleerd antwoord voorbereiden...", "finalizingResponse": "Antwoord afronden..."}, "attachments": {"voiceMessage": "Spraakbericht", "image": "[AFBEELDING]", "pdf": "[PDF]", "voice": "[SPRAAKNOTA]"}, "pdf": {"loadingPdf": "PDF wordt geladen..."}, "dateTime": {"yesterday": "Gisteren, "}, "navbar": {"defaultTitle": "august", "selectedCount": "gese<PERSON>eerd"}, "mediaUpload": {"photoLibrary": "Fotobibliotheek", "takePhoto": "Foto maken", "chooseFile": "<PERSON><PERSON> bestand"}, "comingSoon": {"title": "Binnenkort beschik<PERSON>ar!", "description": " is momenteel in ontwikkeling. Blijf op de hoogte voor updates!", "buttonText": "Oké!"}, "clipboard": {"success": "Link gekopieerd naar klembord"}, "mediaPhotos": {"emptyState": "Nog geen vermeldingen."}, "foodDetail": {"defaultFoodName": "Voedingsmiddel", "nutrition": {"totalCalories": "Totale calorieën", "proteins": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Vet", "sugars": "Suikers", "fibers": "<PERSON><PERSON><PERSON>"}}, "reports": {"defaultTitle": "Media-item", "defaultFoodName": "Voedingsmiddel", "defaultName": "Document", "openButton": "Openen in externe viewer", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "<PERSON><PERSON><PERSON>", "headerRefRange": "Referentiebereik", "headerStatus": "Status"}, "noData": "Geen biomarkergegevens be<PERSON>"}, "setup": {"title": "We regelen alles voor je", "inProgress": "Bezig...", "progressMessages": {"0": "Dagelijkse calorieën berekenen", "1": "Macroverdeling optimaliseren", "2": "Eetplan maken", "3": "Gezondheidsscore berekenen", "4": "Setup afronden"}, "checklistItems": {"0": "Uw gezondheidsgegevens analyseren", "1": "Optimale voedingsplan berekenen", "2": "<PERSON><PERSON> a<PERSON>bevelingen personaliseren", "3": "<PERSON><PERSON> ma<PERSON>d suggesties maken", "4": "Uw setup afronden"}}, "foodEntry": {"emptyState": "Nog geen voedselinformatie. Maak een foto van je maaltijd om deze toe te voegen!"}, "nutritionReview": {"congratulations": "Gefeliciteerd!", "subtitle": "<PERSON><PERSON><PERSON>e voedingsplan staat klaar", "submitButton": "Laten we beginnen!", "dailyTargetsTitle": "<PERSON><PERSON><PERSON><PERSON> voedingsdoelen", "macroLabels": {"calories": "Calorieën", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "fats": "Vetten"}}, "editModal": {"titlePrefix": "Bewerk ", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Volgende"}, "processing": {"stages": {"scanning": "<PERSON>ten scannen...", "identifying": "Ingrediënten identificeren...", "extracting": "Voedingsstoffen extraheren...", "finalizing": "Resultaten afronden..."}, "error": {"defaultMessage": "<PERSON><PERSON> eten gedetecteerd", "subtitle": "<PERSON><PERSON><PERSON> een andere hoek"}, "retakeButton": "Tik om de foto opnieuw te maken", "notification": "Je krijgt een melding als het klaar is!"}, "chart": {"title": "Voedingsregistratie in de tijd", "selectNutrient": "Voedingsstof select<PERSON>n:", "emptyState": "Nog geen voedingsgegevens besch<PERSON>ar.", "dropdown": {"calories": "Calorieën", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Vet", "sugars": "Suikers"}}, "foodModal": {"defaultName": "Voedingsmiddel", "defaultDate": "Vandaag", "defaultTime": "Onbekend tijdstip", "saveChanges": "Wijzigingen opslaan", "error": {"title": "Fout", "message": "Het bijwer<PERSON> van de voedingsgegevens is mislukt. Probeer het opnieuw."}, "nutrition": {"calories": "🔥 Calorieën", "proteins": "🥩 <PERSON><PERSON><PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON>ten", "sugars": "🍬 Suikers", "fat": "🥑 Vet"}, "macroBreakdown": {"title": "Macronutriënten verdeling", "noData": "<PERSON>n macronutriëntgegevens beschikbaar voor dit voedingsmiddel."}, "macroLabels": {"calories": "Calorieën", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Vet", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Gedetailleerde info", "edit": "Bewerken", "save": "Opsla<PERSON>", "saving": "Aan het opslaan...", "enterValue": "Waarde invoeren", "notSet": "<PERSON><PERSON> ing<PERSON>", "age": "Leeftijd", "heightCm": "Lengte (cm)", "weightKg": "Gewicht (kg)", "targetWeight": "Streefgewicht", "nutritionTargets": "Voedingsdoelen", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "Vetten", "gm": "gm", "editNote": "<PERSON><PERSON><PERSON> waarden in of laat ze leeg voor automatische berekening.", "autoCalculateNote": "<PERSON><PERSON>'s worden automatisch berekend op basis van jouw gegevens.", "validation": {"ageMin": "Leeftijd moet minimaal 18 zijn", "ageMax": "Leeftijd moet onder de 125 zijn", "heightMin": "Lengte moet minimaal 50cm zijn", "heightMax": "<PERSON>gte moet onder de 250cm zijn", "weightMin": "Gewicht moet minimaal 30kg zijn", "weightMax": "Gewicht moet onder de 500kg zijn", "targetWeightMin": "Streefgewicht moet minimaal 30kg zijn", "targetWeightMax": "Streefgewicht moet onder de 500kg zijn", "proteinMin": "Eiwitten moeten 0 of meer zijn", "carbsMin": "Koolhydraten moeten 0 of meer zijn", "fatsMin": "Vetten moeten 0 of meer zijn"}}, "tracker": {"calories": "Calorieën", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Vet", "excess": "overschrij<PERSON>", "remaining": "rest"}, "specialistConstants": {"nutritionist": {"name": "Voedingsdeskundige", "description": "Deskundig advies over voeding, dieet en gezonde eetgewoonten", "featureName": "Voedingsdeskundige"}, "cardiologist": {"name": "Cardioloog", "description": "Gespecialiseerd in hartgezondheid en cardiovasculaire aandoeningen", "featureName": "Cardioloog"}, "neurologist": {"name": "Neuroloog", "description": "Gefocust op aandoeningen van de hers<PERSON>n, het ruggenmerg en het zenuwstelsel", "featureName": "Neuroloog"}, "oncologist": {"name": "Oncoloog", "description": "Gespecialiseerd in kankerdiagnose en behandelmogelijkheden", "featureName": "Oncoloog"}, "endocrinologist": {"name": "Endocrinoloog", "description": "Expert in hormoonafhankelijke aandoeningen en stofwisselingsstoornissen", "featureName": "Endocrinoloog"}}, "discoverCards": {"categories": {"nutrition": "Voeding", "heartHealth": "Hartgezondheid", "mentalHealth": "Geestelijke gezondheid", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "titles": {"vitaminB12Recovery": "Hoe lang duurt het om te herstellen van een vitamine B12-tekort?", "vitaminDeficiencyGanglion": "Welk vitaminetekort veroorzaakt ganglioncysten?", "vitaminDeficiencyHairFall": "Welk vitaminetekort veroorzaakt ha<PERSON>?", "vitaminWaters": "<PERSON><PERSON>jn <PERSON> goed voor je?", "cholesterolHeadaches": "Kan een hoog cholesterolgehalte hoofdpijn veroorzaken?", "cholesterolEyes": "Wat zijn de symptomen van een hoog cholesterolgehalte die in de ogen te zien zijn?", "diabetesHeadaches": "Kan diabetes hoofdpijn veroorzaken?", "chestPainDrinking": "Waarom doet mijn borst pijn na het drinken?", "stressDizziness": "Kan stress duizeligheid veroorzaken?", "bulimiaFace": "Wat is een bulimia-gezicht?", "kneeTwitch": "Waarom trilt mijn knie?", "noseTwitching": "Waarom trekt mijn neus?", "piriformisVsSciatica": "Wat zijn de verschillen tussen het piriformis syndroom en ischias?", "shoulderBladePinched": "Hoe behandel je een beknelde zenuw in het schouderb<PERSON>?", "shoulderPinched": "Hoe behandel je een beknelde zenuw in de schouder?", "meniscusTorn": "Hoe genees je een gescheurde meniscus op natuurlijke wijze?", "hydrateQuickly": "Hoe hydrateer je snel?", "periodConstipation": "Is het normaal om constipatie te hebben tijdens je menstruatie?", "acneScars": "Hoe verwijder je acne littekens op natuurlijke wijze binnen een week?", "perimenopausePregnancy": "Kun je zwanger worden tijdens de perimenopauze?"}, "descriptions": {"vitaminB12Recovery": "Ontdek de herstelperiode bij een vitamine B12-tekort en effectieve remedies om je energieniveau te verhogen.", "vitaminDeficiencyGanglion": "Verken de link tussen vitaminetekorten en de ontwikkeling van ganglioncysten in het lichaam.", "vitaminDeficiencyHairFall": "<PERSON>r hoe een gebrek aan essentiële vitaminen kan leiden tot ha<PERSON>val en wat je eraan kunt doen om het te voorko<PERSON>.", "vitaminWaters": "Ontdek de voordelen en mogelijke nadelen van vitaminewaters als onderdeel van je dagelijkse voeding.", "cholesterolHeadaches": "Onderzoek het mogelijke verband tussen hoge cholesterolwaarden en het ontstaan van hoofdpijn.", "cholesterolEyes": "<PERSON>r hoe een hoge cholesterol zich kan manifesteren in je ogen en op welke symptomen je moet letten.", "diabetesHeadaches": "Onderzoek de relatie tussen diabetes en het voorkomen van hoofdpijn in het dagelijks leven.", "chestPainDrinking": "Verken de redenen achter pijn op de borst na het drinken van bepaalde dranken.", "stressDizziness": "Duik in hoe stress je evenwicht en algehele welzijn kan be<PERSON>, wat kan leiden tot duizelighei<PERSON>.", "bulimiaFace": "Begrijp de fysieke tekenen van boulimia, inclusief de effecten op het uiterlijk van het gezicht.", "kneeTwitch": "Onderzoek mogelijke oorzaken van onwillekeurig knie-trillen en de relatie met stress of vermoeidheid.", "noseTwitching": "<PERSON><PERSON> over de mogelijke redenen voor neus-trillen en de link met angst of andere factoren.", "piriformisVsSciatica": "Vergelijk de symptomen van piriformis syndroom en ischias om je aandoening beter te begrijpen.", "shoulderBladePinched": "Ontdek effectieve technieken om een beknelde zenuw in je schouderblad te verlichten en de mobiliteit te herstellen.", "shoulderPinched": "<PERSON>r eenvoudige oefeningen en stretches om zenuwcompressie in het schoudergebied te verlichten.", "meniscusTorn": "Verken natuurlijke methoden en oefeningen ter ondersteuning van de genezing van een gescheurde meniscus.", "hydrateQuickly": "Ontdek snelle en effectieve manieren om te rehydrateren en een optimale lichaamsvochtbalans te behouden.", "periodConstipation": "Begrijp de redenen achter constipatie tijdens de menstruatie en leer natuurlijke remedies.", "acneScars": "Ontdek natuurlijke remedies en huidverzorgingstips om het uiterlijk van acne littekens snel te verminderen.", "perimenopausePregnancy": "<PERSON><PERSON> over de perime<PERSON>, vruchtbaarheidsoverwegingen en wat je kunt verwachten tijdens deze leven<PERSON>se."}}}