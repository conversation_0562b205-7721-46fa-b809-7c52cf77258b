import {User} from '@/types/user'
export interface AuthState {
  // token removed from state and stored only in SecureStore
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  
  // Token methods
  getAccessToken: () => Promise<string | null>;
  setAccessToken: (token: string | null) => Promise<void>;
  getRefreshToken: () => Promise<string | null>;
  setRefreshToken: (token: string | null) => Promise<void>;
  setTokens: (accessToken: string | null, refreshToken: string | null) => Promise<void>;
  
  // User methods
  setUser: (user: User | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  logout: () => Promise<void>;
}

export interface OtpState {
  phone: string;
  isRequestingOtp: boolean;
  isVerifyingOtp: boolean;
  requestError: string | null;
  verifyError: string | null;
  setPhone: (phone: string) => void;
  requestOtp: (phone: string) => Promise<boolean>;
  verifyOtp: (otp: string) => Promise<any>;
  resetState: () => void;
}
