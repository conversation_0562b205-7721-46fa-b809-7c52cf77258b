import { logger } from "react-native-logs";
import { sendSlackNotification } from "../slack/sendSlackNotification";

// Set severity based on environment
const log = logger.createLogger({
  severity: __DEV__ ? "debug" : "error", // 👈 All logs in dev, only error in prod
});

if (__DEV__) {
  console.log('Running in development mode');
} else {
  console.log('Running in production mode');
}

const customLogger = {
  debug: (message: string, ...args: any[]) => {
    if (__DEV__) {
      console.log(`[DEBUG] ${message}`, ...args);
      log.debug(message, ...args);
    }
  },
  info: (message: string, ...args: any[]) => {
    if (__DEV__) {
      console.log(`[INFO] ${message}`, ...args);
      log.info(message, ...args);
    }
  },
  warn: (message: string, ...args: any[]) => {
    if (__DEV__) {
      console.warn(`[WARN] ${message}`, ...args);
      log.warn(message, ...args);
    }
  },
  error: (message: string, ...args: any[]) => {
    sendSlackNotification(`[ERROR] ${message} , ARGS: ${args}`);
    console.error(`[ERROR] ${message}`, ...args);
    log.error(message, ...args);
  },

  apiResponse: (endpoint: string, response: any) => {
    if (__DEV__) {
      console.log(`[API RESPONSE] [${endpoint}]`, response);
      log.info(`API RESPONSE [${endpoint}]`, response);
    }
  },

  webPubSubMessage: (message: any) => {
    if (__DEV__) {
      console.log(`[WEBPUBSUB] Received message:`, message);
      log.info(`WebPubSub message:`, message);
    }
  }
};

export default customLogger;
