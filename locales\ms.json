{"common": {"error": "<PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "sometimes": "Kadang-kadang", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "Simpan", "next": "Seterusnya", "loading": "Sedang memuat...", "version": "v0.0.1.7"}, "welcome": "Log masuk untuk mula berbual dengan August", "notFound": {"title": "Oops!", "message": "<PERSON><PERSON>rin ini tidak wujud.", "goHome": "Pergi ke skrin utama!"}, "library": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "specialists": {"title": "<PERSON><PERSON>", "description": "Berunding dengan profesional kesihatan pakar untuk masalah kesihatan yang lebih spesifik. <PERSON><PERSON><PERSON> pakar di bawah:", "generalPhysician": {"title": "Doktor Perubatan Am", "description": "Untuk masalah kesihatan umum dan penjagaan primer."}, "nutritionist": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> nasihat diet, p<PERSON><PERSON>an, dan pengu<PERSON>an berat badan."}, "cardiologist": {"title": "<PERSON><PERSON>", "description": "Untuk masalah berkaitan jantung dan kesihatan kardiovask<PERSON>."}, "neurologist": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ma<PERSON>ah <PERSON>, sara<PERSON>, dan sistem saraf."}, "oncologist": {"title": "<PERSON><PERSON>", "description": "Untuk masalah dan rawatan berkaitan kanser."}, "endocrinologist": {"title": "<PERSON><PERSON>", "description": "Untuk gangguan berkaitan hormon dan pengurusan diabetes."}, "dermatologist": {"title": "<PERSON><PERSON>", "description": "Untuk keadaan kulit, rambut, dan kuku."}, "psychiatrist": {"title": "<PERSON><PERSON>", "description": "<PERSON>tuk masalah kesihatan mental dan kesejahteraan psikologi."}}, "profile": {"title": "Profil", "defaultName": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama anda", "saving": "Sedang menyimpan...", "noPhoneNumber": "Tiada nombor telefon", "loggingOut": "Sedang log keluar...", "about": {"title": "Tentang", "description": "<PERSON><PERSON><PERSON> lebih lanjut tentang August"}, "whatsapp": {"title": "WhatsApp", "description": "Sembang dengan August di WhatsApp"}, "refer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Suka August? <PERSON><PERSON> dengan rakan anda"}, "deleteAccount": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> sedih melihat anda pergi"}, "logout": {"title": "Log keluar", "description": "<PERSON>pai jumpa lagi. <PERSON><PERSON> akan merindui anda"}, "shareMessage": "👋Hei, <PERSON>hat aplikasi hebat yang saya gunakan!\n\n\n\n➡️Saya telah menggunakan August untuk mendapatkan maklumat dan panduan kesihatan yang cepat dan boleh dipercayai. Ia seperti mempunyai doktor di dalam poket anda! Lihat di sini:", "error": {"loadFailed": "Gagal memuatkan data pengguna", "fetchError": "<PERSON><PERSON> berlaku semasa mengambil data pengguna", "updateNameFailed": "<PERSON><PERSON> men<PERSON> nama", "updateNameError": "<PERSON><PERSON> be<PERSON>aku semasa men<PERSON> nama", "loadFoodData": "Gagal memuatkan data makanan", "logoutError": "<PERSON><PERSON> semasa log keluar:", "shareError": "<PERSON><PERSON> me<PERSON>j:"}}, "error": {"title": "<PERSON> sesuatu yang tidak kena", "checkLogs": "Sila semak log peranti anda untuk maklumat lanjut.", "unknown": "<PERSON><PERSON> t<PERSON>", "unknownFile": "<PERSON><PERSON> tidak <PERSON>", "unknownLine": "<PERSON>s tida<PERSON>", "unknownColumn": "<PERSON><PERSON><PERSON> t<PERSON>"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON>", "searchCountries": "<PERSON><PERSON> negara", "validation": {"invalidPhone": "Sila masukkan nombor telefon yang sah", "invalidDigits": "Sila masukkan nombor telefon yang sah (7-15 digit)"}}, "header": {"title": "Dapatkan penjelasan tentang kebimbangan kesihatan anda dengan serta-merta dan secara peribadi", "subtitle": "Bimbingan yang ber<PERSON>ah. Tiada tergesa-gesa. <PERSON>iada k<PERSON>.", "emphasis": "<PERSON><PERSON>."}, "greeting": "Hai 👋", "phoneNumber": "Nombor Telefon", "requestOTP": "Permintaan OTP", "otp": {"title": "<PERSON><PERSON>", "verify": "Sahkan OTP", "sending": "Mengirim...", "countdown": "<PERSON><PERSON> semula <PERSON> da<PERSON> {{countdown}}s", "resend": "Hantar semula OTP", "sentTo": "OTP dihantar ke ", "whatsappSuffix": " <PERSON> What<PERSON>pp"}, "disclaimer": {"prefix": "<PERSON><PERSON> menda<PERSON>, anda bersetuju dengan kami ", "continuePrefix": "<PERSON>gan <PERSON>, anda bersetuju dengan kami ", "termsOfService": "<PERSON><PERSON>", "and": " dan ", "privacyPolicy": "<PERSON><PERSON>", "whatsappConsent": ", dan bersetuju untuk menerima kemas kini & per<PERSON>tan daripada kami melalui WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Selamat datang ke August!", "buttonText": "<PERSON><PERSON> mula"}}, "postAuth": {"step1": {"title": "Hai!", "subtitle": "Saya August 👋", "description": "Anggap saya sebagai sudut yang selesa di peranti anda di mana anda meneroka semua rasa ingin tahu kesihatan anda.", "subdescription": "<PERSON>an ragu untuk bertanya apa sahaja yang terlintas di fikiran anda.\n<PERSON><PERSON><PERSON>, Tiada had!", "placeholder": "Apa nama yang ingin saya panggil anda?"}, "step2": {"title": "Hai {{userName}},", "subtitle": "<PERSON><PERSON>h yang boleh saya lakukan:", "features": {"health": {"title": "<PERSON><PERSON><PERSON> anda tentang", "subtitle": "<PERSON><PERSON><PERSON><PERSON>"}, "nutrition": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> anda"}, "reports": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>"}}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON>", "careful": "<PERSON><PERSON><PERSON><PERSON>-hati", "accurate": "Tepat"}, "features": {"symptoms": {"title": "<PERSON><PERSON><PERSON> simptom anda", "description": "Saya mual selama seminggu. Apa yang berlaku kepada saya?"}, "prescriptions": {"title": "Mengan<PERSON><PERSON> presk<PERSON> anda", "description": "Muat naik dan fahami preskripsi seperti doktor."}, "medicine": {"title": "<PERSON><PERSON><PERSON> ubat anda", "description": "<PERSON><PERSON><PERSON> untuk PCOS saya berinteraksi dengan pil ADHD saya?"}, "plans": {"title": "Dapatkan pelan peribadi", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda memberi saya pelan pemakanan & kecergasan untuk mengurangkan paras HbA1c saya?"}}, "buttons": {"getStarted": "<PERSON><PERSON>", "next": "Seterusnya"}, "errors": {"nameRequired": "<PERSON><PERSON> masukkan nama anda"}}, "tabs": {"chat": "Sembang", "discover": "Terokai", "nutrition": "<PERSON><PERSON><PERSON><PERSON>", "personalize": "Peribadikan"}, "chat": {"nav": {"title": "Ogos"}, "me": "<PERSON><PERSON>", "augustName": "Ogos", "input": {"placeholder": "<PERSON>...", "disclaimer": "<PERSON><PERSON> mungkin membuat kesilapan. Sila sahkan dengan doktor"}, "list": {"loadingMessages": "Sedang memuatkan mesej...", "noMessages": "Tiada mesej lagi. <PERSON><PERSON><PERSON> per<PERSON>!"}, "connection": {"offlineMessage": "<PERSON><PERSON><PERSON><PERSON> anda di luar talian. Sambung semula untuk menghantar mesej.", "connecting": "Menyambung...", "tryAgain": "Cuba Lagi"}, "prompts": {"uploadReport": "<PERSON>at naik <PERSON>oran", "speakInHindi": "Bercakap dalam Bahasa Hindi", "notFeelingWell": "<PERSON>a tidak sihat", "whatIsMyBMI": "Apakah BMI saya?", "nutritionAdvice": "<PERSON><PERSON><PERSON>", "sleepBetter": "<PERSON><PERSON><PERSON> dengan lebih lena"}, "citations": {"referenceText": "Untuk maklumat lanjut mengenai perbualan ini, sila rujuk kepada:"}, "actions": {"copiedToClipboard": "<PERSON><PERSON>in ke papan klip", "copied": "Di<PERSON>in"}, "share": {"introText": "👋Hei, lihat perbualan saya dengan <PERSON>:\n\n", "downloadText": "\n\n➡️Muat turun Ogos untuk bersembang dengan rakan AI kesihatan anda yang mesra:\n"}}, "discover": {"nav": {"title": "Terokai"}, "categories": {"all": "<PERSON><PERSON><PERSON>", "heartHealth": "<PERSON><PERSON><PERSON><PERSON>", "nutrition": "<PERSON><PERSON><PERSON><PERSON>", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "<PERSON><PERSON><PERSON><PERSON>", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "cards": {"empty": "Tiada kad yang tersedia untuk kategori ini"}, "sections": {"features": "Ciri-ciri"}, "features": {"healthLibrary": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kepada maklumat perubatan yang dipercayai, boleh diperca<PERSON>i, dan terkini secara percuma."}, "nutritionTracker": {"title": "<PERSON><PERSON><PERSON>", "description": "Pernah tertanya-tanya jika anda boleh memuat naik foto makanan anda dan mengesan semua matlamat pemakanan anda? Ogos boleh melakukannya!"}, "multilingualSupport": {"title": "Sokongan Pelbagai Bahasa", "description": "Anda boleh berkomunikasi dengan <PERSON>gos dalam mana-mana bahasa yang anda selesa! Ogos sentiasa di sini untuk mendengar, <PERSON><PERSON><PERSON><PERSON>, dan memberi respons kepada anda bila-bila masa anda me<PERSON>an.", "samplePrompt": "Bercakap dalam Bahasa Hindi"}, "labReportAnalysis": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> anda berca<PERSON>p dengan Ogos tentang laporan makmal anda, anda mendapat ketepatan yang tinggi. Ogos telah memproses lebih daripada 4.7 juta laporan dengan ketepatan pengekstrakan biopenanda sebanyak 98.4%."}}}, "nutrition": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "meals": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Ketik untuk melihat makronutrien dalam setiap hidangan"}, "upload": {"loading": "Memuat naik imej..."}, "defaultFoodName": "<PERSON><PERSON>", "today": "<PERSON>", "unknownTime": "<PERSON>sa tidak <PERSON>", "calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "🍬 Gula", "fat": "🥑 Lemak", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "Lemak", "sugarLabel": "<PERSON><PERSON>", "tips": "Petua:", "macroBreakdown": "<PERSON><PERSON><PERSON><PERSON>", "noMacroData": "Tiada data makronutrien yang tersedia untuk item makanan ini.", "disclaimer": "Untuk tujuan pendidikan sahaja. <PERSON><PERSON><PERSON> lebih lan<PERSON>t", "disclaimerLink": "di sini", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "<PERSON><PERSON><PERSON><PERSON>?", "subtitle": "Ini akan digunakan untuk menentukur pelan tersuai anda.", "male": "<PERSON><PERSON><PERSON>", "female": "Perempuan", "other": "Lain-lain"}, "age": {"title": "<PERSON><PERSON><PERSON><PERSON>?", "subtitle": "Ini akan digunakan untuk mengira k<PERSON>luan harian anda."}, "measurements": {"title": "Tinggi & Berat", "subtitle": "<PERSON>la masukkan tinggi anda dalam sentimeter dan berat dalam kilogram."}, "activity": {"title": "Tahap Aktiviti", "subtitle": "<PERSON><PERSON> kerap anda bersenam?", "none": "<PERSON><PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON>", "high": "Tingg<PERSON>"}, "goal": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Apa<PERSON>h yang ingin anda capai?", "increase": "Meningkatkan", "maintain": "Mengekalkan", "decrease": "Men<PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> berat sasaran anda dalam kilogram?"}, "setup": {"title": "<PERSON>yed<PERSON><PERSON>", "subtitle": "<PERSON>la tunggu sementara kami menyediakan pelan pemakanan anda."}, "review": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Semak dan suaikan pelan pemakanan anda."}, "height": {"label": "Tinggi (cm)"}, "weight": {"label": "Berat (kg)"}}, "error": {"updateFailed": "Gagal mengemas kini data pemakanan. Sila cuba lagi.", "parsingError": "<PERSON><PERSON> data makanan:", "fetchReportsFailed": "Gagal mendapatkan data laporan. Sila cuba lagi.", "missingReportId": "ID Laporan hilang"}}, "personalize": {"nav": {"title": "Peribadikan"}, "button": {"saving": "Menyimpan", "review": "Semak", "saveNext": "Simpan & Seterusnya"}}, "basicInfo": {"title": "Mari kita kenal anda dengan lebih baik", "subtitle": "Maklumat ini membantu kami memperibadikan cadangan kesihatan anda", "age": {"question": "<PERSON><PERSON><PERSON> umur anda?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> umur anda"}, "sex": {"question": "<PERSON><PERSON><PERSON>h jantina anda?", "placeholder": "<PERSON><PERSON><PERSON> jantina anda", "male": "<PERSON><PERSON><PERSON>", "female": "Perempuan", "other": "Lain-lain"}, "height": {"question": "<PERSON><PERSON><PERSON> tinggi anda? (cm)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tinggi anda"}, "weight": {"question": "<PERSON><PERSON><PERSON> berat anda? (kg)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> berat anda"}}, "lifestyle": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> tabiat harian anda membantu kami memberikan cadangan yang lebih baik.", "diet": {"question": "<PERSON><PERSON><PERSON>h jenis diet yang anda amalkan?", "placeholder": "<PERSON><PERSON>h diet anda", "vegetarian": "Vegetarian", "nonVegetarian": "Bukan Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "<PERSON><PERSON><PERSON> anda bersenam secara tetap?"}, "drinking": {"question": "<PERSON><PERSON>h anda minum alkohol?"}, "smoking": {"question": "<PERSON><PERSON>h anda merokok?"}, "sleep": {"question": "<PERSON><PERSON>a jam anda tidur setiap malam?", "value": "{{sleep}} jam"}, "hydration": {"question": "Berapa banyak cawan air yang anda minum setiap hari?", "value": "{{hydration}} cawan ({{liters}}L)"}}, "allergies": {"title": "<PERSON><PERSON>h anda mempunyai sebarang alahan?", "subtitle": "<PERSON><PERSON><PERSON><PERSON> alahan anda membantu kami memberikan cadangan yang lebih selamat", "allergyIndex": "<PERSON><PERSON> {{index}}", "name": {"question": "Apa<PERSON>h anda alah kepada apa?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> (cth., Ka<PERSON>g <PERSON>, Debu)"}, "severity": {"question": "<PERSON><PERSON> teruknya alahan ini?", "placeholder": "<PERSON><PERSON><PERSON> ta<PERSON>p k<PERSON>", "mild": "<PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON>", "severe": "Sangat teruk"}, "addButton": "Tambah Alahan Lain", "noAllergiesButton": "<PERSON>a tidak mempunyai sebarang alahan."}, "medications": {"title": "Ubat-ubatan & Suplemen", "subtitle": "<PERSON><PERSON><PERSON> kami tentang sebarang ubat atau suplemen yang anda sedang ambil.", "medicationIndex": "Ubat {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama ubat"}, "startDate": {"question": "Bila awak mula mengambilnya?", "placeholder": "<PERSON><PERSON><PERSON> ta<PERSON>h"}, "type": {"label": "<PERSON><PERSON>", "shortTerm": "<PERSON><PERSON>", "longTerm": "<PERSON><PERSON>"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "unit": {"label": "Unit"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "perDay": "sehari", "perWeek": "<PERSON><PERSON><PERSON>", "perMonth": "sebulan", "perYear": "<PERSON><PERSON><PERSON>"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "hembus", "drops": "Titis", "tsp": "sudu teh", "tbsp": "sudu besar", "cups": "cawan"}, "addButton": "Tambah Ubat Lain", "noMedicationsButton": "<PERSON>a tidak mengambil sebarang ubat.", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "conditions": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> kami tentang sebarang keadaan perubatan yang anda ada atau pernah ada sebelum ini.", "conditionIndex": "<PERSON><PERSON>an {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> (cth Asthma, dll)"}, "since": {"question": "<PERSON>jak bila anda mengalami keadaan ini?", "placeholder": "<PERSON><PERSON><PERSON> ta<PERSON>h"}, "current": {"question": "Adakah ia menyusahkan anda sekarang?"}, "medicated": {"question": "<PERSON><PERSON><PERSON> anda mengambil sebarang ubat untuk ini?"}, "addButton": "Tambah Syarat Lain", "noConditionsButton": "<PERSON>a tidak mempunyai sebarang masalah kesihatan.", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Maklumat ini membantu kami menyediakan cadangan kesihatan yang lebih peribadi", "menstruation": {"question": "<PERSON><PERSON><PERSON><PERSON> anda mengalami haid?", "detailsTitle": "Maklumat Haid", "regularity": {"question": "Betapa kerapnya kitaran anda?", "regular": "<PERSON><PERSON><PERSON>", "irregular": "Tidak Teratur", "notSure": "Tidak Pasti"}, "cycleLength": {"label": "<PERSON><PERSON><PERSON> panjang kitaran (hari)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> panjang kitaran"}, "flowDays": {"label": "Tempoh haid: {{flowDays}}", "min": "1 hari", "max": "15 hari"}, "padsPerDay": {"label": "Pad/tampon sehari: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Ada sebarang simptom semasa haid?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> simptom (cth., kek<PERSON><PERSON><PERSON>, sakit kepala)"}}, "childbirth": {"question": "<PERSON><PERSON><PERSON>h anda melahirkan anak?", "detailsTitle": "Maklumat Bersalin", "children": {"label": "Bilangan Anak"}, "pregnancies": {"label": "Bilangan <PERSON>"}, "complications": {"question": "Ada sebarang komplikasi semasa mengandung atau bersalin?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON> (jika ada)"}}}, "review": {"title": "Se<PERSON>k <PERSON>", "subtitle": "<PERSON>la semak maklumat yang telah anda berikan sebelum mengemukakan", "sections": {"basicInfo": "Ma<PERSON><PERSON><PERSON>", "lifestyle": "<PERSON><PERSON>", "allergies": "<PERSON><PERSON>", "medications": "Ubat-ubatan & Suplemen", "conditions": "<PERSON><PERSON><PERSON>", "reproductive": "<PERSON><PERSON><PERSON><PERSON>", "menstruationDetails": "Maklumat Haid", "childbirthDetails": "Maklumat Bersalin"}, "fields": {"age": "Umur:", "sex": "<PERSON><PERSON>:", "height": "Tinggi:", "weight": "Berat:", "diet": "Diet:", "exercise": "Senaman:", "drinking": "Minuman:", "smoking": "Merokok:", "sleep": "Tidur:", "hydration": "<PERSON><PERSON><PERSON>:", "allergyIndex": "<PERSON><PERSON> {{index}}:", "dose": "Dos:", "frequency": "Frekuensi:", "type": "<PERSON><PERSON>:", "since": "Sejak:", "currentlyActive": "<PERSON>kt<PERSON>:", "takingMedication": "Mengambil Ubat:", "hasMenstruated": "<PERSON><PERSON>:", "regularity": "Kekerapan:", "cycleLength": "Panjang Kitaran:", "flowDays": "Tempoh Haid:", "padsPerDay": "Pad/Tampons Sehari:", "hasChildbirth": "<PERSON><PERSON>:", "children": "Anak:", "pregnancies": "Kehamilan:"}, "notProvided": "Tidak diberikan", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} jam sehari", "hydration": "{{hydration}} cawan ({{liters}}L) sehari", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} hari"}, "noData": {"allergies": "<PERSON><PERSON><PERSON> al<PERSON>n", "medications": "<PERSON><PERSON><PERSON> u<PERSON>-u<PERSON><PERSON>n", "conditions": "<PERSON><PERSON>da keadaan perubatan diberikan"}, "submitButton": "Hantar <PERSON>"}, "success": {"title": "Maklumat Dikemaskini!", "message": "Te<PERSON> kasih kerana memberikan maklumat kesihatan anda. <PERSON><PERSON> akan menggunakannya untuk memperibadikan pengalaman anda dan memberikan cadangan yang lebih baik.", "benefits": {"insights": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "reminders": "Pengingat ubat yang lebih baik", "recommendations": "Cadangan kesihatan yang disesuaikan"}, "continueButton": "Teruskan ke Papan Pemuka"}, "permissions": {"microphonePermissionDenied": "<PERSON><PERSON><PERSON> mi<PERSON><PERSON><PERSON>", "microphoneAccessDescription": "August memer<PERSON>an akses kepada mikrofon anda untuk merakam audio dan menghantar nota suara", "permissionDenied": "<PERSON><PERSON><PERSON>", "cameraPermissionRequired": "<PERSON><PERSON> me<PERSON>an kebenaran kamera untuk memastikan ini berfungsi!", "mediaLibraryPermissionRequired": "<PERSON><PERSON> me<PERSON>an kebenaran pustaka media untuk memastikan ini berfungsi!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON><PERSON>", "recordingTooLongMessage": "<PERSON><PERSON><PERSON> suara mestilah kurang daripada 5 minit. <PERSON><PERSON> rakam mesej yang lebih pendek."}, "errors": {"uploadFailed": "<PERSON><PERSON>", "voiceUploadFailed": "Tidak dapat memuat naik rakaman suara.", "voiceRecordingFailed": "<PERSON><PERSON> men<PERSON> rakaman suara", "failedToStopRecording": "<PERSON><PERSON> rakaman", "photoUploadFailed": "Tidak dapat memuat naik foto.", "failedToTakePhoto": "Gagal mengambil foto", "imageUploadFailed": "Tidak dapat memuat naik imej: {{fileName}}", "failedToPickImage": "<PERSON><PERSON> memilih imej", "documentUploadFailed": "Tidak dapat memuat naik dokumen: {{fileName}}", "failedToPickDocument": "<PERSON>l memilih dokumen"}, "audioPlayer": {"downloadingAudio": "Memuat turun audio...", "loadingAudio": "Memuatkan audio..."}, "mediaProcessing": {"processingFile": "Memproses Fail <PERSON>", "uploadingSecuring": "Me<PERSON>at naik dan mengam<PERSON>kan fail...", "analyzingContent": "Menganalisis kandungan dokumen...", "extractingInfo": "Mengekstrak maklumat utama...", "processingInsights": "Memproses maklumat...", "preparingResponse": "Menyediakan respons terperinci...", "finalizingResponse": "Memuktamadkan respons..."}, "attachments": {"voiceMessage": "<PERSON><PERSON><PERSON>", "image": "[GAMBAR]", "pdf": "[PDF]", "voice": "[NOTA SUARA]"}, "pdf": {"loadingPdf": "Memuatkan PDF..."}, "dateTime": {"yesterday": "Semalam, "}, "navbar": {"defaultTitle": "august", "selectedCount": "dipilih"}, "mediaUpload": {"photoLibrary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "takePhoto": "Ambil Foto", "chooseFile": "<PERSON><PERSON><PERSON>"}, "comingSoon": {"title": "<PERSON><PERSON>!", "description": " sedang dibangunkan.  Nantikan kemas kini!", "buttonText": "Faham!"}, "clipboard": {"success": "Pautan disalin ke papan klip"}, "mediaPhotos": {"emptyState": "Tiada entri lagi."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON>", "nutrition": {"totalCalories": "<PERSON><PERSON><PERSON>", "proteins": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugars": "<PERSON><PERSON>", "fibers": "Serat"}}, "reports": {"defaultTitle": "Item Media", "defaultFoodName": "<PERSON><PERSON>", "defaultName": "Dokumen", "openButton": "<PERSON><PERSON> da<PERSON>", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "<PERSON><PERSON>", "headerRefRange": "Julat Rujukan", "headerStatus": "Status"}, "noData": "Tiada data biomarker tersedia"}, "setup": {"title": "<PERSON><PERSON>g men<PERSON>kan semuanya untuk anda", "inProgress": "Sedang diproses...", "progressMessages": {"0": "<PERSON><PERSON><PERSON>", "1": "Mengopt<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "4": "Memuktamadkan Setup"}, "checklistItems": {"0": "Menganalisis data kesihatan anda", "1": "Mengira pelan pemakanan optimum", "2": "Memperibadikan cadangan anda", "3": "Mencipta cadangan makanan anda", "4": "Memuktamad<PERSON> persed<PERSON> anda"}}, "foodEntry": {"emptyState": "Tiada entri makanan lagi. <PERSON><PERSON> gambar makanan anda untuk menambahnya!"}, "nutritionReview": {"congratulations": "<PERSON><PERSON><PERSON>!", "subtitle": "Pelan nutrisi tersuai anda sudah siap", "submitButton": "Jom mula!", "dailyTargetsTitle": "<PERSON><PERSON><PERSON>", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "fats": "Lemak"}}, "editModal": {"titlePrefix": "Sunting ", "cancelButton": "<PERSON><PERSON>", "saveButton": "Seterusnya"}, "processing": {"stages": {"scanning": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>...", "identifying": "Mengenal pasti bahan-bahan...", "extracting": "Mengekstrak nutrien...", "finalizing": "Memuktamadkan keputusan..."}, "error": {"defaultMessage": "<PERSON><PERSON><PERSON> makanan di<PERSON>an", "subtitle": "Cuba sudut yang berbeza"}, "retakeButton": "Ketik untuk ambil gambar semula", "notification": "<PERSON><PERSON> akan memak<PERSON>kan anda apabila se<PERSON>ai!"}, "chart": {"title": "Pemantauan Nutrisi Sepanjang Ma<PERSON>", "selectNutrient": "<PERSON><PERSON><PERSON>:", "emptyState": "Tiada data nutrisi tersedia lagi.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugars": "<PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON>", "defaultDate": "<PERSON> ini", "defaultTime": "<PERSON>sa tidak <PERSON>", "saveChanges": "<PERSON><PERSON><PERSON>", "error": {"title": "<PERSON><PERSON>", "message": "Gagal mengemas kini data nutrisi. Sila cuba lagi."}, "nutrition": {"calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "🍬 Gula", "fat": "🥑 Lemak"}, "macroBreakdown": {"title": "<PERSON><PERSON><PERSON>", "noData": "Tiada data makronutrien tersedia untuk item makanan ini."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugar": "<PERSON><PERSON>"}}, "infoModal": {"title": "Maklumat Lanjut", "edit": "Sunting", "save": "Simpan", "saving": "Menyimpan..", "enterValue": "<PERSON><PERSON><PERSON><PERSON> nilai", "notSet": "Tidak ditetapkan", "age": "<PERSON><PERSON>", "heightCm": "Tinggi (cm)", "weightKg": "Berat (kg)", "targetWeight": "<PERSON><PERSON>", "nutritionTargets": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "Lemak", "gm": "gm", "editNote": "<PERSON><PERSON><PERSON><PERSON> nilai atau kosongkan untuk pengiraan automatik.", "autoCalculateNote": "Makro dikira secara automatik berdasarkan data anda.", "validation": {"ageMin": "<PERSON><PERSON> mesti sekurang-<PERSON><PERSON>ngnya 18", "ageMax": "<PERSON><PERSON> mesti kurang da<PERSON> 125", "heightMin": "Tinggi mesti sekurang-kurangnya 50cm", "heightMax": "Tinggi mesti kurang daripada 250cm", "weightMin": "<PERSON>rat mesti sekurang-kurangnya 30kg", "weightMax": "Berat mesti kurang daripada 500kg", "targetWeightMin": "<PERSON><PERSON> sasaran mesti sekurang-kurangnya 30kg", "targetWeightMax": "<PERSON><PERSON> sasaran mesti kurang daripada 500kg", "proteinMin": "Protein mesti 0 atau lebih", "carbsMin": "Karbohidrat mesti 0 atau lebih", "fatsMin": "Lemak mesti 0 atau lebih"}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "excess": "le<PERSON>han", "remaining": "baki"}, "specialistConstants": {"nutritionist": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> pakar tentang diet, pemakanan, dan tabiat pemakanan sihat", "featureName": "<PERSON><PERSON>"}, "cardiologist": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> dalam kesihatan jantung dan keadaan kardiovaskular", "featureName": "<PERSON><PERSON>"}, "neurologist": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kepada gang<PERSON>n ota<PERSON>, sara<PERSON>, dan sistem saraf", "featureName": "<PERSON><PERSON>"}, "oncologist": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> da<PERSON> kanser dan pilihan rawatan", "featureName": "<PERSON><PERSON>"}, "endocrinologist": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> dalam keadaan hormon dan gangguan metabolik", "featureName": "<PERSON><PERSON>"}}, "discoverCards": {"categories": {"nutrition": "<PERSON><PERSON><PERSON><PERSON>", "heartHealth": "<PERSON><PERSON><PERSON><PERSON>", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "<PERSON><PERSON><PERSON><PERSON>", "wellness": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>"}, "titles": {"vitaminB12Recovery": "<PERSON>rapa <PERSON> Dar<PERSON>ngan Vitamin B12", "vitaminDeficiencyGanglion": "Kekurangan <PERSON> Menyebabkan Kista Ganglion", "vitaminDeficiencyHairFall": "Kekurangan <PERSON> Menyebabkan Rambut Gugur", "vitaminWaters": "Adakah Air Vitamin Baik Untuk Anda", "cholesterolHeadaches": "<PERSON><PERSON><PERSON> Tinggi Menyebabkan Sakit Kepala", "cholesterolEyes": "<PERSON><PERSON><PERSON><PERSON>l Tinggi Yang <PERSON>", "diabetesHeadaches": "Bolehkah Diabetes Menyebabkan Sakit <PERSON>", "chestPainDrinking": "Mengapa Dada Sakit <PERSON>", "stressDizziness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bulimiaFace": "<PERSON><PERSON><PERSON><PERSON>", "kneeTwitch": "Mengapa Lutut Saya Terkejut", "noseTwitching": "Mengapa Hidung Terkejut", "piriformisVsSciatica": "Apakah Perbezaan Antara Sindrom Piriformis Vs Sciatica", "shoulderBladePinched": "<PERSON>", "shoulderPinched": "<PERSON>", "meniscusTorn": "Cara Menyembuhkan Meniskus Yang Koyak Secara Semula <PERSON>", "hydrateQuickly": "Cara Menghidrat <PERSON>", "periodConstipation": "Adakah Ia Normal Untuk Sembelit Se<PERSON>", "acneScars": "Cara <PERSON>gh<PERSON>kan Jerawat Secara Semula J<PERSON>", "perimenopausePregnancy": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "descriptions": {"vitaminB12Recovery": "Ke<PERSON><PERSON> garis masa pemulihan untuk kekurangan vitamin B12 dan ubat-ubatan berkesan untuk meningkatkan tahap tenaga anda.", "vitaminDeficiencyGanglion": "<PERSON><PERSON><PERSON> hubungan antara kekurangan vitamin dan perkembangan sista ganglion dalam badan.", "vitaminDeficiencyHairFall": "<PERSON><PERSON><PERSON> bagaimana kekurangan vitamin penting boleh menyebabkan rambut gugur dan apa yang boleh anda lakukan untuk mencegahnya.", "vitaminWaters": "Ungkap manfaat dan potensi kelemahan air vitamin sebagai sebahagian daripada pemakanan harian anda.", "cholesterolHeadaches": "<PERSON>ik<PERSON> kemungkinan hubungan antara paras kolesterol tinggi dan bermulanya sakit kepala.", "cholesterolEyes": "Ketahui bagaimana kolesterol tinggi boleh muncul di mata anda dan simptom yang perlu dipantau.", "diabetesHeadaches": "<PERSON><PERSON><PERSON> hubungan antara diabetes dan kejadian sakit kepala dalam kehidupan seharian.", "chestPainDrinking": "<PERSON><PERSON><PERSON> sebab-sebab di sebalik sakit dada selepas pengambilan minuman tertentu.", "stressDizziness": "<PERSON><PERSON>i bagaimana tekanan boleh memberi kesan kepada keseimbangan dan kesejahteraan keseluruhan anda, yang membawa kepada pening.", "bulimiaFace": "<PERSON><PERSON><PERSON> tanda-tanda fizikal bulimia, termas<PERSON> kesan pada penampilan wajah.", "kneeTwitch": "Siyasat punca-punca berpotensi di sebalik kekejangan lutut yang tidak disengajakan dan hubungannya dengan tekanan atau keletihan.", "noseTwitching": "<PERSON><PERSON><PERSON> tentang kemungkinan sebab-sebab kekejangan hidung dan kaitannya dengan kebimbangan atau faktor lain.", "piriformisVsSciatica": "Bandingkan simptom sindrom piriformis dan sciatica untuk lebih memahami keadaan anda.", "shoulderBladePinched": "Temui teknik berkesan untuk melegakan saraf yang tersepit di tulang belikat anda dan memulihkan mobiliti.", "shoulderPinched": "<PERSON><PERSON><PERSON> senaman dan regangan mudah untuk mengurangkan tekanan saraf di kawasan bahu.", "meniscusTorn": "<PERSON><PERSON><PERSON> kaedah semula jadi dan senaman untuk menyokong penyembuhan meniskus yang koyak.", "hydrateQuickly": "<PERSON><PERSON><PERSON> cara cepat dan berkesan untuk menghidrat semula dan mengekalkan penghidratan badan yang optimum.", "periodConstipation": "<PERSON><PERSON><PERSON> sebab-sebab di sebalik sembelit semasa haid dan ketahui ubat semula jadi.", "acneScars": "<PERSON><PERSON><PERSON> ubat semula jadi dan petua penjagaan kulit untuk mengurangkan penampilan parut jerawat dengan cepat.", "perimenopausePregnancy": "<PERSON><PERSON><PERSON> tentang perimenopause, per<PERSON><PERSON><PERSON><PERSON> kes<PERSON>n, dan apa yang diharapkan semasa peringkat kehidupan ini."}}}