import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView, 
  KeyboardAvoidingView, 
  Platform, 
  TouchableWithoutFeedback, 
  Keyboard,
  TouchableOpacity,
  StatusBar,
  Image,
  Linking,
  Dimensions,
  useWindowDimensions,
  ScrollView
} from 'react-native';
import { router } from 'expo-router';
import { useOtpStore } from '@/store/otp-store';
import { useAuthStore, useAuthLoading, useAuthError } from '@/store/auth-store';
import OtpInput from '@/components/auth/OtpInput';
import Button from '@/components/auth/Button';
import Logo from '@/components/common/logos/Logo';
import { colors } from '@/constants/colors';
import { ArrowLeft } from 'lucide-react-native';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { identifyUser, trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';
import { identifyClarityUser, trackClarityEvent } from '@/utils/clarity/clarity-utils';
import logger from '@/utils/logger/logger';
import { useTranslation } from 'react-i18next';
import { AppEventsLogger } from 'react-native-fbsdk-next';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

function VerifyScreen() {
  const { 
    phone, 
    verifyOtp, 
    requestOtp,
    isVerifyingOtp, 
    isRequestingOtp,
    verifyError,
    resetState
  } = useOtpStore();
  const { t } = useTranslation();

  const { setTokens, setUser } = useAuthStore();
  const isLoading = useAuthLoading();
  const authError = useAuthError();
  const [countdown, setCountdown] = useState(60);
  const [otp, setOtp] = useState('');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const { height } = useWindowDimensions();
  
  const scrollViewRef = useRef(null);
  const otpInputRef = useRef(null);
  const isMounted = useRef(true);
  
  // Memoize derived values
  const isSmallDevice = useMemo(() => height < 700, [height]);

  useEffect(() => {
    // Track component mount state
    isMounted.current = true;
    
    if (!phone) {
      router.replace('/auth/phone');
      return;
    }
    trackUserInteraction('Verfiy OTP page viewed')
    const timer = setInterval(() => {
      setCountdown(prev => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => {
      isMounted.current = false;
      clearInterval(timer);
    };
  }, [phone]);

  // Handle keyboard events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardVisible(true);
        
        // Only scroll if we have refs and they're not null
        if (scrollViewRef.current && otpInputRef.current) {
          setTimeout(() => {
            // Double-check refs still exist after timeout and component is mounted
            if (!scrollViewRef.current || !otpInputRef.current || !isMounted.current) {
              return;
            }
            
            try {
              (otpInputRef.current as any).measureLayout(
                scrollViewRef.current,
                (x: number, y: number, width: number, height: number) => {
                  // Check if component is still mounted before scrolling
                  if (!isMounted.current || !scrollViewRef.current) {
                    return;
                  }
                  
                  const keyboardHeight = e.endCoordinates.height;
                  const scrollViewHeight = SCREEN_HEIGHT - keyboardHeight;
                  const inputBottom = y + height;
                  
                  // Add some padding for the button below the input
                  const requiredSpace = inputBottom + moderateVerticalScale(120);
                  
                  // Only scroll if the input + button would be hidden
                  if (requiredSpace > scrollViewHeight && scrollViewRef.current) {
                    const scrollTo = requiredSpace - scrollViewHeight + moderateVerticalScale(20);
                    (scrollViewRef.current as any).scrollTo({
                      y: scrollTo,
                      animated: true,
                    });
                  }
                },
                () => {
                  // Fail silently - this can happen during navigation
                  logger.info('Failed to measure layout - likely during navigation');
                }
              );
            } catch (error) {
              // Catch any errors that might occur during measurement
              logger.info('Error measuring layout:', error);
            }
          }, 100);
        }
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        // Scroll back to top when keyboard hides
        if (scrollViewRef.current && isMounted.current) {
          (scrollViewRef.current as any).scrollTo({ y: 0, animated: true });
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleVerifyOtp = useCallback(async () => {
    if (otp.length !== 6) return;
    
    logger.info('Verifying OTP:', { phone, otp });
    trackUserInteraction('Verify OTP button clicked')
    
    const result = await verifyOtp(otp);
    if (result && result.success) {
      // Store both tokens from the API response
      await setTokens(result.accessToken, result.refreshToken);
      logger.info('Stored both access and refresh tokens');
      // Set the user with data from the response
      setUser({
        id: result.user?.id,
        phone: result.user?.phone || phone,
        tenant: 'default',
        isMobileOnboardingDone: result.user?.isMobileOnboardingDone 
      });

      trackClarityEvent('OTP_Verification_Success', {
        userId: result.user?.id,
        hasUserData: !!result.user
      });
      
      AppEventsLogger.logEvent('verified_otp');

      logger.info('Storing user data:', {
        id: result.user?.id,
        phone: result.user?.phone || phone
      });
      
      // Register the user's device with Mixpanel using their phone number
      await identifyUser(result.user?.phone || phone);
      
      // Register the user in Clarity
      identifyClarityUser(result.user?.phone || phone, {
        userId: result.user?.id,
        tenant: 'default'
      });

      // Add Mixpanel tracking
      trackUserInteraction('OTP Verified Successfully', {
        userId: result.user?.id,
        hasUserData: !!result.user
      });
      
      resetState();
      const { user } = useAuthStore.getState();
      if (result.user?.isMobileOnboardingDone || user?.isMobileOnboardingDone) {
        router.replace("/(tabs)/chat");
      } else {
        router.replace("/onboarding/PostAuthOnboarding");
      }
    }
  }, [otp, phone, verifyOtp, setTokens, setUser, resetState]);

  const handleResendOtp = useCallback(async () => {
    if (countdown > 0) return;
    
    logger.info('Resending OTP to:', phone);
    
    await requestOtp(phone);
    setCountdown(60);
  }, [countdown, phone, requestOtp]);

  const handleGoBack = useCallback(() => {
    // Reset the phone state before navigating back
    resetState();
    router.back();
  }, [resetState]);

  const formatPhone = useCallback((phoneNumber: string) => {
    if (!phoneNumber) return '';
    
    // Simple formatting for display
    return phoneNumber;
  }, []);

  const handleTermsPress = useCallback(() => {
    Linking.openURL("https://meetaugust.ai/terms");
  }, []);

  const handlePrivacyPress = useCallback(() => {
    Linking.openURL("https://meetaugust.ai/privacy");
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={0}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            <ScrollView
              ref={scrollViewRef}
              bounces={false}
              contentContainerStyle={[
                styles.scrollViewContent,
                keyboardVisible && styles.scrollViewContentWithKeyboard
              ]}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              scrollEnabled={keyboardVisible}
              overScrollMode="never"
            >
              {/* Top Section */}
              <View
                style={[
                  styles.topSection,
                  isSmallDevice && styles.topSectionSmall,
                ]}
              >
                <View style={styles.topBar}>
                  <TouchableOpacity
                    onPress={handleGoBack}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <ArrowLeft size={24} color={colors.white} />
                  </TouchableOpacity>
                  <Logo size="small" />
                  <Image
                    source={{
                      uri: "https://augustbuckets.blob.core.windows.net/mobile-app-assets/mobile-logo.png",
                    }}
                    style={styles.topRightLogo}
                    resizeMode="contain"
                  />
                </View>
                <View style={styles.headerTextContainer}>
                  <Text
                    style={[
                      styles.headerTitle,
                      isSmallDevice && styles.headerTitleSmall,
                    ]}
                    numberOfLines={2}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.title')}
                  </Text>
                  <Text 
                    style={styles.headerSubtitle}
                    numberOfLines={2}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.subtitle')}
                  </Text>
                  <Text 
                    style={styles.headerEmphasis}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.emphasis')}
                  </Text>
                </View>
              </View>

              {/* Bottom Section */}
              <View style={[
                styles.bottomSection,
                isSmallDevice && styles.bottomSectionSmall
              ]}>
                <View style={styles.formContainer}>
                  <Text 
                    style={styles.title}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                  >
                    One-Time Password
                  </Text>

                  <View ref={otpInputRef}>
                    <OtpInput length={6} onOtpComplete={setOtp} autoFocus={true} />
                  </View>

                  {(verifyError || authError) && (
                    <Text style={styles.errorText}>
                      {verifyError || authError}
                    </Text>
                  )}

                  <Button
                    title="Verify OTP"
                    onPress={handleVerifyOtp}
                    loading={isVerifyingOtp || isLoading}
                    disabled={otp.length !== 6}
                    style={styles.verifyButton}
                  />

                  <TouchableOpacity
                    onPress={handleResendOtp}
                    disabled={countdown > 0 || isRequestingOtp}
                    style={styles.resendButton}
                  >
                    <Text
                      style={[
                        styles.resendActionText,
                        (countdown > 0 || isRequestingOtp) &&
                          styles.resendDisabled,
                      ]}
                    >
                      {isRequestingOtp
                        ? t('auth.otp.sending')
                        : countdown > 0
                        ? t('auth.otp.countdown', { countdown })
                        : t('auth.otp.resend')}
                    </Text>
                  </TouchableOpacity>

                  <Text style={styles.otpMessage}>
                    {t('auth.otp.sentTo')} {formatPhone(phone)} {t('auth.otp.whatsappSuffix')}
                  </Text>
                  <Text style={styles.disclaimer}>
                    {t('auth.disclaimer.continuePrefix')} {" "}
                    <Text style={styles.link} onPress={handleTermsPress}>
                     {t('auth.disclaimer.termsOfService')}
                    </Text>{" "}
                    {t('auth.disclaimer.and')}{" "}
                    <Text style={styles.link} onPress={handlePrivacyPress}>
                    {t('auth.disclaimer.privacyPolicy')}
                    </Text>
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 0.95,
    backgroundColor: colors.primary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  scrollViewContentWithKeyboard: {
    paddingBottom: moderateVerticalScale(20),
  },
  topSection: {
    backgroundColor: colors.primary,
    paddingHorizontal: moderateScale(28),
    paddingTop: moderateVerticalScale(24),
    paddingBottom: moderateVerticalScale(90),
    alignItems: 'flex-start',
  },
  topSectionSmall: {
    paddingBottom: moderateVerticalScale(20),
    paddingTop: moderateVerticalScale(16),
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: moderateVerticalScale(16),
  },
  topRightLogo: {
    width: moderateScale(100),
    height: moderateVerticalScale(40),
    maxWidth: 100,
    maxHeight: 40,
  },
  headerTextContainer: {
    marginTop: 0,
    alignItems: 'flex-start',
    width: '100%',
  },
  headerTitle: {
    fontSize: moderateScale(28),
    fontWeight: '500',
    color: colors.white,
    textAlign: 'left',
    marginBottom: moderateVerticalScale(16),
    lineHeight: moderateScale(34),
  },
  headerTitleSmall: {
    fontSize: moderateScale(24),
    marginBottom: moderateVerticalScale(12),
  },
  headerSubtitle: {
    fontSize: moderateScale(14),
    color: colors.white,
    opacity: 0.9,
    textAlign: 'left',
    lineHeight: moderateScale(20),
  },
  headerEmphasis: {
    fontSize: moderateScale(14),
    fontStyle: 'italic',
    fontWeight: 'bold',
    color: colors.white,
    opacity: 0.9,
    textAlign: 'left',
    marginTop: moderateVerticalScale(4),
  },
  bottomSection: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 48,
    borderTopRightRadius: 48,
    overflow: 'hidden',
  },
  bottomSectionSmall: {
    // Keep same styling
  },
  formContainer: {
    paddingHorizontal: moderateScale(28),
    paddingTop: moderateVerticalScale(32),
    backgroundColor: colors.white,
    flex: 1,
    paddingBottom: moderateVerticalScale(28),
  },
  title: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: colors.gray[500],
    marginBottom: moderateVerticalScale(8),
    textAlign: 'center',
    lineHeight: moderateScale(22),
  },
  errorText: {
    color: colors.error,
    marginBottom: moderateVerticalScale(16),
    textAlign: 'center',
    fontSize: moderateScale(14),
  },
  verifyButton: {
    marginTop: moderateVerticalScale(16),
    width: '100%',
  },
  resendButton: {
    marginTop: moderateVerticalScale(24),
    padding: moderateScale(8),
    alignSelf: 'center',
  },
  resendActionText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: colors.primary,
  },
  resendDisabled: {
    opacity: 0.6,
  },
  otpMessage: {
    fontSize: moderateScale(14),
    color: colors.gray[600],
    marginTop: moderateVerticalScale(8),
    textAlign: 'center',
    lineHeight: moderateScale(20),
  },
  disclaimer: {
    fontSize: moderateScale(12),
    color: colors.gray[500],
    textAlign: 'center',
    marginTop: moderateVerticalScale(24),
    paddingHorizontal: moderateScale(20),
    lineHeight: moderateScale(16),
  },
  link: {
    color: colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
});

export default React.memo(VerifyScreen);