import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getNutritionData, updateNutritionData } from '@/services/nutritionService';
import { getMedicalData, updateMedicalData } from '@/services/medicalService';
import logger from '@/utils/logger/logger';

type UserDataState = {
  nutritionData: any | null;
  medicalData: any | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchUserData: () => Promise<void>;
  updateNutrition: (data: any) => Promise<void>;
  updateMedical: (data: any) => Promise<void>;
};

export const useUserDataStore = create<UserDataState>()(
  persist(
    (set, get) => ({
      nutritionData: null,
      medicalData: null,
      isLoading: false,
      error: null,
      
      fetchUserData: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // Fetch nutrition data
          const nutritionResponse = await getNutritionData();
          
          // Fetch medical data
          const medicalResponse = await getMedicalData();
          
          set({
            nutritionData: nutritionResponse?.success ? nutritionResponse.nutritionData : null,
            medicalData: medicalResponse?.success ? medicalResponse.medicalData : null,
            isLoading: false
          });
        } catch (error: any) {
          if (error?.response?.status !== 401) {
            logger.error('Error fetching user data:', error);
          }
          set({ 
            error: 'Failed to fetch user data. Please try again.',
            isLoading: false 
          });
        }
      },
      
      updateNutrition: async (data) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await updateNutritionData(data);
          if (response?.status=='success' || response?.status=='200') {
            // Refresh nutrition data after update
            const nutritionResponse = await getNutritionData();
            
            logger.info('nutritionResponse test:', nutritionResponse);
            const medicalResponse = await getMedicalData();
            logger.info('Fetched medical data after nutrition update:', medicalResponse);
            
            set({
              nutritionData: nutritionResponse?.success ? nutritionResponse.nutritionData : get().nutritionData,
              medicalData: medicalResponse?.success ? medicalResponse.medicalData : get().medicalData,
              isLoading: false
            });
          } else {
            throw new Error('Update failed');
          }
        } catch (error : any) {
          if (error?.response?.status !== 401) {
            logger.error('Error updating nutrition data:', error);
          }
          set({ 
            error: 'Failed to update nutrition data. Please try again.',
            isLoading: false 
          });
        }
      },
      
      updateMedical: async (data) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await updateMedicalData(data);
          logger.info('Medical data update response:', response);
          if (response?.status=="success") {
            // Refresh medical data after update
            const medicalResponse = await getMedicalData();
            const nutritionResponse = await getNutritionData();
            
            set({
              medicalData: medicalResponse?.success ? medicalResponse.medicalData : get().medicalData,
              nutritionData: nutritionResponse?.success ? nutritionResponse.nutritionData : get().nutritionData,
              isLoading: false
            });
          } else {
            throw new Error('Update failed');
          }
        } catch (error: any) {
          if (error?.response?.status !== 401) {
            logger.error('Error updating medical data:', error);
           }
          set({ 
            error: 'Failed to update medical data. Please try again.',
            isLoading: false 
          });
        }
      },
    }),
    {
      name: 'user-data-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
