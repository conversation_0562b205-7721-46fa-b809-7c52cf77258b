{"common": {"error": "Erro", "yes": "<PERSON>m", "no": "Não", "sometimes": "<PERSON>s vezes", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "next": "Se<PERSON><PERSON>", "loading": "A carregar...", "version": "v0.0.1.7"}, "welcome": "Inicie sessão para começar a conversar com o August", "notFound": {"title": "Ups!", "message": "Este ecrã não existe.", "goHome": "Ir para o ecrã inicial!"}, "library": {"title": "Biblioteca de Saúde"}, "specialists": {"title": "Especialistas", "description": "Consulte profissionais de saúde especializados para preocupações de saúde mais específicas. Escolha um especialista abaixo:", "generalPhysician": {"title": "Médico Generalista", "description": "Para preocupações de saúde gerais e cuidados primários."}, "nutritionist": {"title": "Nutricionista", "description": "Para aconselhamento sobre dieta, nutrição e controlo de peso."}, "cardiologist": {"title": "Cardiologista", "description": "Para preocupações relacionadas com o coração e saúde cardiovascular."}, "neurologist": {"title": "Neurologist<PERSON>", "description": "Para problemas cerebrais, da medula espinhal e do sistema nervoso."}, "oncologist": {"title": "Oncologista", "description": "Para preocupações e tratamentos relacionados com o cancro."}, "endocrinologist": {"title": "Endocrinologist<PERSON>", "description": "Para distúrbios hormonais e gestão da diabetes."}, "dermatologist": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Para condições de pele, cabelo e unhas."}, "psychiatrist": {"title": "Psiquiatra", "description": "Para preocupações de saúde mental e bem-estar psicológico."}}, "profile": {"title": "Perfil", "defaultName": "Convidado", "namePlaceholder": "Introduza o seu nome", "saving": "A guardar...", "noPhoneNumber": "Sem número de telefone", "loggingOut": "A sair...", "about": {"title": "Sobre", "description": "Saiba mais sobre o August"}, "whatsapp": {"title": "WhatsApp", "description": "Converse com o August no WhatsApp"}, "refer": {"title": "Referir", "description": "Gostou do August? Partilhe com os seus amigos"}, "deleteAccount": {"title": "Eliminar conta", "description": "Lamentamos vê-lo partir"}, "logout": {"title": "<PERSON><PERSON>", "description": "Volte em breve. Vamos sentir sua falta"}, "shareMessage": "👋Olá, Veja este aplicativo incrível que tenho usado!\n\n\n\n➡️Tenho usado o August para obter informações e orientações de saúde rápidas e confiáveis. É como ter um médico no seu bolso! Confira aqui:", "error": {"loadFailed": "Falha ao carregar os dados do utilizador", "fetchError": "Ocorreu um erro ao obter os dados do utilizador", "updateNameFailed": "Falha ao atualizar o nome", "updateNameError": "Ocorreu um erro ao atualizar o nome", "loadFoodData": "Falha ao carregar os dados alimentares", "logoutError": "Erro durante a sessão de encerramento:", "shareError": "Erro ao partilhar mensagens:"}}, "error": {"title": "Algo correu mal", "checkLogs": "Por favor, verifique os registos do seu dispositivo para mais detalhes.", "unknown": "<PERSON><PERSON>conhe<PERSON>", "unknownFile": "<PERSON><PERSON><PERSON> desconhecido", "unknownLine": "<PERSON><PERSON>a", "unknownColumn": "Coluna desconhecida"}, "auth": {"phone": {"selectCountry": "Selecionar País", "searchCountries": "Pesquisar países", "validation": {"invalidPhone": "Por favor, insira um número de telefone válido", "invalidDigits": "Por favor, insira um número de telefone válido (7-15 dígitos)"}}, "header": {"title": "Obtenha clareza sobre as suas preocupações de saúde instantaneamente e em privado", "subtitle": "Orientação cuidadosa. Sem pressa. Sem confusão.", "emphasis": "A<PERSON>as clareza."}, "greeting": "O<PERSON><PERSON> 👋", "phoneNumber": "Número de Telefone", "requestOTP": "Solicitar OTP", "otp": {"title": "Palavra-Passe Única", "verify": "Verificar OTP", "sending": "A enviar...", "countdown": "Reenviar OTP em {{countdown}}s", "resend": "Reenviar OTP", "sentTo": "OTP enviado para ", "whatsappSuffix": " no WhatsApp"}, "disclaimer": {"prefix": "Ao inscrever-se, aceita os nossos ", "continuePrefix": "<PERSON>o continuar, aceita os nossos ", "termsOfService": "Termos de Serviço", "and": " e ", "privacyPolicy": "Política de Privacidade", "whatsappConsent": ", e consente em receber atualizações e lembretes nossos via WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Bem-vindo à August!", "buttonText": "<PERSON><PERSON><PERSON> começar"}}, "postAuth": {"step1": {"title": "Ol<PERSON>!", "subtitle": "Eu sou a August 👋", "description": "Pense em mim como o canto confortável do seu\naparelho onde explora todas as suas\ncuriosidades de saúde.", "subdescription": "Sinta-se à vontade para perguntar tudo o que lhe vier à mente.\nSem julgamentos, sem limites!", "placeholder": "Como devo chamá-lo?"}, "step2": {"title": "<PERSON><PERSON><PERSON>, {{userName}},", "subtitle": "Aqui está o que posso fazer:", "features": {"health": {"title": "Responda às suas", "subtitle": "Perguntas de saúde"}, "nutrition": {"title": "Acompanhe esses", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Relatórios"}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON>", "careful": "Cuidadoso", "accurate": "Preciso"}, "features": {"symptoms": {"title": "Verifique os seus sintomas", "description": "Tenho náuseas há uma semana. O que me está a acontecer?"}, "prescriptions": {"title": "<PERSON><PERSON><PERSON> as suas receitas", "description": "Carregue e entenda as receitas como um médico."}, "medicine": {"title": "Conhece o seu medicamento", "description": "A Metformina para a minha SOP interage com as minhas pílulas de TDAH?"}, "plans": {"title": "Obtenha planos personalizados", "description": "Pode dar-me um plano de nutrição e fitness para reduzir os meus níveis de HbA1c?"}}, "buttons": {"getStarted": "<PERSON><PERSON><PERSON>", "next": "Se<PERSON><PERSON>"}, "errors": {"nameRequired": "Por favor, insira o seu nome"}}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Descobrir", "nutrition": "Nutrição", "personalize": "Personalizar"}, "chat": {"nav": {"title": "Agosto"}, "me": "Eu", "augustName": "Agosto", "input": {"placeholder": "Pergunte ao Agosto...", "disclaimer": "O Agosto pode cometer erros. Confirme com um médico"}, "list": {"loadingMessages": "A carregar mensagens...", "noMessages": "Ainda não há mensagens. Comece uma conversa!"}, "connection": {"offlineMessage": "Parece que está offline. Reconnecte-se para enviar mensagens.", "connecting": "A conectar...", "tryAgain": "Tentar novamente"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON>", "speakInHindi": "Fale em hindi", "notFeelingWell": "<PERSON><PERSON> me sinto bem", "whatIsMyBMI": "Qual é o meu IMC?", "nutritionAdvice": "Conselhos nutricionais", "sleepBetter": "<PERSON><PERSON><PERSON> me<PERSON>"}, "citations": {"referenceText": "Para mais detalhes sobre esta conversa, consulte:"}, "actions": {"copiedToClipboard": "Copiado para a área de transferência", "copied": "Copiado"}, "share": {"introText": "👋 Ol<PERSON>, veja a conversa que tive com o Agosto:\n\n", "downloadText": "\n\n➡️ Faça o download do Agosto para conversar com o seu simpático assistente de saúde de IA:\n"}}, "discover": {"nav": {"title": "Descobrir"}, "categories": {"all": "Todos", "heartHealth": "Saúde <PERSON>", "nutrition": "Nutrição", "mentalHealth": "<PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "Bem-estar"}, "cards": {"empty": "Não há cartões disponíveis para esta categoria"}, "sections": {"features": "Funcionalidades"}, "features": {"healthLibrary": {"title": "Biblioteca de Saúde", "description": "Acesso a informações médicas fidedignas, confiáveis e atualizadas, totalmente gratuito."}, "nutritionTracker": {"title": "Rast<PERSON>or Nutric<PERSON>", "description": "Já se perguntou se poderia simplesmente carregar uma foto da sua comida e acompanhar todas as suas metas nutricionais? O Agosto pode fazer exatamente isso!"}, "multilingualSupport": {"title": "Suporte Multilingue", "description": "Pode comunicar com o Agosto em qualquer idioma com que se sinta confortável! O Agosto está sempre aqui para o ouvir, apoiar e responder sempre que precisar.", "samplePrompt": "Fale em hindi"}, "labReportAnalysis": {"title": "Análise de Relatórios Laboratoriais", "description": "Quando fala com o Agosto sobre os seus relatórios laboratoriais, obtém precisão extrema. O Agosto processou mais de 4,7 milhões de relatórios com uma precisão de extração de biomarcadores de 98,4%."}}}, "nutrition": {"nav": {"title": "Nutrição"}, "meals": {"title": "Suas Refeições", "subtitle": "Toque para ver os macros de cada refeição"}, "upload": {"loading": "A carregar imagem..."}, "defaultFoodName": "Alimento", "today": "Hoje", "unknownTime": "<PERSON>ra desconhecida", "calories": "🔥 Calorias", "proteins": "🥩 Proteínas", "carbs": "🍞 Hidratos de Carbono", "sugars": "🍬 Açúcares", "fat": "🥑 Gord<PERSON>", "caloriesLabel": "Caloria<PERSON>", "proteinLabel": "<PERSON><PERSON><PERSON><PERSON>", "carbohydratesLabel": "Hidratos de Carbono", "fatLabel": "Gordura", "sugarLabel": "<PERSON><PERSON><PERSON><PERSON>", "tips": "Dicas:", "macroBreakdown": "Distribuição de Macronutrientes", "noMacroData": "Não há dados de macronutrientes disponíveis para este alimento.", "disclaimer": "Apenas para fins educativos. Saiba mais", "disclaimerLink": "aqui", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Qual é o seu género?", "subtitle": "Isto será usado para calibrar o seu plano personalizado.", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "other": "Outro"}, "age": {"title": "Qual é a sua idade?", "subtitle": "Isto será usado para calcular as suas necessidades diárias."}, "measurements": {"title": "Altura e Peso", "subtitle": "Por favor, insira a sua altura em centímetros e o seu peso em quilogramas."}, "activity": {"title": "Nível de Atividade", "subtitle": "Com que frequência faz exercício?", "none": "<PERSON><PERSON>", "moderate": "Moderado", "high": "<PERSON><PERSON><PERSON>"}, "goal": {"title": "Objetivo de Peso", "subtitle": "O que gostaria de alcançar?", "increase": "Aumentar", "maintain": "<PERSON><PERSON>", "decrease": "<PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Peso Alvo", "subtitle": "Qual é o seu peso alvo em quilogramas?"}, "setup": {"title": "A Configurar o Seu Plano", "subtitle": "Por favor, aguarde enquanto preparamos o seu plano nutricional."}, "review": {"title": "Rever o Seu Plano", "subtitle": "Rever e personalizar o seu plano nutricional."}, "height": {"label": "Altura (cm)"}, "weight": {"label": "Peso (kg)"}}, "error": {"updateFailed": "Falha ao atualizar os dados nutricionais. Por favor, tente novamente.", "parsingError": "Erro a analisar os dados alimentares:", "fetchReportsFailed": "Falha ao obter dados de relatórios. Por favor, tente novamente.", "missingReportId": "O ID do relatório está em falta"}}, "personalize": {"nav": {"title": "Personalizar"}, "button": {"saving": "A Guardar", "review": "<PERSON>er", "saveNext": "Guardar & Seguinte"}}, "basicInfo": {"title": "Vamos conhe<PERSON>-lo melhor", "subtitle": "Esta informação ajuda-nos a personalizar as suas recomendações de saúde", "age": {"question": "Qual a sua idade?", "placeholder": "Insira a sua idade"}, "sex": {"question": "Qual é o seu sexo?", "placeholder": "Selecione o seu sexo", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "other": "Outro"}, "height": {"question": "Qual é a sua altura? (cm)", "placeholder": "Insira a sua altura"}, "weight": {"question": "Qual é o seu peso? (kg)", "placeholder": "Insira o seu peso"}}, "lifestyle": {"title": "Os Seus Hábitos de Vida", "subtitle": "Compreender os seus hábitos diários ajuda-nos a fornecer melhores recomendações", "diet": {"question": "Que tipo de dieta segue?", "placeholder": "Selecione a sua dieta", "vegetarian": "Vegetariana", "nonVegetarian": "Não <PERSON>", "vegan": "Vegan", "pescatarian": "Pescatariana", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Faz exercício regularmente?"}, "drinking": {"question": "Consome álcool?"}, "smoking": {"question": "Fu<PERSON>?"}, "sleep": {"question": "Quantas horas dorme por noite?", "value": "{{sleep}} horas"}, "hydration": {"question": "Quantas chávenas de água bebe diariamente?", "value": "{{hydration}} ch<PERSON><PERSON>as ({{liters}}L)"}}, "allergies": {"title": "Tem alguma alergia?", "subtitle": "<PERSON><PERSON><PERSON> as suas alergias ajuda-nos a fornecer recomendaç<PERSON>es mais seguras", "allergyIndex": "Alergia {{index}}", "name": {"question": "A que é alérgico?", "placeholder": "Introduza a alergia (ex: Amendoins, Poeira)"}, "severity": {"question": "Quão grave é esta alergia?", "placeholder": "Selecione a gravidade", "mild": "Ligeira", "moderate": "Moderada", "severe": "Grave"}, "addButton": "Adicionar outra alergia", "noAllergiesButton": "Não tenho alergias"}, "medications": {"title": "Medicamentos e Suplementos", "subtitle": "Informe-nos sobre quaisquer medicamentos ou suplementos que esteja a tomar atualmente", "medicationIndex": "Medicamento {{index}}", "name": {"label": "Nome do Medicamento", "placeholder": "Introduza o nome do medicamento"}, "startDate": {"question": "Quando começou a tomá-lo?", "placeholder": "Selecione a data"}, "type": {"label": "Tipo de Medicamento", "shortTerm": "Curto Prazo", "longTerm": "Longo Prazo"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Quantidade"}, "unit": {"label": "Unidade"}, "frequency": {"label": "Frequência", "placeholder": "Vezes", "perDay": "por dia", "perWeek": "por semana", "perMonth": "por mês", "perYear": "por ano"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "gotas", "tsp": "tsp", "tbsp": "tbsp", "cups": "<PERSON><PERSON><PERSON><PERSON>"}, "addButton": "Adicionar outro medicamento", "noMedicationsButton": "Não tomo nenhum medicamento", "calendar": {"title": "Selecione a Data de Início"}}, "conditions": {"title": "Condições Médicas", "subtitle": "Informe-nos sobre quaisquer condições médicas que tenha ou tenha tido no passado", "conditionIndex": "Condição {{index}}", "name": {"label": "Nome da Condição", "placeholder": "Introduza a condição (ex: As<PERSON>, etc)"}, "since": {"question": "Desde quando tem esta condição?", "placeholder": "Selecione a data"}, "current": {"question": "Está atualmente a incomodá-lo?"}, "medicated": {"question": "Está a tomar algum medicamento para isto?"}, "addButton": "Adicionar outra condição", "noConditionsButton": "Não tenho nenhuma condição médica", "calendar": {"title": "Selecione a Data"}}, "reproductive": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Esta informação ajuda-nos a fornecer recomendações de saúde mais personalizadas", "menstruação": {"question": "Já teve a menstruação?", "detailsTitle": "<PERSON>al<PERSON> da Menstruação", "regularity": {"question": "Qual a regularidade do seu ciclo?", "regular": "Regular", "irregular": "Irregular", "notSure": "<PERSON><PERSON> sei"}, "cycleLength": {"label": "Comprimento médio do ciclo (dias)", "placeholder": "Introduza o comprimento do ciclo"}, "flowDays": {"label": "Dias de fluxo: {{flowDays}}", "min": "1 dia", "max": "15 dias"}, "padsPerDay": {"label": "Compressa/Tampão por dia: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Quaisquer sintomas durante o seu período?", "placeholder": "Introduza os sintomas (ex: cólicas, dores de cabeça)"}}, "childbirth": {"question": "J<PERSON> teve filhos?", "detailsTitle": "Detalhes do Parto", "children": {"label": "Número de <PERSON>hos"}, "pregnancies": {"label": "Número de Gravidezes"}, "complications": {"question": "Quaisquer complicações durante a gravidez ou parto?", "placeholder": "Introduza complicações (se houver)"}}}, "review": {"title": "Rever a sua Informação", "subtitle": "Por favor, reveja a informação que forneceu antes de submeter", "sections": {"basicInfo": "Informação Básica", "lifestyle": "Estilo de Vida", "allergies": "Alergias", "medications": "Medicamentos e Suplementos", "conditions": "Condições Médicas", "reproductive": "<PERSON><PERSON><PERSON>", "menstruationDetails": "<PERSON>al<PERSON> da Menstruação", "childbirthDetails": "Detalhes do Parto"}, "fields": {"age": "Idade:", "sex": "Sexo:", "height": "Altura:", "weight": "Peso:", "diet": "Dieta:", "exercise": "Exer<PERSON><PERSON><PERSON>:", "drinking": "Álcool:", "smoking": "Tabaco:", "sleep": "Sono:", "hydration": "Hidratação:", "allergyIndex": "Alergia {{index}}:", "dose": "Dose:", "frequency": "Frequência:", "type": "Tipo:", "since": "Desde:", "currentlyActive": "Ativo atualmente:", "takingMedication": "A tomar medicação:", "hasMenstruated": "<PERSON><PERSON> teve a menstruação:", "regularity": "Regularidade:", "cycleLength": "Comprimento do ciclo:", "flowDays": "Dias de fluxo:", "padsPerDay": "Compressas/Tampões por dia:", "hasChildbirth": "<PERSON><PERSON> teve filhos:", "children": "Filhos:", "pregnancies": "Gravidezes:"}, "notProvided": "Não fornecido", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} horas por dia", "hydration": "{{hydration}} ch<PERSON><PERSON><PERSON> ({{liters}}L) por dia", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} dias"}, "noData": {"allergies": "Não foram fornecidas alergias", "medications": "Não foram fornecidos medicamentos", "conditions": "Não foram fornecidas condições médicas"}, "submitButton": "Submeter Informação"}, "success": {"title": "Informação Atualizada!", "message": "Obrigado por fornecer a sua informação de saúde. Vamos usar isto para personalizar a sua experiência e fornecer melhores recomendações.", "benefits": {"insights": "Perspetivas de saúde personalizadas", "reminders": "<PERSON><PERSON>s lembre<PERSON> de medicação", "recommendations": "Recomendações de saúde personalizadas"}, "continueButton": "Continuar para o <PERSON>el"}, "permissions": {"microphonePermissionDenied": "Permissão do microfone negada", "microphoneAccessDescription": "A August precisa de acesso ao seu microfone para gravar áudio e enviar notas de voz", "permissionDenied": "Permissão Negada", "cameraPermissionRequired": "Precisamos de permissões de câmara para que isto funcione!", "mediaLibraryPermissionRequired": "Precisamos de permissões da biblioteca de media para que isto funcione!"}, "voiceRecording": {"recordingTooLong": "Gravação Demasiado Longa", "recordingTooLongMessage": "As gravações de voz devem ter menos de 5 minutos. Por favor, grave uma mensagem mais curta."}, "errors": {"uploadFailed": "Erro no Upload", "voiceUploadFailed": "Não foi possível carregar a gravação de voz.", "voiceRecordingFailed": "Falha ao enviar gravação de voz", "failedToStopRecording": "Falha ao parar a gravação", "photoUploadFailed": "Não foi possível carregar a fotografia.", "failedToTakePhoto": "Falha ao tirar fotografia", "imageUploadFailed": "Não foi possível carregar a imagem: {{fileName}}", "failedToPickImage": "Falha ao selecionar imagem", "documentUploadFailed": "Não foi possível carregar o documento: {{fileName}}", "failedToPickDocument": "Falha ao selecionar documento"}, "audioPlayer": {"downloadingAudio": "A descarregar áudio...", "loadingAudio": "A carregar áudio..."}, "mediaProcessing": {"processingFile": "A processar o seu ficheiro", "uploadingSecuring": "A carregar e a proteger o ficheiro...", "analyzingContent": "A analisar o conteúdo do documento...", "extractingInfo": "A extrair informação chave...", "processingInsights": "A processar informações...", "preparingResponse": "A preparar resposta detalhada...", "finalizingResponse": "A finalizar resposta..."}, "attachments": {"voiceMessage": "Mensagem de Voz", "image": "[IMAGEM]", "pdf": "[PDF]", "voice": "[NOTA DE VOZ]"}, "pdf": {"loadingPdf": "A carregar PDF..."}, "dateTime": {"yesterday": "Ontem, "}, "navbar": {"defaultTitle": "august", "selectedCount": "selecionados"}, "mediaUpload": {"photoLibrary": "Biblioteca de Fotos", "takePhoto": "<PERSON><PERSON><PERSON>", "chooseFile": "<PERSON><PERSON><PERSON><PERSON>"}, "comingSoon": {"title": "Em breve!", "description": " está atualmente em desenvolvimento. Aguarde atualizações!", "buttonText": "Entendido!"}, "clipboard": {"success": "Link copiado para a área de transferência"}, "mediaPhotos": {"emptyState": "Ainda não existem entradas."}, "foodDetail": {"defaultFoodName": "Alimento", "nutrition": {"totalCalories": "<PERSON><PERSON><PERSON>", "proteins": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Hidratos de Carbono", "fat": "Gordura", "sugars": "Açúcares", "fibers": "Fi<PERSON><PERSON>"}}, "reports": {"defaultTitle": "<PERSON><PERSON>", "defaultFoodName": "Alimento", "defaultName": "Documento", "openButton": "Abrir numa visualizadora externa", "biomarker": {"headerBiomarker": "Biomarcador", "headerValue": "Valor", "headerRefRange": "Intervalo de Referência", "headerStatus": "Estado"}, "noData": "Sem dados de biomarcadores disponíveis"}, "setup": {"title": "Estamos a configurar tudo para si", "inProgress": "Em progresso...", "progressMessages": {"0": "A calcular as calorias di<PERSON>rias", "1": "A otimizar a divisão de macros", "2": "A criar um plano de refeições", "3": "A calcular a pontuação de saúde", "4": "A finalizar a configuração"}, "checklistItems": {"0": "A analisar os seus dados de saúde", "1": "A calcular o plano nutricional ótimo", "2": "A personalizar as suas recomendações", "3": "A criar as suas sugestões de refeições", "4": "A finalizar a sua configuração"}}, "foodEntry": {"emptyState": "Ainda não há entradas de comida. Tire uma foto da sua refeição para a adicionar!"}, "nutritionReview": {"congratulations": "Parabéns!", "subtitle": "O seu plano nutricional personalizado está pronto", "submitButton": "Vamos começar!", "dailyTargetsTitle": "As Suas Metas Diárias de Nutrição", "macroLabels": {"calories": "Caloria<PERSON>", "carbs": "Hidratos de carbono", "protein": "<PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Como alcançar os seus objetivos:", "healthScores": "Use pontuações de saúde para melhorar a sua rotina", "trackFood": "Acompanhe a sua ingestão alimentar consistentemente", "followCalories": "Siga a sua recomendação diária de calorias", "balanceMacros": "Equilibre a sua ingestão de hidratos de carbono, proteínas e gorduras."}}, "editModal": {"titlePrefix": "<PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Próximo"}, "processing": {"stages": {"scanning": "A digitalizar comida...", "identifying": "A identificar ingredientes...", "extracting": "A extrair nutrientes...", "finalizing": "A finalizar resultados..."}, "error": {"defaultMessage": "Nenhum alimento de<PERSON>o", "subtitle": "Experimente um ângulo diferente"}, "retakeButton": "Toque para tirar outra foto", "notification": "Avisámo-lo quando estiver concluído!"}, "chart": {"title": "Acompanhamento Nutricional ao Longo do Tempo", "selectNutrient": "Selecionar Nutriente:", "emptyState": "Ainda não há dados nutricionais disponíveis.", "dropdown": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Hidratos de carbono", "fat": "<PERSON><PERSON>", "sugars": "Açucares"}}, "foodModal": {"defaultName": "<PERSON><PERSON>", "defaultDate": "Hoje", "defaultTime": "Tempo desconhecido", "saveChanges": "Guardar alterações", "error": {"title": "Erro", "message": "Falha ao atualizar os dados nutricionais. Por favor, tente novamente."}, "nutrition": {"calories": "🔥 Calorias", "proteins": "🥩 Proteínas", "carbs": "pão 🍞 hidratos de carbono", "sugars": "🍬 Açúcares", "fat": "🥑 Gord<PERSON>"}, "macroBreakdown": {"title": "Distribuição de Macronutrientes", "noData": "Não há dados disponíveis sobre macronutrientes para este alimento."}, "macroLabels": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Hidratos de carbono", "fat": "<PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "save": "Guardar", "saving": "A guardar...", "enterValue": "Introduza valor", "notSet": "<PERSON><PERSON> definido", "age": "<PERSON><PERSON>", "heightCm": "Altura (cm)", "weightKg": "Peso (kg)", "targetWeight": "Peso Alvo", "nutritionTargets": "Objetivos Nutricionais", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Hidratos de carbono", "fats": "<PERSON><PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Introduza valores ou deixe em branco para cálculo automático.", "autoCalculateNote": "As macros são calculadas automaticamente com base nos seus dados.", "validation": {"ageMin": "A idade deve ser no mínimo 18 anos.", "ageMax": "A idade deve ser inferior a 125", "heightMin": "A altura deve ter pelo menos 50cm", "heightMax": "A altura deve ser inferior a 250 cm", "weightMin": "O peso deve ser no mínimo 30 kg", "weightMax": "O peso deve ser inferior a 500 kg", "targetWeightMin": "O peso alvo deve ser pelo menos 30 kg", "targetWeightMax": "O peso alvo deve ser inferior a 500 kg", "proteinMin": "Proteína deve ser 0 ou mais", "carbsMin": "Os hidratos de carbono devem ser 0 ou mais.", "fatsMin": "Gorduras devem ser 0 ou mais"}}, "tracker": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Hidratos de carbono", "fat": "<PERSON><PERSON>", "excess": "excesso", "remaining": "restantes"}, "specialistConstants": {"nutritionist": {"name": "Nutricionista", "description": "Conselhos especializados sobre dieta, nutrição e hábitos alimentares saudáveis", "featureName": "Especialista em Nutrição"}, "cardiologist": {"name": "Cardiologista", "description": "Especializado em saúde cardíaca e condições cardiovasculares", "featureName": "Especialista em Cardiologia"}, "neurologist": {"name": "Neurologist<PERSON>", "description": "Focado em distúrbios do cérebro, medula espinhal e sistema nervoso", "featureName": "Especialista em Neurologia"}, "oncologist": {"name": "Oncologista", "description": "Especializado em diagnóstico e opções de tratamento para cancro", "featureName": "Especialista em Oncologia"}, "endocrinologist": {"name": "Endocrinologist<PERSON>", "description": "Especialista em condições hormonais e distúrbios metabólicos", "featureName": "Especialista em Endocrinologia"}}, "discoverCards": {"categories": {"nutrition": "Nutrição", "heartHealth": "Saúde <PERSON>", "mentalHealth": "<PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "Bem-estar"}, "titles": {"vitaminB12Recovery": "Quanto tempo leva a recuperar de uma deficiência de vitamina B12?", "vitaminDeficiencyGanglion": "Qual deficiência de vitamina causa cistos de gânglio?", "vitaminDeficiencyHairFall": "Qual deficiência de vitamina causa queda de cabelo?", "vitaminWaters": "As águas vitaminadas são boas para a saúde?", "cholesterolHeadaches": "O colesterol alto causa dores de cabeça?", "cholesterolEyes": "Quais são os sintomas de colesterol alto que podem ser vistos nos olhos?", "diabetesHeadaches": "A diabetes pode causar dores de cabeça?", "chestPainDrinking": "Por que a minha caixa torácica dói depois de beber?", "stressDizziness": "O stress pode causar tonturas?", "bulimiaFace": "O que é a face da bulimia?", "kneeTwitch": "Por que o meu joelho treme?", "noseTwitching": "Por que ocorre o tremor no nariz?", "piriformisVsSciatica": "<PERSON><PERSON><PERSON> <PERSON><PERSON> as diferenças entre a síndrome do piriforme e a ciática?", "shoulderBladePinched": "Como aliviar um nervo comprimido na omoplata?", "shoulderPinched": "Como aliviar um nervo comprimido no ombro?", "meniscusTorn": "Como curar um menisco rasgado naturalmente?", "hydrateQuickly": "Como hidratar-me rapidamente?", "periodConstipation": "É normal ter prisão de ventre durante a menstruação?", "acneScars": "Como se livrar das cicatrizes de acne naturalmente em uma semana?", "perimenopausePregnancy": "É possível engravidar durante a perimenopausa?"}, "descriptions": {"vitaminB12Recovery": "Descubra a linha temporal de recuperação da deficiência de vitamina B12 e remédios eficazes para aumentar os seus níveis de energia.", "vitaminDeficiencyGanglion": "Explore a ligação entre as deficiências de vitaminas e o desenvolvimento de cistos de gânglio no corpo.", "vitaminDeficiencyHairFall": "Saiba como a falta de vitaminas essenciais pode levar à queda de cabelo e o que pode fazer para a prevenir.", "vitaminWaters": "Descubra os benefícios e potenciais inconvenientes das águas vitaminadas como parte da sua nutrição diária.", "cholesterolHeadaches": "Examine a possível ligação entre níveis elevados de colesterol e o aparecimento de dores de cabeça.", "cholesterolEyes": "Saiba como o colesterol alto pode manifestar-se nos seus olhos e que sintomas deve observar.", "diabetesHeadaches": "Investigue a relação entre a diabetes e a ocorrência de dores de cabeça na vida diária.", "chestPainDrinking": "Explore as razões por detrás da dor no peito após o consumo de certas bebidas.", "stressDizziness": "Analise como o stress pode afetar o seu equilíbrio e bem-estar geral, levando a tonturas.", "bulimiaFace": "Compreenda os sinais físicos da bulimia, incluindo os efeitos na aparência facial.", "kneeTwitch": "Investigue as potenciais causas por detrás das contrações involuntárias do joelho e a sua relação com o stress ou fadiga.", "noseTwitching": "Saiba sobre as possíveis razões para a contração do nariz e a sua ligação à ansiedade ou outros fatores.", "piriformisVsSciatica": "Compare os sintomas da síndrome do piriforme e da ciática para melhor compreender a sua condição.", "shoulderBladePinched": "Descubra técnicas eficazes para aliviar um nervo comprimido na sua omoplata e restaurar a mobilidade.", "shoulderPinched": "Aprenda exercícios e alongamentos simples para aliviar a compressão nervosa na área do ombro.", "meniscusTorn": "Explore métodos naturais e exercícios para apoiar a cicatrização de um menisco rasgado.", "hydrateQuickly": "Descubra formas rápidas e eficazes de se reidratar e manter a hidratação corporal ideal.", "periodConstipation": "<PERSON><PERSON><PERSON><PERSON> as razões por detrás da prisão de ventre durante a menstruação e aprenda remédios naturais.", "acneScars": "Descubra remédios naturais e dicas de cuidados de pele para reduzir a aparência de cicatrizes de acne rapidamente.", "perimenopausePregnancy": "Saiba mais sobre a perimenopausa, considerações sobre a fertilidade e o que esperar durante esta fase da vida."}}}