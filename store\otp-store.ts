import { create } from 'zustand';
import { OtpState } from '@/types/auth';
import { requestOtp, verifyOtp } from '@/services/auth-service';
import logger from '@/utils/logger/logger';
import { usePhoneStore } from './phoneStore';
import { useMemo } from 'react';

export const useOtpStore = create<OtpState>((set, get) => ({
  phone: '',
  isRequestingOtp: false,
  isVerifyingOtp: false,
  requestError: null,
  verifyError: null,

  setPhone: (phone: string) => set({ phone }),

  requestOtp: async (phone: string) => {
    try {
      set({ isRequestingOtp: true, requestError: null });
      logger.info('OTP Store: Requesting OTP for', phone);
      
      const result = await requestOtp(phone);
      
      if (!result.success) {
        logger.error(`OTP Store: Request failed for phone: ${JSON.stringify({ phone, error: result.error })}`);
        // Handle rate limit error specifically
        if (result.status === 429) {
          set({
            requestError: 'Too many attempts. Please try again in a few minutes.',
            isRequestingOtp: false
          });
          return false;
        }
        if (result.error ==="Invalid WhatsApp number") {
          set({ 
            requestError: 'Invalid WhatsApp number', 
            isRequestingOtp: false 
          });
          return false;
        }

        set({ 
          requestError: 'Failed to request OTP', 
          isRequestingOtp: false 
        });
        return false;
      }

      logger.info('OTP Store: Request successful');
      set({ 
        phone, 
        isRequestingOtp: false,
        requestError: null
      });
      return true;
    } catch (error) {
      logger.error('OTP Store: Request error', JSON.stringify(error));
      set({ 
        requestError: 'Network error. Please try again.', 
        isRequestingOtp: false 
      });
      return false;
    }
  },

  verifyOtp: async (otp: string) => {
    try {
      set({ isVerifyingOtp: true, verifyError: null });
      const { phone } = get();
      
      logger.info('OTP Store: Verifying OTP', { phone, otp });
      
      
      const result = await verifyOtp(phone, otp);
      
      if (!result.success) {
        logger.error('OTP Store: Verification failed for phone: ', JSON.stringify({ phone, error: result.error }));
        set({ 
          verifyError: result.error || 'Invalid OTP', 
          isVerifyingOtp: false 
        });
        return false;
      }
      // Store the phone number in the phone store if available in the response
      if (result.user?.phone) {
        usePhoneStore.getState().setPhoneNumber(result.user.phone);
        logger.info('OTP Store: Stored phone number in phone store:', result.user.phone);
      }

      set({ 
        isVerifyingOtp: false,
        verifyError: null
      });
      
      // Add more detailed logging
      logger.info('OTP verification result details:', {
        success: result.success,
        hasAccessToken: !!result.accessToken,
        hasRefreshToken: !!result.refreshToken,
        user: result.user
      });
      
      // Return the tokens, and user data for the auth store to use
      return {
        success: true,
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        user: result.user
      };
    } catch (error) {
      logger.error('OTP Store: Verification error',JSON.stringify(error));
      set({ 
        verifyError: 'Network error. Please try again.', 
        isVerifyingOtp: false 
      });
      return false;
    }
  },

  resetState: () => set({
    phone: '',
    isRequestingOtp: false,
    isVerifyingOtp: false,
    requestError: null,
    verifyError: null
  })
}));

// Custom hooks for memoized selectors
export const usePhone = () => {
  const phone = useOtpStore(state => state.phone);
  return useMemo(() => phone, [phone]);
};

export const useOtpRequestStatus = () => {
  const isRequestingOtp = useOtpStore(state => state.isRequestingOtp);
  const requestError = useOtpStore(state => state.requestError);
  
  return useMemo(() => ({
    isRequestingOtp,
    requestError
  }), [isRequestingOtp, requestError]);
};

export const useOtpVerifyStatus = () => {
  const isVerifyingOtp = useOtpStore(state => state.isVerifyingOtp);
  const verifyError = useOtpStore(state => state.verifyError);
  
  return useMemo(() => ({
    isVerifyingOtp,
    verifyError
  }), [isVerifyingOtp, verifyError]);
};
