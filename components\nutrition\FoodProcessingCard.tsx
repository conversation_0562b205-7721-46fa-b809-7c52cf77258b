import React, { useState, useEffect, useRef, useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  Dimensions,
  Animated,
  Easing
} from 'react-native';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
import { colors } from '@/constants/colors';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { X } from 'lucide-react-native';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      width: "100%",
      marginBottom: moderateVerticalScale(16),
      height: moderateVerticalScale(140),
    },
    card: {
      flexDirection: "row",
      backgroundColor: theme.colors.secondary[50],
      borderRadius: moderateScale(theme.radii.lg),
      overflow: "hidden",
      borderColor : colors.gray[300],
      borderWidth :moderateScale(1),
    },
    imageContainer: {
      width: "40%",
      position: "relative",
      height: "100%", // Take full height of card
    },
    image: {
      width: "100%",
      height: "100%",
    },
    imageDimOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    focusCircle: {
      width: moderateScale(50),
      height: moderateScale(50),
      borderRadius: moderateScale(theme.radii["3xl"]),
      borderWidth: 2,
      borderColor: theme.colors.secondary[50],
    },
    contentContainer: {
      flex: 1,
      padding: moderateScale(12),
      justifyContent: "center",
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: moderateVerticalScale(8),
    },
    title: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      color: colors.black,
      marginBottom: moderateVerticalScale(16),
    },
    errorTitle: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      color: theme.colors.error,
      flex: 1,
    },
    closeButton: {
      padding: moderateScale(4),
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginTop: moderateVerticalScale(8),
    },
    retakeButton: {
      marginTop: moderateVerticalScale(8),
      paddingVertical: moderateVerticalScale(6),
      paddingHorizontal: moderateScale(12),
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.xl),
      alignItems: "center",
      justifyContent: "center",
    },
    retakeButtonText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
  });

// Processing stages with corresponding messages
const getProcessingStages = (t: any) => [
  t('processing.stages.scanning'),
  t('processing.stages.identifying'),
  t('processing.stages.extracting'),
  t('processing.stages.finalizing')
];

type FoodProcessingCardProps = {
  imageUri: string;
  isError?: boolean;
  errorMessage?: string;
  onRetake?: () => void;
  onClose?: () => void;
};

const SkeletonLoader = () => {
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startShimmerAnimation = () => {
      Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          easing: Easing.linear,
          useNativeDriver: false,
        })
      ).start();
    };

    startShimmerAnimation();
    return () => shimmerAnimation.stopAnimation();
  }, [shimmerAnimation]);

  const shimmerTranslate = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-300, 300]
  });

  return (
    <View style={skeletonStyles.container}>
      <View style={skeletonStyles.row}>
        <View style={skeletonStyles.skeletonLarge}>
          <Animated.View 
            style={[
              skeletonStyles.shimmer,
              {
                transform: [{ translateX: shimmerTranslate }]
              }
            ]} 
          />
        </View>
      </View>
      
      <View style={skeletonStyles.row}>
        <View style={skeletonStyles.skeletonSmall}>
          <Animated.View 
            style={[
              skeletonStyles.shimmer,
              {
                transform: [{ translateX: shimmerTranslate }]
              }
            ]} 
          />
        </View>
        <View style={skeletonStyles.skeletonSmall}>
          <Animated.View 
            style={[
              skeletonStyles.shimmer,
              {
                transform: [{ translateX: shimmerTranslate }]
              }
            ]} 
          />
        </View>
        <View style={skeletonStyles.skeletonSmall}>
          <Animated.View 
            style={[
              skeletonStyles.shimmer,
              {
                transform: [{ translateX: shimmerTranslate }]
              }
            ]} 
          />
        </View>
      </View>
    </View>
  );
};

const FoodProcessingCard = ({ 
  imageUri, 
  isError = false, 
  errorMessage = "No food detected", 
  onRetake,
  onClose
}: FoodProcessingCardProps) => {
  const { t } = useTranslation();
  const [currentStage, setCurrentStage] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const PROCESSING_STAGES = getProcessingStages(t);
  // Cycle through processing stages with fade animation
  useEffect(() => {
    if (isError) return;
    
    const cycleMessages = () => {
      // Fade out
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        // Change message
        setCurrentStage(prev => (prev + 1) % PROCESSING_STAGES.length);
        
        // Fade in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      });
    };
    
    const interval = setInterval(cycleMessages, 3000);
    
    return () => clearInterval(interval);
  }, [isError, fadeAnim]);
  
  if (isError) {
    return (
      <View style={styles.container}>
        <View style={styles.card}>
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: imageUri }} 
              style={styles.image}
              contentFit="cover"
              transition={300}
            />
          </View>
          
          <View style={styles.contentContainer}>
            <View style={styles.headerContainer}>
              <Text style={styles.errorTitle}>{errorMessage || t('processing.error.noFoodDetected')}</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.subtitle}>{t('processing.error.tryDifferentAngle')}</Text>
            
            <TouchableOpacity 
              style={styles.retakeButton}
              onPress={onRetake}
            >
              <Text style={styles.retakeButtonText}>{t('processing.retakeButton')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.imageContainer}>
          <Image 
            source={{ uri: imageUri }} 
            style={styles.image}
            contentFit="cover"
            transition={300}
          />
          <View style={styles.imageDimOverlay}>
            <View style={styles.focusCircle} />
          </View>
        </View>
        
        <View style={styles.contentContainer}>
          <Animated.Text style={[styles.title, { opacity: fadeAnim }]}>
            {PROCESSING_STAGES[currentStage]}
          </Animated.Text>
          
          <SkeletonLoader />
          
          <Text style={styles.subtitle}>{t('processing.notification')}</Text>
        </View>
      </View>
    </View>
  );
};

const skeletonStyles = StyleSheet.create({
  container: {
    marginVertical: moderateVerticalScale(4),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: moderateVerticalScale(6),
  },
  skeletonLarge: {
    height: moderateVerticalScale(12),
    borderRadius: moderateScale(4),
    backgroundColor: colors.gray[200],
    overflow: 'hidden',
    width: '100%',
  },
  skeletonSmall: {
    height: moderateVerticalScale(8),
    borderRadius: moderateScale(3),
    backgroundColor: colors.gray[200],
    overflow: 'hidden',
    width: '30%',
  },
  shimmer: {
    width: 100,
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    position: 'absolute',
  }
});

// const styles = StyleSheet.create({
//   container: {
//     width: '100%',
//     marginBottom: moderateVerticalScale(16),
//     height: moderateVerticalScale(140), 
//   },
//   card: {
//     flexDirection: 'row',
//     backgroundColor: colors.white,
//     borderRadius: moderateScale(16),
//     overflow: 'hidden',
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 8,
//     elevation: 2,
//     height: '100%', // Take full height of container
//   },
//   imageContainer: {
//     width: '40%',
//     position: 'relative',
//     height: '100%', // Take full height of card
//   },
//   image: {
//     width: '100%',
//     height: '100%',
//   },
//   imageDimOverlay: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: 'rgba(0, 0, 0, 0.3)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   focusCircle: {
//     width: moderateScale(50),
//     height: moderateScale(50),
//     borderRadius: moderateScale(30),
//     borderWidth: 2,
//     borderColor: colors.white,
//   },
//   contentContainer: {
//     flex: 1,
//     padding: moderateScale(12),
//     justifyContent: 'center',
//   },
//   headerContainer: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'flex-start',
//     marginBottom: moderateVerticalScale(8),
//   },
//   title: {
//     fontSize: moderateScale(16),
//     fontWeight: '600',
//     color: colors.black,
//     marginBottom: moderateVerticalScale(16),
//   },
//   errorTitle: {
//     fontSize: moderateScale(16),
//     fontWeight: '600',
//     color: colors.error,
//     flex: 1,
//   },
//   closeButton: {
//     padding: moderateScale(4),
//   },
//   subtitle: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//     marginTop: moderateVerticalScale(8),
//   },
//   retakeButton: {
//     marginTop: moderateVerticalScale(8),
//     paddingVertical: moderateVerticalScale(6),
//     paddingHorizontal: moderateScale(12),
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: moderateScale(20),
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   retakeButtonText: {
//     fontSize: moderateScale(14),
//     color: colors.black,
//   },
// });

export default FoodProcessingCard;
