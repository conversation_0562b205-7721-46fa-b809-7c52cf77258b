import React, { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { DiscoverCard } from '@/types/discoverCards';
import { colors } from '@/constants/colors';
import { Bookmark, Share2, Copy } from 'lucide-react-native';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import Swiper from 'react-native-deck-swiper';
import customLogger from '@/utils/logger/logger';

interface SwipeableDeckCardProps {
  cards: DiscoverCard[];
  isScrollEnabled:any;
  setIsScrollEnabled: any;
  onSwipeLeft: (cardIndex: number) => void;
  onSwipeRight: (cardIndex: number) => void;
  onCardPress: (card: DiscoverCard) => void;
  onCopy: (card: DiscoverCard) => void;
  onShare: (card: DiscoverCard) => void;
  currentIndex: number;
  onIndexChange: (index: number) => void;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    card: {
      width: SCREEN_WIDTH - moderateScale(32),
      height: moderateVerticalScale(400),
      borderRadius: moderateScale(theme.radii.xl),
      overflow: "hidden",
      backgroundColor: theme.colors.secondary[50],
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.2,
      shadowRadius: moderateScale(8),
      elevation: 5,
    },
    cardContent: {
      width: "100%",
      height: "100%",
    },
    image: {
      width: "100%",
      height: "100%",
      resizeMode: "cover",
    },
    gradient: {
      position: "absolute",
      left: 0,
      right: 0,
      bottom: 0,
      height: "60%",
      padding: moderateScale(20),
      justifyContent: "flex-end",
    },
    categoryContainer: {
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      paddingHorizontal: moderateScale(12),
      paddingVertical: moderateVerticalScale(4),
      borderRadius: moderateScale(theme.radii.lg),
      alignSelf: "flex-start",
      marginBottom: moderateVerticalScale(8),
    },
    category: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(12),
      fontWeight: "600",
    },
    title: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize["2xl"]),
      fontWeight: "bold",
      marginBottom: moderateVerticalScale(8),
    },
    description: {
      color: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
      marginBottom: moderateVerticalScale(16),
      opacity: 0.9,
    },
    actions: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    actionButton: {
      width: moderateScale(40),
      height: moderateScale(40),
      borderRadius: moderateScale(theme.radii.xl),
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      justifyContent: "center",
      alignItems: "center",
      marginLeft: moderateScale(12),
    },
  });

const SCREEN_WIDTH = Dimensions.get('window').width;

const SwipeableDeckCard: React.FC<SwipeableDeckCardProps> = ({
  cards,
  onSwipeLeft,
  onSwipeRight,
  onCardPress,
  setIsScrollEnabled,
  isScrollEnabled,
  onCopy,
  onShare,
  currentIndex,
  onIndexChange,
}) => {
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const swiperRef = useRef<Swiper<DiscoverCard>>(null);
   
  const [isSwiping, setIsSwiping] = useState(false);
  
  // state updates
  useEffect(() => {
    if (isSwiping && isScrollEnabled) {
      setIsScrollEnabled(false);
    } else if (!isSwiping && !isScrollEnabled) {
      setIsScrollEnabled(true);
    }
  }, [isSwiping, isScrollEnabled, setIsScrollEnabled]);

  const renderCard = (card: DiscoverCard) => {
    // Add safety check for undefined cards
    if (!card) {
      return <View style={styles.card}></View>;
    }

    return (
      <View style={styles.card}>
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => card && onCardPress(card)}
          style={styles.cardContent}
          delayPressIn={150}
        >
          <Image
            source={{ uri: card.imageUrl }}
            style={styles.image}
            contentFit="cover"
            transition={300}
            cachePolicy="memory-disk"
            priority="high"
          />
          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.8)"]}
            style={styles.gradient}
          >
            <View style={styles.categoryContainer}>
              <Text style={styles.category}>{card.category}</Text>
            </View>
            <Text style={styles.title}>{card.title}</Text>
            <Text style={styles.description} numberOfLines={2}>
              {card.description}
            </Text>
            <View style={styles.actions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  if (card) onCopy(card);
                }}
              >
                <Copy size={moderateScale(20)} color={colors.white} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  if (card) onShare(card);
                }}
              >
                <Share2 size={moderateScale(20)} color={colors.white} />
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {cards.length > 0 && (
        <Swiper
          ref={swiperRef}
          cards={cards}
          renderCard={renderCard}
          onSwipedAborted={useCallback(() => {
            setIsSwiping(false);
          }, [])}
          onSwiping={useCallback(() => {
            if (!isSwiping) {
            setIsSwiping(true);
          }
          }, [isSwiping])}
          onSwipedLeft={(cardIndex) => onSwipeLeft(cardIndex)}
          onSwipedRight={(cardIndex) => onSwipeRight(cardIndex)}
          onSwiped={useCallback((cardIndex:number) => {
            const nextIndex = (cardIndex + 1) % cards.length;
            onIndexChange(nextIndex);
            setIsSwiping(false);
          }, [cards.length, onIndexChange])}
          cardIndex={currentIndex}
          backgroundColor="transparent"
          stackSize={2}
          stackSeparation={-15}
          stackScale={0.95}
          infinite
          animateOverlayLabelsOpacity
          animateCardOpacity
          swipeBackCard
          disableBottomSwipe
          verticalSwipe={false}
          disableTopSwipe
          containerStyle={{
            alignItems: "center",
            marginTop: moderateVerticalScale(-40),
          }}
        />
      )}
    </View>
  );
};

export default React.memo(SwipeableDeckCard);
