// components/OfflineMessage.tsx
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { WifiOff } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { router } from 'expo-router';

export default function OfflineMessage() {
  const handleRetry = () => {
    router.replace('/');
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.iconWrapper}>
        <WifiOff size={48} color={colors.gray[500]} />
      </View>
      <Text style={styles.title}>You're offline</Text>
      <Text style={styles.message}>
        Check your internet connection and try again.
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  iconWrapper: {
    marginBottom: moderateVerticalScale(20),
    backgroundColor: colors.gray[100],
    padding: moderateScale(16),
    borderRadius: moderateScale(48),
  },
  title: {
    fontSize: moderateScale(20),
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: moderateVerticalScale(8),
  },
  message: {
    fontSize: moderateScale(14),
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: moderateVerticalScale(20),
    lineHeight: moderateScale(20),
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingVertical: moderateVerticalScale(12),
    paddingHorizontal: moderateScale(32),
    borderRadius: moderateScale(8),
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: moderateVerticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 2,
  },
  retryButtonText: {
    color: colors.white,
    fontSize: moderateScale(15),
    fontWeight: '600',
  },
});
