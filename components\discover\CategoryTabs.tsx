import React, { useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { colors } from '@/constants/colors';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
interface CategoryTabsProps {
  categories: string[];
  activeCategory: string;
  onCategoryPress: (category: string) => void;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateVerticalScale(10),
      marginTop: moderateVerticalScale(4),
    },
    tab: {
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateVerticalScale(8),
      borderRadius: moderateScale(theme.radii.xl),
      marginRight: moderateScale(8),
      backgroundColor: theme.colors.gray[100],
    },
    activeTab: {
      backgroundColor: theme.colors.primary.main,
    },
    tabText: {
      fontSize: moderateScale(theme.fontSize.xs),
      fontWeight: "500",
      color: theme.colors.gray[700],
    },
    activeTabText: {
      color: theme.colors.secondary[50],
    },
  });

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  categories,
  activeCategory,
  onCategoryPress,
}) => {
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      {categories.map((category: string) => (
        <TouchableOpacity
          key={category}
          style={[
            styles.tab,
            activeCategory === category && styles.activeTab,
          ]}
          onPress={() => onCategoryPress(category)}
          activeOpacity={0.7}
        >
          <Text
            style={[
              styles.tabText,
              activeCategory === category && styles.activeTabText,
            ]}
          >
            {category}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     paddingHorizontal: moderateScale(16),
//     paddingVertical: moderateVerticalScale(10),
//   },
//   tab: {
//     paddingHorizontal: moderateScale(16),
//     paddingVertical: moderateVerticalScale(8),
//     borderRadius: moderateScale(20),
//     marginRight: moderateScale(8),
//     backgroundColor: colors.gray[100],
//   },
//   activeTab: {
//     backgroundColor: colors.primary,
//   },
//   tabText: {
//     fontSize: moderateScale(13),
//     fontWeight: '500',
//     color: colors.gray[700],
//   },
//   activeTabText: {
//     color: colors.white,
//   },
// });

export default React.memo(CategoryTabs);
