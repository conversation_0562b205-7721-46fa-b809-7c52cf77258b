import React, { useState, useCallback, useMemo } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useRouter } from 'expo-router';
import { Heart, ArrowRight } from 'lucide-react-native';
import TopNavbar from '@/components/navigation/TopNavbar';
import { Stack } from 'expo-router';
import ComingSoonModal from '@/components/common/modals/ComingSoonModal';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';

// Move specialists array outside the component to prevent recreation on each render
const getSpecialistsData = (t: any) => [
  { title: t('specialists.generalPhysician.title'), description: t('specialists.generalPhysician.description') },
  { title: t('specialists.nutritionist.title'), description: t('specialists.nutritionist.description'), featureName: 'Nutrition Specialist' },
  { title: t('specialists.cardiologist.title'), description: t('specialists.cardiologist.description') , featureName: 'Cardiology Specialist'},
  { title: t('specialists.neurologist.title'), description: t('specialists.neurologist.description'), featureName: 'Neurology Specialist' },
  { title: t('specialists.oncologist.title'), description: t('specialists.oncologist.description'), featureName: 'Oncology Specialist' },
  { title: t('specialists.endocrinologist.title'), description: t('specialists.endocrinologist.description'), featureName: 'Endocrinology Specialist' },
  { title: t('specialists.dermatologist.title'), description: t('specialists.dermatologist.description'),featureName: 'Dermatology Specialist' },
  { title: t('specialists.psychiatrist.title'), description: t('specialists.psychiatrist.description') , featureName: 'Psychiatry Specialist'},
];

function SpecialistsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSpecialist, setSelectedSpecialist] = useState('');
  
  // Memoize specialists data if it needs to be transformed
  const specialists = useMemo(() => getSpecialistsData(t), [t]);

  // Memoize the modal close handler
  const handleCloseModal = useCallback(() => {
    setModalVisible(false);
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('specialists.title')} showProfile={true} showBackButton={true} />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <Text style={styles.description}>
          {t('specialists.description')}
        </Text>
        
        <View style={styles.specialistsList}>
          {specialists.map((specialist, index) => {
            // Create a memoized handler for each specialist
            const handleSpecialistPress = useCallback(() => {
              if (specialist.title === t('specialists.generalPhysician.title')) {
                router.push('/chat');
              } else {
                setSelectedSpecialist(specialist.featureName || specialist.title);
                setModalVisible(true);
              }
            }, [specialist]);
            
            return (
              <TouchableOpacity 
                key={index} 
                style={styles.specialistItem}
                onPress={handleSpecialistPress}
              >
                <View style={styles.specialistContent}>
                  <View style={styles.specialistIconContainer}>
                    <Heart size={22} color={colors.primary} />
                  </View>
                  <View style={styles.specialistInfo}>
                    <Text style={styles.specialistTitle}>{specialist.title}</Text>
                    <Text style={styles.specialistDescription}>{specialist.description}</Text>
                  </View>
                </View>
                <ArrowRight size={20} color={colors.gray[400]} />
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>

      <ComingSoonModal 
        isVisible={modalVisible}
        onClose={handleCloseModal}
        featureName={selectedSpecialist}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: moderateScale(20),
    paddingBottom: moderateVerticalScale(32),
    paddingTop: moderateVerticalScale(30)
  },
  description: {
    fontSize: moderateScale(15),
    color: colors.gray[700],
    lineHeight: moderateVerticalScale(24),
    marginBottom: moderateVerticalScale(24),
  },
  specialistsList: {
    gap: moderateVerticalScale(14),
  },
  specialistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    padding: moderateScale(16),
    borderRadius: moderateScale(12),
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: moderateVerticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 2,
  },
  specialistContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  specialistIconContainer: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(20),
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: moderateScale(12),
  },
  specialistInfo: {
    flex: 1,
  },
  specialistTitle: {
    fontSize: moderateScale(15),
    fontWeight: '600',
    color: colors.black,
    marginBottom: moderateVerticalScale(4),
  },
  specialistDescription: {
    fontSize: moderateScale(13),
    color: colors.gray[600],
  },
});

export default React.memo(SpecialistsScreen);
