{"common": {"error": "Ошибка", "yes": "Да", "no": "Нет", "sometimes": "Иногда", "close": "Закрыть", "cancel": "Отмена", "save": "Сохранить", "next": "Далее", "loading": "Загрузка...", "version": "v0.0.1.7"}, "welcome": "Войдите, чтобы начать общение с Августом", "notFound": {"title": "Ой!", "message": "Этот экран не существует.", "goHome": "Вернуться на главный экран!"}, "library": {"title": "Библиотека здоровья"}, "specialists": {"title": "Специалисты", "description": "Проконсультируйтесь со специализированными медицинскими работниками по более конкретным вопросам здоровья. Выберите специалиста ниже:", "generalPhysician": {"title": "Терапевт", "description": "Для общих проблем со здоровьем и первичной медицинской помощи."}, "nutritionist": {"title": "Диетолог", "description": "Для получения советов по диете, питанию и контролю веса."}, "cardiologist": {"title": "Кардиолог", "description": "Для проблем, связанных с сердцем, и здоровья сердечно-сосудистой системы."}, "neurologist": {"title": "Невролог", "description": "Для проблем с головным и спинным мозгом и нервной системой."}, "oncologist": {"title": "Онколог", "description": "Для проблем, связанных с раком, и методов лечения."}, "endocrinologist": {"title": "Эндокринолог", "description": "Для гормональных расстройств и лечения диабета."}, "dermatologist": {"title": "Дерматолог", "description": "Для заболеваний кожи, волос и ногтей."}, "psychiatrist": {"title": "Психиатр", "description": "Для проблем с психическим здоровьем и психологическим благополучием."}}, "profile": {"title": "Профиль", "defaultName": "Гость", "namePlaceholder": "Введите ваше имя", "saving": "Сохранение...", "noPhoneNumber": "Номер телефона отсутствует", "loggingOut": "Выход...", "about": {"title": "О приложении", "description": "Узнайте больше об Августе"}, "whatsapp": {"title": "WhatsApp", "description": "Пообщайтесь с Августом в WhatsApp"}, "refer": {"title": "Рекомендовать", "description": "Понравился Август? Поделитесь с друзьями"}, "deleteAccount": {"title": "Удалить аккаунт", "description": "Нам очень жаль, что вы уходите"}, "logout": {"title": "Выйти", "description": "Возвращайтесь скоро. Мы будем скучать по вам"}, "shareMessage": "👋Привет, посмотри это крутое приложение, которое я использую!\n\n\n\n➡️Я использую Август, чтобы получать быструю и надежную информацию и рекомендации по здоровью. Это как иметь врача в кармане! Посмотри здесь:", "error": {"loadFailed": "Не удалось загрузить данные пользователя", "fetchError": "Произошла ошибка при загрузке данных пользователя", "updateNameFailed": "Не удалось обновить имя", "updateNameError": "Произошла ошибка при обновлении имени", "loadFoodData": "Не удалось загрузить данные о еде", "logoutError": "Ошибка во время выхода:", "shareError": "Ошибка при отправке сообщения:"}}, "error": {"title": "Что-то пошло не так", "checkLogs": "Пожалуйста, проверьте журналы вашего устройства для получения более подробной информации.", "unknown": "Неизвестная ошибка", "unknownFile": "Неизвестный файл", "unknownLine": "Неизвестная строка", "unknownColumn": "Неизвестный столбец"}, "auth": {"phone": {"selectCountry": "Выберите страну", "searchCountries": "Поиск стран", "validation": {"invalidPhone": "Пожалуйста, введите действительный номер телефона", "invalidDigits": "Пожалуйста, введите действительный номер телефона (7-15 цифр)"}}, "header": {"title": "Получите ответы на свои вопросы о здоровье мгновенно и конфиденциально", "subtitle": "Внимательное руководство. Без спешки. Без путаницы.", "emphasis": "Только ясность."}, "greeting": "Привет 👋", "phoneNumber": "Номер телефона", "requestOTP": "Запросить OTP", "otp": {"title": "Одноразовый пароль", "verify": "Подтвердить OTP", "sending": "Отправка...", "countdown": "Повторная отправка OTP через {{countdown}} сек.", "resend": "Повторная отправка OTP", "sentTo": "OTP отправлен на ", "whatsappSuffix": " в WhatsApp"}, "disclaimer": {"prefix": "Регистрируясь, вы соглашаетесь с нашими ", "continuePrefix": "Продолжая, вы соглашаетесь с нашими ", "termsOfService": "Условиями обслуживания", "and": " и ", "privacyPolicy": "Политикой конфиденциальности", "whatsappConsent": ", а также даёте согласие на получение обновлений и напоминаний от нас через WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Добро пожаловать в August!", "buttonText": "Начнём!"}}, "postAuth": {"step1": {"title": "Привет!", "subtitle": "Я — August 👋", "description": "Считайте меня уютным уголком на вашем устройстве, где вы можете изучать все, что вас интересует в сфере здоровья.", "subdescription": "Спрашивайте обо всём, что вас волнует.\nБез осуждения, без ограничений!", "placeholder": "Как мне к вам обращаться?"}, "step2": {"title": "Привет, {{userName}},", "subtitle": "Вот что я умею:", "features": {"health": {"title": "Отвечать на ваши", "subtitle": "Вопросы о здоровье"}, "nutrition": {"title": "Отслеживать ваши", "subtitle": "Макронутриенты"}, "reports": {"title": "Анализ<PERSON><PERSON><PERSON>а<PERSON>ь", "subtitle": "От<PERSON>ёты"}}}}, "pills": {"thoughtful": "Вдумчивый", "careful": "Внимательный", "accurate": "Точный"}, "features": {"symptoms": {"title": "Проверьте свои симптомы", "description": "У меня тошнит уже неделю. Что со мной происходит?"}, "prescriptions": {"title": "Проанализируйте свои рецепты", "description": "Загрузите и поймите свои рецепты как врач."}, "medicine": {"title": "Узнайте о своих лекарствах", "description": "Взаимодействует ли метформин для лечения СПКЯ с моими таблетками от СДВГ?"}, "plans": {"title": "Получите персонализированные планы", "description": "Можете ли вы составить для меня план питания и фитнеса для снижения уровня HbA1c?"}}, "buttons": {"getStarted": "Начать", "next": "Далее"}, "errors": {"nameRequired": "Пожалуйста, введите ваше имя"}}, "tabs": {"chat": "Чат", "discover": "Обзор", "nutrition": "Питание", "personalize": "Персонализация"}, "chat": {"nav": {"title": "Август"}, "me": "Я", "augustName": "Август", "input": {"placeholder": "Спросите Августа...", "disclaimer": "Август может ошибаться. Проконсультируйтесь с врачом"}, "list": {"loadingMessages": "Загрузка сообщений...", "noMessages": "Сообщений пока нет. Начните разговор!"}, "connection": {"offlineMessage": "Похоже, вы офлайн. Подключитесь, чтобы отправить сообщения.", "connecting": "Подключение...", "tryAgain": "Попробовать снова"}, "prompts": {"uploadReport": "Загрузить отчет", "speakInHindi": "Поговорим на хинди", "notFeelingWell": "Мне нездоровится", "whatIsMyBMI": "Какой у меня ИМТ?", "nutritionAdvice": "Советы по питанию", "sleepBetter": "Лучше спать"}, "citations": {"referenceText": "Для получения дополнительной информации об этом разговоре, пожалуйста, обратитесь к:"}, "actions": {"copiedToClipboard": "Скопировано в буфер обмена", "copied": "Скопировано"}, "share": {"introText": "👋Привет, посмотри на мой разговор с Августом:\n\n", "downloadText": "\n\n➡️Загрузите Августа, чтобы пообщаться с вашим дружелюбным помощником по вопросам здоровья с ИИ:\n"}}, "discover": {"nav": {"title": "Обзор"}, "categories": {"all": "Все", "heartHealth": "Здоровье сердца", "nutrition": "Питание", "mentalHealth": "Психическое здоровье", "fitness": "Фит<PERSON><PERSON>с", "wellness": "Здоровье"}, "cards": {"empty": "Карточек для этой категории нет"}, "sections": {"features": "Возможности"}, "features": {"healthLibrary": {"title": "Медицинская библиотека", "description": "Доступ к проверенной, надежной и актуальной медицинской информации совершенно бесплатно."}, "nutritionTracker": {"title": "Трекер питания", "description": "Задумывались ли вы, что можно просто загрузить фото вашей еды и отслеживать все ваши цели в питании? Август может это сделать!"}, "multilingualSupport": {"title": "Многоязычная поддержка", "description": "Вы можете общаться с Августом на любом языке, на котором вам удобно! Август всегда готов выслушать, поддержать и ответить вам в любое время.", "samplePrompt": "Поговорим на хинди"}, "labReportAnalysis": {"title": "Ана<PERSON><PERSON>з лабораторных анализов", "description": "Когда вы говорите с Августом о своих лабораторных анализах, вы получаете высочайшую точность. Август обработал более 4,7 миллионов отчетов с точностью извлечения биомаркеров 98,4%."}}}, "nutrition": {"nav": {"title": "Питание"}, "meals": {"title": "Ваши приемы пищи", "subtitle": "Нажмите, чтобы посмотреть макросы каждого приема пищи"}, "upload": {"loading": "Загрузка изображения..."}, "defaultFoodName": "Продукт питания", "today": "Сегодня", "unknownTime": "Неизвестное время", "calories": "🔥 Калории", "proteins": "🥩 Белки", "carbs": "🍞 Углеводы", "sugars": "🍬 Сахара", "fat": "🥑 Жиры", "caloriesLabel": "Калории", "proteinLabel": "Белок", "carbohydratesLabel": "Углеводы", "fatLabel": "<PERSON><PERSON><PERSON>", "sugarLabel": "Сахар", "tips": "Советы:", "macroBreakdown": "Распределение макронутриентов", "noMacroData": "Для этого продукта питания нет данных о макронутриентах.", "disclaimer": "Только для образовательных целей. Узнать больше", "disclaimerLink": "здесь", "unit": {"kcal": "ккал", "g": "г"}, "form": {"gender": {"title": "Какой у вас пол?", "subtitle": "Это будет использовано для калибровки вашего индивидуального плана.", "male": "Мужской", "female": "Женский", "other": "Друг<PERSON>й"}, "age": {"title": "Сколько вам лет?", "subtitle": "Это будет использовано для расчета ваших ежедневных потребностей."}, "measurements": {"title": "Рост и вес", "subtitle": "Пожалуйста, введите свой рост в сантиметрах и вес в килограммах."}, "activity": {"title": "Уровень активности", "subtitle": "Как часто вы занимаетесь спортом?", "none": "Без тренировок", "moderate": "Умеренный", "high": "Высокий"}, "goal": {"title": "Цель по весу", "subtitle": "Чего бы вы хотели достичь?", "increase": "Увеличить", "maintain": "Поддерживать", "decrease": "Уменьшить"}, "targetWeight": {"title": "Целевой вес", "subtitle": "Какой ваш целевой вес в килограммах?"}, "setup": {"title": "Настройка вашего плана", "subtitle": "Пожалуйста, подождите, пока мы подготовим ваш план питания."}, "review": {"title": "Просмотрите ваш план", "subtitle": "Просмотрите и настройте свой план питания."}, "height": {"label": "Рост (см)"}, "weight": {"label": "Вес (кг)"}}, "error": {"updateFailed": "Не удалось обновить данные о питании. Пожалуйста, попробуйте еще раз.", "parsingError": "Ошибка разбора данных о продуктах питания:", "fetchReportsFailed": "Не удалось получить данные отчетов. Пожалуйста, попробуйте еще раз.", "missingReportId": "Отсутствует ID отчета"}}, "personalize": {"nav": {"title": "Персонализация"}, "button": {"saving": "Сохранение", "review": "Просмотр", "saveNext": "Сохранить и далее"}}, "basicInfo": {"title": "Давайте лучше узнаем вас", "subtitle": "Эта информация помогает нам персонализировать ваши рекомендации по здоровью", "age": {"question": "Сколько вам лет?", "placeholder": "Введите ваш возраст"}, "sex": {"question": "Какой у вас пол?", "placeholder": "Выберите ваш пол", "male": "Мужской", "female": "Женский", "other": "Друг<PERSON>й"}, "height": {"question": "Какой ваш рост? (см)", "placeholder": "Введите ваш рост"}, "weight": {"question": "Какой ваш вес? (кг)", "placeholder": "Введите ваш вес"}}, "lifestyle": {"title": "Ваши привычки в образе жизни", "subtitle": "Понимание ваших ежедневных привычек помогает нам давать лучшие рекомендации", "diet": {"question": "Какого типа диету вы придерживаетесь?", "placeholder": "Выберите вашу диету", "vegetarian": "Вегетарианская", "nonVegetarian": "Не вегетарианская", "vegan": "Веганская", "pescatarian": "Пескатарианская", "keto": "Кето", "paleo": "Палео"}, "exercise": {"question": "Вы регулярно занимаетесь спортом?"}, "drinking": {"question": "Вы употребляете алкоголь?"}, "smoking": {"question": "Вы курите?"}, "sleep": {"question": "Сколько часов вы спите за ночь?", "value": "{{sleep}} ча<PERSON><PERSON>"}, "hydration": {"question": "Сколько стаканов воды вы выпиваете ежедневно?", "value": "{{hydration}} ста<PERSON><PERSON><PERSON><PERSON> ({{liters}} л)"}}, "allergies": {"title": "Есть ли у вас аллергии?", "subtitle": "Знание ваших аллергий помогает нам давать более безопасные рекомендации", "allergyIndex": "Аллергия {{index}}", "name": {"question": "На что у вас аллергия?", "placeholder": "Введите аллерген (например, арахис, пыль)"}, "severity": {"question": "Насколько сильна эта аллергия?", "placeholder": "Выберите степень тяжести", "mild": "Слабая", "moderate": "Умеренная", "severe": "Сильная"}, "addButton": "Добавить ещё одну аллергию", "noAllergiesButton": "У меня нет аллергии"}, "medications": {"title": "Лекарства и добавки", "subtitle": "Расскажите нам о любых лекарствах или добавках, которые вы принимаете в настоящее время", "medicationIndex": "Лекарство {{index}}", "name": {"label": "Название лекарства", "placeholder": "Введите название лекарства"}, "startDate": {"question": "Когда вы начали его принимать?", "placeholder": "Выберите дату"}, "type": {"label": "Тип лекарства", "shortTerm": "Краткосрочный", "longTerm": "Долгосрочный"}, "dose": {"label": "Доза", "placeholder": "Количество"}, "unit": {"label": "Единица измерения"}, "frequency": {"label": "Частота", "placeholder": "Раз", "perDay": "в день", "perWeek": "в неделю", "perMonth": "в месяц", "perYear": "в год"}, "units": {"mg": "мг", "ml": "мл", "iu": "МЕ", "puffs": "вдохов", "drops": "капель", "tsp": "ч.л.", "tbsp": "ст.л.", "cups": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addButton": "Добавить ещё одно лекарство", "noMedicationsButton": "Я не принимаю никаких лекарств", "calendar": {"title": "Выберите дату начала"}}, "conditions": {"title": "Медицинские состояния", "subtitle": "Расскажите нам о любых медицинских состояниях, которые у вас были или есть в прошлом", "conditionIndex": "Состояние {{index}}", "name": {"label": "Название состояния", "placeholder": "Введите состояние (например, астма и т.д.)"}, "since": {"question": "С какого времени у вас это состояние?", "placeholder": "Выберите дату"}, "current": {"question": "Беспокоит ли оно вас сейчас?"}, "medicated": {"question": "Вы принимаете какие-либо лекарства от этого?"}, "addButton": "Добавить ещё одно состояние", "noConditionsButton": "У меня нет медицинских состояний", "calendar": {"title": "Выберите дату"}}, "reproductive": {"title": "Репродуктивное здоровье", "subtitle": "Эта информация поможет нам предоставить более персонализированные рекомендации по здоровью", "menstruation": {"question": "Были ли у вас менструации?", "detailsTitle": "Подробности о менструации", "regularity": {"question": "Насколько регулярны ваши циклы?", "regular": "Регулярные", "irregular": "Нерегулярные", "notSure": "Не уверен(а)"}, "cycleLength": {"label": "Средняя длительность цикла (дней)", "placeholder": "Введите длительность цикла"}, "flowDays": {"label": "Дни менструации: {{flowDays}}", "min": "1 день", "max": "15 дней"}, "padsPerDay": {"label": "Прокладок/тампонов в день: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Какие-либо симптомы во время менструации?", "placeholder": "Введите симптомы (например, спазмы, головная боль)"}}, "childbirth": {"question": "Были ли у вас роды?", "detailsTitle": "Подробности о родах", "children": {"label": "Количество детей"}, "pregnancies": {"label": "Количество беременностей"}, "complications": {"question": "Были ли какие-либо осложнения во время беременности или родов?", "placeholder": "Введите осложнения (если есть)"}}}, "review": {"title": "Проверьте вашу информацию", "subtitle": "Пожалуйста, проверьте предоставленную вами информацию перед отправкой", "sections": {"basicInfo": "Основная информация", "lifestyle": "Образ жизни", "allergies": "Аллергии", "medications": "Лекарства и добавки", "conditions": "Медицинские состояния", "reproductive": "Репродуктивное здоровье", "menstruationDetails": "Подробности о менструации", "childbirthDetails": "Подробности о родах"}, "fields": {"age": "Возраст:", "sex": "Пол:", "height": "Рост:", "weight": "Вес:", "diet": "Диета:", "exercise": "Физические упражнения:", "drinking": "Употребление алкоголя:", "smoking": "Курение:", "sleep": "Сон:", "hydration": "Гидратация:", "allergyIndex": "Аллергия {{index}}:", "dose": "Доза:", "frequency": "Частота:", "type": "Тип:", "since": "С:", "currentlyActive": "В настоящее время активен:", "takingMedication": "Принимает лекарство:", "hasMenstruated": "Были менструации:", "regularity": "Регулярность:", "cycleLength": "<PERSON><PERSON>ина цикла:", "flowDays": "Дни менструации:", "padsPerDay": "Прокладок/тампонов в день:", "hasChildbirth": "Были роды:", "children": "Дети:", "pregnancies": "Беременности:"}, "notProvided": "Не указано", "units": {"cm": "{{height}} см", "kg": "{{weight}} кг"}, "values": {"sleepHours": "{{sleep}} часов в день", "hydration": "{{hydration}} ст<PERSON><PERSON><PERSON><PERSON><PERSON> ({{liters}} л) в день", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} дн<PERSON><PERSON>"}, "noData": {"allergies": "Аллергии не указаны", "medications": "Лекарства не указаны", "conditions": "Медицинские состояния не указаны"}, "submitButton": "Отправить информацию"}, "success": {"title": "Информация обновлена!", "message": "Спасибо за предоставление информации о вашем здоровье. Мы будем использовать её для персонализации вашего опыта и предоставления лучших рекомендаций.", "benefits": {"insights": "Персонализированные данные о здоровье", "reminders": "Лучшие напоминания о лекарствах", "recommendations": "Индивидуальные рекомендации по здоровью"}, "continueButton": "Продолжить на главную страницу"}, "permissions": {"microphonePermissionDenied": "Доступ к микрофону запрещён", "microphoneAccessDescription": "August нуждается в доступе к вашему микрофону для записи аудио и отправки голосовых сообщений", "permissionDenied": "Доступ запрещён", "cameraPermissionRequired": "Нам нужен доступ к камере, чтобы это работало!", "mediaLibraryPermissionRequired": "Нам нужен доступ к медиатеке, чтобы это работало!"}, "voiceRecording": {"recordingTooLong": "Слишком длинная запись", "recordingTooLongMessage": "Голосовые сообщения должны быть короче 5 минут. Пожалуйста, запишите более короткое сообщение."}, "errors": {"uploadFailed": "Загрузка не удалась", "voiceUploadFailed": "Не удалось загрузить голосовую запись.", "voiceRecordingFailed": "Не удалось отправить голосовую запись", "failedToStopRecording": "Не удалось остановить запись", "photoUploadFailed": "Не удалось загрузить фото.", "failedToTakePhoto": "Не удалось сделать фото", "imageUploadFailed": "Не удалось загрузить изображение: {{fileName}}", "failedToPickImage": "Не удалось выбрать изображение", "documentUploadFailed": "Не удалось загрузить документ: {{fileName}}", "failedToPickDocument": "Не удалось выбрать документ"}, "audioPlayer": {"downloadingAudio": "Загрузка аудио...", "loadingAudio": "Загрузка аудио..."}, "mediaProcessing": {"processingFile": "Обработка вашего файла", "uploadingSecuring": "Загрузка и защита файла...", "analyzingContent": "Анализ содержимого документа...", "extractingInfo": "Извлечение ключевой информации...", "processingInsights": "Обработка данных...", "preparingResponse": "Подготовка подробного ответа...", "finalizingResponse": "Завершение ответа..."}, "attachments": {"voiceMessage": "Голосовое сообщение", "image": "[ИЗОБРАЖЕНИЕ]", "pdf": "[PDF]", "voice": "[ГОЛОСОВОЕ СООБЩЕНИЕ]"}, "pdf": {"loadingPdf": "Загрузка PDF..."}, "dateTime": {"yesterday": "Вчера, "}, "navbar": {"defaultTitle": "august", "selectedCount": "выбрано"}, "mediaUpload": {"photoLibrary": "Фотопленка", "takePhoto": "Сделать фото", "chooseFile": "Выбрать файл"}, "comingSoon": {"title": "Скоро!", "description": "находится в разработке. Следите за обновлениями!", "buttonText": "Понятно!"}, "clipboard": {"success": "Ссылка скопирована в буфер обмена"}, "mediaPhotos": {"emptyState": "Пока нет записей."}, "foodDetail": {"defaultFoodName": "Продукт", "nutrition": {"totalCalories": "Общая калорийность", "proteins": "Белки", "carbs": "Углеводы", "fat": "<PERSON><PERSON><PERSON><PERSON>", "sugars": "Сахара", "fibers": "Клетчатка"}}, "reports": {"defaultTitle": "<PERSON>ед<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultFoodName": "Продукт", "defaultName": "Документ", "openButton": "Открыть во внешнем просмотрщике", "biomarker": {"headerBiomarker": "Биомар<PERSON><PERSON>р", "headerValue": "Значение", "headerRefRange": "Референтный диапазон", "headerStatus": "Статус"}, "noData": "Данные биомаркеров отсутствуют"}, "setup": {"title": "Мы всё настраиваем для вас", "inProgress": "В процессе...", "progressMessages": {"0": "Расчёт суточной калорийности", "1": "Оптимизация соотношения макронутриентов", "2": "Создание плана питания", "3": "Расчёт индекса здоровья", "4": "Завершение настройки"}, "checklistItems": {"0": "Анализ ваших данных о здоровье", "1": "Расчёт оптимального плана питания", "2": "Персонализация рекомендаций", "3": "Создание предложений по питанию", "4": "Завершение настройки"}}, "foodEntry": {"emptyState": "Пока нет записей о еде. Сфотографируйте свою еду, чтобы добавить её!"}, "nutritionReview": {"congratulations": "Поздравляем!", "subtitle": "Ваш индивидуальный план питания готов", "submitButton": "Давайте начнём!", "dailyTargetsTitle": "Ваши ежедневные цели по питанию", "macroLabels": {"calories": "Калории", "carbs": "Углеводы", "protein": "Белок", "fats": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Как достичь своих целей:", "healthScores": "Используйте показатели здоровья для улучшения своего режима", "trackFood": "Регулярно отслеживайте потребление пищи", "followCalories": "Следуйте своим ежедневным рекомендациям по калориям", "balanceMacros": "Сбалансируйте потребление углеводов, белков и жиров"}}, "editModal": {"titlePrefix": "Редактировать", "cancelButton": "Отмена", "saveButton": "Далее"}, "processing": {"stages": {"scanning": "Сканирование продуктов...", "identifying": "Идентификация ингредиентов...", "extracting": "Извлечение питательных веществ...", "finalizing": "Завершение обработки результатов..."}, "error": {"defaultMessage": "Еда не обнаружена", "subtitle": "Попробуйте другой угол"}, "retakeButton": "Нажмите, чтобы переснять фото", "notification": "Мы сообщим вам, когда закончим!"}, "chart": {"title": "Отслеживание питания с течением времени", "selectNutrient": "Выберите питательное вещество:", "emptyState": "Данные о питательной ценности пока недоступны.", "dropdown": {"calories": "Калории", "protein": "Белок", "carbs": "Углеводы", "fat": "<PERSON><PERSON><PERSON>", "sugars": "Сахара"}}, "foodModal": {"defaultName": "Продукт питания", "defaultDate": "Сегодня", "defaultTime": "Неизвестное время", "saveChanges": "Сохранить изменения", "error": {"title": "Ошибка", "message": "Не удалось обновить данные о питании. Пожалуйста, попробуйте ещё раз."}, "nutrition": {"calories": "🔥 Калории", "proteins": "🥩 Белки", "carbs": "🍞 Углеводы", "sugars": "🍬 Сахара", "fat": "🥑 Жир"}, "macroBreakdown": {"title": "Распределение макронутриентов", "noData": "Данные о макронутриентах для этого продукта питания отсутствуют."}, "macroLabels": {"calories": "Калории", "protein": "Белок", "carbs": "Углеводы", "fat": "<PERSON><PERSON><PERSON>", "sugar": "Сахар"}}, "infoModal": {"title": "Подробная информация", "edit": "Редактировать", "save": "Сохранить", "saving": "Сохранение...", "enterValue": "Введите значение", "notSet": "Не установлено", "age": "Возраст", "heightCm": "Рост (см)", "weightKg": "Вес (кг)", "targetWeight": "Целевой вес", "nutritionTargets": "Целевые показатели питания", "protein": "Белок", "carbs": "Углеводы", "fats": "<PERSON><PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Введите значения или оставьте поля пустыми для автоматического расчёта.", "autoCalculateNote": "Макросы рассчитываются автоматически на основе ваших данных.", "validation": {"ageMin": "Возраст должен быть не менее 18 лет", "ageMax": "Возраст должен быть меньше 125", "heightMin": "Высота должна быть не менее 50 см", "heightMax": "Высота должна быть менее 250 см", "weightMin": "Вес должен быть не менее 30 кг", "weightMax": "Вес должен быть менее 500 кг", "targetWeightMin": "Целевой вес должен быть не менее 30 кг", "targetWeightMax": "Целевой вес должен быть менее 500 кг", "proteinMin": "Белок должен быть 0 или больше", "carbsMin": "Углеводов должно быть 0 или больше", "fatsMin": "Жиров должно быть 0 или больше"}}, "tracker": {"calories": "Калории", "protein": "Белок", "carbs": "Углеводы", "fat": "<PERSON><PERSON><PERSON>", "excess": "избыток", "remaining": "оставшиеся"}, "specialistConstants": {"nutritionist": {"name": "Диетолог", "description": "Экспертные советы по диете, питанию и здоровым привычкам", "featureName": "Специалист по питанию"}, "cardiologist": {"name": "Кардиолог", "description": "Специалист по здоровью сердца и сердечно-сосудистым заболеваниям", "featureName": "Специалист по кардиологии"}, "neurologist": {"name": "Невролог", "description": "Специалист по заболеваниям головного и спинного мозга, а также нервной системы", "featureName": "Специалист по неврологии"}, "oncologist": {"name": "Онколог", "description": "Специалист по диагностике и лечению рака", "featureName": "Специалист по онкологии"}, "endocrinologist": {"name": "Эндокринолог", "description": "Эксперт по гормональным состояниям и метаболическим нарушениям", "featureName": "Специалист по эндокринологии"}}, "discoverCards": {"categories": {"nutrition": "Питание", "heartHealth": "Здоровье сердца", "mentalHealth": "Психическое здоровье", "fitness": "Фит<PERSON><PERSON>с", "wellness": "Вел<PERSON><PERSON>с"}, "titles": {"vitaminB12Recovery": "Сколько времени нужно, чтобы восстановиться после дефицита витамина B12", "vitaminDeficiencyGanglion": "Какой дефицит витаминов вызывает кисты ганглия", "vitaminDeficiencyHairFall": "Какой дефицит витаминов вызывает выпадение волос", "vitaminWaters": "Полезны ли витаминные воды", "cholesterolHeadaches": "Вызывает ли высокий уровень холестерина головные боли", "cholesterolEyes": "Какие симптомы высокого уровня холестерина можно увидеть в глазах", "diabetesHeadaches": "Может ли диабет вызывать головные боли", "chestPainDrinking": "Почему болит грудь после употребления алкоголя", "stressDizziness": "Может ли стресс вызывать головокружение", "bulimiaFace": "Что такое лицо булимички", "kneeTwitch": "Почему у меня дергается колено", "noseTwitching": "Почему дергается нос", "piriformisVsSciatica": "В чем разница между синдромом грушевидной мышцы и ишиасом", "shoulderBladePinched": "Как снять защемление нерва в лопатке", "shoulderPinched": "Как снять защемление нерва в плече", "meniscusTorn": "Как естественным образом вылечить разрыв мениска", "hydrateQuickly": "Как быстро восстановить водный баланс", "periodConstipation": "Нормально ли иметь запор во время менструации", "acneScars": "Как избавиться от следов акне за неделю естественным путем", "perimenopausePregnancy": "Можно ли забеременеть во время перименопаузы"}, "descriptions": {"vitaminB12Recovery": "Узнайте о сроках восстановления при дефиците витамина B12 и эффективных средствах для повышения уровня энергии.", "vitaminDeficiencyGanglion": "Изучите связь между дефицитом витаминов и развитием ганглионных кист в организме.", "vitaminDeficiencyHairFall": "Узнайте, как недостаток необходимых витаминов может привести к выпадению волос и что вы можете сделать, чтобы предотвратить это.", "vitaminWaters": "Раскройте преимущества и потенциальные недостатки витаминизированной воды как части вашего ежедневного рациона.", "cholesterolHeadaches": "Изучите возможную связь между высоким уровнем холестерина и возникновением головных болей.", "cholesterolEyes": "Узнайте, как высокий уровень холестерина может проявляться в ваших глазах и на какие симптомы следует обратить внимание.", "diabetesHeadaches": "Исследуйте взаимосвязь между диабетом и возникновением головных болей в повседневной жизни.", "chestPainDrinking": "Изучите причины боли в груди после употребления определенных напитков.", "stressDizziness": "Узнайте, как стресс может влиять на ваше равновесие и общее самочувствие, приводя к головокружению.", "bulimiaFace": "Поймите физические признаки булимии, включая влияние на внешний вид лица.", "kneeTwitch": "Изучите потенциальные причины непроизвольного подергивания колена и его связь со стрессом или усталостью.", "noseTwitching": "Узнайте о возможных причинах подергивания носа и его связи с тревогой или другими факторами.", "piriformisVsSciatica": "Сравните симптомы синдрома грушевидной мышцы и ишиаса, чтобы лучше понять свое состояние.", "shoulderBladePinched": "Откройте для себя эффективные методы снятия защемления нерва в лопатке и восстановления подвижности.", "shoulderPinched": "Изучите простые упражнения и растяжки для облегчения компрессии нерва в области плеча.", "meniscusTorn": "Изучите естественные методы и упражнения для поддержки заживления поврежденного мениска.", "hydrateQuickly": "Узнайте быстрые и эффективные способы регидратации и поддержания оптимального водного баланса организма.", "periodConstipation": "Поймите причины запоров во время менструации и узнайте о натуральных средствах.", "acneScars": "Откройте для себя натуральные средства и советы по уходу за кожей, чтобы быстро уменьшить видимость рубцов от акне.", "perimenopausePregnancy": "Узнайте о перименопаузе, вопросах фертильности и о том, чего ожидать на этом этапе жизни."}}}