import axios from "axios";
import axiosInstance from "./axiosInstance";
import logger from "@/utils/logger/logger";
import { getEnvironmentVariable } from "@/utils/getEnvironmentVariable";

export const getUserData = async (): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable(
      "GATEKEEPER_URL"
    )}/user/${getEnvironmentVariable("TENANT")}/get-user`;
    logger.info(`Fetching user data from: ${url}`);
    const response = await axiosInstance.get(url);
    logger.apiResponse("Fetching user data", response.data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error("Error fetching user data:", {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        });
      }
    } else {
      logger.error("Error fetching user data:", error);
    }
    throw error;
  }
};

export const updateUserData = async (name: string): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable(
      "GATEKEEPER_URL"
    )}/user/${getEnvironmentVariable("TENANT")}/change-user`;
    logger.info(`Updating user data at: ${url}`, { name });
    const response = await axiosInstance.post(url, { name });
    //logger.apiResponse("Updating user data", response.data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error("Error updating user data:", {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        });
      }
    } else {
      logger.error("Error updating user data:", error);
    }
    throw error;
  }
};

export const updateUserMetadata = async (
  fieldName?: string,
  fieldValue?: any
): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable(
      "GATEKEEPER_URL"
    )}/user/${getEnvironmentVariable("TENANT")}/change-user-metadata`;

    const response = await axiosInstance.post(url, {
      fieldName,
      fieldValue,
    });

    logger.apiResponse("Updating user metadata", response.data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error("Error updating user metadata:", {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        });
      }
    } else {
      logger.error("Error updating user metadata:", error);
    }
    throw error;
  }
};

export const setExpoPushToken = async (expoPushToken: string): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable(
      "GATEKEEPER_URL"
    )}/user/${getEnvironmentVariable("TENANT")}/set-expo-push-token`;
    logger.info(`Setting Expo push token at: ${url}`);
    const response = await axiosInstance.post(url, { expoPushToken });
    logger.apiResponse("Setting Expo push token", response.data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
        logger.error("Error setting Expo push token:", {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        });
      }
    } else if ((error as any)?.response?.status !== 401) {
      logger.error("Error setting Expo push token:", error);
    }
    throw error;
  }
};
export const registerOnboardingQuestion = async (
  token: string | null,
  stepData: any,
  selectedAnswer: string | number,
  currentStep: number,
  questionType: string
): Promise<any> => {
  if (!token) {
    throw new Error("Token is required to register onboarding question.");
  }

  // try {
    const url = `${getEnvironmentVariable(
      "GATEKEEPER_URL"
    )}/user/${getEnvironmentVariable("TENANT")}/register-onboarding-question`;

    const question =
      stepData.subtitle?.replace(/<highlight>|<\/highlight>/g, "") ||
      "Untitled";
    const questionSlug =
      stepData.slug ||
      `onboarding-step-${stepData.title.toLowerCase().replace(/\s+/g, "-")}`;

    let payload;

    if (questionType === "slider") {
      payload = {
        question,
        questionSlug,
        questionType,
        isRequired: true,
        onboardingStep: currentStep,
        options: {
          min: stepData.slider.min,
          max: stepData.slider.max,
          step: stepData.slider.step,
          defaultValue: stepData.slider.defaultValue,
          prefix: stepData.slider.prefix || "$",
          suffix: stepData.slider.suffix || "",
        },
        answer: selectedAnswer,
      };
    } else {
      payload = {
        question,
        questionSlug,
        questionType,
        isRequired: true,
        onboardingStep: currentStep,
        options: stepData.options?.map((opt: any) => opt.label) || [],
        answer: selectedAnswer,
      };
    }

    logger.info("Registering onboarding question", { url, payload });

    const response = await axiosInstance.post(url, payload, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    logger.apiResponse(
      "Onboarding question registered successfully",
      response.data
    );
    return response.data;
  // } catch (error: any) {
  //   if (axios.isAxiosError(error) && error.response?.status !== 401) {
  //     logger.error("Error registering onboarding question:", {
  //       status: error.response?.status,
  //       message: error.message,
  //     });
  //   } else {
  //     logger.error("Unexpected error registering onboarding question:", error);
  //   }
  //   throw error;
  // }
};

