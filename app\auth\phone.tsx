import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  StatusBar,
  Image,
  Linking,
  Dimensions,
  useWindowDimensions,
  ScrollView,
  TouchableOpacity,
  BackHandler
} from "react-native";
import { router } from "expo-router";
import { useOtpStore } from "@/store/otp-store";
import { useAuthStore, useAuthLoading, useAuthError } from '@/store/auth-store';
import PhoneInput from "@/components/auth/PhoneInput";
import Button from "@/components/auth/Button";
import Logo from "@/components/common/logos/Logo";
import { colors } from "@/constants/colors";
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import { trackClarityEvent, trackClarityScreen } from "@/utils/clarity/clarity-utils";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";
import logger from "@/utils/logger/logger";
import { useFocusEffect } from 'expo-router';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import DeviceInfo from 'react-native-device-info';
import { useTranslation } from 'react-i18next';

// Get screen dimensions
const { height: SCREEN_HEIGHT } = Dimensions.get('window');

function AuthScreen() {
  const { t } = useTranslation();
  
  const { phone, setPhone, requestOtp, isRequestingOtp, requestError } =
    useOtpStore();
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [countryCode, setCountryCode] = useState<string>("+91");
  const [countryIsoCode, setCountryIsoCode] = useState<string>("IN");
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const { height } = useWindowDimensions();
  
  // Use memoized hooks from auth-store
  const isLoading = useAuthLoading();
  const authError = useAuthError();

  const scrollViewRef = useRef(null);
  const phoneInputRef = useRef(null);
  const isMounted = useRef(true);

  useEffect(() => {
    // Track component mount state
    isMounted.current = true;
    // Track screen view in Clarity
    trackUserInteraction('Request OTP page viewed')
    trackClarityScreen("Auth_Phone");
    
    return () => {
      isMounted.current = false;
    };
  }, []);

  useFocusEffect(() => {
    const onBackPress = () => {
      if (Platform.OS === 'android') {
        BackHandler.exitApp();
        return true; 
      }
      return false;
    };

    const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

    return () => subscription.remove();
  });

  // Handle keyboard events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardVisible(true);
        
        // Only scroll if we have refs and they're not null
        if (scrollViewRef.current && phoneInputRef.current) {
          setTimeout(() => {
            // Double-check refs still exist after timeout
            if (!scrollViewRef.current || !phoneInputRef.current) {
              return;
            }
            
            try {
              (phoneInputRef.current as any).measureLayout(
                scrollViewRef.current,
                (x: number, y: number, width: number, height: number) => {
                  const keyboardHeight = e.endCoordinates.height;
                  const scrollViewHeight = SCREEN_HEIGHT - keyboardHeight;
                  const inputBottom = y + height;
                  
                  // Add some padding for the button below the input
                  const requiredSpace = inputBottom + verticalScale(100);
                  
                  // Only scroll if the input + button would be hidden
                  if (requiredSpace > scrollViewHeight && scrollViewRef.current) {
                    const scrollTo = requiredSpace - scrollViewHeight + verticalScale(20);
                    (scrollViewRef.current as any).scrollTo({
                      y: scrollTo,
                      animated: true,
                    });
                  }
                },
                () => {
                  // Fail silently - this can happen during navigation
                  logger.info('Failed to measure layout - likely during navigation');
                }
              );
            } catch (error) {
              // Catch any errors that might occur during measurement
              logger.info('Error measuring layout:', error);
            }
          }, 100);
        }
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        // Scroll back to top when keyboard hides
        if (scrollViewRef.current) {
          (scrollViewRef.current as any).scrollTo({ y: 0, animated: true });
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Memoize derived values
  const isSmallDevice = useMemo(() => height < 700, [height]);

  const validatePhone = useCallback(() => {
    if (!phone || phone.length < 7) {
      setPhoneError(t('auth.phone.validation.invalidDigits'));
      return false;
    }
    const fullNumber = `${countryCode}${phone}`;
    const phoneNumber = parsePhoneNumberFromString(fullNumber);
    if (!phoneNumber || !phoneNumber.isValid()) {
      setPhoneError(t('auth.phone.validation.invalidPhone'));
      return false;
    }
    setPhoneError(null);
    return true;
  }, [phone, countryCode, t]);

  const handleRequestOtp = useCallback(async () => {
    if (!validatePhone()) {
      return;
    }

    const formattedPhone = `${countryCode}${phone}`;
    logger.info("Requesting OTP for:", formattedPhone);
    trackUserInteraction('Request OTP button clicked')

    const success = await requestOtp(formattedPhone);

    if (success) {
      trackUserInteraction('OTP Request Success')
      
      trackClarityEvent('OTP_Request_Success', {
        countryCode,
        phoneLength: phone.length,
        countryIsoCode,
        timestamp: new Date().toISOString()
      });
      
      router.push("/auth/verify");
    } else {
      trackUserInteraction('OTP Request Failed')
      
      trackClarityEvent('OTP_Request_Failure', {
        countryCode,
        phoneLength: phone.length,
        countryIsoCode,
        error: requestError || "Unknown error",
        timestamp: new Date().toISOString()
      });
    }
  }, [validatePhone, phone, countryCode, countryIsoCode, requestOtp, requestError]);

  const handleTermsPress = useCallback(() => {
    trackClarityEvent('Terms_Link_Tap', {
      source: "Auth Screen",
      destination: "https://meetaugust.ai/terms",
      timestamp: new Date().toISOString()
    });
    
    Linking.openURL("https://meetaugust.ai/terms");
  }, []);

  const handlePrivacyPress = useCallback(() => {
    trackClarityEvent('Privacy_Link_Tap', {
      source: "Auth Screen",
      destination: "https://meetaugust.ai/privacy",
      timestamp: new Date().toISOString()
    });
    
    Linking.openURL("https://meetaugust.ai/privacy");
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={0}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            <ScrollView
              ref={scrollViewRef}
              bounces={false}
              contentContainerStyle={[
                styles.scrollViewContent,
                keyboardVisible && styles.scrollViewContentWithKeyboard
              ]}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              scrollEnabled={keyboardVisible}
              overScrollMode="never"
            >
              {/* Top Section */}
              <View
                style={[
                  styles.topSection,
                  isSmallDevice && styles.topSectionSmall,
                ]}
              >
                <View style={styles.topBar}>
                  <Logo size="small" />
                  <Image
                    source={{
                      uri: "https://augustbuckets.blob.core.windows.net/mobile-app-assets/mobile-logo.png",
                    }}
                    style={styles.topRightLogo}
                    resizeMode="contain"
                  />
                </View>
                <View style={styles.headerTextContainer}>
                  <Text
                    style={[
                      styles.headerTitle,
                      isSmallDevice && styles.headerTitleSmall,
                    ]}
                    numberOfLines={2}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.title')}
                  </Text>
                  <Text 
                    style={styles.headerSubtitle}
                    numberOfLines={2}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.subtitle')}
                  </Text>
                  <Text 
                    style={styles.headerEmphasis}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                  >
                    {t('auth.header.emphasis')}
                  </Text>
                </View>
              </View>

              {/* Bottom Section */}
              <View style={[
                styles.bottomSection,
                isSmallDevice && styles.bottomSectionSmall
              ]}>
                <View style={styles.formContainer}>
                  <Text
                    style={[
                      styles.welcomeText,
                      isSmallDevice && styles.welcomeTextSmall,
                    ]}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                  >
                    {t('auth.greeting')}
                  </Text>
                  <Text 
                    style={styles.loginText}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                  >
                    {t('welcome')}
                  </Text>

                  <Text style={styles.inputLabel}>{t('auth.phoneNumber')}</Text>
                  <View ref={phoneInputRef}>
                    <PhoneInput
                      value={phone}
                      onChangeText={(text) => {
                        setPhone(text);
                        if (text.length >= 7 && phoneError) {
                          setPhoneError(null);
                        }
                      }}
                      onChangeCountry={(dialCode, isoCode) => {
                        setCountryCode(dialCode);
                        setCountryIsoCode(isoCode);
                        
                        trackClarityEvent('Country_Code_Changed', {
                          countryCode: dialCode,
                          countryIsoCode: isoCode,
                          timestamp: new Date().toISOString()
                        });
                      }}
                      countryCode={countryCode}
                      countryIsoCode={countryIsoCode}
                      error={phoneError || requestError || authError}
                    />

                    <Button
                      title={t('auth.requestOTP')}
                      onPress={handleRequestOtp}
                      loading={isRequestingOtp || isLoading}
                      disabled={!phone || phone.length < 7}
                    />
                  </View>

                  <Text style={styles.disclaimer}>
                    {t('auth.disclaimer.prefix')}
                    <Text style={styles.link} onPress={handleTermsPress}>
                      {t('auth.disclaimer.termsOfService')}
                    </Text>
                    {t('auth.disclaimer.and')}
                    <Text style={styles.link} onPress={handlePrivacyPress}>
                      {t('auth.disclaimer.privacyPolicy')}
                    </Text>
                    {t('auth.disclaimer.whatsappConsent')}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 0.95,
    backgroundColor: colors.primary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  scrollViewContentWithKeyboard: {
    paddingBottom: verticalScale(20),
  },
  topSection: {
    backgroundColor: colors.primary,
    paddingHorizontal: scale(26),
    paddingTop: verticalScale(24),
    paddingBottom: verticalScale(90),
    alignItems: "flex-start",
  },
  topSectionSmall: {
    paddingBottom: verticalScale(20),
    paddingTop: verticalScale(16),
  },
  topBar: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom: verticalScale(16),
  },
  topRightLogo: {
    width: scale(100),
    height: verticalScale(40),
    maxWidth: 100,
    maxHeight: 40,
  },
  headerTextContainer: {
    marginTop: 0,
    alignItems: "flex-start",
    width: "100%",
  },
  headerTitle: {
    fontSize: moderateScale(25),
    fontWeight: "500",
    color: colors.white,
    textAlign: "left",
    marginBottom: verticalScale(16),
    lineHeight: moderateScale(30),
  },
  headerTitleSmall: {
    fontSize: moderateScale(22),
    marginBottom: verticalScale(12),
  },
  headerSubtitle: {
    fontSize: moderateScale(14),
    color: colors.white,
    opacity: 0.9,
    textAlign: "left",
    lineHeight: moderateScale(20),
  },
  headerEmphasis: {
    fontSize: moderateScale(14),
    fontStyle: "italic",
    fontWeight: "bold",
    color: colors.white,
    opacity: 0.9,
    textAlign: "left",
    marginTop: verticalScale(4),
  },
  bottomSection: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 48,
    borderTopRightRadius: 48,
    overflow: "hidden",
  },
  bottomSectionSmall: {
    // Keep same styling
  },
  formContainer: {
    padding: scale(26),
    paddingTop: verticalScale(32),
    backgroundColor: colors.white,
    flex: 1,
    paddingBottom: verticalScale(28),
  },
  welcomeText: {
    fontSize: moderateScale(32),
    fontWeight: "400",
    color: colors.gray[700],
    marginBottom: verticalScale(8),
    lineHeight: moderateScale(38),
  },
  welcomeTextSmall: {
    fontSize: moderateScale(26),
    lineHeight: moderateScale(32),
  },
  loginText: {
    fontSize: moderateScale(16),
    color: colors.gray[600],
    marginBottom: verticalScale(24),
    lineHeight: moderateScale(22),
  },
  inputLabel: {
    fontSize: moderateScale(14),
    color: colors.gray[600],
    marginBottom: verticalScale(8),
  },
  disclaimer: {
    fontSize: moderateScale(10),
    color: colors.gray[600],
    textAlign: "center",
    marginTop: verticalScale(24),
    paddingHorizontal: scale(8),
    lineHeight: moderateScale(14),
  },
  link: {
    color: colors.primary,
    fontWeight: "500",
    textDecorationLine: "underline",
  },
});

export default React.memo(AuthScreen);