{"common": {"error": "错误", "yes": "是", "no": "否", "sometimes": "有时", "close": "关闭", "cancel": "取消", "save": "保存", "next": "下一步", "loading": "加载中...", "version": "v0.0.1.7"}, "welcome": "登录开始与August对话", "notFound": {"title": "哎呀！", "message": "此屏幕不存在。", "goHome": "返回主屏幕！"}, "library": {"title": "健康资料库"}, "specialists": {"title": "专家", "description": "咨询专业的健康专家，以获得更具体的健康问题解答。请在下方选择专家：", "generalPhysician": {"title": "全科医生", "description": "针对一般健康问题和初级保健。"}, "nutritionist": {"title": "营养师", "description": "提供饮食、营养和体重管理建议。"}, "cardiologist": {"title": "心脏病专家", "description": "针对心脏相关问题和心血管健康。"}, "neurologist": {"title": "神经科医生", "description": "针对大脑、脊髓和神经系统问题。"}, "oncologist": {"title": "肿瘤科医生", "description": "针对癌症相关问题和治疗。"}, "endocrinologist": {"title": "内分泌科医生", "description": "针对激素相关疾病和糖尿病管理。"}, "dermatologist": {"title": "皮肤科医生", "description": "针对皮肤、头发和指甲疾病。"}, "psychiatrist": {"title": "精神科医生", "description": "针对心理健康问题和心理健康。"}}, "profile": {"title": "个人资料", "defaultName": "访客", "namePlaceholder": "输入您的姓名", "saving": "保存中...", "noPhoneNumber": "无电话号码", "loggingOut": "退出登录中...", "about": {"title": "关于", "description": "了解更多关于August的信息"}, "whatsapp": {"title": "WhatsApp", "description": "在WhatsApp上与August聊天"}, "refer": {"title": "推荐", "description": "喜欢August？与您的朋友分享"}, "deleteAccount": {"title": "删除账户", "description": "很遗憾看到您离开"}, "logout": {"title": "退出登录", "description": "很快回来。我们会想念你的"}, "shareMessage": "👋嘿，看看我一直在使用的这个很棒的应用程序！\n\n\n\n➡️我一直在使用August来获取快速、可靠的健康信息和指导。它就像把医生放在口袋里一样！在这里查看：", "error": {"loadFailed": "加载用户数据失败", "fetchError": "获取用户数据时出错", "updateNameFailed": "更新姓名失败", "updateNameError": "更新姓名时出错", "loadFoodData": "加载食物数据失败", "logoutError": "退出登录期间出错：", "shareError": "分享消息时出错："}}, "error": {"title": "出现错误", "checkLogs": "请检查您的设备日志以获取更多详细信息。", "unknown": "未知错误", "unknownFile": "未知文件", "unknownLine": "未知行", "unknownColumn": "未知列"}, "auth": {"phone": {"selectCountry": "选择国家", "searchCountries": "搜索国家", "validation": {"invalidPhone": "请输入有效的电话号码", "invalidDigits": "请输入有效的电话号码（7-15位）"}}, "header": {"title": "即刻获得关于您健康问题的清晰解答，安全私密", "subtitle": "贴心的指导，从容不迫，清晰明了。", "emphasis": "清晰明了。"}, "greeting": "你好 👋", "phoneNumber": "电话号码", "requestOTP": "请求验证码", "otp": {"title": "一次性密码", "verify": "验证验证码", "sending": "发送中...", "countdown": "{{countdown}}秒后重新发送验证码", "resend": "重新发送验证码", "sentTo": "验证码已发送至 ", "whatsappSuffix": " 的WhatsApp"}, "disclaimer": {"prefix": "注册即表示您同意我们的 ", "continuePrefix": "继续即表示您同意我们的 ", "termsOfService": "服务条款", "and": " 和 ", "privacyPolicy": "隐私政策", "whatsappConsent": "，并同意接收我们通过WhatsApp发送的更新和提醒。"}}, "onboarding": {"preAuth": {"welcome": {"title": "欢迎来到August！", "buttonText": "开始吧"}}, "postAuth": {"step1": {"title": "你好！", "subtitle": "我是August 👋", "description": "把我当成您设备上舒适的一角，在这里您可以探索所有健康方面的好奇心。", "subdescription": "随意询问您想到的任何问题。\n没有评判，没有限制！", "placeholder": "我该如何称呼您？"}, "step2": {"title": "你好，{{userName}}，", "subtitle": "我能为您做些什么：", "features": {"health": {"title": "解答您的", "subtitle": "健康问题"}, "nutrition": {"title": "追踪您的", "subtitle": "宏量营养素"}, "reports": {"title": "分析", "subtitle": "报告"}}}}, "pills": {"thoughtful": "贴心", "careful": "细致", "accurate": "精准"}, "features": {"symptoms": {"title": "检查您的症状", "description": "我恶心了一周。我这是怎么了？"}, "prescriptions": {"title": "分析您的处方", "description": "上传并像医生一样理解处方。"}, "medicine": {"title": "了解您的药物", "description": "我的多囊卵巢综合征的二甲双胍会与我的多动症药片相互作用吗？"}, "plans": {"title": "获得个性化方案", "description": "你能给我一个降低HbA1c水平的营养和健身计划吗？"}}, "buttons": {"getStarted": "开始", "next": "下一步"}, "errors": {"nameRequired": "请输入您的姓名"}}, "tabs": {"chat": "聊天", "discover": "发现", "nutrition": "营养", "personalize": "个性化"}, "chat": {"nav": {"title": "八月"}, "me": "我", "augustName": "八月", "input": {"placeholder": "问问八月...", "disclaimer": "八月可能会出错。请向医生确认"}, "list": {"loadingMessages": "正在加载消息...", "noMessages": "还没有消息。开始对话吧！"}, "connection": {"offlineMessage": "看起来您已离线。重新连接以发送消息。", "connecting": "正在连接...", "tryAgain": "重试"}, "prompts": {"uploadReport": "上传报告", "speakInHindi": "说印地语", "notFeelingWell": "我感觉不舒服", "whatIsMyBMI": "我的BMI是多少？", "nutritionAdvice": "营养建议", "sleepBetter": "睡个好觉"}, "citations": {"referenceText": "有关此对话的更多详细信息，请参阅："}, "actions": {"copiedToClipboard": "已复制到剪贴板", "copied": "已复制"}, "share": {"introText": "👋嘿，看看我和八月聊天的内容：\n\n", "downloadText": "\n\n➡️下载八月，与您友好的AI健康伙伴聊天：\n"}}, "discover": {"nav": {"title": "发现"}, "categories": {"all": "全部", "heartHealth": "心脏健康", "nutrition": "营养", "mentalHealth": "心理健康", "fitness": "健身", "wellness": "健康"}, "cards": {"empty": "此类别下没有可用卡片"}, "sections": {"features": "特色"}, "features": {"healthLibrary": {"title": "健康图书馆", "description": "完全免费访问值得信赖、可靠和最新的医疗信息。"}, "nutritionTracker": {"title": "营养追踪器", "description": "曾经想过，如果您可以上传食物照片并追踪所有营养目标？八月可以做到！"}, "multilingualSupport": {"title": "多语言支持", "description": "您可以使用任何您熟悉的语言与八月沟通！八月随时准备倾听、支持和回应您的任何需求。", "samplePrompt": "说印地语"}, "labReportAnalysis": {"title": "化验报告分析", "description": "当您与八月讨论您的化验报告时，您可以获得极高的精确度。八月已处理超过470万份报告，生物标志物提取准确率达98.4%。"}}}, "nutrition": {"nav": {"title": "营养"}, "meals": {"title": "您的膳食", "subtitle": "点击查看每餐的宏量营养素"}, "upload": {"loading": "正在上传图片..."}, "defaultFoodName": "食物名称", "today": "今天", "unknownTime": "未知时间", "calories": "🔥 卡路里", "proteins": "🥩 蛋白质", "carbs": "🍞 碳水化合物", "sugars": "🍬 糖", "fat": "🥑 脂肪", "caloriesLabel": "卡路里", "proteinLabel": "蛋白质", "carbohydratesLabel": "碳水化合物", "fatLabel": "脂肪", "sugarLabel": "糖", "tips": "小贴士：", "macroBreakdown": "宏量营养素分解", "noMacroData": "此食物项目没有可用的宏量营养素数据。", "disclaimer": "仅供教育用途。了解更多", "disclaimerLink": "这里", "unit": {"kcal": "kcal", "g": "克"}, "form": {"gender": {"title": "您的性别？", "subtitle": "这将用于校准您的个性化计划。", "male": "男", "female": "女", "other": "其他"}, "age": {"title": "您的年龄？", "subtitle": "这将用于计算您的每日需求。"}, "measurements": {"title": "身高和体重", "subtitle": "请以厘米为单位输入您的身高，以公斤为单位输入您的体重。"}, "activity": {"title": "活动水平", "subtitle": "您多久锻炼一次？", "none": "不运动", "moderate": "中等", "high": "高强度"}, "goal": {"title": "体重目标", "subtitle": "您想达到什么目标？", "increase": "增加", "maintain": "维持", "decrease": "减少"}, "targetWeight": {"title": "目标体重", "subtitle": "您的目标体重是多少公斤？"}, "setup": {"title": "设置您的计划", "subtitle": "请稍候，我们正在准备您的营养计划。"}, "review": {"title": "查看您的计划", "subtitle": "查看和自定义您的营养计划。"}, "height": {"label": "身高（厘米）"}, "weight": {"label": "体重（公斤）"}}, "error": {"updateFailed": "更新营养数据失败。请重试。", "parsingError": "解析食物数据错误：", "fetchReportsFailed": "获取报告数据失败。请重试。", "missingReportId": "缺少报告 ID"}}, "personalize": {"nav": {"title": "个性化"}, "button": {"saving": "正在保存", "review": "查看", "saveNext": "保存并下一步"}}, "basicInfo": {"title": "让我们更好地了解您", "subtitle": "这些信息有助于我们个性化您的健康建议", "age": {"question": "您多大？", "placeholder": "输入您的年龄"}, "sex": {"question": "您的性别？", "placeholder": "选择您的性别", "male": "男", "female": "女", "other": "其他"}, "height": {"question": "您的身高是多少？（厘米）", "placeholder": "输入您的身高"}, "weight": {"question": "您的体重是多少？（公斤）", "placeholder": "输入您的体重"}}, "lifestyle": {"title": "你的生活习惯", "subtitle": "了解您的日常习惯有助于我们提供更好的建议", "diet": {"question": "你遵循什么类型的饮食？", "placeholder": "选择您的饮食", "vegetarian": "素食", "nonVegetarian": "非素食", "vegan": "素食主义者", "pescatarian": "海鲜素食者", "keto": "酮", "paleo": "旧石器时代饮食法"}, "exercise": {"question": "你经常锻炼吗？"}, "drinking": {"question": "你喝酒吗？"}, "smoking": {"question": "你抽烟吗？"}, "sleep": {"question": "你每晚睡几个小时？", "value": "{{sleep}} 小时"}, "hydration": {"question": "你每天喝多少杯水？", "value": "{{hydration}}杯（{{liters}}升）"}}, "allergies": {"title": "你有什么过敏症吗？", "subtitle": "了解您的过敏信息有助于我们提供更安全的建议", "allergyIndex": "过敏{{index}}", "name": {"question": "你对什么过敏？", "placeholder": "请输入过敏原（例如：花生，灰尘）"}, "severity": {"question": "这个过敏反应有多严重？", "placeholder": "选择严重程度", "mild": "轻微", "moderate": "中等", "severe": "严重"}, "addButton": "添加另一种过敏症", "noAllergiesButton": "我没有过敏。"}, "medications": {"title": "药物及补充剂", "subtitle": "请告诉我们您目前正在服用的任何药物或补充剂。", "medicationIndex": "药物{{index}}", "name": {"label": "药品名称", "placeholder": "请输入药品名称"}, "startDate": {"question": "你是什么时候开始吃的？", "placeholder": "选择日期"}, "type": {"label": "药物类型", "shortTerm": "短期", "longTerm": "长期"}, "dose": {"label": "剂量", "placeholder": "金额"}, "unit": {"label": "单元"}, "frequency": {"label": "频率", "placeholder": "时代", "perDay": "每天", "perWeek": "每周", "perMonth": "每月", "perYear": "每年"}, "units": {"mg": "毫克", "ml": "ml", "iu": "iu", "puffs": "噗", "drops": "滴", "tsp": "茶匙", "tbsp": "汤匙", "cups": "杯子"}, "addButton": "添加另一种药物", "noMedicationsButton": "我没有任何药物。", "calendar": {"title": "选择开始日期"}}, "conditions": {"title": "医学状况", "subtitle": "请告知您目前或过去患有的任何疾病。", "conditionIndex": "条件{{index}}", "name": {"label": "病症名称", "placeholder": "请输入病症（例如哮喘等）"}, "since": {"question": "你是什么时候开始出现这种情况的？", "placeholder": "选择日期"}, "current": {"question": "它现在正在困扰你吗？"}, "medicated": {"question": "你正在服用任何药物吗？"}, "addButton": "添加另一个条件", "noConditionsButton": "我没有任何疾病。", "calendar": {"title": "选择日期"}}, "reproductive": {"title": "生殖健康", "subtitle": "这些信息有助于我们提供更个性化的健康建议", "menstruation": {"question": "你经历过月经吗？", "detailsTitle": "月经详情", "regularity": {"question": "你的月经周期规律吗？", "regular": "规律", "irregular": "不规律", "notSure": "不确定"}, "cycleLength": {"label": "平均周期长度（天）", "placeholder": "输入周期长度"}, "flowDays": {"label": "经期天数：{{flowDays}}", "min": "1 天", "max": "15 天"}, "padsPerDay": {"label": "每天卫生巾/卫生棉条数量：{{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "月经期间有任何症状吗？", "placeholder": "输入症状（例如，痉挛，头痛）"}}, "childbirth": {"question": "你经历过分娩吗？", "detailsTitle": "分娩详情", "children": {"label": "子女数量"}, "pregnancies": {"label": "怀孕次数"}, "complications": {"question": "怀孕或分娩期间有任何并发症吗？", "placeholder": "输入并发症（如有）"}}}, "review": {"title": "查看您的信息", "subtitle": "请在提交前查看您提供的信息", "sections": {"basicInfo": "基本信息", "lifestyle": "生活方式", "allergies": "过敏症", "medications": "药物和补充剂", "conditions": "疾病状况", "reproductive": "生殖健康", "menstruationDetails": "月经详情", "childbirthDetails": "分娩详情"}, "fields": {"age": "年龄：", "sex": "性别：", "height": "身高：", "weight": "体重：", "diet": "饮食：", "exercise": "运动：", "drinking": "饮酒：", "smoking": "吸烟：", "sleep": "睡眠：", "hydration": "水分摄入：", "allergyIndex": "过敏原 {{index}}：", "dose": "剂量：", "frequency": "频率：", "type": "类型：", "since": "从：", "currentlyActive": "目前正在服用：", "takingMedication": "正在服用药物：", "hasMenstruated": "经历过月经：", "regularity": "规律性：", "cycleLength": "周期长度：", "flowDays": "经期天数：", "padsPerDay": "每天卫生巾/卫生棉条数量：", "hasChildbirth": "经历过分娩：", "children": "子女数量：", "pregnancies": "怀孕次数："}, "notProvided": "未提供", "units": {"cm": "{{height}} 厘米", "kg": "{{weight}} 千克"}, "values": {"sleepHours": "{{sleep}} 小时/天", "hydration": "{{hydration}} 杯（{{liters}}升）/天", "allergySeverity": "{{name}}（{{severity}}）", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} 天"}, "noData": {"allergies": "未提供过敏信息", "medications": "未提供药物信息", "conditions": "未提供疾病状况信息"}, "submitButton": "提交信息"}, "success": {"title": "信息已更新！", "message": "感谢您提供您的健康信息。我们将使用这些信息来个性化您的体验并提供更好的建议。", "benefits": {"insights": "个性化的健康见解", "reminders": "更好的药物提醒", "recommendations": "量身定制的健康建议"}, "continueButton": "继续前往仪表盘"}, "permissions": {"microphonePermissionDenied": "麦克风权限已拒绝", "microphoneAccessDescription": "August 需要访问您的麦克风来录制音频和发送语音笔记", "permissionDenied": "权限已拒绝", "cameraPermissionRequired": "我们需要相机权限才能使此功能正常工作！", "mediaLibraryPermissionRequired": "我们需要媒体库权限才能使此功能正常工作！"}, "voiceRecording": {"recordingTooLong": "录音时间过长", "recordingTooLongMessage": "语音录音必须少于 5 分钟。请录制较短的消息。"}, "errors": {"uploadFailed": "上传失败", "voiceUploadFailed": "无法上传语音录音。", "voiceRecordingFailed": "发送语音录音失败", "failedToStopRecording": "停止录音失败", "photoUploadFailed": "无法上传照片。", "failedToTakePhoto": "拍照失败", "imageUploadFailed": "无法上传图片：{{fileName}}", "failedToPickImage": "无法选择图片", "documentUploadFailed": "无法上传文档：{{fileName}}", "failedToPickDocument": "无法选择文档"}, "audioPlayer": {"downloadingAudio": "正在下载音频...", "loadingAudio": "正在加载音频..."}, "mediaProcessing": {"processingFile": "正在处理您的文件", "uploadingSecuring": "正在上传和保护文件...", "analyzingContent": "正在分析文档内容...", "extractingInfo": "正在提取关键信息...", "processingInsights": "正在处理见解...", "preparingResponse": "正在准备详细回复...", "finalizingResponse": "正在完成回复..."}, "attachments": {"voiceMessage": "语音留言", "image": "[图片]", "pdf": "[PDF]", "voice": "[语音笔记]"}, "pdf": {"loadingPdf": "正在加载PDF..."}, "dateTime": {"yesterday": "昨天，"}, "navbar": {"defaultTitle": "august", "selectedCount": "已选择"}, "mediaUpload": {"photoLibrary": "照片库", "takePhoto": "拍照", "chooseFile": "选择文件"}, "comingSoon": {"title": "即将推出！", "description": " 正在开发中。敬请期待更新！", "buttonText": "知道了！"}, "clipboard": {"success": "链接已复制到剪贴板"}, "mediaPhotos": {"emptyState": "暂无条目。"}, "foodDetail": {"defaultFoodName": "食物", "nutrition": {"totalCalories": "总卡路里", "proteins": "蛋白质", "carbs": "碳水化合物", "fat": "脂肪", "sugars": "糖", "fibers": "纤维"}}, "reports": {"defaultTitle": "媒体项目", "defaultFoodName": "食物", "defaultName": "文档", "openButton": "在外部查看器中打开", "biomarker": {"headerBiomarker": "生物标志物", "headerValue": "值", "headerRefRange": "参考范围", "headerStatus": "状态"}, "noData": "无生物标志物数据可用"}, "setup": {"title": "我们正在为您设置一切", "inProgress": "正在进行...", "progressMessages": {"0": "正在计算每日卡路里", "1": "正在优化宏量营养素比例", "2": "正在创建膳食计划", "3": "正在计算健康评分", "4": "正在完成设置"}, "checklistItems": {"0": "正在分析您的健康数据", "1": "正在计算最佳营养计划", "2": "正在个性化您的建议", "3": "正在创建您的膳食建议", "4": "正在完成您的设置"}}, "foodEntry": {"emptyState": "还没有添加任何食物记录。拍下你的食物照片来添加吧！"}, "nutritionReview": {"congratulations": "恭喜！", "subtitle": "您的个性化营养计划已准备就绪", "submitButton": "开始吧！", "dailyTargetsTitle": "您的每日营养目标", "macroLabels": {"calories": "卡路里", "carbs": "碳水化合物", "protein": "蛋白质", "fats": "脂肪"}}, "editModal": {"titlePrefix": "编辑 ", "cancelButton": "取消", "saveButton": "下一步"}, "processing": {"stages": {"scanning": "正在扫描食物...", "identifying": "正在识别食材...", "extracting": "正在提取营养成分...", "finalizing": "正在最终确定结果..."}, "error": {"defaultMessage": "未检测到食物", "subtitle": "请尝试不同的角度"}, "retakeButton": "点击重新拍照", "notification": "完成后我们会通知您！"}, "chart": {"title": "营养追踪", "selectNutrient": "选择营养素：", "emptyState": "暂无营养数据。", "dropdown": {"calories": "卡路里", "protein": "蛋白质", "carbs": "碳水化合物", "fat": "脂肪", "sugars": "糖"}}, "foodModal": {"defaultName": "食物名称", "defaultDate": "今天", "defaultTime": "未知时间", "saveChanges": "保存更改", "error": {"title": "错误", "message": "更新营养数据失败。请重试。"}, "nutrition": {"calories": "🔥 卡路里", "proteins": "🥩 蛋白质", "carbs": "🍞 碳水化合物", "sugars": "🍬 糖", "fat": "🥑 脂肪"}, "macroBreakdown": {"title": "宏量营养素分解", "noData": "此食物项目暂无宏量营养素数据。"}, "macroLabels": {"calories": "卡路里", "protein": "蛋白质", "carbs": "碳水化合物", "fat": "脂肪", "sugar": "糖"}}, "infoModal": {"title": "详细信息", "edit": "编辑", "save": "保存", "saving": "保存中...", "enterValue": "输入数值", "notSet": "未设置", "age": "年龄", "heightCm": "身高 (cm)", "weightKg": "体重 (kg)", "targetWeight": "目标体重", "nutritionTargets": "营养目标", "protein": "蛋白质", "carbs": "碳水化合物", "fats": "脂肪", "gm": "克", "editNote": "输入数值或留空以自动计算。", "autoCalculateNote": "宏量营养素将根据您的数据自动计算。", "validation": {"ageMin": "年龄必须至少18岁", "ageMax": "年龄必须低于125岁", "heightMin": "身高必须至少50厘米", "heightMax": "身高必须低于250厘米", "weightMin": "体重必须至少30公斤", "weightMax": "体重必须低于500公斤", "targetWeightMin": "目标体重必须至少30公斤", "targetWeightMax": "目标体重必须低于500公斤", "proteinMin": "蛋白质必须为0或以上", "carbsMin": "碳水化合物必须为0或以上", "fatsMin": "脂肪必须为0或以上"}}, "tracker": {"calories": "卡路里", "protein": "蛋白质", "carbs": "碳水化合物", "fat": "脂肪", "excess": "超标", "remaining": "剩余"}, "specialistConstants": {"nutritionist": {"name": "营养师", "description": "关于饮食、营养和健康饮食习惯的专业建议", "featureName": "营养专家"}, "cardiologist": {"name": "心脏病专家", "description": "专门研究心脏健康和心血管疾病", "featureName": "心脏病专家"}, "neurologist": {"name": "神经科医生", "description": "专注于大脑、脊髓和神经系统疾病", "featureName": "神经科专家"}, "oncologist": {"name": "肿瘤学家", "description": "专门从事癌症诊断和治疗方案", "featureName": "肿瘤专家"}, "endocrinologist": {"name": "内分泌学家", "description": "激素疾病和代谢紊乱专家", "featureName": "内分泌专家"}}, "discoverCards": {"categories": {"nutrition": "营养", "heartHealth": "心脏健康", "mentalHealth": "心理健康", "fitness": "健身", "wellness": "健康"}, "titles": {"vitaminB12Recovery": "维生素B12缺乏症需要多久才能恢复", "vitaminDeficiencyGanglion": "哪种维生素缺乏会导致腱鞘囊肿", "vitaminDeficiencyHairFall": "哪种维生素缺乏会导致脱发", "vitaminWaters": "维生素水对您有益吗", "cholesterolHeadaches": "高胆固醇会引起头痛吗", "cholesterolEyes": "眼睛里能看出哪些高胆固醇的症状", "diabetesHeadaches": "糖尿病会引起头痛吗", "chestPainDrinking": "喝酒后为什么胸痛", "stressDizziness": "压力会导致头晕吗", "bulimiaFace": "什么是贪食症脸", "kneeTwitch": "为什么我的膝盖会抽搐", "noseTwitching": "为什么鼻子会抽搐", "piriformisVsSciatica": "梨状肌综合征和坐骨神经痛有什么区别", "shoulderBladePinched": "如何缓解肩胛骨神经受压", "shoulderPinched": "如何缓解肩部神经受压", "meniscusTorn": "如何自然治愈半月板撕裂", "hydrateQuickly": "如何快速补水", "periodConstipation": "月经期间便秘正常吗", "acneScars": "如何在一周内自然去除痤疮疤痕", "perimenopausePregnancy": "更年期前期可以怀孕吗"}, "descriptions": {"vitaminB12Recovery": "了解维生素B12缺乏症的恢复时间线以及有效的能量提升方法。", "vitaminDeficiencyGanglion": "探索维生素缺乏与体内腱鞘囊肿形成之间的联系。", "vitaminDeficiencyHairFall": "了解缺乏必需维生素如何导致脱发以及如何预防。", "vitaminWaters": "揭示维生素水作为日常营养一部分的益处和潜在弊端。", "cholesterolHeadaches": "检查高胆固醇水平与头痛发作之间的可能联系。", "cholesterolEyes": "了解高胆固醇如何在眼睛中表现出来以及需要注意哪些症状。", "diabetesHeadaches": "调查日常生活中糖尿病与头痛发生之间的关系。", "chestPainDrinking": "探究饮用某些饮料后胸痛的原因。", "stressDizziness": "深入探讨压力如何影响你的平衡感和整体健康，导致头晕。", "bulimiaFace": "了解暴食症的生理症状，包括对脸部外观的影响。", "kneeTwitch": "调查非自主性膝盖抽搐的潜在原因及其与压力或疲劳的关系。", "noseTwitching": "了解鼻子抽动可能的原因及其与焦虑或其他因素的联系。", "piriformisVsSciatica": "比较梨状肌综合征和坐骨神经痛的症状，以便更好地了解您的病情。", "shoulderBladePinched": "发现有效缓解肩胛骨神经卡压并恢复活动能力的技术。", "shoulderPinched": "学习简单的练习和拉伸运动来缓解肩部神经压迫。", "meniscusTorn": "探索支持半月板撕裂愈合的自然疗法和锻炼方法。", "hydrateQuickly": "快速有效地补水并保持最佳身体水分的方法。", "periodConstipation": "了解月经期间便秘的原因并学习自然疗法。", "acneScars": "快速淡化痘印的天然疗法和护肤技巧", "perimenopausePregnancy": "了解围绝经期、生育考虑因素以及此阶段的预期。"}}}