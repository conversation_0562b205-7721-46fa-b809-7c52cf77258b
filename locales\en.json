{"common": {"error": "Error", "yes": "Yes", "no": "No", "sometimes": "Sometimes", "close": "Close", "cancel": "Cancel", "save": "Save", "next": "Next", "loading": "Loading...", "version": "v0.0.1.7"}, "welcome": "Login to start talking to August", "notFound": {"title": "Oops!", "message": "This screen doesn't exist.", "goHome": "Go to home screen!"}, "library": {"title": "Health Library"}, "specialists": {"title": "Specialists", "description": "Consult with specialized health professionals for more specific health concerns. Choose a specialist below:", "generalPhysician": {"title": "General Physician", "description": "For general health concerns and primary care."}, "nutritionist": {"title": "Nutritionist", "description": "For diet, nutrition, and weight management advice."}, "cardiologist": {"title": "Cardiologist", "description": "For heart-related concerns and cardiovascular health."}, "neurologist": {"title": "Neurologist", "description": "For brain, spinal cord, and nervous system issues."}, "oncologist": {"title": "Oncologist", "description": "For cancer-related concerns and treatments."}, "endocrinologist": {"title": "Endocrinologist", "description": "For hormone-related disorders and diabetes management."}, "dermatologist": {"title": "Dermatologist", "description": "For skin, hair, and nail conditions."}, "psychiatrist": {"title": "Psychiatrist", "description": "For mental health concerns and psychological well-being."}}, "profile": {"title": "Profile", "defaultName": "Guest", "namePlaceholder": "Enter your name", "saving": "Saving...", "noPhoneNumber": "No phone number", "loggingOut": "Logging out...", "about": {"title": "About", "description": "Know more about August"}, "whatsapp": {"title": "WhatsApp", "description": "Chat with August on WhatsApp"}, "refer": {"title": "<PERSON><PERSON>", "description": "Loved august? Share with your friends"}, "deleteAccount": {"title": "Delete account", "description": "We're sorry to see you go"}, "logout": {"title": "Logout", "description": "Come back soon. We'll miss you"}, "shareMessage": "👋Hey, Check out this awesome app I've been using!\n\n\n\n➡️I've been using August to get quick, reliable health info and guidance. It's like having a doctor in your pocket! Check it out here:", "error": {"loadFailed": "Failed to load user data", "fetchError": "An error occurred while fetching user data", "updateNameFailed": "Failed to update name", "updateNameError": "An error occurred while updating name", "loadFoodData": "Failed to load food data", "logoutError": "Error during logout:", "shareError": "Error sharing messages:"}}, "error": {"title": "Something went wrong", "checkLogs": "Please check your device logs for more details.", "unknown": "Unknown error", "unknownFile": "Unknown file", "unknownLine": "Unknown line", "unknownColumn": "Unknown column"}, "auth": {"phone": {"selectCountry": "Select Country", "searchCountries": "Search countries", "validation": {"invalidPhone": "Please enter a valid phone number", "invalidDigits": "Please enter a valid phone number (7-15 digits)"}}, "header": {"title": "Get clarity on your health concerns instantly and privately", "subtitle": "Thoughtful guidance. No rush. No judgment.", "emphasis": "Just clarity."}, "greeting": "Hi 👋", "phoneNumber": "Phone Number", "requestOTP": "Request OTP", "otp": {"title": "One-Time Password", "verify": "Verify OTP", "sending": "Sending...", "countdown": "Resend OTP in {{countdown}}s", "resend": "Resend OTP", "sentTo": "OTP sent to ", "whatsappSuffix": " on Whatsapp"}, "disclaimer": {"prefix": "By signing up, you agree to our ", "continuePrefix": "By continuing, you agree to our ", "termsOfService": "Terms of Service", "and": " and ", "privacyPolicy": "Privacy Policy", "whatsappConsent": ", and consent to receive updates & reminders from us via WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Welcome to August!", "buttonText": "Let's get started"}}, "postAuth": {"step1": {"title": "Hey!", "subtitle": "I'm August 👋", "description": "Think of me as the comfy corner on your\ndevice where you explore all your health\ncuriosities.", "subdescription": "Feel free to ask anything that's on your mind.\nNo judgment, No limits!", "placeholder": "What shall I call you?"}, "step2": {"title": "Hi {{userName}},", "subtitle": "Here's what I can do:", "features": {"health": {"title": "Answer your", "subtitle": "Health queries"}, "nutrition": {"title": "Track those", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "Analyse", "subtitle": "Reports"}}}, "pills": {"thoughtful": "Thoughtful", "careful": "Careful", "accurate": "Accurate"}, "features": {"symptoms": {"title": "Check your symptoms", "description": "I've been nauseous for a week. What's happening to me?"}, "prescriptions": {"title": "Analyse your prescriptions", "description": "Upload and understand prescriptions like a doctor."}, "medicine": {"title": "Know your medicine", "description": "Does Metformin for my PCOS interact with my ADHD pills?"}, "plans": {"title": "Get personalized plans", "description": "Can you give me a nutrition & fitness plan for reducing my HbA1c levels?"}}, "buttons": {"getStarted": "Get Started", "next": "Next"}, "errors": {"nameRequired": "Please enter your name"}}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Discover", "nutrition": "Nutrition", "personalize": "Personalize"}, "chat": {"nav": {"title": "August"}, "me": "Me", "augustName": "August", "input": {"placeholder": "Ask August...", "disclaimer": "August may make mistakes. Confirm with a doctor"}, "list": {"loadingMessages": "Loading messages...", "noMessages": "No messages yet. Start a conversation!"}, "connection": {"offlineMessage": "Looks like you're offline. Reconnect to send messages.", "connecting": "Connecting...", "tryAgain": "Try Again"}, "prompts": {"uploadReport": "Upload report", "speakInHindi": "Hindi mein baat karo", "notFeelingWell": "I'm not feeling well", "whatIsMyBMI": "What's my BMI?", "nutritionAdvice": "Nutrition advice", "sleepBetter": "Sleep better"}, "citations": {"referenceText": "For further details regarding this conversation, please refer to:"}, "actions": {"copiedToClipboard": "Copied to clipboard", "copied": "<PERSON>pied"}, "share": {"introText": "👋Hey, look at the conversation I had with August:\n\n", "downloadText": "\n\n➡️Download August to chat with your friendly AI health companion:\n"}}, "discover": {"nav": {"title": "Discover"}, "categories": {"all": "All", "heartHealth": "Heart Health", "nutrition": "Nutrition", "mentalHealth": "Mental Health", "fitness": "Fitness", "wellness": "Wellness"}, "cards": {"empty": "No cards available for this category"}, "sections": {"features": "Features"}, "features": {"healthLibrary": {"title": "Health Library", "description": "Access to trusted, reliable, and up-to-date medical information for completely free."}, "nutritionTracker": {"title": "Nutrition Tracker", "description": "Ever wondered if you could just upload a photo of your food and track all of your nutritional goals? August can do just that!"}, "multilingualSupport": {"title": "Multilingual Support", "description": "You can communicate with August in any language you're comfortable with! August is always here to listen, support, and respond to you anytime you need.", "samplePrompt": "Hindi mein baat karo"}, "labReportAnalysis": {"title": "Lab Report Analysis", "description": "When you talk to August about your lab reports, you get extreme precision. August has processed over 4.7 million reports with a biomarker extraction accuracy of 98.4%."}}}, "nutrition": {"nav": {"title": "Nutrition"}, "meals": {"title": "Your Meals", "subtitle": "Tap to check out the macros in each meal"}, "upload": {"loading": "Uploading image..."}, "defaultFoodName": "Food Item", "today": "Today", "unknownTime": "Unknown time", "calories": "🔥 Calories", "proteins": "🥩 Proteins", "carbs": "🍞 Carbs", "sugars": "🍬 Sugars", "fat": "🥑 Fat", "caloriesLabel": "Calories", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "Carbohydrates", "fatLabel": "Fat", "sugarLabel": "Sugar", "tips": "Tips:", "macroBreakdown": "Macronutrient Breakdown", "noMacroData": "No macronutrient data available for this food item.", "disclaimer": "For educational use only. Learn more", "disclaimerLink": "here", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "What is your Gender?", "subtitle": "This will be used to calibrate your custom plan.", "male": "Male", "female": "Female", "other": "Other"}, "age": {"title": "What is your Age?", "subtitle": "This will be used to calculate your daily needs."}, "measurements": {"title": "Height & Weight", "subtitle": "Please enter your height in centimeters and weight in kilograms."}, "activity": {"title": "Activity Level", "subtitle": "How often do you exercise?", "none": "No Exercise", "moderate": "Moderate", "high": "High"}, "goal": {"title": "Weight Goal", "subtitle": "What would you like to achieve?", "increase": "Increase", "maintain": "Maintain", "decrease": "Decrease"}, "targetWeight": {"title": "Target Weight", "subtitle": "What is your target weight in kilograms?"}, "setup": {"title": "Setting Up Your Plan", "subtitle": "Please wait while we prepare your nutrition plan."}, "review": {"title": "Review Your Plan", "subtitle": "Review and customize your nutrition plan."}, "height": {"label": "Height (cm)"}, "weight": {"label": "Weight (kg)"}}, "error": {"updateFailed": "Failed to update nutrition data. Please try again.", "parsingError": "Error parsing food data:", "fetchReportsFailed": "Failed to fetch reports data. Please try again.", "missingReportId": "Report ID is missing"}}, "personalize": {"nav": {"title": "Personalize"}, "button": {"saving": "Saving", "review": "Review", "saveNext": "Save & Next"}}, "basicInfo": {"title": "Let's get to know you better", "subtitle": "This information helps us personalize your health recommendations", "age": {"question": "How old are you?", "placeholder": "Enter your age"}, "sex": {"question": "What is your sex?", "placeholder": "Select your sex", "male": "Male", "female": "Female", "other": "Other"}, "height": {"question": "What is your height? (cm)", "placeholder": "Enter your height"}, "weight": {"question": "What is your weight? (kg)", "placeholder": "Enter your weight"}}, "lifestyle": {"title": "Your Lifestyle Habits", "subtitle": "Understanding your daily habits helps us provide better recommendations", "diet": {"question": "What type of diet do you follow?", "placeholder": "Select your diet", "vegetarian": "Vegetarian", "nonVegetarian": "Non-Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Do you exercise regularly?"}, "drinking": {"question": "Do you consume alcohol?"}, "smoking": {"question": "Do you smoke?"}, "sleep": {"question": "How many hours do you sleep per night?", "value": "{{sleep}} hours"}, "hydration": {"question": "How many cups of water do you drink daily?", "value": "{{hydration}} cups ({{liters}}L)"}}, "allergies": {"title": "Do you have any allergies?", "subtitle": "Knowing your allergies helps us provide safer recommendations", "allergyIndex": "Allergy {{index}}", "name": {"question": "What are you allergic to?", "placeholder": "Enter allergy (e.g., Peanuts, Dust)"}, "severity": {"question": "How severe is this allergy?", "placeholder": "Select severity", "mild": "Mild", "moderate": "Moderate", "severe": "Severe"}, "addButton": "Add Another Allergy", "noAllergiesButton": "I don't have any allergies"}, "medications": {"title": "Medications & Supplements", "subtitle": "Tell us about any medications or supplements you're currently taking", "medicationIndex": "Medication {{index}}", "name": {"label": "Medication Name", "placeholder": "Enter medication name"}, "startDate": {"question": "When did you start taking it?", "placeholder": "Select date"}, "type": {"label": "Type of Medication", "shortTerm": "Short Term", "longTerm": "Long Term"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Amount"}, "unit": {"label": "Unit"}, "frequency": {"label": "Frequency", "placeholder": "Times", "perDay": "per day", "perWeek": "per week", "perMonth": "per month", "perYear": "per year"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "Add Another Medication", "noMedicationsButton": "I don't take any medications", "calendar": {"title": "Select Start Date"}}, "conditions": {"title": "Medical Conditions", "subtitle": "Tell us about any medical conditions you have or had in the past", "conditionIndex": "Condition {{index}}", "name": {"label": "Condition Name", "placeholder": "Enter condition (e.g Asthma, etc)"}, "since": {"question": "Since when have you had this condition?", "placeholder": "Select date"}, "current": {"question": "Is it currently troubling you?"}, "medicated": {"question": "Are you taking any medications for this?"}, "addButton": "Add Another Condition", "noConditionsButton": "I don't have any medical conditions", "calendar": {"title": "Select Date"}}, "reproductive": {"title": "Reproductive Health", "subtitle": "This information helps us provide more personalized health recommendations", "menstruation": {"question": "Have you ever menstruated?", "detailsTitle": "Menstruation Details", "regularity": {"question": "How regular is your cycle?", "regular": "Regular", "irregular": "Irregular", "notSure": "Not Sure"}, "cycleLength": {"label": "Average cycle length (days)", "placeholder": "Enter cycle length"}, "flowDays": {"label": "Days of flow: {{flowDays}}", "min": "1 day", "max": "15 days"}, "padsPerDay": {"label": "Pads/tampons per day: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Any symptoms during your period?", "placeholder": "Enter symptoms (e.g., cramps, headaches)"}}, "childbirth": {"question": "Have you experienced childbirth?", "detailsTitle": "Childbirth Details", "children": {"label": "Number of Children"}, "pregnancies": {"label": "Number of Pregnancies"}, "complications": {"question": "Any complications during pregnancy or childbirth?", "placeholder": "Enter complications (if any)"}}}, "review": {"title": "Review Your Information", "subtitle": "Please review the information you've provided before submitting", "sections": {"basicInfo": "Basic Information", "lifestyle": "LifeStyle", "allergies": "Allergies", "medications": "Medications & Supplements", "conditions": "Medical Conditions", "reproductive": "Reproductive Health", "menstruationDetails": "Menstruation Details", "childbirthDetails": "Childbirth Details"}, "fields": {"age": "Age:", "sex": "Sex:", "height": "Height:", "weight": "Weight:", "diet": "Diet:", "exercise": "Exercise:", "drinking": "Drinking:", "smoking": "Smoking:", "sleep": "Sleep:", "hydration": "Hydration:", "allergyIndex": "Allergy {{index}}:", "dose": "Dose:", "frequency": "Frequency:", "type": "Type:", "since": "Since:", "currentlyActive": "Currently Active:", "takingMedication": "Taking Medication:", "hasMenstruated": "Has Menstruated:", "regularity": "Regularity:", "cycleLength": "Cycle Length:", "flowDays": "Flow Days:", "padsPerDay": "Pads/Tampons per Day:", "hasChildbirth": "Has Experienced Childbirth:", "children": "Children:", "pregnancies": "Pregnancies:"}, "notProvided": "Not provided", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} hours per day", "hydration": "{{hydration}} cups ({{liters}}L) per day", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} days"}, "noData": {"allergies": "No allergies provided", "medications": "No medications provided", "conditions": "No medical conditions provided"}, "submitButton": "Submit Information"}, "success": {"title": "Information Updated!", "message": "Thank you for providing your health information. We'll use this to personalize your experience and provide better recommendations.", "benefits": {"insights": "Personalized health insights", "reminders": "Better medication reminders", "recommendations": "Tailored health recommendations"}, "continueButton": "Continue to Dashboard"}, "permissions": {"microphonePermissionDenied": "Microphone permission denied", "microphoneAccessDescription": "August requires accesses your microphone to record audio and send voice notes", "permissionDenied": "Permission Denied", "cameraPermissionRequired": "We need camera permissions to make this work!", "mediaLibraryPermissionRequired": "We need media library permissions to make this work!"}, "voiceRecording": {"recordingTooLong": "Recording Too Long", "recordingTooLongMessage": "Voice recordings must be less than 5 minutes. Please record a shorter message."}, "errors": {"uploadFailed": "Upload Failed", "voiceUploadFailed": "Could not upload the voice recording.", "voiceRecordingFailed": "Failed to send voice recording", "failedToStopRecording": "Failed to stop recording", "photoUploadFailed": "Could not upload the photo.", "failedToTakePhoto": "Failed to take photo", "imageUploadFailed": "Could not upload the image: {{fileName}}", "failedToPickImage": "Failed to pick image", "documentUploadFailed": "Could not upload the document: {{fileName}}", "failedToPickDocument": "Failed to pick document"}, "audioPlayer": {"downloadingAudio": "Downloading audio...", "loadingAudio": "Loading audio..."}, "mediaProcessing": {"processingFile": "Processing Your File", "uploadingSecuring": "Uploading and securing file...", "analyzingContent": "Analyzing document content...", "extractingInfo": "Extracting key information...", "processingInsights": "Processing insights...", "preparingResponse": "Preparing detailed response...", "finalizingResponse": "Finalizing response..."}, "attachments": {"voiceMessage": "Voice Message", "image": "[IMAGE]", "pdf": "[PDF]", "voice": "[VOICE NOTE]"}, "pdf": {"loadingPdf": "Loading PDF..."}, "dateTime": {"yesterday": "Yesterday, "}, "navbar": {"defaultTitle": "august", "selectedCount": "selected"}, "mediaUpload": {"photoLibrary": "Photo Library", "takePhoto": "Take Photo", "chooseFile": "Choose <PERSON>"}, "comingSoon": {"title": "Coming Soon!", "description": " is currently under development. Stay tuned for updates!", "buttonText": "Got it!"}, "clipboard": {"success": "Link copied to clipboard"}, "mediaPhotos": {"emptyState": "No entries yet."}, "foodDetail": {"defaultFoodName": "Food Item", "nutrition": {"totalCalories": "Total Calories", "proteins": "<PERSON>teins", "carbs": "<PERSON><PERSON>", "fat": "Fat", "sugars": "Sugars", "fibers": "Fibers"}}, "reports": {"defaultTitle": "Media Item", "defaultFoodName": "Food Item", "defaultName": "Document", "openButton": "Open in External Viewer", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "Value", "headerRefRange": "Ref Range", "headerStatus": "Status"}, "noData": "No biomarker data available"}, "setup": {"title": "We're setting everything up for you", "inProgress": "In progress...", "progressMessages": {"0": "Calculating Daily Calories", "1": "Optimizing Macro Split", "2": "Creating Meal Plan", "3": "Computing Health Score", "4": "Finalizing Setup"}, "checklistItems": {"0": "Analyzing your health data", "1": "Calculating optimal nutrition plan", "2": "Personalizing your recommendations", "3": "Creating your meal suggestions", "4": "Finalizing your setup"}}, "foodEntry": {"emptyState": "No food entries yet. Take a photo of your meal to add it!"}, "nutritionReview": {"congratulations": "Congratulations!", "subtitle": "Your custom nutrition plan is ready", "submitButton": "Let's get started!", "dailyTargetsTitle": "Your Daily Nutrition Targets", "macroLabels": {"calories": "Calories", "carbs": "<PERSON><PERSON>", "protein": "<PERSON><PERSON>", "fats": "Fats"}, "recommendations": {"title": "How to reach your goals:", "healthScores": "Use health scores to improve your routine", "trackFood": "Track your food intake consistently", "followCalories": "Follow your daily calorie recommendation", "balanceMacros": "Balance your carbs, protein, and fat intake"}}, "editModal": {"titlePrefix": "Edit ", "cancelButton": "Cancel", "saveButton": "Next"}, "processing": {"stages": {"scanning": "Scanning food...", "identifying": "Identifying ingredients...", "extracting": "Extracting nutrients...", "finalizing": "Finalizing results..."}, "error": {"defaultMessage": "No food detected", "subtitle": "Try a different angle"}, "retakeButton": "Tap to retake the pic", "notification": "We'll notify you when done!"}, "chart": {"title": "Nutrition Tracking Over Time", "selectNutrient": "Select Nutrient:", "emptyState": "No nutrition data available yet.", "dropdown": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "sugars": "Sugars"}}, "foodModal": {"defaultName": "Food Item", "defaultDate": "Today", "defaultTime": "Unknown time", "saveChanges": "Save changes", "error": {"title": "Error", "message": "Failed to update nutrition data. Please try again."}, "nutrition": {"calories": "🔥 Calories", "proteins": "🥩 Proteins", "carbs": "🍞 Carbs", "sugars": "🍬 Sugars", "fat": "🥑 Fat"}, "macroBreakdown": {"title": "Macronutrient Breakdown", "noData": "No macronutrient data available for this food item."}, "macroLabels": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "sugar": "Sugar"}}, "infoModal": {"title": "Detailed Info", "edit": "Edit", "save": "Save", "saving": "Saving..", "enterValue": "Enter value", "notSet": "Not set", "age": "Age", "heightCm": "Height (cm)", "weightKg": "Weight (kg)", "targetWeight": "Target Weight", "nutritionTargets": "Nutrition Targets", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fats": "Fats", "gm": "gm", "editNote": "Enter values or leave blank for auto-calculation.", "autoCalculateNote": "Macros are auto-calculated based on your data.", "validation": {"ageMin": "Age must be at least 18", "ageMax": "Age must be below 125", "heightMin": "Height must be at least 50cm", "heightMax": "Height must be below 250cm", "weightMin": "Weight must be at least 30kg", "weightMax": "Weight must be below 500kg", "targetWeightMin": "Target weight must be at least 30kg", "targetWeightMax": "Target weight must be below 500kg", "proteinMin": "Protein must be 0 or more", "carbsMin": "Carbohydrates must be 0 or more", "fatsMin": "Fats must be 0 or more"}}, "tracker": {"calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "excess": "excess", "remaining": "remaining"}, "discoverCards": {"categories": {"nutrition": "Nutrition", "heartHealth": "Heart Health", "mentalHealth": "Mental Health", "fitness": "Fitness", "wellness": "Wellness"}, "titles": {"vitaminB12Recovery": "How Long Does It Take To Recover From Vitamin B12 Deficiency", "vitaminDeficiencyGanglion": "Which Vitamin Deficiency Causes Ganglion Cysts", "vitaminDeficiencyHairFall": "Which Vitamin Deficiency Causes Hair Fall", "vitaminWaters": "Are Vitamin Waters Good For You", "cholesterolHeadaches": "Does High Cholesterol Cause Headaches", "cholesterolEyes": "What Are The Symptoms Of High Cholesterol That Can Be Seen In The Eyes", "diabetesHeadaches": "Can Diabetes Cause Headaches", "chestPainDrinking": "Why Does The Chest Hurt After Drinking", "stressDizziness": "Can Stress Cause Dizziness", "bulimiaFace": "What Is Bulimia Face", "kneeTwitch": "Why Does My Knee Twitch", "noseTwitching": "Why Does Nose Twitching Occur", "piriformisVsSciatica": "What Are The Differences Between Piriformis Syndrome Vs Sciatica", "shoulderBladePinched": "How To Release A Pinched Nerve In The Shoulder Blade", "shoulderPinched": "How To Release A Pinched Nerve In Shoulder", "meniscusTorn": "How To Heal A Torn Meniscus Naturally", "hydrateQuickly": "How To Hydrate Quickly", "periodConstipation": "Is It Normal To Have Constipation During Period", "acneScars": "How To Get Rid Of Acne Scars Naturally Within A Week", "perimenopausePregnancy": "Can You Get Pregnant During Perimenopause"}, "descriptions": {"vitaminB12Recovery": "Discover the recovery timeline for vitamin B12 deficiency and effective remedies to boost your energy levels.", "vitaminDeficiencyGanglion": "Explore the link between vitamin deficiencies and the development of ganglion cysts in the body.", "vitaminDeficiencyHairFall": "Learn about how a lack of essential vitamins can lead to hair loss and what you can do to prevent it.", "vitaminWaters": "Uncover the benefits and potential drawbacks of vitamin waters as part of your daily nutrition.", "cholesterolHeadaches": "Examine the possible connection between high cholesterol levels and the onset of headaches.", "cholesterolEyes": "Learn how high cholesterol can manifest in your eyes and what symptoms to watch for.", "diabetesHeadaches": "Investigate the relationship between diabetes and headache occurrence in daily life.", "chestPainDrinking": "Explore the reasons behind chest pain following the consumption of certain beverages.", "stressDizziness": "Delve into how stress can impact your balance and overall wellbeing, leading to dizziness.", "bulimiaFace": "Understand the physical signs of bulimia, including the effects on facial appearance.", "kneeTwitch": "Investigate potential causes behind involuntary knee twitching and its relation to stress or fatigue.", "noseTwitching": "Learn about the possible reasons for nose twitching and its link to anxiety or other factors.", "piriformisVsSciatica": "Compare the symptoms of piriformis syndrome and sciatica to better understand your condition.", "shoulderBladePinched": "Discover effective techniques to relieve a pinched nerve in your shoulder blade and restore mobility.", "shoulderPinched": "Learn simple exercises and stretches to alleviate nerve compression in the shoulder area.", "meniscusTorn": "Explore natural methods and exercises to support the healing of a torn meniscus.", "hydrateQuickly": "Find out quick and effective ways to rehydrate and maintain optimal body hydration.", "periodConstipation": "Understand the reasons behind constipation during menstruation and learn natural remedies.", "acneScars": "Discover natural remedies and skincare tips to reduce the appearance of acne scars fast.", "perimenopausePregnancy": "Learn about perimenopause, fertility considerations, and what to expect during this stage of life."}}, "specialistConstants": {"nutritionist": {"name": "Nutritionist", "description": "Expert advice on diet, nutrition, and healthy eating habits", "featureName": "Nutrition Specialist"}, "cardiologist": {"name": "Cardiologist", "description": "Specialized in heart health and cardiovascular conditions", "featureName": "Cardiology Specialist"}, "neurologist": {"name": "Neurologist", "description": "Focused on brain, spinal cord, and nervous system disorders", "featureName": "Neurology Specialist"}, "oncologist": {"name": "Oncologist", "description": "Specialized in cancer diagnosis and treatment options", "featureName": "Oncology Specialist"}, "endocrinologist": {"name": "Endocrinologist", "description": "Expert in hormonal conditions and metabolic disorders", "featureName": "Endocrinology Specialist"}}, "languageSwitcher": {"title": "Language", "searchPlaceholder": "Search languages...", "useDeviceLanguage": "Use Device Language", "changingLanguage": "Changing language...", "noResults": "No languages found"}}