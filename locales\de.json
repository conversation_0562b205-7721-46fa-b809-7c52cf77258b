{"common": {"error": "<PERSON><PERSON>", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "sometimes": "<PERSON><PERSON><PERSON>", "close": "Schließen", "cancel": "Abbrechen", "save": "Speichern", "next": "<PERSON><PERSON>", "loading": "Wird geladen...", "version": "v0.0.1.7"}, "welcome": "<PERSON>den Sie sich an, um mit August zu chatten", "notFound": {"title": "<PERSON><PERSON><PERSON>!", "message": "Dieser Bildschirm existiert nicht.", "goHome": "Zum Startbildschirm!"}, "library": {"title": "Gesundheitsbibliothek"}, "specialists": {"title": "Spezialisten", "description": "Beraten Si<PERSON> sich bei spezifischeren gesundheitlichen Bedenken mit spezialisierten Gesundheitsexperten. Wählen Sie unten einen Spezialisten aus:", "generalPhysician": {"title": "Allgemeinmediziner", "description": "Für allgemeine gesundheitliche Bedenken und Grundversorgung."}, "nutritionist": {"title": "Ernährungsberater", "description": "<PERSON><PERSON><PERSON> zu Ernährung, Ernährung und Gewichtsmanagement."}, "cardiologist": {"title": "Kardiologe", "description": "<PERSON><PERSON><PERSON> herzbezogene Bedenken und kardiovaskuläre Gesundheit."}, "neurologist": {"title": "Neurologe", "description": "Für Probleme mit Gehirn, Rückenmark und Nervensystem."}, "oncologist": {"title": "Onkologe", "description": "<PERSON><PERSON>r krebsbezogene Bedenken und Behandlungen."}, "endocrinologist": {"title": "Endokrinologe", "description": "Für hormonbedingte Störungen und Diabetesmanagement."}, "dermatologist": {"title": "Dermatologe", "description": "<PERSON><PERSON><PERSON>, Haar- und Nagelprobleme."}, "psychiatrist": {"title": "Psychiater", "description": "Für psychische Gesundheitsprobleme und psychisches Wohlbefinden."}}, "profile": {"title": "Profil", "defaultName": "Gas<PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Namen ein", "saving": "Wird gespeichert...", "noPhoneNumber": "<PERSON>ine Telefonnummer", "loggingOut": "Melde mich ab...", "about": {"title": "<PERSON><PERSON>", "description": "Erfahren Sie mehr über August"}, "whatsapp": {"title": "WhatsApp", "description": "Chatten Sie mit August auf WhatsApp"}, "refer": {"title": "Weiterempfehlen", "description": "August gefallen? <PERSON>ilen Sie es mit Ihren Freunden"}, "deleteAccount": {"title": "Konto löschen", "description": "Es tut uns leid, dass Si<PERSON> gehen"}, "logout": {"title": "Abmelden", "description": "<PERSON><PERSON><PERSON>e bald wieder. Wir werden Sie vermissen"}, "shareMessage": "👋Hey, schau dir diese tolle App an, die ich benutze!\n\n\n\n➡️Ich benutze August, um schnelle, zuverlässige Gesundheitsinformationen und -beratung zu erhalten. Es ist wie ein Arzt in der Tasche! Schau es dir hier an:", "error": {"loadFailed": "Fehler beim Laden der Benutzerdaten", "fetchError": "Ein Fehler ist beim Abrufen der Benutzerdaten aufgetreten", "updateNameFailed": "Fehler beim Aktualisieren des Namens", "updateNameError": "Ein Fehler ist beim Aktualisieren des Namens aufgetreten", "loadFoodData": "Fehler beim Laden der Essensdaten", "logoutError": "Fehler während der Abmeldung:", "shareError": "<PERSON><PERSON> beim <PERSON> von <PERSON>:"}}, "error": {"title": "Etwas ist schief gelaufen", "checkLogs": "Bitte überprüfen Sie die Protokolle Ihres Geräts auf weitere Details.", "unknown": "Unbekannter Fehler", "unknownFile": "Unbekannte Datei", "unknownLine": "Unbekannte Zeile", "unknownColumn": "Unbekannte Spalte"}, "auth": {"phone": {"selectCountry": "Wähle Land", "searchCountries": "<PERSON><PERSON>", "validation": {"invalidPhone": "Bitte gib eine gültige Telefonnummer ein", "invalidDigits": "<PERSON>te gib eine gültige Telefonnummer ein (7-15 Ziffern)"}}, "header": {"title": "Erhalte sofort und privat Klarheit über deine gesundheitlichen Bedenken", "subtitle": "Durchdachte Anleitung. Kein Stress. Keine Verwirrung.", "emphasis": "Einfach Klarheit."}, "greeting": "Hallo 👋", "phoneNumber": "Telefonnummer", "requestOTP": "OTP anfordern", "otp": {"title": "Einmaliges Passwort", "verify": "OTP verifizieren", "sending": "Sende...", "countdown": "OTP in {{countdown}}s erneut senden", "resend": "OTP erneut senden", "sentTo": "OTP gesendet an ", "whatsappSuffix": " über WhatsApp"}, "disclaimer": {"prefix": "Mit der Registrierung stimmst du unseren ", "continuePrefix": "Durch die Fortsetzung stimmst du unseren ", "termsOfService": "Nutzungsbedingungen", "and": " und ", "privacyPolicy": "Datenschutzbestimmungen", "whatsappConsent": " zu und willigst ein, Updates und Erinnerungen von uns über WhatsApp zu erhalten."}}, "onboarding": {"preAuth": {"welcome": {"title": "Willkommen bei August!", "buttonText": "<PERSON> geht's"}}, "postAuth": {"step1": {"title": "Hey!", "subtitle": "<PERSON><PERSON> bin August 👋", "description": "<PERSON>ell dir mich als die gemütliche Ecke auf deinem\nGerät vor, wo du all deine gesundheitlichen\nNeugierigkeiten erforschst.", "subdescription": "<PERSON><PERSON><PERSON> dich frei, alles zu fragen, was dir auf dem <PERSON>zen liegt.\n<PERSON><PERSON>, keine G<PERSON>zen!", "placeholder": "Wie soll ich dich nennen?"}, "step2": {"title": "<PERSON><PERSON> {{userName}},", "subtitle": "Das kann ich:", "features": {"health": {"title": "Beantworte deine", "subtitle": "Gesundheitsfragen"}, "nutrition": {"title": "Verfolge deine", "subtitle": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Berichte"}}}}, "pills": {"thoughtful": "Durchdacht", "careful": "Sorg<PERSON><PERSON><PERSON><PERSON>", "accurate": "<PERSON><PERSON>"}, "features": {"symptoms": {"title": "Überprüfe deine Symptome", "description": "Mir ist seit einer Woche übel. Was ist los mit mir?"}, "prescriptions": {"title": "Analysiere deine Rezepte", "description": "Lade Rezepte hoch und verstehe sie wie ein Arzt."}, "medicine": {"title": "Kenn deine Medikamente", "description": "Wechselwirkt Metformin bei meinem PCOS mit meinen ADHS-Pillen?"}, "plans": {"title": "Erhalte personalisierte Pläne", "description": "Kannst du mir einen Ernährungs- und Fitnessplan zur Senkung meines HbA1c-Spiegels geben?"}}, "buttons": {"getStarted": "<PERSON> geht's", "next": "<PERSON><PERSON>"}, "errors": {"nameRequired": "<PERSON>te gib deinen <PERSON>n ein"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Entdecken", "nutrition": "Ernährung", "personalize": "Personalisieren"}, "chat": {"nav": {"title": "August"}, "me": "Ich", "augustName": "August", "input": {"placeholder": "Frage August...", "disclaimer": "August kann Fehler machen. Bitte Rücksprache mit einem Arzt halten."}, "list": {"loadingMessages": "Lade Nachrichten...", "noMessages": "Noch keine Nachrichten. Starte ein Gespräch!"}, "connection": {"offlineMessage": "<PERSON>e scheinen offline zu sein. Stellen Sie eine Verbindung wieder her, um Nachrichten zu senden.", "connecting": "Verbin<PERSON>...", "tryAgain": "<PERSON><PERSON><PERSON> versuchen"}, "prompts": {"uploadReport": "Bericht hochladen", "speakInHindi": "Hindi mein baat karo", "notFeelingWell": "<PERSON> geht es nicht gut", "whatIsMyBMI": "Wie hoch ist mein BMI?", "nutritionAdvice": "Ernährungsberatung", "sleepBetter": "<PERSON><PERSON> sch<PERSON>fen"}, "citations": {"referenceText": "Weitere Informationen zu diesem Gespräch finden Sie unter:"}, "actions": {"copiedToClipboard": "In die Zwischenablage kopiert", "copied": "<PERSON><PERSON><PERSON>"}, "share": {"introText": "👋Hey, schau dir mein Gespräch mit August an:\n\n", "downloadText": "\n\n➡️Lade August herunter, um mit deinem freundlichen KI-Gesundheitsbegleiter zu chatten:\n"}}, "discover": {"nav": {"title": "Entdecken"}, "categories": {"all": "Alle", "heartHealth": "Herzgesundheit", "nutrition": "Ernährung", "mentalHealth": "Mentale Gesundheit", "fitness": "Fitness", "wellness": "Wellness"}, "cards": {"empty": "<PERSON><PERSON> diese Kategorie verfügbar"}, "sections": {"features": "Funktionen"}, "features": {"healthLibrary": {"title": "Gesundheitsbibliothek", "description": "<PERSON><PERSON><PERSON> zu vertrauenswürdigen, zuverlässigen und aktuellen medizinischen Informationen – vö<PERSON>g kostenlos."}, "nutritionTracker": {"title": "Ernährungstracker", "description": "Haben <PERSON> sich schon ein<PERSON>, ob <PERSON><PERSON> einfach ein Foto Ihres E<PERSON>s hochladen und all Ihre Ernährungsziele verfolgen könnten? August kann genau das!"}, "multilingualSupport": {"title": "Mehrsprachige Unterstützung", "description": "Sie können mit August in jeder Sprache kommunizieren, mit der Sie sich wohlfühlen! August ist immer da, um zuzuh<PERSON><PERSON>, zu unterstützen und Ihnen jederzeit zu antworten.", "samplePrompt": "Hindi mein baat karo"}, "labReportAnalysis": {"title": "Laborberichtanalyse", "description": "Wenn Sie mit August über Ihre Laborberichte sprechen, erhalten Sie höchste Präzision. August hat über 4,7 Millionen Berichte mit einer Genauigkeit der Biomarker-Extraktion von 98,4 % verarbeitet."}}}, "nutrition": {"nav": {"title": "Ernährung"}, "meals": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>, um die Makros jeder Mahlzeit anzuzeigen"}, "upload": {"loading": "Bild wird hochgeladen..."}, "defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "unknownTime": "Unbekannte Zeit", "calories": "🔥 <PERSON><PERSON>ien", "proteins": "🥩 Proteine", "carbs": "🍞 Kohlenhydrate", "sugars": "🍬 Zucker", "fat": "🥑 <PERSON>tt", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "Kohlenhydrate", "fatLabel": "<PERSON><PERSON>", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Tipps:", "macroBreakdown": "Makronährstoff-Aufschlüsselung", "noMacroData": "<PERSON><PERSON><PERSON> dieses Lebensmittel sind keine Makronährstoffdaten verfügbar.", "disclaimer": "<PERSON><PERSON> zu Bildungszwecken. Me<PERSON> erfahren", "disclaimerLink": "hier", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Was ist dein Geschlecht?", "subtitle": "Dies wird verwendet, um deinen individuellen Plan zu kalibrieren.", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "Divers"}, "age": {"title": "Wie alt bist du?", "subtitle": "Dies wird verwendet, um deinen täglichen Bedarf zu berechnen."}, "measurements": {"title": "Größe & Gewicht", "subtitle": "Bitte gib deine Größe in Zentimetern und dein Gewicht in Kilogramm ein."}, "activity": {"title": "Aktivitätslevel", "subtitle": "Wie oft trainierst du?", "none": "<PERSON><PERSON>", "moderate": "<PERSON>rat", "high": "Hoch"}, "goal": {"title": "Gewichtsziel", "subtitle": "Was möchtest du erreichen?", "increase": "<PERSON><PERSON><PERSON><PERSON>", "maintain": "Beibehalten", "decrease": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Zielgewicht", "subtitle": "Was ist dein Zielgewicht in Kilogramm?"}, "setup": {"title": "Einrichtung deines Plans", "subtitle": "<PERSON>te warte, während wir deinen Ernährungsplan vorbereiten."}, "review": {"title": "Überprüfe deinen Plan", "subtitle": "Überprüfe und passe deinen Ernährungsplan an."}, "height": {"label": "Größe (cm)"}, "weight": {"label": "Gewicht (kg)"}}, "error": {"updateFailed": "Aktualisierung der Ernährungdaten fehlgeschlagen. Bitte versuche es erneut.", "parsingError": "Fehler beim <PERSON> der Lebensmittel-Daten:", "fetchReportsFailed": "Abrufen der Berichtsdaten fehlgeschlagen. Bitte versuche es erneut.", "missingReportId": "Berichts-ID fehlt"}}, "personalize": {"nav": {"title": "Personalisieren"}, "button": {"saving": "Speichern", "review": "Überprüfen", "saveNext": "Speichern & Weiter"}}, "basicInfo": {"title": "Lass uns dich besser kennenlernen", "subtitle": "Diese Informationen helfen uns, deine Gesundheits-Empfehlungen zu personalisieren", "age": {"question": "Wie alt bist du?", "placeholder": "Gib dein Alter ein"}, "sex": {"question": "Was ist dein Geschlecht?", "placeholder": "Wähle dein Geschlecht", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "Divers"}, "height": {"question": "Wie groß bist du? (cm)", "placeholder": "Gib deine Größe ein"}, "weight": {"question": "Wie viel wiegst du? (kg)", "placeholder": "Gib dein Gewicht ein"}}, "lifestyle": {"title": "<PERSON>hre Lifestyle-Gewohnheiten", "subtitle": "Das Verständnis Ihrer täglichen Gewohnheiten hilft uns, bessere Empfehlungen zu geben", "diet": {"question": "Welche Art von Ernährung verfolgen Sie?", "placeholder": "Wählen Sie Ihre Ernährung", "vegetarian": "Vegetarisch", "nonVegetarian": "Nicht-Vegetarisch", "vegan": "Vegan", "pescatarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Treiben Sie regelmäßig Sport?"}, "drinking": {"question": "Konsumieren Sie Alkohol?"}, "smoking": {"question": "Rauchen Sie?"}, "sleep": {"question": "Wie viele Stunden schlafen Sie pro Nacht?", "value": "{{sleep}} <PERSON><PERSON><PERSON>"}, "hydration": {"question": "Wie viele Tassen Was<PERSON> trinken Si<PERSON> täglich?", "value": "{{hydration}} <PERSON><PERSON> ({{liters}}L)"}}, "allergies": {"title": "Haben Sie Allergien?", "subtitle": "Das Wissen um Ihre Allergien hilft uns, sicherere Empfehlungen zu geben", "allergyIndex": "Allergie {{index}}", "name": {"question": "W<PERSON><PERSON> sind Sie allergisch?", "placeholder": "Allergie eingeben (z. B. Erdnüsse, Staub)"}, "severity": {"question": "Wie stark ist diese Allergie?", "placeholder": "Stärke auswählen", "mild": "Mild", "moderate": "<PERSON><PERSON><PERSON>", "severe": "<PERSON>"}, "addButton": "Weitere Allergie hinzufügen", "noAllergiesButton": "Ich habe keine Allergien"}, "medications": {"title": "Medikamente & Nahrungsergänzungsmittel", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns von allen Medikamenten oder Nahrungsergänzungsmitteln, die Si<PERSON> derzeit einnehmen", "medicationIndex": "Medikament {{index}}", "name": {"label": "Medikamentenname", "placeholder": "Medikamentennamen e<PERSON>ben"}, "startDate": {"question": "Wann haben Si<PERSON> mit der Einnahme begonnen?", "placeholder": "Da<PERSON> ausw<PERSON>en"}, "type": {"label": "Art des Medikaments", "shortTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "longTerm": "Lang<PERSON><PERSON><PERSON>"}, "dose": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "unit": {"label": "Einheit"}, "frequency": {"label": "Häufigkeit", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "pro Tag", "perWeek": "pro Woche", "perMonth": "pro <PERSON><PERSON>", "perYear": "pro Jahr"}, "units": {"mg": "mg", "ml": "ml", "iu": "IE", "puffs": "Stöße", "drops": "Tropfen", "tsp": "TL", "tbsp": "EL", "cups": "Tassen"}, "addButton": "Weitere Medikament hinzufügen", "noMedicationsButton": "<PERSON>ch nehme keine Medikamente", "calendar": {"title": "Startdatum auswählen"}}, "conditions": {"title": "Krankheiten", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> un<PERSON> von Krankheiten, die Si<PERSON> haben oder in der Vergangenheit hatten", "conditionIndex": "Krankheit {{index}}", "name": {"label": "Krankheitsname", "placeholder": "Krankheit eingeben (z. B. Asthma usw.)"}, "since": {"question": "Seit wann haben Sie diese Krankheit?", "placeholder": "Da<PERSON> ausw<PERSON>en"}, "current": {"question": "Belästigt sie Sie derzeit?"}, "medicated": {"question": "<PERSON>ehmen Si<PERSON> dafür Medikamente ein?"}, "addButton": "Weitere Krankheit hinzufügen", "noConditionsButton": "Ich habe keine Krankheiten", "calendar": {"title": "Da<PERSON> ausw<PERSON>en"}}, "reproductive": {"title": "Reproduktive Gesundheit", "subtitle": "Diese Informationen helfen uns, personalisiertere Gesundheits Empfehlungen zu geben", "menstruation": {"question": "Hattest du schon deine Menstruation?", "detailsTitle": "Details zur Menstruation", "regularity": {"question": "Wie regelmäßig ist dein Zyklus?", "regular": "Regel<PERSON><PERSON><PERSON><PERSON>", "irregular": "Unregelm<PERSON><PERSON><PERSON>", "notSure": "<PERSON><PERSON><PERSON>"}, "cycleLength": {"label": "Durchschnittliche Zykluslänge (Tage)", "placeholder": "Zykluslänge eingeben"}, "flowDays": {"label": "Tage der Blutung: {{flowDays}}", "min": "1 Tag", "max": "15 Tage"}, "padsPerDay": {"label": "Binden/Tampons pro Tag: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Hast du Symptome während deiner Periode?", "placeholder": "Symptome eingeben (z.B. Krämpfe, Kopfschmerzen)"}}, "childbirth": {"question": "Hast du schon ein <PERSON> g<PERSON>oren?", "detailsTitle": "Details zur Geburt", "children": {"label": "<PERSON><PERSON><PERSON>"}, "pregnancies": {"label": "Anzahl der Schwangerschaften"}, "complications": {"question": "Gab es Komplikationen während der Schwangerschaft oder Geburt?", "placeholder": "Komplikationen (falls vorhanden) eingeben"}}}, "review": {"title": "Überprüfe deine Angaben", "subtitle": "Bitte überprüfe die von dir angegebenen Informationen, bevor du sie abschickst", "sections": {"basicInfo": "Basisinformationen", "lifestyle": "Lebensstil", "allergies": "Allergien", "medications": "Medikamente & Nahrungsergänzungsmittel", "conditions": "Krankheiten", "reproductive": "Reproduktive Gesundheit", "menstruationDetails": "Details zur Menstruation", "childbirthDetails": "Details zur Geburt"}, "fields": {"age": "Alter:", "sex": "Geschlecht:", "height": "Größe:", "weight": "Gewicht:", "diet": "Ernährung:", "exercise": "Sport:", "drinking": "Alkohol:", "smoking": "Rauchen:", "sleep": "Schlaf:", "hydration": "Hydratation:", "allergyIndex": "Allergie {{index}}:", "dose": "Dosierung:", "frequency": "Häufigkeit:", "type": "Art:", "since": "Seit:", "currentlyActive": "Aktuell aktiv:", "takingMedication": "Nimmt Medikamente:", "hasMenstruated": "Hat Menstruation:", "regularity": "Regelmäßigkeit:", "cycleLength": "Zykluslänge:", "flowDays": "Blutungstage:", "padsPerDay": "Binden/Tampons pro Tag:", "hasChildbirth": "Hat ein <PERSON> g<PERSON>oren:", "children": "Kinder:", "pregnancies": "Schwangerschaften:"}, "notProvided": "Nicht angegeben", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} Stunden pro Tag", "hydration": "{{hydration}} <PERSON><PERSON> ({{liters}}L) pro Tag", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} <PERSON>e"}, "noData": {"allergies": "<PERSON>ine Allergien angegeben", "medications": "Keine Medikamente angegeben", "conditions": "<PERSON>ine Krankheiten angegeben"}, "submitButton": "<PERSON><PERSON> absenden"}, "success": {"title": "Informationen aktualisiert!", "message": "Vielen Dank für deine Gesundheitsinformationen. Wir verwenden diese, um deine Erfahrung zu personalisieren und bessere Empfehlungen zu geben.", "benefits": {"insights": "Personalisierte Gesundheitseinblicke", "reminders": "Bessere Medikamentenerinnerungen", "recommendations": "Maßgeschneiderte Gesundheitsempfehlungen"}, "continueButton": "Weiter zum Dashboard"}, "permissions": {"microphonePermissionDenied": "Mikrofonzugriff verweigert", "microphoneAccessDescription": "August ben<PERSON><PERSON><PERSON> auf Ihr Mikrofon, um Audioaufnahmen aufzuzeichnen und Sprachnotizen zu senden", "permissionDenied": "<PERSON><PERSON><PERSON> verweigert", "cameraPermissionRequired": "Wir benötigen Kamerazugriff, damit dies funktioniert!", "mediaLibraryPermissionRequired": "Wir benötigen Zugriff auf Ihre Medienbibliothek, damit dies funktion<PERSON>t!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON><PERSON><PERSON> zu lang", "recordingTooLongMessage": "Sprachaufnahmen dürfen nicht länger als 5 Minuten sein. Bitte nehmen Si<PERSON> eine kürzere Nachricht auf."}, "errors": {"uploadFailed": "Upload fehlgeschlagen", "voiceUploadFailed": "Die Sprachaufnahme konnte nicht hochgeladen werden.", "voiceRecordingFailed": "Senden der Sprachaufnahme fehlgeschlagen", "failedToStopRecording": "Aufnahme konnte nicht beendet werden", "photoUploadFailed": "Das Foto konnte nicht hochgeladen werden.", "failedToTakePhoto": "Foto konnte nicht aufgenommen werden", "imageUploadFailed": "Das Bild konnte nicht hochgeladen werden: {{fileName}}", "failedToPickImage": "Bild konnte nicht ausgewählt werden", "documentUploadFailed": "Das Dokument konnte nicht hochgeladen werden: {{fileName}}", "failedToPickDocument": "Dokument konnte nicht ausgewählt werden"}, "audioPlayer": {"downloadingAudio": "Audio wird heruntergeladen...", "loadingAudio": "Audio wird geladen..."}, "mediaProcessing": {"processingFile": "Verarbeite Ihre Datei", "uploadingSecuring": "Datei wird hochgeladen und gesichert...", "analyzingContent": "<PERSON><PERSON><PERSON><PERSON>...", "extractingInfo": "Extrahiere wichtige Informationen...", "processingInsights": "Verarbeite Erkenntnisse...", "preparingResponse": "Bereite detaillierte Antwort vor...", "finalizingResponse": "Antwort wird fertiggestellt..."}, "attachments": {"voiceMessage": "Sprachnachricht", "image": "[BILD]", "pdf": "[PDF]", "voice": "[SPRECHMEMO]"}, "pdf": {"loadingPdf": "PDF wird geladen..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "ausgewählt"}, "mediaUpload": {"photoLibrary": "Fotobibliothek", "takePhoto": "Foto aufnehmen", "chooseFile": "<PERSON>i ausw<PERSON>hlen"}, "comingSoon": {"title": "Bald verfügbar!", "description": " wird gerade entwickelt. Bleiben Sie für Updates dran!", "buttonText": "Verstanden!"}, "clipboard": {"success": "Link in die Zwischenablage kopiert"}, "mediaPhotos": {"emptyState": "Noch keine Einträge."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nutrition": {"totalCalories": "Gesamtenergie", "proteins": "Proteine", "carbs": "Kohlenhydrate", "fat": "<PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON>", "fibers": "Ballaststoffe"}}, "reports": {"defaultTitle": "Medienelement", "defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultName": "Dokument", "openButton": "In externer <PERSON><PERSON><PERSON><PERSON> ö<PERSON>", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "Wert", "headerRefRange": "Referenzbereich", "headerStatus": "Status"}, "noData": "<PERSON><PERSON>-<PERSON><PERSON>"}, "setup": {"title": "Wir richten alles für <PERSON> ein", "inProgress": "Wird bearbeitet...", "progressMessages": {"0": "Berechne tägliche Kalorien", "1": "Optimiere Makro-Aufteilung", "2": "<PERSON><PERSON>elle Ernährungsplan", "3": "Berechne Gesundheitswert", "4": "Abschluss der Einrichtung"}, "checklistItems": {"0": "<PERSON><PERSON><PERSON>re Ihre Gesundheitsdaten", "1": "Berechne optimalen Ernährungsplan", "2": "Personalisieren Sie Ihre Empfehlungen", "3": "<PERSON><PERSON><PERSON> Ihre Speisevorschläge", "4": "Abschluss Ihrer Einrichtung"}}, "foodEntry": {"emptyState": "Noch keine Nahrungsmitteleinträge. Mach ein Foto deiner Mahlzeit, um sie hinzuzufügen!"}, "nutritionReview": {"congratulations": "Herzlichen Glückwunsch!", "subtitle": "Ihr individueller Ernährungsplan ist fertig", "submitButton": "Los geht's!", "dailyTargetsTitle": "Ihre täglichen Ernährungsziele", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "Kohlenhydrate", "protein": "<PERSON><PERSON>", "fats": "<PERSON><PERSON>"}}, "editModal": {"titlePrefix": "<PERSON><PERSON><PERSON> ", "cancelButton": "Abbrechen", "saveButton": "<PERSON><PERSON>"}, "processing": {"stages": {"scanning": "<PERSON><PERSON><PERSON><PERSON><PERSON> scannen...", "identifying": "Zutaten identifizieren...", "extracting": "Nährstoffe extrahieren...", "finalizing": "Ergebnisse finalisieren..."}, "error": {"defaultMessage": "<PERSON><PERSON>", "subtitle": "Versuchen Sie es mit einem andere<PERSON>"}, "retakeButton": "<PERSON><PERSON><PERSON>, um das Bild erneut aufzunehmen", "notification": "Wir benachrichtigen Si<PERSON>, wenn es fertig ist!"}, "chart": {"title": "Ernährungsverfolgung im Zeitverlauf", "selectNutrient": "Nährstoff auswählen:", "emptyState": "Noch keine Ernährungdaten verfügbar.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Kohlenhydrate", "fat": "<PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultDate": "<PERSON><PERSON>", "defaultTime": "Unbekannte Zeit", "saveChanges": "Änderungen speichern", "error": {"title": "<PERSON><PERSON>", "message": "Die Aktualisierung der Ernährungdaten ist fehlgeschlagen. Bitte versuchen Sie es erneut."}, "nutrition": {"calories": "🔥 <PERSON><PERSON>ien", "proteins": "🥩 Proteine", "carbs": "🍞 Kohlenhydrate", "sugars": "🍬 Zucker", "fat": "🥑 <PERSON>tt"}, "macroBreakdown": {"title": "Makronährstoff-Aufschlüsselung", "noData": "<PERSON><PERSON><PERSON> dieses Lebensmittel sind keine Makronährstoffdaten verfügbar."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Kohlenhydrate", "fat": "<PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Detaillierte Informationen", "edit": "<PERSON><PERSON><PERSON>", "save": "Speichern", "saving": "Speichern...", "enterValue": "<PERSON><PERSON> e<PERSON>ben", "notSet": "<PERSON>cht festgelegt", "age": "Alter", "heightCm": "Größe (cm)", "weightKg": "Gewicht (kg)", "targetWeight": "Zielgewicht", "nutritionTargets": "Ernährungsziele", "protein": "<PERSON><PERSON>", "carbs": "Kohlenhydrate", "fats": "<PERSON><PERSON>", "gm": "g", "editNote": "<PERSON><PERSON><PERSON> Sie Werte ein oder lassen Si<PERSON> die Felder leer, um eine automatische Berechnung durchzuführen.", "autoCalculateNote": "<PERSON><PERSON><PERSON> werden basierend auf Ihren Daten automatisch berechnet.", "validation": {"ageMin": "Das Alter muss mindestens 18 Jahre betragen", "ageMax": "Das Alter muss unter 125 Jahren liegen", "heightMin": "Die Größe muss mindestens 50 cm betragen", "heightMax": "Die Größe muss unter 250 cm liegen", "weightMin": "Das Gewicht muss mindestens 30 kg betragen", "weightMax": "Das Gewicht muss unter 500 kg liegen", "targetWeightMin": "Das Zielgewicht muss mindestens 30 kg betragen", "targetWeightMax": "Das Zielgewicht muss unter 500 kg liegen", "proteinMin": "Protein muss 0 oder mehr betragen", "carbsMin": "Kohlenhydrate müssen 0 oder mehr betragen", "fatsMin": "Fette müssen 0 oder mehr betragen"}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "Kohlenhydrate", "fat": "<PERSON><PERSON>", "excess": "Überschuss", "remaining": "verbleibend"}, "specialistConstants": {"nutritionist": {"name": "Ernährungsberater", "description": "Expertenrat zu Ernährung, Diät und gesunden Essgewohnheiten", "featureName": "Ernährungsberatung"}, "cardiologist": {"name": "Kardiologe", "description": "Spezialisiert auf Herzkreislauferkrankungen", "featureName": "Kardiologie"}, "neurologist": {"name": "Neurologe", "description": "Konzentriert auf Erkrankungen des Gehirns, des Rückenmarks und des Nervensystems", "featureName": "Neurologie"}, "oncologist": {"name": "Onkologe", "description": "Spezialisiert auf Krebsdiagnose und Behandlungsmöglichkeiten", "featureName": "Onkologie"}, "endocrinologist": {"name": "Endokrinologe", "description": "Experte für Hormonstörungen und Stoffwechselerkrankungen", "featureName": "Endokrinologie"}}, "discoverCards": {"categories": {"nutrition": "Ernährung", "heartHealth": "Herzgesundheit", "mentalHealth": "Mentale Gesundheit", "fitness": "Fitness", "wellness": "Wellness"}, "titles": {"vitaminB12Recovery": "Wie lange dauert es, sich von einem Vitamin-B12-<PERSON><PERSON> zu erholen?", "vitaminDeficiencyGanglion": "Welcher Vitaminmangel verursacht Ganglienzysten?", "vitaminDeficiencyHairFall": "Welcher <PERSON><PERSON><PERSON><PERSON> veru<PERSON>cht Haarausfall?", "vitaminWaters": "Sind Vitaminwässer gut für einen?", "cholesterolHeadaches": "Verursacht hoher Cholesterinspiegel Kopfschmerzen?", "cholesterolEyes": "Welche Symptome eines hohen Cholesterinspiegels sind an den Augen erkennbar?", "diabetesHeadaches": "Kann Diabetes Kopfschmerzen verursachen?", "chestPainDrinking": "Warum schmerzt die Brust nach dem Trinken?", "stressDizziness": "Kann Stress Schwindel verursachen?", "bulimiaFace": "Was ist ein Bulimie-Gesicht?", "kneeTwitch": "Warum zuckt mein Knie?", "noseTwitching": "Warum zuckt die Nase?", "piriformisVsSciatica": "Was sind die Unterschiede zwischen Piriformis-Syndrom und Ischias?", "shoulderBladePinched": "Wie kann man einen eingeklemmten Nerv im Schulterblatt befreien?", "shoulderPinched": "Wie kann man einen eingeklemmten Nerv in der Schulter befreien?", "meniscusTorn": "Wie heilt man einen Meniskusriss auf natürliche Wei<PERSON>?", "hydrateQuickly": "Wie kann man sich schnell hydrieren?", "periodConstipation": "Ist es normal, während der Periode Verstopfung zu haben?", "acneScars": "Wie kann man Aknenarben innerhalb einer Woche auf natürliche Wei<PERSON> entfernen?", "perimenopausePregnancy": "Kann man während der Perimenopause schwanger werden?"}, "descriptions": {"vitaminB12Recovery": "Entdecken Sie den Genesungszeitraum bei Vitamin-B12-Mangel und effektive Mittel zur Steigerung Ihres Energieniveaus.", "vitaminDeficiencyGanglion": "Erfahren Sie mehr über den Zusammenhang zwischen Vitaminmangel und der Entstehung von Ganglienzysten im Körper.", "vitaminDeficiencyHairFall": "Informieren Si<PERSON> sich da<PERSON><PERSON><PERSON>, wie ein Mangel an essentiellen Vitaminen zu Haarausfall führen kann und was Sie dagegen tun können.", "vitaminWaters": "Entdecken Sie die Vorteile und möglichen Nachteile von Vitaminwässern als Teil Ihrer täglichen Ernährung.", "cholesterolHeadaches": "Untersuchen Sie den möglichen Zusammenhang zwischen hohen Cholesterinwerten und dem Auftreten von Kopfsch<PERSON>zen.", "cholesterolEyes": "<PERSON><PERSON><PERSON><PERSON>, wie sich hoher Cholesterinspiegel in Ihren Augen manifestieren kann und auf welche Symptome Sie achten sollten.", "diabetesHeadaches": "Untersuchen Sie den Zusammenhang zwischen Diabetes und dem Auftreten von Kopfschmerzen im Alltag.", "chestPainDrinking": "Ergründen Sie die Ursachen für Brustschmerzen nach dem Konsum bestimmter Getränke.", "stressDizziness": "Tauchen Si<PERSON> ein in die Auswirkungen von Stress auf Ihr Gleichgewicht und Ihr allgemeines Wohlbefinden, die zu Schwindel führen können.", "bulimiaFace": "Verstehen Sie die körperlichen Anzeichen von Bulimie, einschließlich der Auswirkungen auf das Gesichtsbild.", "kneeTwitch": "Untersuchen Sie mögliche Ursachen für unwillkürliches Zucken des Knies und dessen Zusammenhang mit Stress oder Müdigkeit.", "noseTwitching": "Erfahren Sie mehr über die möglichen Gründe für Nasenzucken und dessen Zusammenhang mit Angstzuständen oder anderen Faktoren.", "piriformisVsSciatica": "Vergleichen Sie die Symptome des Piriformis-Syndroms und der Ischias, um Ihre Erkrankung besser zu verstehen.", "shoulderBladePinched": "Entdecken Sie effektive Techniken zur Linderung eines eingeklemmten Nervs in Ihrem Schulterblatt und zur Wiederherstellung der Beweglichkeit.", "shoulderPinched": "<PERSON>rnen Sie einfache Übungen und Dehnungen, um Nervenkompressionen im Schulterbereich zu lindern.", "meniscusTorn": "Erkunden Sie natürliche Methoden und Übungen zur Unterstützung der Heilung eines Meniskusrisses.", "hydrateQuickly": "Finden Sie schnelle und effektive Möglichkeiten, um Ihren Flüssigkeitshaushalt wieder aufzufüllen und eine optimale Körperhydration aufrechtzuerhalten.", "periodConstipation": "Verstehen Sie die Gründe für Verstopfung während der Menstruation und lernen Sie natürliche Heilmittel kennen.", "acneScars": "Entdecken Sie natürliche Heilmittel und Hautpflegetipps, um das Erscheinungsbild von Aknenarben schnell zu reduzieren.", "perimenopausePregnancy": "Erfahren Sie mehr über die Perimenopause, Überlegungen zur Fruchtbarkeit und was Sie in dieser Lebensphase erwarten können."}}}