import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { moderateScale } from 'react-native-size-matters';

const heights = [8, 12, 18, 24, 18, 12, 8]; // Symmetric tapering
const barCount = 40;

const DummyWaveform: React.FC = () => {
  return (
    <View style={styles.container}>
      {Array.from({ length: barCount }).map((_, idx) => {
        const patternHeight = heights[idx % heights.length];
        return (
          <View
            key={idx}
            style={[
              styles.bar,
              { height: patternHeight } as ViewStyle,
            ]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 50,
    backgroundColor: '#eaeaea',
    borderRadius: 15,
    paddingHorizontal: 6,
    paddingVertical: 4,
    marginLeft: moderateScale(6),
  },
  bar: {
    width: 3,
    marginHorizontal: 1.5,
    backgroundColor: '#5c5c5c',
    borderRadius: 2,
  },
});

export default DummyWaveform;