import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  ScrollView,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  useWindowDimensions,
  Linking,
  FlatList,
} from "react-native";
import { colors } from "@/constants/colors";
import { X, FileText } from "lucide-react-native";
import { parseNutritionalData } from "@/utils/nutrition/parseNutritionalData";
import logger from '@/utils/logger/logger';
import { useTranslation } from 'react-i18next';

// Responsive sizing helper functions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");
const scale = SCREEN_WIDTH / 375; // Base width is 375 (iPhone X)

const normalize = (size: number) => {
  const newSize = size * scale;
  if (Platform.OS === "ios") {
    return Math.round(newSize * 1.15);
  }
  return Math.round(newSize);
};

const vw = (percentage: number) => {
  return SCREEN_WIDTH * (percentage / 100);
};

const vh = (percentage: number) => {
  return SCREEN_HEIGHT * (percentage / 100);
};

type MediaModalProps = {
  visible: boolean;
  media: any;
  onClose: () => void;
  agentName?: string;
};

type NutrientRowProps = {
  label: string;
  value: string;
  icon?: string;
};

type BiomarkerRowProps = {
  biomarker: string;
  value: string;
  refRange: string;
  status: string;
};

const NutrientRow: React.FC<NutrientRowProps> = ({ label, value, icon }) => (
  <View style={styles.nutrientRow}>
    <View style={styles.nutrientLabelContainer}>
      {icon && <Text style={styles.nutrientIcon}>{icon}</Text>}
      <Text style={styles.nutrientLabel}>{label}</Text>
    </View>
    <Text style={styles.nutrientValue}>{value}</Text>
  </View>
);

const BiomarkerRow: React.FC<BiomarkerRowProps> = ({
  biomarker,
  value,
  refRange,
  status,
}) => (
  <View style={styles.biomarkerRow}>
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      style={[styles.biomarkerText, styles.biomarkerColBiomarker]}
    >
      {biomarker}
    </Text>
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      style={[styles.biomarkerValue, styles.biomarkerColValue]}
    >
      {value}
    </Text>
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      style={[styles.biomarkerRefRange, styles.biomarkerColRefRange]}
    >
      {refRange}
    </Text>
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      style={[styles.biomarkerStatus, styles.biomarkerColStatus]}
    >
      {status}
    </Text>
  </View>
);

const parseBiomarkerData = (text: string) => {
  if (!text) return [];
  const lines = text.split("\n");
  const dataStartIndex = lines.findIndex((line) =>
    line.includes("Biomarker, Value, Ref Range, Status")
  );
  return dataStartIndex === -1
    ? []
    : lines
        .slice(dataStartIndex + 1)
        .filter((line) => line.trim())
        .map((line) => {
          const [biomarker, value, refRange, status] = line
            .split(",")
            .map((item) => item.trim());
          return { biomarker, value, refRange, status };
        });
};

const processBodyPartDescription = (text: string) => {
  if (!text) return null;

  // Check for both formats: "image shows" and "image that shows:"
  const searchPhrases = ["image shows", "image that shows:"];
  let startIndex = -1;

  for (const phrase of searchPhrases) {
    startIndex = text.toLowerCase().indexOf(phrase);
    if (startIndex !== -1) {
      // Found one of the phrases
      startIndex += phrase.length;
      break;
    }
  }

  if (startIndex === -1) return null;

  return text.slice(startIndex).split("(Disclaimer")[0].trim();
};

const MediaModal: React.FC<MediaModalProps> = ({
  visible,
  media,
  onClose,
  agentName = "",
}) => {
  const { t } = useTranslation();
  const { height } = useWindowDimensions();
  const isSmallDevice = height < 700;

  const handleOpenPDF = async () => {
    if (media?.url) {
      try {
        const supported = await Linking.canOpenURL(media.url);
        if (supported) {
          await Linking.openURL(media.url);
        }
      } catch (error) {
        logger.error("Error opening PDF:", error);
      }
    }
  };

  // Extract title from media based on type
  const getTitle = () => {
    if (!media) return t('reports.defaultTitle');

    switch (media.type) {
      case "FOOD_ITEM":
        // Extract food name from the first line of processed output or use a default
        if (media.processed_output) {
          const nameText = media.processed_output
            .split("\n")[0]
            .split(/\d/)[0]
            .replace(/^[\s-]+/, "")
            .trim();
          return nameText || t('reports.defaultFoodName');
        }
        return media.name || t('reports.defaultFoodName');

      case "LAB_REPORT":
        return media.name || t('reports.defaultLabReportName');

      case "BODY_PART":
        return media.name || t('reports.defaultBodyPartName');

      default:
        return media.name || t('reports.defaultName');
    }
  };

  const renderContent = () => {
    if (!media) return null;

    if (media.mimeType?.includes("pdf")) {
      return (
        <View style={styles.pdfContainer}>
          <FileText size={normalize(60)} color={colors.primary} />
          <Text
            style={[styles.mediaTitle, isSmallDevice && styles.mediaTitleSmall]}
          >
            {media.name || t('reports.defaultName')}
          </Text>
          <TouchableOpacity style={styles.pdfButton} onPress={handleOpenPDF}>
            <Text style={styles.pdfButtonText}>{t('reports.openButton')}</Text>
          </TouchableOpacity>
        </View>
      );
    }

    switch (media.type) {
      case "LAB_REPORT":
        const biomarkers = parseBiomarkerData(media.biomarker_prompt);
        return (
          <View style={styles.biomarkerOuterContainer}>
            {biomarkers.length > 0 ? (
              <View style={styles.labReportContainer}>
                <FlatList
                  nestedScrollEnabled={true}
                  data={biomarkers}
                  keyExtractor={(_, index) => `biomarker-${index}`}
                  showsVerticalScrollIndicator={true}
                  scrollEnabled={true}
                  scrollEventThrottle={16}
                  directionalLockEnabled={true}
                  alwaysBounceVertical={false}
                  onStartShouldSetResponder={() => true}
                  onResponderTerminationRequest={() => false}
                  style={styles.biomarkerScrollView}
                  contentContainerStyle={styles.biomarkerScrollContent}
                  ListHeaderComponent={
                    <View style={styles.biomarkerHeader}>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={[
                          styles.biomarkerHeaderText,
                          styles.biomarkerColBiomarker,
                        ]}
                      >
                        {t('reports.biomarker.headerBiomarker')}
                      </Text>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={[
                          styles.biomarkerHeaderText,
                          styles.biomarkerColValue,
                        ]}
                      >
                        {t('reports.biomarker.headerValue')}
                      </Text>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={[
                          styles.biomarkerHeaderText,
                          styles.biomarkerColRefRange,
                        ]}
                      >
                        {t('reports.biomarker.headerRefRange')}
                      </Text>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={[
                          styles.biomarkerHeaderText,
                          styles.biomarkerColStatus,
                        ]}
                      >
                        {t('reports.biomarker.headerStatus')}
                      </Text>
                    </View>
                  }
                  renderItem={({ item }) => (
                    <BiomarkerRow
                      biomarker={item.biomarker}
                      value={item.value}
                      refRange={item.refRange}
                      status={item.status}
                    />
                  )}
                />
              </View>
            ) : (
              <Text style={styles.noDataText}>{t('reports.noData')}</Text>
            )}
          </View>
        );
      case "FOOD_ITEM":
        const nutritionalData = parseNutritionalData(media.processed_output);
        return (
          <View style={styles.nutrientTable}>
            {nutritionalData?.map((nutrient: { label: string, value: string }, index: number) => (
              <NutrientRow
                key={index}
                label={nutrient.label.replace(/^[^\w\s]+ /, "")}
                value={nutrient.value}
                icon={nutrient.label.match(/^[^\w\s]+/)?.[0] || ""}
              />
            ))}
          </View>
        );

      case "BODY_PART":
        const description = processBodyPartDescription(media.processed_output);
        return (
          <View style={styles.descriptionContainer}>
            {description && (
              <Text style={styles.description}>
                <Text style={styles.agentName}>{agentName}</Text> {description}
              </Text>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  if (!media) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      {/* <TouchableWithoutFeedback onPress={onClose}> */}
      <View style={styles.modalOverlay}>
        {/* <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()} > */}
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={StyleSheet.absoluteFill} />
        </TouchableWithoutFeedback>
        <View style={styles.modalContent}>
          {media.url && media.type !== "PDF" && (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: media.url }}
                style={[
                  styles.mediaImage,
                  // Apply 35vh for INVALID type
                  media.type === "INVALID" && styles.mediaImageInvalid,
                  // Keep small device adjustment for non-INVALID types
                  isSmallDevice &&
                    media.type !== "INVALID" &&
                    styles.mediaImageSmall,
                ]}
              />
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X size={normalize(24)} color={colors.white} />
              </TouchableOpacity>
            </View>
          )}

          {/* <ScrollView 
            style={styles.detailsContainer}
            nestedScrollEnabled={true}
            
            > */}
          {getTitle() && (
            <Text
              style={[
                styles.mediaTitle,
                isSmallDevice && styles.mediaTitleSmall,
              ]}
            >
              {getTitle()}
            </Text>
          )}

          {renderContent()}
          {/* </ScrollView> */}
        </View>
        {/* </TouchableWithoutFeedback> */}
      </View>
      {/* </TouchableWithoutFeedback> */}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: vw(90),
    maxHeight: vh(80),
    backgroundColor: colors.white,
    borderRadius: 16,
    overflow: "hidden",
  },
  imageContainer: {
    position: "relative",
    width: "100%",
  },
  closeButton: {
    position: "absolute",
    top: vh(1.5),
    right: vw(3),
    padding: vw(2),
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    zIndex: 10,
  },
  mediaImage: {
    width: "100%",
    height: vh(25),
    // marginBottom: vh(1),
    resizeMode: "cover",
  },
  mediaImageSmall: {
    height: vh(20),
  },
  detailsContainer: {
    padding: vw(4),
  },
  mediaTitle: {
    fontSize: normalize(20),
    fontWeight: "600",
    marginBottom: vh(2),
    textAlign: "center",
  },
  mediaTitleSmall: {
    fontSize: normalize(18),
    marginBottom: vh(1.5),
  },
  nutrientTable: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: vw(4),
    marginBottom: vh(2),
  },
  nutrientRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: vh(1.5),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  nutrientLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  nutrientIcon: {
    marginRight: vw(2),
    fontSize: normalize(10),
  },
  nutrientLabel: {
    fontSize: normalize(13),
    color: colors.gray[700],
  },
  nutrientValue: {
    fontSize: normalize(13),
    fontWeight: "500",
    color: colors.gray[900],
  },

  // --- Styles for Lab Report Table ---
  biomarkerOuterContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    marginBottom: vh(2),
    overflow: "hidden",
    padding: vw(1),
    width: "100%",
  },
  labReportContainer: {
    width: "100%",
    height: vh(40),
    overflow: "hidden",
  },
  biomarkerScrollView: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  biomarkerScrollContent: {
    paddingBottom: vh(2),
  },
  biomarkerHeader: {
    flexDirection: "row",
    paddingHorizontal: vw(1),
    paddingVertical: vw(2),
    paddingBottom: vh(1),
    borderBottomWidth: 2,
    borderBottomColor: colors.gray[300],
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    width: "100%",
  },
  biomarkerRow: {
    flexDirection: "row",
    paddingVertical: vh(1),
    paddingHorizontal: vw(1),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    alignItems: "center",
    width: "100%",
  },
  biomarkerColBiomarker: {
    width: "30%",
    paddingRight: 2,
  },
  biomarkerColValue: {
    width: "15%",
    textAlign: "center",
  },
  biomarkerColRefRange: {
    width: "30%",
    textAlign: "center",
    paddingHorizontal: 1,
  },
  biomarkerColStatus: {
    width: "25%",
    textAlign: "center",
  },
  biomarkerHeaderText: {
    fontSize: normalize(10),
    fontWeight: "600",
    color: colors.gray[800],
  },
  biomarkerText: {
    fontSize: normalize(9),
    color: colors.gray[700],
  },
  biomarkerValue: {
    fontSize: normalize(9),
    fontWeight: "500",
    color: colors.gray[900],
    textAlign: "center",
  },
  biomarkerRefRange: {
    fontSize: normalize(9),
    color: colors.gray[700],
    textAlign: "center",
  },
  biomarkerStatus: {
    fontSize: normalize(9),
    color: colors.gray[700],
    textAlign: "center",
  },
  descriptionContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: vw(4),
    marginBottom: vh(2),
  },
  description: {
    fontSize: normalize(13),
    lineHeight: normalize(24),
    color: colors.gray[800],
  },
  agentName: {
    fontWeight: "700",
    color: colors.primary,
  },
  pdfContainer: {
    alignItems: "center",
    padding: vw(4),
  },
  pdfButton: {
    backgroundColor: colors.primary,
    paddingVertical: vh(1.5),
    paddingHorizontal: vw(8),
    borderRadius: 8,
    marginTop: vh(2),
  },
  pdfButtonText: {
    color: colors.white,
    fontWeight: "500",
    fontSize: normalize(16),
  },
  noDataText: {
    fontSize: normalize(16),
    color: colors.gray[600],
    textAlign: "center",
    padding: vw(4),
  },
  mediaImageInvalid: {
    height: vh(35), // Height for INVALID type
  },
});

export default MediaModal;