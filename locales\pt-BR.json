{"common": {"error": "Erro", "yes": "<PERSON>m", "no": "Não", "sometimes": "<PERSON>s vezes", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "next": "Próximo", "loading": "Carregando...", "version": "v0.0.1.7"}, "welcome": "Faça login para começar a conversar com o August", "notFound": {"title": "Oops!", "message": "Esta tela não existe.", "goHome": "Ir para a tela inicial!"}, "library": {"title": "Biblioteca de Saúde"}, "specialists": {"title": "Especialistas", "description": "Consulte profissionais de saúde especializados para problemas de saúde mais específicos. Escolha um especialista abaixo:", "generalPhysician": {"title": "Clínico Geral", "description": "Para problemas de saúde gerais e atenção primária."}, "nutritionist": {"title": "Nutricionista", "description": "Para orientações sobre dieta, nutrição e controle de peso."}, "cardiologist": {"title": "Cardiologista", "description": "Para problemas relacionados ao coração e saúde cardiovascular."}, "neurologist": {"title": "Neurologist<PERSON>", "description": "Para problemas cerebrais, da medula espinhal e do sistema nervoso."}, "oncologist": {"title": "Oncologista", "description": "Para problemas e tratamentos relacionados ao câncer."}, "endocrinologist": {"title": "Endocrinologist<PERSON>", "description": "Para distúrbios hormonais e controle do diabetes."}, "dermatologist": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Para condições de pele, cabelo e unhas."}, "psychiatrist": {"title": "Psiquiatra", "description": "Para problemas de saúde mental e bem-estar psicológico."}}, "profile": {"title": "Perfil", "defaultName": "Convidado", "namePlaceholder": "Digite seu nome", "saving": "Salvando...", "noPhoneNumber": "Sem número de telefone", "loggingOut": "Saindo...", "about": {"title": "Sobre", "description": "Saiba mais sobre o August"}, "whatsapp": {"title": "WhatsApp", "description": "Converse com o August no WhatsApp"}, "refer": {"title": "Indicar", "description": "Gostou do August? Compartilhe com seus amigos"}, "deleteAccount": {"title": "Excluir conta", "description": "Sentiremos sua falta"}, "logout": {"title": "<PERSON><PERSON>", "description": "Volte em breve. Sentiremos sua falta"}, "shareMessage": "👋Oi, confira este aplicativo incrível que tenho usado!\n\n\n\n➡️Tenho usado o August para obter informações e orientações de saúde rápidas e confiáveis. É como ter um médico no seu bolso! Confira aqui:", "error": {"loadFailed": "Falha ao carregar os dados do usuário", "fetchError": "Ocorreu um erro ao buscar os dados do usuário", "updateNameFailed": "Falha ao atualizar o nome", "updateNameError": "Ocorreu um erro ao atualizar o nome", "loadFoodData": "Falha ao carregar os dados de alimentos", "logoutError": "<PERSON>rro durante o logout:", "shareError": "Erro ao compartilhar mensagens:"}}, "error": {"title": "<PERSON>go deu errado", "checkLogs": "Verifique os logs do seu dispositivo para obter mais detalhes.", "unknown": "<PERSON><PERSON>conhe<PERSON>", "unknownFile": "Arquivo desconhecido", "unknownLine": "<PERSON><PERSON>a", "unknownColumn": "Coluna desconhecida"}, "auth": {"phone": {"selectCountry": "Selecione o País", "searchCountries": "Pesquisar países", "validation": {"invalidPhone": "Por favor, insira um número de telefone válido", "invalidDigits": "Por favor, insira um número de telefone válido (7-15 dígitos)"}}, "header": {"title": "Obtenha clareza sobre suas preocupações de saúde instantaneamente e com privacidade", "subtitle": "Orientação atenciosa. Sem pressa. Sem confusão.", "emphasis": "A<PERSON>as clareza."}, "greeting": "O<PERSON><PERSON> 👋", "phoneNumber": "Número de Telefone", "requestOTP": "Solicitar OTP", "otp": {"title": "Senha de Uso Único", "verify": "Verificar OTP", "sending": "Enviando...", "countdown": "Reenviar OTP em {{countdown}}s", "resend": "Reenviar OTP", "sentTo": "OTP enviado para ", "whatsappSuffix": " no Whatsapp"}, "disclaimer": {"prefix": "Ao se cadastrar, você concorda com nossos ", "continuePrefix": "Ao continuar, você concorda com nossos ", "termsOfService": "Termos de Serviço", "and": " e ", "privacyPolicy": "Política de Privacidade", "whatsappConsent": ", e consente em receber atualizações e lembretes nossos via WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Bem-vindo ao August!", "buttonText": "<PERSON><PERSON><PERSON> começar"}}, "postAuth": {"step1": {"title": "Oi!", "subtitle": "Eu sou o August 👋", "description": "Pense em mim como o cantinho aconchegante do seu\naparelho onde você explora todas as suas\ncuriosidades sobre saúde.", "subdescription": "Sinta-se à vontade para perguntar tudo o que estiver em sua mente.\nSem julgamentos, sem limites!", "placeholder": "Como posso te chamar?"}, "step2": {"title": "Oi {{userName}},", "subtitle": "Aqui está o que eu posso fazer:", "features": {"health": {"title": "Responda suas", "subtitle": "Consultas de saúde"}, "nutrition": {"title": "Acompanhe seus", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Relatórios"}}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON>", "careful": "Cuidadoso", "accurate": "Preciso"}, "features": {"symptoms": {"title": "Verifique seus sintomas", "description": "Estou com náuseas há uma semana. O que está acontecendo comigo?"}, "prescriptions": {"title": "Analise suas prescrições", "description": "Carregue e entenda as prescrições como um médico."}, "medicine": {"title": "Conheça seus medicamentos", "description": "A Metformina para minha SOP interage com minhas pílulas para TDAH?"}, "plans": {"title": "Obtenha planos personalizados", "description": "Você pode me dar um plano de nutrição e condicionamento físico para reduzir meus níveis de HbA1c?"}}, "buttons": {"getStarted": "<PERSON><PERSON><PERSON>", "next": "Próximo"}, "errors": {"nameRequired": "Por favor, insira seu nome"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Descobrir", "nutrition": "Nutrição", "personalize": "Personalizar"}, "chat": {"nav": {"title": "Agosto"}, "me": "Eu", "augustName": "Agosto", "input": {"placeholder": "Pergunte ao Agosto...", "disclaimer": "O Agosto pode cometer erros. Confirme com um médico"}, "list": {"loadingMessages": "Carregando mensagens...", "noMessages": "Ainda não há mensagens. Comece uma conversa!"}, "connection": {"offlineMessage": "Parece que você está offline. Reconeecte-se para enviar mensagens.", "connecting": "Conectando...", "tryAgain": "Tentar novamente"}, "prompts": {"uploadReport": "Enviar relatório", "speakInHindi": "Fale em hindi", "notFeelingWell": "<PERSON>ão estou me sentindo bem", "whatIsMyBMI": "Qual é o meu IMC?", "nutritionAdvice": "Orientação nutricional", "sleepBetter": "<PERSON><PERSON><PERSON> me<PERSON>"}, "citations": {"referenceText": "Para mais detalhes sobre esta conversa, consulte:"}, "actions": {"copiedToClipboard": "Copiado para a área de transferência", "copied": "Copiado"}, "share": {"introText": "👋 Ol<PERSON>, veja a conversa que tive com o Agosto:\n\n", "downloadText": "\n\n➡️Baixe o Agosto para conversar com seu amigável assistente de saúde com IA:\n"}}, "discover": {"nav": {"title": "Descobrir"}, "categories": {"all": "Todos", "heartHealth": "Saúde do Coração", "nutrition": "Nutrição", "mentalHealth": "<PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "Bem-estar"}, "cards": {"empty": "Nenhum cartão disponível para esta categoria"}, "sections": {"features": "Recursos"}, "features": {"healthLibrary": {"title": "Biblioteca de Saúde", "description": "Acesso a informações médicas confiáveis, atualizadas e de confiança, totalmente gratuito."}, "nutritionTracker": {"title": "Rast<PERSON>or Nutric<PERSON>", "description": "Já se perguntou se você poderia simplesmente enviar uma foto da sua comida e rastrear todas as suas metas nutricionais? O Agosto pode fazer exatamente isso!"}, "multilingualSupport": {"title": "Suporte Multilíngue", "description": "Você pode se comunicar com o Agosto em qualquer idioma com o qual se sinta confortável! O Agosto está sempre aqui para ouvir, apoiar e responder a você sempre que precisar.", "samplePrompt": "Fale em hindi"}, "labReportAnalysis": {"title": "Análise de Relatórios de Laboratório", "description": "Quando você fala com o Agosto sobre seus relatórios de laboratório, você obtém precisão extrema. O Agosto processou mais de 4,7 milhões de relatórios com uma precisão de extração de biomarcadores de 98,4%."}}}, "nutrition": {"nav": {"title": "Nutrição"}, "meals": {"title": "Suas Refeições", "subtitle": "Toque para ver os macros de cada refeição"}, "upload": {"loading": "Carregando imagem..."}, "defaultFoodName": "Alimento", "today": "Hoje", "unknownTime": "<PERSON><PERSON><PERSON><PERSON>", "calories": "🔥 Calorias", "proteins": "🥩 Proteínas", "carbs": "🍞 Carboidratos", "sugars": "🍬 Açúcares", "fat": "🥑 Gord<PERSON>", "caloriesLabel": "Caloria<PERSON>", "proteinLabel": "<PERSON><PERSON><PERSON><PERSON>", "carbohydratesLabel": "Carboidratos", "fatLabel": "Gordura", "sugarLabel": "<PERSON><PERSON><PERSON><PERSON>", "tips": "Dicas:", "macroBreakdown": "Distribuição de Macronutrientes", "noMacroData": "Nenhum dado de macronutrientes disponível para este alimento.", "disclaimer": "Para fins educacionais apenas. Aprenda mais", "disclaimerLink": "aqui", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Qual o seu gênero?", "subtitle": "<PERSON><PERSON> será usado para calibrar seu plano personalizado.", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "other": "Outro"}, "age": {"title": "Qual a sua idade?", "subtitle": "<PERSON><PERSON> será usado para calcular suas necessidades diárias."}, "measurements": {"title": "Altura e Peso", "subtitle": "Por favor, insira sua altura em centímetros e peso em quilogramas."}, "activity": {"title": "Nível de Atividade", "subtitle": "Com que frequência você se exercita?", "none": "Sem exerc<PERSON>cios", "moderate": "Moderado", "high": "Alto"}, "goal": {"title": "Objetivo de Peso", "subtitle": "O que você gostaria de alcançar?", "increase": "Aumentar", "maintain": "<PERSON><PERSON>", "decrease": "<PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Peso Alvo", "subtitle": "Qual o seu peso alvo em quilogramas?"}, "setup": {"title": "Configurando seu Plano", "subtitle": "Por favor, aguarde enquanto preparamos seu plano nutricional."}, "review": {"title": "Revise seu <PERSON>", "subtitle": "Revise e personalize seu plano nutricional."}, "height": {"label": "Altura (cm)"}, "weight": {"label": "Peso (kg)"}}, "error": {"updateFailed": "Falha ao atualizar os dados nutricionais. Por favor, tente novamente.", "parsingError": "Erro ao analisar os dados do alimento:", "fetchReportsFailed": "Falha ao buscar os dados dos relatórios. Por favor, tente novamente.", "missingReportId": "ID do relatório está faltando"}}, "personalize": {"nav": {"title": "Personalizar"}, "button": {"saving": "<PERSON><PERSON><PERSON>", "review": "Rev<PERSON><PERSON>", "saveNext": "Salvar e Próximo"}}, "basicInfo": {"title": "Vamos conhe<PERSON>-lo melhor", "subtitle": "Essas informações nos ajudam a personalizar suas recomendações de saúde", "age": {"question": "Qual a sua idade?", "placeholder": "Digite sua idade"}, "sex": {"question": "Qual o seu sexo?", "placeholder": "Selecione seu sexo", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "other": "Outro"}, "height": {"question": "Qual a sua altura? (cm)", "placeholder": "Digite sua altura"}, "weight": {"question": "Qual o seu peso? (kg)", "placeholder": "Digite seu peso"}}, "lifestyle": {"title": "Seus Hábitos de Vida", "subtitle": "Entender seus hábitos diários nos ajuda a fornecer melhores recomendações", "diet": {"question": "Que tipo de dieta você segue?", "placeholder": "Selecione sua dieta", "vegetarian": "Vegetariana", "nonVegetarian": "Não <PERSON>", "vegan": "Vegan", "pescatarian": "Pescetariana", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Você se exercita regularmente?"}, "drinking": {"question": "Você consome álcool?"}, "smoking": {"question": "Você fuma?"}, "sleep": {"question": "Quantas horas você dorme por noite?", "value": "{{sleep}} horas"}, "hydration": {"question": "Quantos copos de água você bebe diariamente?", "value": "{{hydration}} copos ({{liters}}L)"}}, "allergies": {"title": "Você tem alguma alergia?", "subtitle": "Conhecer suas alergias nos ajuda a fornecer recomendações mais seguras", "allergyIndex": "Alergia {{index}}", "name": {"question": "A o que você é alérgico?", "placeholder": "Digite a alergia (ex: Amendoim, Poeira)"}, "severity": {"question": "Qual a gravidade dessa alergia?", "placeholder": "Selecione a gravidade", "mild": "<PERSON><PERSON>", "moderate": "Moderada", "severe": "Grave"}, "addButton": "Adicionar outra alergia", "noAllergiesButton": "Não tenho alergias"}, "medications": {"title": "Medicamentos e Suplementos", "subtitle": "Conte-nos sobre quaisquer medicamentos ou suplementos que você esteja tomando atualmente", "medicationIndex": "Medicamento {{index}}", "name": {"label": "Nome do Medicamento", "placeholder": "Digite o nome do medicamento"}, "startDate": {"question": "Quando você começou a tomar?", "placeholder": "Selecione a data"}, "type": {"label": "Tipo de Medicamento", "shortTerm": "Curto Prazo", "longTerm": "Longo Prazo"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Quantidade"}, "unit": {"label": "Unidade"}, "frequency": {"label": "Frequência", "placeholder": "Vezes", "perDay": "por dia", "perWeek": "por semana", "perMonth": "por mês", "perYear": "por ano"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "b<PERSON><PERSON><PERSON>", "drops": "gotas", "tsp": "colheres de ch<PERSON>", "tbsp": "colheres de sopa", "cups": "xícaras"}, "addButton": "Adicionar outro medicamento", "noMedicationsButton": "Não tomo nenhum medicamento", "calendar": {"title": "Selecione a Data de Início"}}, "conditions": {"title": "Condições Médicas", "subtitle": "Conte-nos sobre quaisquer condições médicas que você tenha ou teve no passado", "conditionIndex": "Condição {{index}}", "name": {"label": "Nome da Condição", "placeholder": "Digite a condição (ex: <PERSON><PERSON>, etc)"}, "since": {"question": "Desde quando você tem essa condição?", "placeholder": "Selecione a data"}, "current": {"question": "Está te incomodando atualmente?"}, "medicated": {"question": "Você está tomando algum medicamento para isso?"}, "addButton": "Adicionar outra condição", "noConditionsButton": "Não tenho nenhuma condição médica", "calendar": {"title": "Selecione a Data"}}, "reproductive": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Essas informações nos ajudam a fornecer recomendações de saúde mais personalizadas", "menstruação": {"question": "Você já menstruou?", "detailsTitle": "<PERSON>al<PERSON> da Menstruação", "regularity": {"question": "Qual a regularidade do seu ciclo?", "regular": "Regular", "irregular": "Irregular", "notSure": "<PERSON><PERSON> sei"}, "cycleLength": {"label": "Comprimento médio do ciclo (dias)", "placeholder": "Insira o comprimento do ciclo"}, "flowDays": {"label": "Dias de fluxo: {{flowDays}}", "min": "1 dia", "max": "15 dias"}, "padsPerDay": {"label": "Absorventes/tampões por dia: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Algum sintoma durante o período?", "placeholder": "Insira os sintomas (ex: cólicas, dores de cabeça)"}}, "childbirth": {"question": "Você já teve filhos?", "detailsTitle": "Detalhes do Parto", "children": {"label": "Número <PERSON>"}, "pregnancies": {"label": "Número de Gravidezes"}, "complications": {"question": "Alguma complicação durante a gravidez ou parto?", "placeholder": "<PERSON><PERSON><PERSON> as complicações (se houver)"}}}, "review": {"title": "Revise suas informações", "subtitle": "Por favor, revise as informações fornecidas antes de enviar", "sections": {"basicInfo": "Informações básicas", "lifestyle": "Estilo de vida", "allergies": "Alergias", "medications": "Medicamentos e suplementos", "conditions": "Condições médicas", "reproductive": "<PERSON><PERSON><PERSON> reproduti<PERSON>", "menstruationDetails": "Detalhes da menstruação", "childbirthDetails": "Detalhes do parto"}, "fields": {"age": "Idade:", "sex": "Sexo:", "height": "Altura:", "weight": "Peso:", "diet": "Dieta:", "exercise": "Exercícios:", "drinking": "Bebidas alcoólicas:", "smoking": "Tabagismo:", "sleep": "Sono:", "hydration": "Hidratação:", "allergyIndex": "Alergia {{index}}:", "dose": "Dose:", "frequency": "Frequência:", "type": "Tipo:", "since": "Desde:", "currentlyActive": "Ativo atualmente:", "takingMedication": "Fazendo uso de medicamento:", "hasMenstruated": "<PERSON><PERSON> menst<PERSON>ou:", "regularity": "Regularidade:", "cycleLength": "Comprimento do ciclo:", "flowDays": "Dias de fluxo:", "padsPerDay": "Absorventes/tampões por dia:", "hasChildbirth": "<PERSON><PERSON> teve filhos:", "children": "Filhos:", "pregnancies": "Gravidezes:"}, "notProvided": "Não informado", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} horas por dia", "hydration": "{{hydration}} copos ({{liters}}L) por dia", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} dias"}, "noData": {"allergies": "Nenhuma alergia informada", "medications": "Nenhum medicamento informado", "conditions": "Nenhuma condição médica informada"}, "submitButton": "Enviar informações"}, "success": {"title": "Informações atualizadas!", "message": "<PERSON><PERSON><PERSON> por fornecer suas informações de saúde. Usaremos isso para personalizar sua experiência e fornecer melhores recomendações.", "benefits": {"insights": "Insights personalizados sobre sua saúde", "reminders": "<PERSON><PERSON><PERSON> lembre<PERSON> de medicamentos", "recommendations": "Recomendações de saúde personalizadas"}, "continueButton": "Continuar para o painel"}, "permissions": {"microphonePermissionDenied": "Permissão do microfone negada", "microphoneAccessDescription": "O August precisa acessar seu microfone para gravar áudio e enviar notas de voz", "permissionDenied": "Permissão Negada", "cameraPermissionRequired": "Precisamos de permissão da câmera para que isso funcione!", "mediaLibraryPermissionRequired": "Precisamos de permissão da biblioteca de mídia para que isso funcione!"}, "voiceRecording": {"recordingTooLong": "Gravação muito longa", "recordingTooLongMessage": "As gravações de voz devem ter menos de 5 minutos. Grave uma mensagem mais curta."}, "errors": {"uploadFailed": "Falha no upload", "voiceUploadFailed": "Não foi possível enviar a gravação de voz.", "voiceRecordingFailed": "Falha ao enviar a gravação de voz", "failedToStopRecording": "Falha ao parar a gravação", "photoUploadFailed": "Não foi possível enviar a foto.", "failedToTakePhoto": "Falha ao tirar foto", "imageUploadFailed": "Não foi possível enviar a imagem: {{fileName}}", "failedToPickImage": "Falha ao selecionar imagem", "documentUploadFailed": "Não foi possível enviar o documento: {{fileName}}", "failedToPickDocument": "Falha ao selecionar documento"}, "audioPlayer": {"downloadingAudio": "<PERSON><PERSON><PERSON>...", "loadingAudio": "Carregando <PERSON>..."}, "mediaProcessing": {"processingFile": "Processando seu arquivo", "uploadingSecuring": "Enviando e protegendo o arquivo...", "analyzingContent": "Analisando o conteúdo do documento...", "extractingInfo": "Extraindo informações importantes...", "processingInsights": "Processando insights...", "preparingResponse": "Preparando resposta detalhada...", "finalizingResponse": "Finalizando resposta..."}, "attachments": {"voiceMessage": "Mensagem de voz", "image": "[IMAGEM]", "pdf": "[PDF]", "voice": "[NOTA DE VOZ]"}, "pdf": {"loadingPdf": "Carregando PDF..."}, "dateTime": {"yesterday": "Ontem, "}, "navbar": {"defaultTitle": "august", "selectedCount": "selecionados"}, "mediaUpload": {"photoLibrary": "Biblioteca de fotos", "takePhoto": "Tirar foto", "chooseFile": "Escolher arquivo"}, "comingSoon": {"title": "Em breve!", "description": " está em desenvolvimento. Fique ligado para atualizações!", "buttonText": "Entendi!"}, "clipboard": {"success": "Link copiado para a área de transferência"}, "mediaPhotos": {"emptyState": "Ainda não há entradas."}, "foodDetail": {"defaultFoodName": "Alimento", "nutrition": {"totalCalories": "<PERSON><PERSON><PERSON>", "proteins": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carboidratos", "fat": "Gordura", "sugars": "Açúcares", "fibers": "Fi<PERSON><PERSON>"}}, "reports": {"defaultTitle": "<PERSON><PERSON>", "defaultFoodName": "Alimento", "defaultName": "Documento", "openButton": "Abrir em visualizador externo", "biomarker": {"headerBiomarker": "Biomarcador", "headerValue": "Valor", "headerRefRange": "Faixa de referência", "headerStatus": "Status"}, "noData": "Nenhum dado de biomarcador disponível"}, "setup": {"title": "Estamos configurando tudo para você", "inProgress": "Em progresso...", "progressMessages": {"0": "Calculando calorias diárias", "1": "Otimizando a divisão de macros", "2": "Criando plano de refeições", "3": "Calculando a pontuação de saúde", "4": "Finalizando a configuração"}, "checklistItems": {"0": "<PERSON><PERSON><PERSON><PERSON> seus dados de saúde", "1": "Calculando o plano nutricional ideal", "2": "Personalizando suas recomendações", "3": "Criando suas sugestões de refeições", "4": "Finalizando sua configuração"}}, "foodEntry": {"emptyState": "Ainda não há entradas de comida. Tire uma foto da sua refeição para adicioná-la!"}, "nutritionReview": {"congratulations": "Parabéns!", "subtitle": "Seu plano nutricional personalizado está pronto", "submitButton": "Vamos começar!", "dailyTargetsTitle": "Suas Metas Diárias de Nutrição", "macroLabels": {"calories": "Caloria<PERSON>", "carbs": "Carboidratos", "protein": "<PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendations": {"title": "Como alcançar seus objetivos:", "healthScores": "Use pontuações de saúde para melhorar sua rotina", "trackFood": "Acompanhe sua ingestão de alimentos consistentemente", "followCalories": "Siga sua recomendação diária de calorias", "balanceMacros": "Equilibre sua ingestão de carboidratos, proteínas e gorduras."}}, "editModal": {"titlePrefix": "<PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Próximo"}, "processing": {"stages": {"scanning": "Escaneando alimento...", "identifying": "Identificando ingredientes...", "extracting": "Extraindo nutrientes...", "finalizing": "Finalizando resultados..."}, "error": {"defaultMessage": "Nenhum alimento detectado", "subtitle": "Tente um ângulo diferente"}, "retakeButton": "Toque para tirar a foto novamente", "notification": "Avisaremos você quando terminar!"}, "chart": {"title": "Acompanhamento Nutricional ao Longo do Tempo", "selectNutrient": "Selecione o Nutriente:", "emptyState": "Ainda não há dados nutricionais disponíveis.", "dropdown": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carboidratos", "fat": "<PERSON><PERSON>", "sugars": "Açúcares"}}, "foodModal": {"defaultName": "<PERSON><PERSON>", "defaultDate": "Hoje", "defaultTime": "Tempo desconhecido", "saveChanges": "<PERSON><PERSON>", "error": {"title": "Erro", "message": "Falha ao atualizar os dados nutricionais. Tente novamente."}, "nutrition": {"calories": "🔥 Calorias", "proteins": "🥩 Proteínas", "carbs": "🍞 Carboidratos", "sugars": "🍬 Açúcares", "fat": "🥑 Gord<PERSON>"}, "macroBreakdown": {"title": "Distribuição de Macronutrientes", "noData": "Não há dados de macronutrientes disponíveis para este alimento."}, "macroLabels": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carboidratos", "fat": "<PERSON><PERSON>", "sugar": "<PERSON><PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Informações Detalhada", "edit": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvando...", "enterValue": "Inserir valor", "notSet": "<PERSON><PERSON> definido", "age": "<PERSON><PERSON>", "heightCm": "Altura (cm)", "weightKg": "Peso (kg)", "targetWeight": "Peso Alvo", "nutritionTargets": "Metas Nutricionais", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carboidratos", "fats": "<PERSON><PERSON><PERSON><PERSON>", "gm": "gm", "editNote": "Insira valores ou deixe em branco para cálculo automático.", "autoCalculateNote": "Macros são calculados automaticamente com base nos seus dados.", "validation": {"ageMin": "A idade deve ser de pelo menos 18 anos.", "ageMax": "A idade deve ser inferior a 125", "heightMin": "A altura deve ser de pelo menos 50cm", "heightMax": "Altura deve ser inferior a 250cm", "weightMin": "Peso mínimo de 30kg", "weightMax": "Peso deve ser inferior a 500kg", "targetWeightMin": "Peso alvo deve ser de pelo menos 30kg", "targetWeightMax": "Peso alvo deve ser inferior a 500 kg", "proteinMin": "Proteína deve ser 0 ou mais", "carbsMin": "Carboidratos devem ser 0 ou mais", "fatsMin": "Gorduras devem ser 0 ou mais"}}, "tracker": {"calories": "Caloria<PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carboidratos", "fat": "<PERSON><PERSON>", "excess": "excesso", "remaining": "restante"}, "specialistConstants": {"nutritionist": {"name": "Nutricionista", "description": "Orientação especializada em dieta, nutrição e hábitos alimentares saudáveis", "featureName": "Especialista em Nutrição"}, "cardiologist": {"name": "Cardiologista", "description": "Especializado em saúde cardíaca e condições cardiovasculares", "featureName": "Especialista em Cardiologia"}, "neurologist": {"name": "Neurologist<PERSON>", "description": "Focado em distúrbios do cérebro, medula espinhal e sistema nervoso", "featureName": "Especialista em Neurologia"}, "oncologist": {"name": "Oncologista", "description": "Especializado em diagnóstico e opções de tratamento para o câncer", "featureName": "Especialista em Oncologia"}, "endocrinologist": {"name": "Endocrinologist<PERSON>", "description": "Especialista em condições hormonais e distúrbios metabólicos", "featureName": "Especialista em Endocrinologia"}}, "discoverCards": {"categories": {"nutrition": "Nutrição", "heartHealth": "Saúde do Coração", "mentalHealth": "<PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "Bem-estar"}, "titles": {"vitaminB12Recovery": "Quanto tempo leva para se recuperar da deficiência de vitamina B12?", "vitaminDeficiencyGanglion": "Qual deficiência de vitamina causa cistos de gânglio?", "vitaminDeficiencyHairFall": "Qual deficiência de vitamina causa queda de cabelo?", "vitaminWaters": "Águas vitaminadas fazem bem à saúde?", "cholesterolHeadaches": "O colesterol alto causa dores de cabeça?", "cholesterolEyes": "Quais são os sintomas de colesterol alto que podem ser vistos nos olhos?", "diabetesHeadaches": "O diabetes pode causar dores de cabeça?", "chestPainDrinking": "Por que o peito dói depois de beber?", "stressDizziness": "O estresse pode causar tontura?", "bulimiaFace": "O que é a face de bulimia?", "kneeTwitch": "Por que meu joelho treme?", "noseTwitching": "Por que ocorre o tremor no nariz?", "piriformisVsSciatica": "<PERSON><PERSON><PERSON> <PERSON><PERSON> as diferenças entre a síndrome do piriforme e a ciática?", "shoulderBladePinched": "Como liberar um nervo comprimido na escápula?", "shoulderPinched": "Como liberar um nervo comprimido no ombro?", "meniscusTorn": "Como curar um menisco rompido naturalmente?", "hydrateQuickly": "Como se hidratar rapidamente?", "periodConstipation": "É normal ter constipação durante a menstruação?", "acneScars": "Como se livrar das cicatrizes de acne naturalmente em uma semana?", "perimenopausePregnancy": "É possível engravidar durante a perimenopausa?"}, "descriptions": {"vitaminB12Recovery": "Descubra a linha do tempo de recuperação da deficiência de vitamina B12 e remédios eficazes para aumentar seus níveis de energia.", "vitaminDeficiencyGanglion": "Explore a ligação entre deficiências de vitaminas e o desenvolvimento de cistos de gânglio no corpo.", "vitaminDeficiencyHairFall": "Aprenda como a falta de vitaminas essenciais pode levar à queda de cabelo e o que você pode fazer para preveni-la.", "vitaminWaters": "Descubra os benefícios e as possíveis desvantagens das águas vitaminadas como parte de sua nutrição diária.", "cholesterolHeadaches": "Examine a possível conexão entre altos níveis de colesterol e o início de dores de cabeça.", "cholesterolEyes": "Aprenda como o colesterol alto pode se manifestar em seus olhos e quais sintomas observar.", "diabetesHeadaches": "Investigue a relação entre diabetes e a ocorrência de dores de cabeça na vida diária.", "chestPainDrinking": "Explore os motivos por trás da dor no peito após o consumo de certas bebidas.", "stressDizziness": "Aprofunde-se em como o estresse pode afetar seu equilíbrio e bem-estar geral, levando à tontura.", "bulimiaFace": "Entenda os sinais físicos da bulimia, incluindo os efeitos na aparência facial.", "kneeTwitch": "Investigue as possíveis causas por trás das contrações involuntárias no joelho e sua relação com estresse ou fadiga.", "noseTwitching": "Saiba mais sobre as possíveis razões para a contração do nariz e sua ligação com a ansiedade ou outros fatores.", "piriformisVsSciatica": "Compare os sintomas da síndrome do piriforme e da ciática para melhor entender sua condição.", "shoulderBladePinched": "Descubra técnicas eficazes para aliviar um nervo comprimido na sua omoplata e restaurar a mobilidade.", "shoulderPinched": "Aprenda exercícios e alongamentos simples para aliviar a compressão nervosa na área do ombro.", "meniscusTorn": "Explore métodos naturais e exercícios para apoiar a cicatrização de um menisco rompido.", "hydrateQuickly": "Descubra maneiras rápidas e eficazes de se reidratar e manter a hidratação corporal ideal.", "periodConstipation": "Entenda os motivos por trás da constipação durante a menstruação e aprenda remédios naturais.", "acneScars": "Descubra remédios naturais e dicas de cuidados com a pele para reduzir a aparência de cicatrizes de acne rapidamente.", "perimenopausePregnancy": "Aprenda sobre a perimenopausa, considerações sobre fertilidade e o que esperar durante esta fase da vida."}}}