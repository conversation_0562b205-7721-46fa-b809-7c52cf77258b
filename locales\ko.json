{"common": {"error": "오류", "yes": "예", "no": "아니오", "sometimes": "때때로", "close": "닫기", "cancel": "취소", "save": "저장", "next": "다음", "loading": "로드 중...", "version": "v0.0.1.7"}, "welcome": "August와 대화를 시작하려면 로그인하세요", "notFound": {"title": "앗!", "message": "이 화면은 존재하지 않습니다.", "goHome": "홈 화면으로 이동!"}, "library": {"title": "건강 도서관"}, "specialists": {"title": "전문가", "description": "보다 구체적인 건강 문제에 대해 전문 의료 전문가와 상담하세요. 아래에서 전문가를 선택하세요:", "generalPhysician": {"title": "일반의", "description": "일반적인 건강 문제 및 1차 진료를 위한 전문가입니다."}, "nutritionist": {"title": "영양사", "description": "식단, 영양 및 체중 관리 조언을 위한 전문가입니다."}, "cardiologist": {"title": "심장 전문의", "description": "심장 관련 문제 및 심혈관 건강을 위한 전문가입니다."}, "neurologist": {"title": "신경과 전문의", "description": "뇌, 척수 및 신경계 문제를 위한 전문가입니다."}, "oncologist": {"title": "종양 전문의", "description": "암 관련 문제 및 치료를 위한 전문가입니다."}, "endocrinologist": {"title": "내분비 전문의", "description": "호르몬 관련 장애 및 당뇨병 관리를 위한 전문가입니다."}, "dermatologist": {"title": "피부과 전문의", "description": "피부, 모발 및 손톱 상태를 위한 전문가입니다."}, "psychiatrist": {"title": "정신과 의사", "description": "정신 건강 문제 및 심리적 안녕을 위한 전문가입니다."}}, "profile": {"title": "프로필", "defaultName": "게스트", "namePlaceholder": "이름을 입력하세요", "saving": "저장 중...", "noPhoneNumber": "전화번호 없음", "loggingOut": "로그아웃 중...", "about": {"title": "소개", "description": "August에 대해 자세히 알아보세요"}, "whatsapp": {"title": "WhatsApp", "description": "WhatsApp에서 August와 채팅하세요"}, "refer": {"title": "추천", "description": "August가 마음에 드셨나요? 친구들과 공유하세요"}, "deleteAccount": {"title": "계정 삭제", "description": "이용해주셔서 감사합니다"}, "logout": {"title": "로그아웃", "description": "곧 다시 만나요. 그리울 거예요"}, "shareMessage": "👋 안녕하세요, 제가 사용하고 있는 멋진 앱을 확인해보세요!\n\n\n\n➡️빠르고 신뢰할 수 있는 건강 정보와 안내를 받기 위해 August를 사용하고 있습니다. 주머니 속에 의사를 둔 것과 같아요! 여기에서 확인해보세요:", "error": {"loadFailed": "사용자 데이터를 로드하는 데 실패했습니다", "fetchError": "사용자 데이터를 가져오는 동안 오류가 발생했습니다", "updateNameFailed": "이름을 업데이트하는 데 실패했습니다", "updateNameError": "이름을 업데이트하는 동안 오류가 발생했습니다", "loadFoodData": "음식 데이터를 로드하는 데 실패했습니다", "logoutError": "로그아웃 중 오류 발생:", "shareError": "메시지 공유 중 오류 발생:"}}, "error": {"title": "문제가 발생했습니다", "checkLogs": "자세한 내용은 기기 로그를 확인하세요.", "unknown": "알 수 없는 오류", "unknownFile": "알 수 없는 파일", "unknownLine": "알 수 없는 줄", "unknownColumn": "알 수 없는 열"}, "auth": {"phone": {"selectCountry": "국가 선택", "searchCountries": "국가 검색", "validation": {"invalidPhone": "올바른 전화번호를 입력해주세요.", "invalidDigits": "올바른 전화번호를 입력해주세요 (7-15자리)"}}, "header": {"title": "건강에 대한 궁금증을 즉시, 그리고 비밀리에 해결하세요", "subtitle": "세심한 안내. 서두르지 않아도 괜찮아요. 복잡하지 않아요.", "emphasis": "명확성만을 드립니다."}, "greeting": "안녕하세요 👋", "phoneNumber": "전화번호", "requestOTP": "OTP 요청", "otp": {"title": "일회용 비밀번호", "verify": "OTP 확인", "sending": "전송 중...", "countdown": "{{countdown}}초 후에 OTP 재전송", "resend": "OTP 재전송", "sentTo": "OTP가 다음 번호로 전송되었습니다: ", "whatsappSuffix": " (WhatsApp)"}, "disclaimer": {"prefix": "가입 시, 귀하는 당사의 ", "continuePrefix": "계속 진행하면 당사의 ", "termsOfService": "이용 약관", "and": " 및 ", "privacyPolicy": "개인정보 보호정책", "whatsappConsent": "에 동의하며, WhatsApp를 통해 업데이트 및 알림을 수신하는 것에 동의합니다."}}, "onboarding": {"preAuth": {"welcome": {"title": "August에 오신 것을 환영합니다!", "buttonText": "시작하기"}}, "postAuth": {"step1": {"title": "안녕하세요!", "subtitle": "저는 August입니다 👋", "description": "기기에서 건강에 대한 모든 궁금증을 탐구할 수 있는 편안한 공간이라고 생각하세요.", "subdescription": "궁금한 점이 있으면 언제든지 편하게 물어보세요.\n판단하지 않고, 제한 없이 도와드리겠습니다!", "placeholder": "무엇이라고 불러드릴까요?"}, "step2": {"title": "{{userName}}님, 안녕하세요,", "subtitle": "제가 할 수 있는 일은 다음과 같습니다:", "features": {"health": {"title": "건강 관련", "subtitle": "문의에 답변해 드립니다"}, "nutrition": {"title": "매크로", "subtitle": "추적"}, "reports": {"title": "보고서", "subtitle": "분석"}}}}, "pills": {"thoughtful": "세심한", "careful": "신중한", "accurate": "정확한"}, "features": {"symptoms": {"title": "증상 확인", "description": "일주일 동안 메스꺼움을 느꼈습니다. 무슨 일이 일어나고 있는 걸까요?"}, "prescriptions": {"title": "처방전 분석", "description": "처방전을 업로드하고 의사처럼 이해하세요."}, "medicine": {"title": "약 정보 확인", "description": "PCOS에 대한 메트포르민이 ADHD 약과 상호 작용할까요?"}, "plans": {"title": "맞춤형 계획 수립", "description": "HbA1c 수치를 낮추기 위한 영양 및 피트니스 계획을 세워주시겠어요?"}}, "buttons": {"getStarted": "시작하기", "next": "다음"}, "errors": {"nameRequired": "이름을 입력해주세요."}}, "tabs": {"chat": "채팅", "discover": "발견", "nutrition": "영양", "personalize": "개인 설정"}, "chat": {"nav": {"title": "8월"}, "me": "나", "augustName": "August", "input": {"placeholder": "August에게 질문하세요...", "disclaimer": "August는 실수할 수 있습니다. 의사와 상담하십시오."}, "list": {"loadingMessages": "메시지를 불러오는 중...", "noMessages": "아직 메시지가 없습니다. 대화를 시작하세요!"}, "connection": {"offlineMessage": "오프라인 상태인 것 같습니다. 다시 연결하여 메시지를 보내세요.", "connecting": "연결 중...", "tryAgain": "다시 시도"}, "prompts": {"uploadReport": "보고서 업로드", "speakInHindi": "힌디어로 말하기", "notFeelingWell": "몸이 좋지 않아요", "whatIsMyBMI": "제 BMI는 얼마인가요?", "nutritionAdvice": "영양 조언", "sleepBetter": "숙면하기"}, "citations": {"referenceText": "이 대화에 대한 자세한 내용은 다음을 참조하십시오:"}, "actions": {"copiedToClipboard": "클립보드에 복사되었습니다.", "copied": "복사됨"}, "share": {"introText": "👋 안녕하세요, August와 나눈 대화를 보세요:\n\n", "downloadText": "\n\n➡️친근한 AI 건강 동반자인 August를 다운로드하여 채팅하세요:\n"}}, "discover": {"nav": {"title": "발견"}, "categories": {"all": "전체", "heartHealth": "심장 건강", "nutrition": "영양", "mentalHealth": "정신 건강", "fitness": "피트니스", "wellness": "웰니스"}, "cards": {"empty": "이 카테고리에 사용 가능한 카드가 없습니다."}, "sections": {"features": "특징"}, "features": {"healthLibrary": {"title": "건강 도서관", "description": "신뢰할 수 있고 최신 의료 정보에 무료로 접근할 수 있습니다."}, "nutritionTracker": {"title": "영양 추적기", "description": "음식 사진을 업로드하여 영양 목표를 추적할 수 있다면 어떨까요? August가 바로 그 일을 해낼 수 있습니다!"}, "multilingualSupport": {"title": "다국어 지원", "description": "편안하게 사용하는 언어로 August와 소통할 수 있습니다! August는 언제든지 귀 기울이고 지원하며 응답할 준비가 되어 있습니다.", "samplePrompt": "힌디어로 말하기"}, "labReportAnalysis": {"title": "검사 보고서 분석", "description": "August에게 검사 보고서에 대해 이야기하면 매우 정확한 결과를 얻을 수 있습니다. August는 470만 건 이상의 보고서를 처리하여 98.4%의 바이오마커 추출 정확도를 달성했습니다."}}}, "nutrition": {"nav": {"title": "영양"}, "meals": {"title": "내 식사", "subtitle": "각 식사의 매크로를 확인하려면 탭하세요"}, "upload": {"loading": "이미지 업로드 중..."}, "defaultFoodName": "음식 품목", "today": "오늘", "unknownTime": "알 수 없는 시간", "calories": "🔥 칼로리", "proteins": "🥩 단백질", "carbs": "🍞 탄수화물", "sugars": "🍬 설탕", "fat": "🥑 지방", "caloriesLabel": "칼로리", "proteinLabel": "단백질", "carbohydratesLabel": "탄수화물", "fatLabel": "지방", "sugarLabel": "설탕", "tips": "팁:", "macroBreakdown": "매크로 영양소 분해", "noMacroData": "이 음식 품목에 대한 매크로 영양소 데이터가 없습니다.", "disclaimer": "교육용으로만 사용됩니다. 자세히 알아보기", "disclaimerLink": "여기", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "성별은 무엇입니까?", "subtitle": "맞춤 계획을 보정하는 데 사용됩니다.", "male": "남성", "female": "여성", "other": "기타"}, "age": {"title": "나이는 몇 살입니까?", "subtitle": "일일 필요량을 계산하는 데 사용됩니다."}, "measurements": {"title": "키와 몸무게", "subtitle": "키는 센티미터, 몸무게는 킬로그램으로 입력해주세요."}, "activity": {"title": "활동 수준", "subtitle": "얼마나 자주 운동하십니까?", "none": "운동 안 함", "moderate": "보통", "high": "높음"}, "goal": {"title": "체중 목표", "subtitle": "무엇을 달성하고 싶습니까?", "increase": "증가", "maintain": "유지", "decrease": "감소"}, "targetWeight": {"title": "목표 체중", "subtitle": "목표 체중(킬로그램)은 얼마입니까?"}, "setup": {"title": "계획 설정", "subtitle": "영양 계획을 준비하는 동안 기다려주세요."}, "review": {"title": "계획 검토", "subtitle": "영양 계획을 검토하고 사용자 지정하세요."}, "height": {"label": "키 (cm)"}, "weight": {"label": "몸무게 (kg)"}}, "error": {"updateFailed": "영양 데이터 업데이트에 실패했습니다. 다시 시도해주세요.", "parsingError": "음식 데이터 구문 분석 오류:", "fetchReportsFailed": "보고서 데이터를 가져오지 못했습니다. 다시 시도해주세요.", "missingReportId": "보고서 ID가 없습니다."}}, "personalize": {"nav": {"title": "개인 설정"}, "button": {"saving": "저장 중", "review": "검토", "saveNext": "저장 및 다음"}}, "basicInfo": {"title": "더 잘 알아가도록 해요", "subtitle": "이 정보는 건강 권장 사항을 개인화하는 데 도움이 됩니다.", "age": {"question": "나이는 몇 살입니까?", "placeholder": "나이를 입력하세요"}, "sex": {"question": "성별은 무엇입니까?", "placeholder": "성별을 선택하세요", "male": "남성", "female": "여성", "other": "기타"}, "height": {"question": "키는 얼마입니까? (cm)", "placeholder": "키를 입력하세요"}, "weight": {"question": "몸무게는 얼마입니까? (kg)", "placeholder": "몸무게를 입력하세요"}}, "lifestyle": {"title": "여러분의 생활 습관", "subtitle": "일상 습관을 이해하면 더 나은 권장 사항을 제공하는 데 도움이 됩니다", "diet": {"question": "어떤 종류의 식단을 따르시나요?", "placeholder": "식단 선택", "vegetarian": "채식주의자", "nonVegetarian": "비건이 아닌", "vegan": "비건", "pescatarian": "페스카토리안", "keto": "케토", "paleo": "팔레오"}, "exercise": {"question": "규칙적으로 운동하시나요?"}, "drinking": {"question": "술을 드시나요?"}, "smoking": {"question": "흡연하시나요?"}, "sleep": {"question": "하루에 몇 시간 정도 주무시나요?", "value": "{{sleep}}시간"}, "hydration": {"question": "매일 몇 컵의 물을 드시나요?", "value": "{{hydration}}컵 ({{liters}}L)"}}, "allergies": {"title": "알레르기가 있으신가요?", "subtitle": "알레르기를 알면 더 안전한 권장 사항을 제공하는 데 도움이 됩니다", "allergyIndex": "{{index}}번 알레르기", "name": {"question": "무엇에 알레르기가 있으신가요?", "placeholder": "알레르기 입력 (예: 땅콩, 먼지)"}, "severity": {"question": "알레르기의 심각도는 어느 정도입니까?", "placeholder": "심각도 선택", "mild": "경미함", "moderate": "보통", "severe": "심각함"}, "addButton": "알레르기 추가", "noAllergiesButton": "알레르기가 없습니다"}, "medications": {"title": "복용 중인 약 및 보충제", "subtitle": "현재 복용 중인 약이나 보충제에 대해 알려주세요", "medicationIndex": "{{index}}번 약", "name": {"label": "약 이름", "placeholder": "약 이름 입력"}, "startDate": {"question": "언제부터 복용하셨나요?", "placeholder": "날짜 선택"}, "type": {"label": "약 종류", "shortTerm": "단기간", "longTerm": "장기간"}, "dose": {"label": "복용량", "placeholder": "양"}, "unit": {"label": "단위"}, "frequency": {"label": "복용 빈도", "placeholder": "횟수", "perDay": "하루", "perWeek": "주", "perMonth": "달", "perYear": "년"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "약 추가", "noMedicationsButton": "약을 복용하지 않습니다", "calendar": {"title": "시작 날짜 선택"}}, "conditions": {"title": "질병 및 병력", "subtitle": "과거 또는 현재 앓고 있는 질병에 대해 알려주세요", "conditionIndex": "{{index}}번 질병", "name": {"label": "질병 이름", "placeholder": "질병 입력 (예: 천식 등)"}, "since": {"question": "언제부터 이 질병을 앓고 있으신가요?", "placeholder": "날짜 선택"}, "current": {"question": "현재 증상이 있으신가요?"}, "medicated": {"question": "이 질병에 대한 약을 복용하고 있으신가요?"}, "addButton": "질병 추가", "noConditionsButton": "질병이 없습니다", "calendar": {"title": "날짜 선택"}}, "reproductive": {"title": "생식 건강", "subtitle": "이 정보는 보다 개인화된 건강 권장 사항을 제공하는 데 도움이 됩니다", "menstruation": {"question": "월경을 경험한 적이 있습니까?", "detailsTitle": "월경 세부 정보", "regularity": {"question": "생리 주기는 얼마나 규칙적인가요?", "regular": "규칙적", "irregular": "불규칙적", "notSure": "잘 모르겠음"}, "cycleLength": {"label": "평균 생리 주기 길이 (일)", "placeholder": "생리 주기 길이를 입력하세요"}, "flowDays": {"label": "생리 기간: {{flowDays}}", "min": "1일", "max": "15일"}, "padsPerDay": {"label": "하루 생리대/탐폰 개수: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "생리 기간 중 증상이 있습니까?", "placeholder": "증상을 입력하세요 (예: 경련, 두통)"}}, "childbirth": {"question": "출산 경험이 있습니까?", "detailsTitle": "출산 세부 정보", "children": {"label": "자녀 수"}, "pregnancies": {"label": "임신 횟수"}, "complications": {"question": "임신 또는 출산 중 합병증이 있었습니까?", "placeholder": "합병증이 있는 경우 입력하세요"}}}, "review": {"title": "정보 검토", "subtitle": "제출하기 전에 제공한 정보를 검토해주세요", "sections": {"basicInfo": "기본 정보", "lifestyle": "라이프스타일", "allergies": "알레르기", "medications": "약물 및 보충제", "conditions": "질병", "reproductive": "생식 건강", "menstruationDetails": "월경 세부 정보", "childbirthDetails": "출산 세부 정보"}, "fields": {"age": "나이:", "sex": "성별:", "height": "키:", "weight": "몸무게:", "diet": "식단:", "exercise": "운동:", "drinking": "음주:", "smoking": "흡연:", "sleep": "수면:", "hydration": "수분 섭취:", "allergyIndex": "{{index}}번 알레르기:", "dose": "복용량:", "frequency": "빈도:", "type": "종류:", "since": "시작 시점:", "currentlyActive": "현재 복용 중:", "takingMedication": "약 복용 여부:", "hasMenstruated": "월경 경험 여부:", "regularity": "주기:", "cycleLength": "주기 길이:", "flowDays": "생리 기간:", "padsPerDay": "하루 생리대/탐폰 개수:", "hasChildbirth": "출산 경험 여부:", "children": "자녀 수:", "pregnancies": "임신 횟수:"}, "notProvided": "제공되지 않음", "units": {"cm": "{{height}}cm", "kg": "{{weight}}kg"}, "values": {"sleepHours": "하루 {{sleep}}시간 수면", "hydration": "하루 {{hydration}}컵 ({{liters}}L) 수분 섭취", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}}일"}, "noData": {"allergies": "알레르기 정보 없음", "medications": "약물 정보 없음", "conditions": "질병 정보 없음"}, "submitButton": "정보 제출"}, "success": {"title": "정보가 업데이트되었습니다!", "message": "건강 정보를 제공해주셔서 감사합니다. 이 정보를 사용하여 개인화된 경험을 제공하고 더 나은 권장 사항을 드리겠습니다.", "benefits": {"insights": "개인화된 건강 정보", "reminders": "더 나은 약 복용 알림", "recommendations": "맞춤형 건강 권장 사항"}, "continueButton": "대시보드로 이동"}, "permissions": {"microphonePermissionDenied": "마이크 권한이 거부되었습니다", "microphoneAccessDescription": "August는 오디오를 녹음하고 음성 메모를 보내기 위해 마이크에 접근해야 합니다", "permissionDenied": "권한 거부됨", "cameraPermissionRequired": "이 기능을 사용하려면 카메라 권한이 필요합니다!", "mediaLibraryPermissionRequired": "이 기능을 사용하려면 미디어 라이브러리 권한이 필요합니다!"}, "voiceRecording": {"recordingTooLong": "녹음 시간이 너무 깁니다", "recordingTooLongMessage": "음성 녹음은 5분 미만이어야 합니다. 더 짧은 메시지를 녹음해주세요."}, "errors": {"uploadFailed": "업로드 실패", "voiceUploadFailed": "음성 녹음을 업로드할 수 없습니다.", "voiceRecordingFailed": "음성 녹음 전송 실패", "failedToStopRecording": "녹음을 중지하지 못했습니다", "photoUploadFailed": "사진을 업로드할 수 없습니다.", "failedToTakePhoto": "사진 촬영에 실패했습니다", "imageUploadFailed": "{{fileName}} 이미지를 업로드할 수 없습니다.", "failedToPickImage": "이미지 선택에 실패했습니다", "documentUploadFailed": "{{fileName}} 문서를 업로드할 수 없습니다.", "failedToPickDocument": "문서 선택에 실패했습니다"}, "audioPlayer": {"downloadingAudio": "오디오 다운로드 중...", "loadingAudio": "오디오 로딩 중..."}, "mediaProcessing": {"processingFile": "파일 처리 중입니다", "uploadingSecuring": "파일 업로드 및 보안 처리 중...", "analyzingContent": "문서 내용 분석 중...", "extractingInfo": "주요 정보 추출 중...", "processingInsights": "인사이트 처리 중...", "preparingResponse": "자세한 응답 준비 중...", "finalizingResponse": "응답 마무리 중..."}, "attachments": {"voiceMessage": "음성 메시지", "image": "[이미지]", "pdf": "[PDF]", "voice": "[음성 메모]"}, "pdf": {"loadingPdf": "PDF 로딩 중..."}, "dateTime": {"yesterday": "어제, "}, "navbar": {"defaultTitle": "august", "selectedCount": "선택됨"}, "mediaUpload": {"photoLibrary": "사진 보관함", "takePhoto": "사진 찍기", "chooseFile": "파일 선택"}, "comingSoon": {"title": "곧 출시됩니다!", "description": "는 현재 개발 중입니다. 업데이트를 기다려주세요!", "buttonText": "알겠습니다!"}, "clipboard": {"success": "링크가 클립보드에 복사되었습니다"}, "mediaPhotos": {"emptyState": "아직 항목이 없습니다."}, "foodDetail": {"defaultFoodName": "음식", "nutrition": {"totalCalories": "총 칼로리", "proteins": "단백질", "carbs": "탄수화물", "fat": "지방", "sugars": "당류", "fibers": "섬유질"}}, "reports": {"defaultTitle": "미디어 항목", "defaultFoodName": "음식", "defaultName": "문서", "openButton": "외부 뷰어에서 열기", "biomarker": {"headerBiomarker": "바이오마커", "headerValue": "값", "headerRefRange": "참조 범위", "headerStatus": "상태"}, "noData": "바이오마커 데이터가 없습니다"}, "setup": {"title": "모든 것을 설정해 드리고 있습니다", "inProgress": "진행 중...", "progressMessages": {"0": "일일 칼로리 계산 중", "1": "매크로 분할 최적화 중", "2": "식단 계획 생성 중", "3": "건강 점수 계산 중", "4": "설정 마무리 중"}, "checklistItems": {"0": "건강 데이터 분석 중", "1": "최적의 영양 계획 계산 중", "2": "개인 맞춤형 권장 사항 생성 중", "3": "식사 제안 생성 중", "4": "설정 마무리 중"}}, "foodEntry": {"emptyState": "아직 음식 기록이 없습니다. 식사 사진을 찍어 추가해 보세요!"}, "nutritionReview": {"congratulations": "축하합니다!", "subtitle": "맞춤 영양 계획이 준비되었습니다", "submitButton": "시작해 봅시다!", "dailyTargetsTitle": "매일 영양 목표", "macroLabels": {"calories": "칼로리", "carbs": "탄수화물", "protein": "단백질", "fats": "지방"}, "recommendations": {"title": "목표 달성 방법:", "healthScores": "건강 점수를 활용하여 일상을 개선하세요", "trackFood": "꾸준히 식단을 기록하세요", "followCalories": "매일 권장 칼로리 섭취량을 따르세요", "balanceMacros": "탄수화물, 단백질, 지방 섭취량을 균형 있게 조절하세요"}}, "editModal": {"titlePrefix": "수정", "cancelButton": "취소", "saveButton": "다음"}, "processing": {"stages": {"scanning": "음식 스캔 중...", "identifying": "재료 확인 중...", "extracting": "영양분 추출 중...", "finalizing": "결과를 최종 처리 중입니다..."}, "error": {"defaultMessage": "음식이 감지되지 않았습니다", "subtitle": "다른 각도로 시도해 보세요"}, "retakeButton": "사진 다시 찍으려면 탭하세요", "notification": "완료되면 알려드리겠습니다!"}, "chart": {"title": "시간 경과에 따른 영양 추적", "selectNutrient": "영양소 선택:", "emptyState": "아직 영양 정보가 없습니다.", "dropdown": {"calories": "칼로리", "protein": "단백질", "carbs": "탄수화물", "fat": "지방", "sugars": "설탕류"}}, "foodModal": {"defaultName": "식품", "defaultDate": "오늘", "defaultTime": "알 수 없는 시간", "saveChanges": "변경 사항 저장", "error": {"title": "오류", "message": "영양 정보 업데이트에 실패했습니다. 다시 시도해주세요."}, "nutrition": {"calories": "🔥 칼로리", "proteins": "🥩 단백질", "carbs": "🍞 탄수화물", "sugars": "🍬 설탕", "fat": "🥑 지방"}, "macroBreakdown": {"title": "마크로 영양소 분포", "noData": "이 식품에 대한 거대 영양소 데이터가 없습니다."}, "macroLabels": {"calories": "칼로리", "protein": "<PERSON><PERSON>", "carbs": "탄수화물", "fat": "지방", "sugar": "설탕"}}, "infoModal": {"title": "상세 정보", "edit": "편집", "save": "저장", "saving": "저장 중...", "enterValue": "값을 입력하세요", "notSet": "설정되지 않음", "age": "나이", "heightCm": "키 (cm)", "weightKg": "체중 (kg)", "targetWeight": "목표 체중", "nutritionTargets": "영양 목표", "protein": "단백질", "carbs": "탄수화물", "fats": "지방", "gm": "gm", "editNote": "값을 입력하거나 비워두면 자동 계산됩니다.", "autoCalculateNote": "매크로는 데이터를 기반으로 자동 계산됩니다.", "validation": {"ageMin": "만 18세 이상이어야 합니다", "ageMax": "나이는 125세 미만이어야 합니다.", "heightMin": "높이는 50cm 이상이어야 합니다.", "heightMax": "키는 250cm 미만이어야 합니다.", "weightMin": "무게는 최소 30kg 이상이어야 합니다.", "weightMax": "무게는 500kg 미만이어야 합니다.", "targetWeightMin": "목표 체중은 최소 30kg 이상이어야 합니다.", "targetWeightMax": "목표 중량은 500kg 미만이어야 합니다.", "proteinMin": "단백질은 0 이상이어야 합니다", "carbsMin": "탄수화물은 0 이상이어야 합니다", "fatsMin": "지방은 0 이상이어야 합니다"}}, "tracker": {"calories": "칼로리", "protein": "단백질", "carbs": "탄수화물", "fat": "지방", "excess": "과잉", "remaining": "남은"}, "specialistConstants": {"nutritionist": {"name": "영양사", "description": "식단, 영양 및 건강한 식습관에 대한 전문가 조언", "featureName": "영양 전문가"}, "cardiologist": {"name": "심장 전문의", "description": "심장 건강 및 심혈관 질환 전문", "featureName": "심장 전문의"}, "neurologist": {"name": "신경과 전문의", "description": "뇌, 척수 및 신경계 질환 전문", "featureName": "신경과 전문의"}, "oncologist": {"name": "종양 전문의", "description": "암 진단 및 치료 옵션 전문", "featureName": "종양 전문의"}, "endocrinologist": {"name": "내분비 전문의", "description": "호르몬 질환 및 대사 장애 전문가", "featureName": "내분비 전문의"}}, "discoverCards": {"categories": {"nutrition": "영양", "heartHealth": "심장 건강", "mentalHealth": "정신 건강", "fitness": "피트니스", "wellness": "웰니스"}, "titles": {"vitaminB12Recovery": "비타민 B12 결핍에서 회복하는 데 얼마나 걸립니까?", "vitaminDeficiencyGanglion": "어떤 비타민 결핍이 갱글리온 낭종을 유발합니까?", "vitaminDeficiencyHairFall": "어떤 비타민 결핍이 탈모를 유발합니까?", "vitaminWaters": "비타민워터는 건강에 좋은가요?", "cholesterolHeadaches": "고콜레스테롤이 두통을 유발합니까?", "cholesterolEyes": "눈에 보이는 고콜레스테롤의 증상은 무엇입니까?", "diabetesHeadaches": "당뇨병이 두통을 유발할 수 있습니까?", "chestPainDrinking": "술을 마신 후 가슴이 아픈 이유는 무엇입니까?", "stressDizziness": "스트레스가 어지러움을 유발할 수 있습니까?", "bulimiaFace": "폭식증 얼굴이란 무엇입니까?", "kneeTwitch": "무릎이 떨리는 이유는 무엇입니까?", "noseTwitching": "코가 떨리는 이유는 무엇입니까?", "piriformisVsSciatica": "이상근 증후군과 좌골 신경통의 차이점은 무엇입니까?", "shoulderBladePinched": "견갑골의 눌린 신경을 풀어주는 방법은 무엇입니까?", "shoulderPinched": "어깨의 눌린 신경을 풀어주는 방법은 무엇입니까?", "meniscusTorn": "반월상 연골판 파열을 자연적으로 치료하는 방법은 무엇입니까?", "hydrateQuickly": "빠르게 수분을 공급하는 방법은 무엇입니까?", "periodConstipation": "생리 중 변비가 오는 것이 정상입니까?", "acneScars": "일주일 안에 여드름 흉터를 자연적으로 없애는 방법은 무엇입니까?", "perimenopausePregnancy": "폐경 전에 임신이 가능합니까?"}, "descriptions": {"vitaminB12Recovery": "비타민 B12 결핍의 회복 기간과 에너지 레벨을 높이는 효과적인 방법을 알아보세요.", "vitaminDeficiencyGanglion": "비타민 결핍과 신체의 건초염 발생 간의 연관성을 살펴봅니다.", "vitaminDeficiencyHairFall": "필수 비타민 부족이 탈모로 이어지는 과정과 예방법을 알아보세요.", "vitaminWaters": "일상 영양 섭취의 일환으로 비타민워터의 효능과 잠재적 단점을 알아보세요.", "cholesterolHeadaches": "높은 콜레스테롤 수치와 두통 발생 간의 가능한 연관성을 조사합니다.", "cholesterolEyes": "높은 콜레스테롤이 눈에 어떻게 나타나는지와 주의해야 할 증상을 알아보세요.", "diabetesHeadaches": "일상생활에서 당뇨병과 두통 발생의 관계를 조사합니다.", "chestPainDrinking": "특정 음료 섭취 후 가슴 통증의 원인을 알아봅니다.", "stressDizziness": "스트레스가 균형감과 전반적인 건강에 미치는 영향과 어지럼증으로 이어지는 과정을 자세히 알아봅니다.", "bulimiaFace": "얼굴 외모에 미치는 영향을 포함하여 신경성 식욕부진의 신체적 징후를 이해합니다.", "kneeTwitch": "무의식적인 무릎 경련의 잠재적 원인과 스트레스 또는 피로와의 관계를 조사합니다.", "noseTwitching": "코가 씰룩거리는 가능한 이유와 불안감이나 기타 요인과의 연관성에 대해 알아봅니다.", "piriformisVsSciatica": "이상근 증후군과 좌골 신경통의 증상을 비교하여 자신의 상태를 더 잘 이해합니다.", "shoulderBladePinched": "견갑골의 핀치드 신경을 완화하고 가동성을 회복하는 효과적인 방법을 알아보세요.", "shoulderPinched": "어깨 부위의 신경 압박을 완화하는 간단한 운동과 스트레칭을 알아보세요.", "meniscusTorn": "반월상 연골 파열 치유를 돕는 자연적인 방법과 운동을 알아보세요.", "hydrateQuickly": "빠르고 효과적인 수분 공급 방법과 최적의 수분 유지를 위한 방법을 알아보세요.", "periodConstipation": "월경 중 변비의 원인과 자연적인 치료법을 알아보세요.", "acneScars": "여드름 흉터의 모양을 빠르게 줄이는 자연적인 치료법과 스킨케어 팁을 알아보세요.", "perimenopausePregnancy": "폐경 전 증후군, 임신 고려 사항 및 이 시기에 예상되는 사항에 대해 알아보세요."}}}