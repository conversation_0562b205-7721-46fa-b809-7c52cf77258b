import React, { useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { Specialist } from '@/types/specialists';
import { colors } from '@/constants/colors';
import { Heart, Utensils, Brain, Stethoscope, Activity } from 'lucide-react-native';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';

interface SpecialistCardProps {
  specialist: Specialist;
  onPress: (specialist: Specialist) => void;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      width: moderateScale(160),
      height: moderateVerticalScale(100),
      borderRadius: moderateScale(theme.radii.lg),
      padding: moderateScale(12),
      marginRight: moderateScale(12),
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateVerticalScale(1) },
      shadowOpacity: 0.001,
      shadowRadius: 0,
      elevation: 2,
      justifyContent: "space-between",
      borderWidth: 1,
      borderColor: theme.colors.gray[100],
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(8),
    },
    iconContainer: {
      width: moderateScale(30),
      height: moderateScale(30),
      borderRadius: moderateScale(16),
      backgroundColor: theme.colors.secondary[50],
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1.5,
      borderColor: theme.colors.primary.main,
    },
    name: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      flex: 1,
      color: theme.colors.gray[800],
    },
    description: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[600],
      lineHeight: moderateVerticalScale(19),
    },
  });

const SpecialistCard: React.FC<SpecialistCardProps> = ({ specialist, onPress }) => {
  
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  const renderIcon = useMemo(() => {
    const iconProps = { size: moderateScale(16), color: colors.primary };
    switch (specialist.icon) {
      case 'Heart':
        return <Heart {...iconProps} />;
      case 'Utensils':
        return <Utensils {...iconProps} />;
      case 'Brain':
        return <Brain {...iconProps} />;
      case 'Stethoscope':
        return <Stethoscope {...iconProps} />;
      case 'Activity':
        return <Activity {...iconProps} />;
      default:
        return <Heart {...iconProps} />;
    }
  }, [specialist.icon]);

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.white }]}
      onPress={() => onPress(specialist)}
      activeOpacity={0.8}
    >
      <View style={styles.headerRow}>
        <View style={styles.iconContainer}>
          {renderIcon}
        </View>
        <Text style={styles.name} numberOfLines={1}>
          {specialist.name}
        </Text>
      </View>
      <Text style={styles.description} numberOfLines={2}>
        {specialist.description}
      </Text>
    </TouchableOpacity>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     width: moderateScale(160),
//     height: moderateVerticalScale(100),
//     borderRadius: moderateScale(16),
//     padding: moderateScale(12),
//     marginRight: moderateScale(12),
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(1) },
//     shadowOpacity: 0.001,
//     shadowRadius: 0,
//     elevation: 2,
//     justifyContent: 'space-between',
//     borderWidth: 1,
//     borderColor: colors.gray[100],
//   },
//   headerRow: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     gap: moderateScale(8),
//   },
//   iconContainer: {
//     width: moderateScale(30),
//     height: moderateScale(30),
//     borderRadius: moderateScale(16),
//     backgroundColor: colors.white,
//     justifyContent: 'center',
//     alignItems: 'center',
//     borderWidth: 1.5,
//     borderColor: colors.primary,
//   },
//   name: {
//     fontSize: moderateScale(14),
//     fontWeight: '500',
//     flex: 1,
//     color: colors.gray[800],
//   },
//   description: {
//     fontSize: moderateScale(12),
//     color: colors.gray[600],
//     lineHeight: moderateVerticalScale(19),
//   },
// });

const MemoizedSpecialistCard = React.memo(
  SpecialistCard,
  (prevProps, nextProps) => {
    return (
      prevProps.specialist.id === nextProps.specialist.id &&
      prevProps.specialist.name === nextProps.specialist.name &&
      prevProps.specialist.description === nextProps.specialist.description &&
      prevProps.specialist.icon === nextProps.specialist.icon
    );
  }
);

export default MemoizedSpecialistCard;
