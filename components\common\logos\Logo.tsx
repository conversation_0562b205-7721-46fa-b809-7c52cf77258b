import React from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { moderateScale, moderateVerticalScale } from 'react-native-size-matters';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
}

const Logo: React.FC<LogoProps> = ({ size = 'medium' }) => {
  const logoSizes = {
    small: { width: 80, height: 80 },
    medium: { width: 120, height: 120 },
    large: { width: 160, height: 160 },
  };

  return (
    <View style={styles.container}>
      <Image
        source={{ uri: "https://res.cloudinary.com/dpgnd3ad7/image/upload/v1738557729/august_logo_green_nd4fn9.svg" }}
        style={[styles.logo, logoSizes[size]]}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: moderateScale(120),
    height: moderateVerticalScale(120),
  },
});

export default Logo;
