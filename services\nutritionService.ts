import axios from 'axios'; // For type checking
import axiosInstance from './axiosInstance';
import logger from '@/utils/logger/logger';
import { getEnvironmentVariable } from '@/utils/getEnvironmentVariable';

export const updateNutritionData = async (nutritionData: any): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/change-nutrition-data`;
    
    logger.info(`Updating nutrition data at: ${url}`, nutritionData);
    logger.info('Nutrition data payload is:', nutritionData);
    const response = await axiosInstance.post(url, nutritionData);
    
    return response;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
      logger.error('Error updating nutrition data:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });}
    } else {
      logger.error('Error updating nutrition data:', error);
    }
    throw error;
  }
};

export const getNutritionData = async (): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/get-nutrition-data`;
    logger.info(`Fetching nutrition data from: ${url}`);
    const response = await axiosInstance.get(url);
    logger.info('nutrition fata is', response.data)
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
      logger.error('Error fetching nutrition data:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });}
    } else {
      logger.error('Error fetching nutrition data:', JSON.stringify(error));
    }
    throw error;
  }
};

export const updateFoodData = async (reportId: string, foodData: any): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/change-food-data`;
    
    logger.info(`Updating food data at: ${url}`, { reportId, ...foodData });
    
    const response = await axiosInstance.post(url, {
      report_id: reportId,
      food_images: foodData.food_images,
      food_components: foodData.food_components
    });
    
    logger.debug('Food data update response status:', response.status);
    
    return response;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
      logger.error('Error updating food data:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });}
    } else {
      logger.error('Error updating food data:', error);
    }
    throw error;
  }
};

export const getReportsData = async (): Promise<any> => {
  try {
    const url = `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/get-files`;
    
    logger.info(`fetching file data from: ${url}`);
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status !== 401) {
      logger.error('Error fetching reports data:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });}
    } else {
      logger.error('Error fetching nutrition data:', JSON.stringify(error));
    }
    throw error;
  }
};
