import { create } from 'zustand';
import { ScrollView } from 'react-native';

type ScrollStore = {
  discoverRef: React.RefObject<ScrollView> | null;
  nutritionRef: React.RefObject<ScrollView> | null;
  personalizeRef: React.RefObject<ScrollView> | null;
  setDiscoverRef: (ref: React.RefObject<ScrollView>) => void;
  setNutritionRef: (ref: React.RefObject<ScrollView>) => void;
  setPersonalizeRef: (ref: React.RefObject<ScrollView>) => void;
};

export const useScrollStore = create<ScrollStore>((set) => ({
  discoverRef: null,
  nutritionRef: null,
  personalizeRef: null,
  setDiscoverRef: (ref) => set({ discoverRef: ref }),
  setNutritionRef: (ref) => set({ nutritionRef: ref }),
  setPersonalizeRef: (ref) => set({ personalizeRef: ref }),
}));