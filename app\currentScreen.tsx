import { useEffect, useRef } from 'react';
import { useNavigationState } from '@react-navigation/native';
import { trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';

type TrackingData = {
  screen: string;
  [key: string]: any;
};

export function useScreenTracking(
  screenName: string, 
  trackingData: TrackingData
): string | undefined {
  // Get current screen name from navigation state
  const currentScreen = useNavigationState(state => {
    const route = state.routes[state.index];
    
    // Handle nested navigation
    if (route?.state?.routes && typeof route.state.index === 'number') {
      const tabRoute = route.state.routes[route.state.index];
      return tabRoute?.name;
    }
    
    return route?.name;
  });

  const previousScreen = useRef<string | undefined>();

  useEffect(() => {
    if (currentScreen === screenName.toLowerCase() && previousScreen.current !== screenName.toLowerCase()) {
      //console.log(currentScreen, 'IT IS',screenName)
      trackUserInteraction(`${screenName} Screen View`, trackingData);
    }
    previousScreen.current = currentScreen;
  }, [currentScreen, screenName, trackingData]);

  return currentScreen;
}