import { create } from 'zustand';
import { Audio } from 'expo-av';
import { IWaveformRef } from '@simform_solutions/react-native-audio-waveform';

type AudioPlayer = {
    sound: Audio.Sound;
    waveformRef: React.RefObject<IWaveformRef> | null;
    messageId: string;
  };
  
  type AudioStore = {
    currentPlayer: AudioPlayer | null;
    setCurrentPlayer: (player: AudioPlayer | null) => void;
  };

export const useAudioStore = create<AudioStore>((set) => ({
    currentPlayer: null,
    setCurrentPlayer: (player) => set({ currentPlayer: player }),
  }));
  