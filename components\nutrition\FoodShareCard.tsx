import React from "react";
import { View, Text, StyleSheet, Image, Platform } from "react-native";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { useTranslation } from "react-i18next";

interface FoodShareCardProps {
  foodName: string;
  foodImage: string;
  date: string;
  time: string;
  macros: {
    calories: number;
    protein: number;
    fat: number;
    carbohydrates: number;
  };
}

const CARD_SIZE = 340;
const CARD_RADIUS = 24;

export default function FoodShareCard({
  foodName,
  foodImage,
  date,
  time,
  macros,
}: FoodShareCardProps) {
  const { t } = useTranslation()
  return (
    <View style={styles.cardWrapper}>
      <BlurView
        intensity={60}
        tint="light"
        style={styles.lowerContent}
        experimentalBlurMethod={Platform.OS === "android" ? "dimezisBlurView" : undefined}
      >
        <LinearGradient
          colors={["#FFF8F2", "#F7E7D6", "#FDF6ED"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={StyleSheet.absoluteFill}
        />
        <View style={styles.lowerContentInner}>
          <View style={styles.headerSection}>
            {/* <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <Text style={styles.foodName}>{foodName}</Text>
            </View> */}
            <Text style={styles.dateTime}>{date}  •  {time}</Text>
          </View>
          {/* Calories */}
          <View style={styles.caloriesRow}>
            <Text style={styles.fireEmoji}>🔥</Text>
            <Text style={styles.caloriesValue}>{macros.calories}</Text>
            <Text style={styles.kcalLabel}>kcal</Text>
          </View>
          {/* Macros box */}
          <View style={styles.macrosBox}>
            <View style={styles.macroCol}>
              <Text style={[styles.macroValue, { color: '#973131' }]}>{macros.protein}g</Text>
              <Text style={styles.macroLabel}> {t('nutrition.proteinLabel')}</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.macroCol}>
              <Text style={[styles.macroValue, { color: '#EE7214' }]}>{macros.carbohydrates}g</Text>
              <Text style={styles.macroLabel}>{t('nutrition.carbohydratesLabel')}</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.macroCol}>
              <Text style={[styles.macroValue, { color: '#D24545' }]}>{macros.fat}g</Text>
              <Text style={styles.macroLabel}>{t('nutrition.fatLabel')}</Text>
            </View>
          </View>
          <View style={styles.macroBarContainer}>
            {(() => {
              const total = macros.protein + macros.carbohydrates + macros.fat;
              const proteinPct = total ? (macros.protein / total) * 100 : 0;
              const carbsPct = total ? (macros.carbohydrates / total) * 100 : 0;
              const fatPct = total ? (macros.fat / total) * 100 : 0;
              return (
                <View style={styles.macroBarBg}>
                  <View style={[styles.macroBarSection, { backgroundColor: '#973131', width: `${proteinPct}%`, borderTopLeftRadius: 8, borderBottomLeftRadius: 8 }]} />
                  <View style={[styles.macroBarSection, { backgroundColor: '#EE7214', width: `${carbsPct}%` }]} />
                  <View style={[styles.macroBarSection, { backgroundColor: '#D24545', width: `${fatPct}%`, borderTopRightRadius: 8, borderBottomRightRadius: 8 }]} />
                </View>
              );
            })()}
          </View>
          <View style={styles.augustFooter}>
            <Image source={require("../../assets/images/august.ai.png")} style={styles.logoA} />
          </View>
        </View>
      </BlurView>
      <View style={styles.foodImageContainer}>
        <Image source={{ uri: foodImage }} style={styles.foodImage} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  cardWrapper: {
    width: CARD_SIZE,
    borderRadius: CARD_RADIUS,
    overflow: "hidden",
    backgroundColor: "rgba(255,255,255,0.7)",
    shadowColor: "#000",
    shadowOpacity: 0.10,
    shadowRadius: 12,
    elevation: 4,
    flexDirection: "column",
    position: "relative",
  },
  foodImageContainer: {
    width: "100%",
    height: CARD_SIZE *0.9,
    overflow: "hidden",
    backgroundColor: "#fff",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 0,
  },
  foodImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  lowerContent: {
    marginTop: CARD_SIZE *0.8, // Pull lowerContent down to overlap image
    width: "100%",
    borderRadius: CARD_RADIUS,
    overflow: "hidden",
    zIndex: 1,
    backgroundColor: "rgba(255,255,255,0.25)",
  },
  lowerContentInner: {
    width: "100%",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingHorizontal: 24,
    paddingTop: 18,
    paddingBottom: 12,
  },
  headerSection: {
    alignItems: "center",
    width: "100%",
    marginBottom: 0,
  },
  foodName: {
    fontSize: 28,
    fontWeight: "800",
    color: "#3B2C13",
    textAlign: "center",
  },
  emoji: {
    fontSize: 26,
    marginLeft: 6,
    marginTop: 2,
  },
  dateTime: {
    fontSize: 12,
    color: "#8B6F3A",
    fontWeight: "400",
    marginBottom: 6,
    textAlign: "center",
  },
  caloriesRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10,
    marginTop: 2,
  },
  fireEmoji: {
    fontSize: 22,
    marginRight: 6,
    marginTop: 2,
  },
  caloriesValue: {
    fontSize: 30,
    fontWeight: "500",
    color: "#347928", 
    marginRight: 4,
  },
  kcalLabel: {
    fontSize: 16,
    color: "#347928", 
    fontWeight: "300",
    marginStart: 2,
    marginBottom: 4,
    alignSelf: "flex-end",
  },
  macrosBox: {
    flexDirection: "row",
    backgroundColor: "rgba(255, 248, 242, 0.85)",
    borderRadius: 18,
    width: "100%",
    alignSelf: "center",
    marginTop: 8,
    marginBottom: 18,
    paddingVertical: 16,
    paddingHorizontal: 0,
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#9A7E6F", 
  },
  macroCol: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  macroValue: {
    fontSize: 24,
    fontWeight: "500",
    color: "#A97A3B",
    marginBottom: 2,
  },
  macroLabel: {
    fontSize: 13,
    color: "#9A7E6F",
    fontWeight: "300",
  },
  verticalDivider: {
    width: 1,
    height: 36,
    backgroundColor: "#9A7E6F",
    marginHorizontal: 0,
  },
  macroBarContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: -6,
  },
  macroBarBg: {
    width: '99%',
    height: 10,
    borderRadius: 8,
    backgroundColor: '#E6E6E6',
    flexDirection: 'row',
    overflow: 'hidden',
  },
  macroBarSection: {
    height: '100%',
  },
  augustFooter: {
    width :"100%",
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "flex-end",
    borderRadius: 10,
    marginTop: 8,
    alignSelf: "center",
    marginBottom: 12,
  },
  logoA: {
    width: 80,
    height: 32,
    resizeMode: "contain",
    marginLeft: 8, 
    marginRight: 0,
  },
});