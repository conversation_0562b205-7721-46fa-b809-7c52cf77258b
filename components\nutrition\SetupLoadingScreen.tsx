import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, Animated, Easing, Dimensions, Platform } from 'react-native';
import { colors } from '@/constants/colors';
import { Check } from 'lucide-react-native';
import { moderateScale, moderateVerticalScale, scale } from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from 'react-i18next';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const isSmallScreen = SCREEN_HEIGHT < 700;
const isAndroid = Platform.OS === 'android';
const FONT_SCALE_FACTOR = Platform.OS === 'android' ? 0.85 : 1;


interface SetupLoadingScreenProps {
  onComplete: () => void;
} 
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.gray[50],
      justifyContent: "center",
    },
    content: {
      paddingHorizontal: moderateScale(20),
      paddingVertical: moderateVerticalScale(isSmallScreen ? 16 : 24),
    },
    topSection: {
      alignItems: "center",
      marginBottom: moderateVerticalScale(32),
    },
    percentage: {
      fontSize: moderateScale(theme.fontSize["5xl"] * FONT_SCALE_FACTOR),
      fontWeight: Platform.select({ ios: "700", android: "700" }),
      color: theme.colors.primary.main,
      marginBottom: moderateVerticalScale(8),
      includeFontPadding: false,
      textAlignVertical: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl * FONT_SCALE_FACTOR),
      fontWeight: Platform.select({ ios: "600", android: "600" }),
      color: theme.colors.gray[800],
      textAlign: "center",
      marginBottom: moderateVerticalScale(24),
      includeFontPadding: false,
    },
    progressBarContainer: {
      width: "100%",
      height: moderateScale(6),
      backgroundColor: theme.colors.gray[100],
      borderRadius: moderateScale(theme.radii.xs),
      overflow: "hidden",
    },
    progressBar: {
      height: "100%",
      backgroundColor: theme.colors.primary.main,
      borderRadius: moderateScale(theme.radii.xs),
    },
    checklistContainer: {
      backgroundColor: theme.colors.gray[50],
      borderRadius: moderateScale(theme.radii.lg),
      padding: moderateScale(20),
      marginTop: moderateVerticalScale(16),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
      elevation: isAndroid ? 2 : 0,
      shadowColor: theme.colors.gray[400],
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },
    checklistItem: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: moderateVerticalScale(16),
    },
    lastChecklistItem: {
      marginBottom: 0,
    },
    checkCircle: {
      width: moderateScale(24),
      height: moderateScale(24),
      borderRadius: moderateScale(theme.radii.md),
      backgroundColor: theme.colors.primary.main,
      alignItems: "center",
      justifyContent: "center",
    },
    emptyCircle: {
      width: moderateScale(24),
      height: moderateScale(24),
      borderRadius: moderateScale(theme.radii.md),
      borderWidth: 1.5,
      borderColor: theme.colors.gray[300],
      backgroundColor: theme.colors.secondary[50],
      alignItems: "center",
      justifyContent: "center",
    },
    textContainer: {
      flex: 1,
      marginLeft: moderateScale(12),
    },
    checklistText: {
      fontSize: moderateScale(theme.fontSize.sm * FONT_SCALE_FACTOR),
      fontWeight: Platform.select({ ios: "500", android: "500" }),
      color: theme.colors.gray[600],
      includeFontPadding: false,
      textAlignVertical: "center",
    },
    activeChecklistText: {
      color: theme.colors.gray[900],
      fontWeight: Platform.select({ ios: "600", android: "600" }),
    },
    completedText: {
      color: theme.colors.gray[400],
    },
    inProgressText: {
      fontSize: moderateScale(theme.fontSize.xs * FONT_SCALE_FACTOR),
      color: theme.colors.primary.main,
      marginTop: moderateVerticalScale(2),
      fontStyle: "italic",
      includeFontPadding: false,
    },
    stepNumber: {
      fontSize: moderateScale(theme.fontSize.xs * FONT_SCALE_FACTOR),
      color: theme.colors.gray[400],
      fontWeight: Platform.select({ ios: "600", android: "600" }),
    },
    progressMessage: {
      fontSize: moderateScale(theme.fontSize.sm * FONT_SCALE_FACTOR),
      color: theme.colors.gray[600],
      textAlign: "center",
      marginTop: moderateVerticalScale(8),
      fontStyle: "italic",
    },
  });
const SetupLoadingScreen: React.FC<SetupLoadingScreenProps> = React.memo(
  ({ onComplete }) => {
    const { t } = useTranslation();
    const CHECKLIST_ITEMS = React.useMemo(() => [
      t('setup.checklistItems.0'),
      t('setup.checklistItems.1'),
      t('setup.checklistItems.2'),
      t('setup.checklistItems.3'),
      t('setup.checklistItems.4')
    ], [t]);
    
      const { theme } = useTheme();
      const styles = useMemo(() => createStyles(theme), [theme]);
    const [percentage, setPercentage] = useState(0);
    const [currentStep, setCurrentStep] = useState(-1);
    const [currentProgress, setCurrentProgress] = useState("");
    const progressAnim = useRef(new Animated.Value(0)).current;

    // Initialize fadeAnims only once
    const fadeAnims = useRef(
      CHECKLIST_ITEMS.map(() => new Animated.Value(0))
    ).current;

    // Use callback instead of useMemo for functions
    const fadeInItem = useCallback(
      (index: number) => {
        Animated.sequence([
          Animated.timing(fadeAnims[index], {
            toValue: 1,
            duration: 400,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.delay(100),
          ...(index < CHECKLIST_ITEMS.length - 1
            ? [
                Animated.timing(fadeAnims[index], {
                  toValue: 1,
                  duration: 300,
                  easing: Easing.out(Easing.ease),
                  useNativeDriver: true,
                }),
              ]
            : []),
        ]).start();
        updateProgressMessage(index);
      },
      [fadeAnims, t]
    );

    const updateProgressMessage = (index: number) => {
      Animated.sequence([
        Animated.timing(progressAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(progressAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
      setCurrentProgress(t(`setup.progressMessages.${index}`));
    };

    // Track the current step ref to avoid dependency in interval
    const currentStepRef = useRef(currentStep);
    currentStepRef.current = currentStep;

    // Track onComplete ref to avoid dependency in cleanup
    const onCompleteRef = useRef(onComplete);
    onCompleteRef.current = onComplete;

    useEffect(() => {
      const interval = setInterval(() => {
        setPercentage((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            const timeoutId = setTimeout(() => {
              onCompleteRef.current();
            }, 1500);
            return 100;
          }

          // Add specific delays between 50-54%
          if (prev >= 11 && prev < 12) {
            return prev + 0.1; // 1 sec
          }
          if (prev >= 12 && prev < 13) {
            return prev + 0.15; // 1 sec delay
          }
          if (prev >= 52 && prev < 53) {
            return prev + 0.15; // 1 sec delay
          }
          if (prev >= 53 && prev < 54) {
            return prev + 0.15; // 1 sec delay
          }

          // Add specific delays between 90-94%
          if (prev >= 90 && prev < 91) {
            return prev + 0.1; // Very slow
          }
          if (prev >= 91 && prev < 92) {
            return prev + 0.15; // 2 sec delay
          }
          if (prev >= 92 && prev < 93) {
            return prev + 0.15; // 2 sec delay
          }
          if (prev >= 98 && prev < 99) {
            return prev + 0.8; // 2 sec delay
          }
          if (prev >= 99 && prev < 100) {
            return prev + 0.8; // 2 sec delay
          }

          const newStep = Math.floor(prev / 20);
          if (
            newStep !== currentStepRef.current &&
            newStep < CHECKLIST_ITEMS.length
          ) {
            setCurrentStep(newStep);
            fadeInItem(newStep);
          }

          return prev + 0.75; // Normal speed
        });
      }, 50);

      return () => {
        clearInterval(interval);
      };
    }, [fadeInItem]);

    // Use callback instead of useMemo for these style functions
    const getChecklistItemStyle = useCallback(
      (index: number, fadeAnim: Animated.Value) => [
        styles.checklistItem,
        index === CHECKLIST_ITEMS.length - 1 && styles.lastChecklistItem,
        { opacity: fadeAnim },
      ],
      []
    );

    const getCircleStyle = useCallback(
      (index: number, fadeAnim: Animated.Value) => [
        index <= currentStep ? styles.checkCircle : styles.emptyCircle,
        isAndroid && {
          transform: [
            {
              scale: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.5, 1],
              }),
            },
          ],
        },
      ],
      [currentStep]
    );

    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.topSection}>
            <Text style={styles.percentage}>{Math.round(percentage)}%</Text>
            <Text style={styles.title}>
              {t('setup.title')}
            </Text>

            <Animated.View style={styles.progressBarContainer}>
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    // Use percentage directly instead of progress.interpolate
                    width: `${percentage}%`,
                  },
                ]}
              />
            </Animated.View>
            <Animated.Text
              style={[styles.progressMessage, { opacity: progressAnim }]}
            >
              {currentProgress}
            </Animated.Text>
          </View>

          <View style={styles.checklistContainer}>
            {CHECKLIST_ITEMS.map((item, index) => (
              <Animated.View
                key={index}
                style={getChecklistItemStyle(index, fadeAnims[index])}
              >
                <Animated.View style={getCircleStyle(index, fadeAnims[index])}>
                  {index <= currentStep ? (
                    <Check size={scale(14)} color={colors.white} />
                  ) : (
                    <Text style={styles.stepNumber}>{index + 1}</Text>
                  )}
                </Animated.View>

                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.checklistText,
                      index === currentStep && styles.activeChecklistText,
                      index < currentStep && styles.completedText,
                    ]}
                  >
                    {item}
                  </Text>
                  {index === currentStep && (
                    <Text style={styles.inProgressText}>{t('setup.inProgress')}</Text>
                  )}
                </View>
              </Animated.View>
            ))}
          </View>
        </View>
      </View>
    );
  },
  (prevProps, nextProps) => prevProps.onComplete === nextProps.onComplete
);

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//     justifyContent: 'center',
//   },
//   content: {
//     paddingHorizontal: moderateScale(20),
//     paddingVertical: moderateVerticalScale(isSmallScreen ? 16 : 24),
//   },
//   topSection: {
//     alignItems: 'center',
//     marginBottom: moderateVerticalScale(32),
//   },
//   percentage: {
//     fontSize: moderateScale(48 * FONT_SCALE_FACTOR),
//     fontWeight: Platform.select({ ios: '700', android: '700' }),
//     color: colors.primary,
//     marginBottom: moderateVerticalScale(8),
//     includeFontPadding: false,
//     textAlignVertical: 'center',
//   },
//   title: {
//     fontSize: moderateScale(20 * FONT_SCALE_FACTOR),
//     fontWeight: Platform.select({ ios: '600', android: '600' }),
//     color: colors.gray[800],
//     textAlign: 'center',
//     marginBottom: moderateVerticalScale(24),
//     includeFontPadding: false,
//   },
//   progressBarContainer: {
//     width: '100%',
//     height: moderateScale(6),
//     backgroundColor: colors.gray[100],
//     borderRadius: moderateScale(3),
//     overflow: 'hidden',
//   },
//   progressBar: {
//     height: '100%',
//     backgroundColor: colors.primary,
//     borderRadius: moderateScale(3),
//   },
//   checklistContainer: {
//     backgroundColor: colors.gray[50],
//     borderRadius: moderateScale(16),
//     padding: moderateScale(20),
//     marginTop: moderateVerticalScale(16),
//     borderWidth: 1,
//     borderColor: colors.gray[200],
//     elevation: isAndroid ? 2 : 0,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 8,
//   },
//   checklistItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: moderateVerticalScale(16),
//   },
//   lastChecklistItem: {
//     marginBottom: 0,
//   },
//   checkCircle: {
//     width: moderateScale(24),
//     height: moderateScale(24),
//     borderRadius: moderateScale(12),
//     backgroundColor: colors.primary,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   emptyCircle: {
//     width: moderateScale(24),
//     height: moderateScale(24),
//     borderRadius: moderateScale(12),
//     borderWidth: 1.5,
//     borderColor: colors.gray[300],
//     backgroundColor: colors.white,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   textContainer: {
//     flex: 1,
//     marginLeft: moderateScale(12),
//   },
//   checklistText: {
//     fontSize: moderateScale(14 * FONT_SCALE_FACTOR),
//     fontWeight: Platform.select({ ios: '500', android: '500' }),
//     color: colors.gray[600],
//     includeFontPadding: false,
//     textAlignVertical: 'center',
//   },
//   activeChecklistText: {
//     color: colors.gray[900],
//     fontWeight: Platform.select({ ios: '600', android: '600' }),
//   },
//   completedText: {
//     color: colors.gray[400],
//   },
//   inProgressText: {
//     fontSize: moderateScale(11 * FONT_SCALE_FACTOR),
//     color: colors.primary,
//     marginTop: moderateVerticalScale(2),
//     fontStyle: 'italic',
//     includeFontPadding: false,
//   },
//   stepNumber: {
//     fontSize: moderateScale(12 * FONT_SCALE_FACTOR),
//     color: colors.gray[400],
//     fontWeight: Platform.select({ ios: '600', android: '600' }),
//   },
//   progressMessage: {
//     fontSize: moderateScale(14 * FONT_SCALE_FACTOR),
//     color: colors.gray[600],
//     textAlign: 'center',
//     marginTop: moderateVerticalScale(8),
//     fontStyle: 'italic',
//   },
// });

export default SetupLoadingScreen;
