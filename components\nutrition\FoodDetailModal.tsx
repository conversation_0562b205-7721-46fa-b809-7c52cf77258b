import React, { useRef, useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  Modal, 
  TouchableOpacity, 
  Image, 
  ScrollView, 
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  useWindowDimensions,
  PanResponder,
  Animated
} from 'react-native';
import { colors } from '@/constants/colors';
import { X } from 'lucide-react-native';
import { parseNutritionalData } from '@/utils/nutrition/parseNutritionalData';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import logger from '@/utils/logger/logger';
import { useTranslation } from 'react-i18next';

// Responsive sizing helper functions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const scale = SCREEN_WIDTH / 375; // Base width is 375 (iPhone X)
const DISMISS_THRESHOLD = SCREEN_HEIGHT * 0.15; // 15% of screen height
const VELOCITY_THRESHOLD = 0.7;

type NutrientRowProps = {
  label: string;
  value: string;
  icon: string;
};

const NutrientRow: React.FC<NutrientRowProps> = ({ label, value, icon }) => (
  <View style={styles.nutrientRow}>
    <View style={styles.nutrientLabelContainer}>
      <Text style={styles.nutrientIcon}>{icon}</Text>
      <Text style={styles.nutrientLabel}>{label}</Text>
    </View>
    <Text style={styles.nutrientValue}>{value}</Text>
  </View>
);

type FoodDetailModalProps = {
  visible: boolean;
  food: any;
  onClose: () => void;
};

const FoodDetailModal: React.FC<FoodDetailModalProps> = ({ visible, food, onClose }) => {
  const { t } = useTranslation();
  const { width, height } = useWindowDimensions();
  const isSmallDevice = height < 700;
  
  // Animation values for pan gesture
  const pan = useRef(new Animated.ValueXY()).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  
  // State for tracking gesture
  const [isDragging, setIsDragging] = useState(false);
  
  // Reset animations when modal becomes visible
  useEffect(() => {
    if (visible) {
      // Reset all animated values when modal opens
      pan.setValue({ x: 0, y: 0 });
      scaleValue.setValue(1);
      opacityValue.setValue(1);
    }
  }, [visible, pan, scaleValue, opacityValue]);
  
  // Create pan responder for gesture handling
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to significant vertical movement
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 && 
                               Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      onMoveShouldSetPanResponderCapture: (_, gestureState) => {
        // Capture vertical movements with higher priority
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 && 
                               Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      
      onPanResponderGrant: () => {
        setIsDragging(true);
        pan.flattenOffset();
        pan.extractOffset();
      },
      
      onPanResponderMove: (_, gestureState) => {
        pan.setValue({ x: gestureState.dx, y: gestureState.dy });
        
        // Calculate progress for scale and opacity
        const progress = Math.min(Math.abs(gestureState.dy) / (SCREEN_HEIGHT * 0.5), 1);
        scaleValue.setValue(1 - (0.2 * progress));
        opacityValue.setValue(1 - (0.7 * progress));
      },
      
      onPanResponderRelease: (_, gestureState) => {
        pan.flattenOffset();
        setIsDragging(false);
        
        // Determine if we should dismiss based on distance or velocity
        const shouldDismiss = 
          Math.abs(gestureState.dy) > DISMISS_THRESHOLD || 
          Math.abs(gestureState.vy) > VELOCITY_THRESHOLD;
        
        if (shouldDismiss) {
          Animated.timing(pan, {
            toValue: { 
              x: 0, 
              y: gestureState.dy > 0 ? SCREEN_HEIGHT : -SCREEN_HEIGHT 
            },
            duration: 200,
            useNativeDriver: false, // Set to false to allow opacity animation
          }).start(() => {
            onClose();
          });
        } else {
          // Animate back to original position with spring physics
          Animated.spring(pan, {
            toValue: { x: 0, y: 0 },
            tension: 40,
            friction: 7,
            useNativeDriver: false, // Set to false to allow opacity animation
          }).start();
        }
      },
      
      onPanResponderTerminate: () => {
        pan.flattenOffset();
        setIsDragging(false);
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          tension: 40,
          friction: 7,
          useNativeDriver: false, // Set to false to allow opacity animation
        }).start();
      },
    })
  ).current;
  
  // Create interpolated background color
  const animatedBackgroundStyle = {
    backgroundColor: opacityValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(0,0,0,0)', 'rgba(0,0,0,0.5)'],
    }),
  };
  
  if (!food) return null;
  
  // Parse nutritional data from the processed output if it's a report
  const nutritionalItems = food.processed_output 
    ? parseNutritionalData(food.processed_output)
    : null;
  logger.info('NUTRITIONAL ITEM',nutritionalItems)
  // Extract food name from the first line of processed output or use a default
  const foodName = food.processed_output
  ? (() => {
      const nameText = food.processed_output.split('\n')[0].split(/\d/)[0].replace(/^[\s-]+/, '').trim();
      return nameText || t('foodDetail.defaultFoodName');
    })()
  : food.name || t('foodDetail.defaultFoodName');
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      {/* Main container with gesture handling */}
      <Animated.View 
        style={[styles.modalOverlay, animatedBackgroundStyle]}
        {...panResponder.panHandlers}
      >
        {/* Main content container with animations */}
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale: scaleValue }
              ]
            }
          ]}
        >
          {food.url && (
            <View style={styles.imageContainer}>
              <Image 
                source={{ uri: food.url }} 
                style={[
                  styles.foodImage,
                  isSmallDevice && styles.foodImageSmall
                ]} 
              />
              <TouchableOpacity 
                style={styles.closeButton} 
                onPress={onClose}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X size={moderateScale(24)} color={colors.white} />
              </TouchableOpacity>
            </View>
          )}
          
          <ScrollView style={styles.detailsContainer}>
            <Text style={[
              styles.foodName,
              isSmallDevice && styles.foodNameSmall
            ]}>
              {foodName}
            </Text>
            
            <View style={styles.nutrientTable}>
              {nutritionalItems ? (
                // Display parsed nutritional data from report
                nutritionalItems.map((item, index) => (
                  <NutrientRow 
                    key={index}
                    label={item.label.replace(/^[^\w\s]+ /, '')} 
                    value={item.value} 
                    icon={item.label.match(/^[^\w\s]+/)?.[0] || ''}
                  />
                ))
              ) : (
                // Fallback to direct food properties if not a report
                <>
                  <NutrientRow 
                    label={t('foodDetail.nutrition.totalCalories')} 
                    value={`🔥 ${food.calories} kcal`} 
                    icon="🔥"
                  />
                  <NutrientRow 
                    label={t('foodDetail.nutrition.proteins')} 
                    value={`🥚 ${food.protein}g`} 
                    icon="🥚"
                  />
                  <NutrientRow 
                    label={t('foodDetail.nutrition.carbs')} 
                    value={`🍚 ${food.carbs}g`} 
                    icon="🍚"
                  />
                  <NutrientRow 
                    label={t('foodDetail.nutrition.fat')} 
                    value={`🥑 ${food.fat}g`} 
                    icon="🥑"
                  />
                  {food.sugars !== undefined && (
                    <NutrientRow 
                      label={t('foodDetail.nutrition.sugars')} 
                      value={`🍯 ${food.sugars}g`} 
                      icon="🍯"
                    />
                  )}
                  {food.fibers !== undefined && (
                    <NutrientRow 
                      label={t('foodDetail.nutrition.fibers')} 
                      value={`🌾 ${food.fibers}g`} 
                      icon="🌾"
                    />
                  )}
                </>
              )}
            </View>
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: colors.white,
    borderRadius: moderateScale(16),
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: moderateVerticalScale(12),
    right: moderateScale(12),
    padding: moderateScale(8),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: moderateScale(20),
    zIndex: 10,
  },
  foodImage: {
    width: '100%',
    height: moderateVerticalScale(200),
    resizeMode: 'cover',
  },
  foodImageSmall: {
    height: moderateVerticalScale(160),
  },
  detailsContainer: {
    padding: moderateScale(16),
  },
  foodName: {
    fontSize: moderateScale(20),
    fontWeight: '600',
    marginBottom: moderateVerticalScale(16),
    textAlign: 'center',
  },
  foodNameSmall: {
    fontSize: moderateScale(18),
    marginBottom: moderateVerticalScale(12),
  },
  nutrientTable: {
    backgroundColor: colors.gray[50],
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    marginBottom: moderateVerticalScale(16),
  },
  nutrientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: moderateVerticalScale(10),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  nutrientLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nutrientIcon: {
    marginRight: moderateScale(8),
    fontSize: moderateScale(14),
  },
  nutrientLabel: {
    fontSize: moderateScale(15),
    color: colors.gray[700],
  },
  nutrientValue: {
    fontSize: moderateScale(15),
    fontWeight: '500',
    color: colors.gray[900],
  },
});

export default FoodDetailModal;