{"common": {"error": "Hiba", "yes": "Igen", "no": "Nem", "sometimes": "Néha", "close": "Bezárás", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "save": "Men<PERSON>s", "next": "Következő", "loading": "Betöltés...", "version": "v0.0.1.7"}, "welcome": "Jelentkezz be, hogy elkezdd a beszélgetést Augusttal", "notFound": {"title": "Upsz!", "message": "Ez az oldal nem létezik.", "goHome": "Vissza a főképernyőre!"}, "library": {"title": "Egészségügyi könyvtár"}, "specialists": {"title": "Szakemberek", "description": "Forduljon speciális egészségügyi szakemberekhez konkrét egészségügyi problémáival kapcsolatban. Válasszon egy szakembert az alábbiak közül:", "generalPhysician": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "description": "Általános egészségügyi problémák és alapellátás esetén."}, "nutritionist": {"title": "Táplálkozási szakértő", "description": "<PERSON><PERSON><PERSON>, táplálkozás és testsúlykezelés tanácsadásához."}, "cardiologist": {"title": "Kardiológus", "description": "Szívproblémák és szív-érrendszeri egészség esetén."}, "neurologist": {"title": "Neurológus", "description": "<PERSON><PERSON>, g<PERSON><PERSON><PERSON>ő és idegrendszeri problémák esetén."}, "oncologist": {"title": "Onkológus", "description": "Rákbetegségekkel és kezelésükkel kapcsolatos problémák esetén."}, "endocrinologist": {"title": "Endokrinológus", "description": "Hormonális rendellenességek és cukorbetegség kezelése esetén."}, "dermatologist": {"title": "Bőrgyógyász", "description": "<PERSON><PERSON><PERSON><PERSON>, haj- <PERSON>s körömproblémák esetén."}, "psychiatrist": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Mentális egészségügyi problémák és pszichés jóllét esetén."}}, "profile": {"title": "Profil", "defaultName": "Vendég", "namePlaceholder": "<PERSON>rd be a neved", "saving": "Mentés...", "noPhoneNumber": "<PERSON>ncs telefonsz<PERSON>m", "loggingOut": "Kijelentkezés...", "about": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> meg tö<PERSON>t <PERSON>"}, "whatsapp": {"title": "WhatsApp", "description": "Be<PERSON><PERSON><PERSON><PERSON><PERSON> Augusttal WhatsAppon"}, "refer": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Szeretted Augustot? Oszd meg barátaiddal!"}, "deleteAccount": {"title": "Fiók törlése", "description": "Sajnáljuk, hogy távozol"}, "logout": {"title": "Kijelentkezés", "description": "Hamarosan gyere vissza! Hiányozni fogsz"}, "shareMessage": "👋Szia, nézd meg ezt a szuper appot, amit has<PERSON>!\n\n\n\n➡️August segítségével gyors és megbízható egészségügyi információkat és útmutatást kapok. <PERSON><PERSON><PERSON>, mintha egy orvos lenne a zsebedben! Nézd meg itt:", "error": {"loadFailed": "Sikertelen felhasználói adatok betöltése", "fetchError": "Hiba történt a felhasználói adatok betöltése közben", "updateNameFailed": "Sikertelen névfrissítés", "updateNameError": "Hiba történt a névfrissítés közben", "loadFoodData": "Sikertelen étel adatok betöltése", "logoutError": "Hiba a kijelentkezés során:", "shareError": "Hiba az üzenetek megosztása közben:"}}, "error": {"title": "<PERSON><PERSON> baj <PERSON>", "checkLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizd az eszköz naplóit további részletekért.", "unknown": "Ismeretlen hiba", "unknownFile": "Ismeretlen fájl", "unknownLine": "Ismeretlen sor", "unknownColumn": "Ismeretlen o<PERSON>lop"}, "auth": {"phone": {"selectCountry": "Ország kiválasztása", "searchCountries": "Országok keresése", "validation": {"invalidPhone": "Érvényes telefonszámot adj meg.", "invalidDigits": "Érvényes telefonszámot adj meg (7-15 számjegy)"}}, "header": {"title": "Azonnal és diszkréten kapj tisztázást az egészségügyi kérdéseidre", "subtitle": "Gondos útmutatás. Nincs kapkodás. Nincs zavar.", "emphasis": "Csak tisztázás."}, "greeting": "Szia 👋", "phoneNumber": "Telefonszám", "requestOTP": "OTP kérés", "otp": {"title": "<PERSON><PERSON> e<PERSON> j<PERSON>", "verify": "OTP ellenőrzése", "sending": "<PERSON><PERSON><PERSON><PERSON>...", "countdown": "OTP <PERSON> {{countdown}} mp múlva", "resend": "OTP újraküldése", "sentTo": "OTP elküldve: ", "whatsappSuffix": " a WhatsApp-on"}, "disclaimer": {"prefix": "A regisztrációval elfogadod a(z) ", "continuePrefix": "A folytatással elfogadod a(z) ", "termsOfService": "Szolgáltatási feltételeket", "and": " és ", "privacyPolicy": "Adatvé<PERSON><PERSON>", "whatsappConsent": ", és hozzáj<PERSON><PERSON><PERSON>, hogy frissítéseket és emlékeztetőket kapj tőlünk WhatsApp-on keresztül."}}, "onboarding": {"preAuth": {"welcome": {"title": "Üdvözlünk az Augustban!", "buttonText": "Kezdjük!"}}, "postAuth": {"step1": {"title": "Szia!", "subtitle": "Én vagyok az August 👋", "description": "Gondolj rá<PERSON>, mint a kényelmes sarokra az eszközödön, ahol felfedezheted az egészséggel kapcsolatos kérdéseidet.", "subdescription": "Nyugodtan ké<PERSON> b<PERSON><PERSON><PERSON>, ami eszedbe jut.\n<PERSON><PERSON><PERSON>, ninc<PERSON><PERSON> korlátok!", "placeholder": "Hogy szólítsalak?"}, "step2": {"title": "Szia {{userName}},", "subtitle": "<PERSON><PERSON>, mit tudok c<PERSON>álni:", "features": {"health": {"title": "Válaszolj az", "subtitle": "Egészségügyi kérdéseidre"}, "nutrition": {"title": "<PERSON><PERSON><PERSON><PERSON> nyomon a", "subtitle": "Makróidat"}, "reports": {"title": "Elemezd a", "subtitle": "Jelentéseket"}}}}, "pills": {"thoughtful": "Gondos", "careful": "<PERSON><PERSON><PERSON>", "accurate": "Pontos"}, "features": {"symptoms": {"title": "Ellenőrizd a tüneteidet", "description": "Egy hete h<PERSON> van. Mi történik velem?"}, "prescriptions": {"title": "Elemezd a recepteidet", "description": "Töltsd fel és értelmezd a recepteket, mint egy orvos."}, "medicine": {"title": "Ismerd meg a gyógyszereidet", "description": "A Metformin a PCOS-re kölcsönhatásba lép az ADHD gyógyszereimmel?"}, "plans": {"title": "Szerezz sze<PERSON><PERSON>re sza<PERSON>t terveket", "description": "<PERSON><PERSON><PERSON><PERSON> adni egy táplálkozási és fitnesz tervet a HbA1c szintem csökkentésére?"}}, "buttons": {"getStarted": "Kezdés", "next": "Következő"}, "errors": {"nameRequired": "Add meg a neved!"}}, "tabs": {"chat": "Csevegés", "discover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nutrition": "Táplálkozás", "personalize": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chat": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "me": "<PERSON><PERSON>", "augustName": "<PERSON><PERSON><PERSON><PERSON>", "input": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>...", "disclaimer": "<PERSON><PERSON><PERSON><PERSON> hib<PERSON>. Ellenőrizd le orvosoddal!"}, "list": {"loadingMessages": "Üzenetek betöltése...", "noMessages": "Még nincsenek üzenetek. Kezdj el beszélgetni!"}, "connection": {"offlineMessage": "<PERSON><PERSON> tű<PERSON>, hogy offline vagy. Csatlakozz újra az üzenetek küldéséhez.", "connecting": "Csatlakozás...", "tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON>", "speakInHindi": "Hindi nyelven beszélj", "notFeelingWell": "Rosszul érzem magam", "whatIsMyBMI": "<PERSON>ny<PERSON> a BMI-m?", "nutritionAdvice": "Táplálkozási tanácsadás", "sleepBetter": "<PERSON><PERSON>"}, "citations": {"referenceText": "A beszélgetéssel kapcsolatos további részletekért lásd:"}, "actions": {"copiedToClipboard": "Másolva a vágólapra", "copied": "M<PERSON>ol<PERSON>"}, "share": {"introText": "👋 Szia, nézd meg a beszélgetésemet <PERSON>:\n\n", "downloadText": "\n\n➡️Töltsd le <PERSON>, hogy cse<PERSON><PERSON>ess a barátságos AI egészségügyi társaddal:\n"}}, "discover": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "categories": {"all": "Összes", "heartHealth": "Szív egészség", "nutrition": "Táplálkozás", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cards": {"empty": "<PERSON><PERSON><PERSON><PERSON> kártyák ebben a kategóriában"}, "sections": {"features": "Funkciók"}, "features": {"healthLibrary": {"title": "Egészségügyi könyvtár", "description": "Hozzáférés <PERSON>, naprakész orvosi információkhoz teljesen ingyen."}, "nutritionTracker": {"title": "Táplálkozási nyomon kö<PERSON>ő", "description": "<PERSON>ah<PERSON> is azon gondolk<PERSON><PERSON>, hogy feltölthetsz egy fotót az ételeidről és nyomon követheted a táplálkozási céljaidat? Augusztus ezt meg tudja tenni!"}, "multilingualSupport": {"title": "Többnyelvű támogatás", "description": "Bármilyen nyelven kommunikál<PERSON><PERSON>, am<PERSON><PERSON> k<PERSON>yelmesen be<PERSON>élsz! <PERSON><PERSON><PERSON><PERSON> mindig itt van, ho<PERSON> hall<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> és válas<PERSON><PERSON>jon neked, am<PERSON><PERSON> s<PERSON>ü<PERSON> van rá.", "samplePrompt": "Hindi nyelven beszélj"}, "labReportAnalysis": {"title": "Laboratóriumi jelentés elemzés", "description": "Amikor Augusz<PERSON> beszélsz a laboratóriumi jelentéseidről, rendkívüli pontosságot kapsz. Augusztus több mint 4,7 millió jelentést dolgozott fel 98,4%-os biomarkerextrakciós pontossággal."}}}, "nutrition": {"nav": {"title": "Táplálkozás"}, "meals": {"title": "Az étkezéseid", "subtitle": "Érintsd meg, hogy megnézd az egyes étkezések makróit"}, "upload": {"loading": "<PERSON><PERSON><PERSON>..."}, "defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Ma", "unknownTime": "Ismeretlen id<PERSON>", "calories": "🔥 Kalória", "proteins": "🥩 <PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "🍞 <PERSON>z<PERSON>hidr<PERSON>", "sugars": "🍬 <PERSON><PERSON><PERSON>", "fat": "🥑 Zs<PERSON><PERSON>", "caloriesLabel": "Kalória", "proteinLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "Zsír", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Tippek:", "macroBreakdown": "Makrotápanyag bontás", "noMacroData": "Nincsenek makrotápanyag adatok ehhez az élelmiszerhez.", "disclaimer": "Csak tájékoztató jellegű. További információ", "disclaimerLink": "itt", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Mi a nemed?", "subtitle": "Ez a személyre sza<PERSON>t terv kalibrálásához lesz felhasználva.", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Nő", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "age": {"title": "<PERSON><PERSON><PERSON> vagy?", "subtitle": "Ez a napi szükségleted kiszámításához lesz felhasználva."}, "measurements": {"title": "Magasság és súly", "subtitle": "Add meg a magasságodat centiméterben és a súlyodat kilogrammal."}, "activity": {"title": "Aktivitási szint", "subtitle": "<PERSON><PERSON><PERSON> gyakran edzel?", "none": "<PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "Magas"}, "goal": {"title": "Súlycél", "subtitle": "<PERSON>t s<PERSON><PERSON>é<PERSON>?", "increase": "Növelés", "maintain": "Fenntartás", "decrease": "Csökkentés"}, "targetWeight": {"title": "Célsúly", "subtitle": "Mennyi a célsúlyod kilogrammal?"}, "setup": {"title": "A terv beállítása", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, amíg elkészítjük a táplálkozási tervedet."}, "review": {"title": "A terv á<PERSON>se", "subtitle": "Tekintsd át és személyre szabd a táplálkozási tervedet."}, "height": {"label": "Magasság (cm)"}, "weight": {"label": "Súly (kg)"}}, "error": {"updateFailed": "A táplálkozási adatok frissítése sikertelen. K<PERSON><PERSON>j<PERSON>k, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "parsingError": "Hiba az élelmiszer adatok feldolgozása közben:", "fetchReportsFailed": "A jelentések adatainak lekérése sikertelen. K<PERSON>rj<PERSON>k, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "missingReportId": "A jelentés azonosítója hiányzik"}}, "personalize": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "button": {"saving": "Men<PERSON>s", "review": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saveNext": "Mentés és tovább"}}, "basicInfo": {"title": "Ismerjük meg egy kicsit jobban!", "subtitle": "Ez az információ segít nekünk személyre szabni az egészségügyi ajánlásainkat", "age": {"question": "<PERSON><PERSON><PERSON> vagy?", "placeholder": "Add meg az életkorodat"}, "sex": {"question": "Mi a nemed?", "placeholder": "Válaszd ki a nemedet", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Nő", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "height": {"question": "<PERSON>ny<PERSON> a magasságod? (cm)", "placeholder": "Add meg a magasságodat"}, "weight": {"question": "<PERSON>ny<PERSON> a súlyod? (kg)", "placeholder": "Add meg a súlyodat"}}, "lifestyle": {"title": "Az Ön életmódbeli szokásai", "subtitle": "A mindennapi szokásainak megértése segít nekünk jobb ajánlások nyújtásában", "diet": {"question": "<PERSON><PERSON><PERSON> t<PERSON>?", "placeholder": "Válassza ki az étrendjét", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonVegetarian": "<PERSON><PERSON>", "vegan": "Vegán", "pescatarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Rendszeresen sportol?"}, "drinking": {"question": "Fogyaszt alkoholt?"}, "smoking": {"question": "Dohányzik?"}, "sleep": {"question": "<PERSON><PERSON>y órát alszik éjszakánként?", "value": "{{sleep}} <PERSON>ra"}, "hydration": {"question": "<PERSON><PERSON><PERSON> poh<PERSON>r vizet iszik nap<PERSON>?", "value": "{{hydration}} p<PERSON><PERSON><PERSON> ({{liters}}L)"}}, "allergies": {"title": "Vannak-e allergiái?", "subtitle": "Az allergiái ismerete segít nekünk biztonságosabb ajánlások nyújtásában", "allergyIndex": "Allergia {{index}}", "name": {"question": "Mire allergiás?", "placeholder": "Adja meg az allergiát (pl.: <PERSON><PERSON><PERSON><PERSON>, Por)"}, "severity": {"question": "<PERSON><PERSON><PERSON> s<PERSON> ez az allergia?", "placeholder": "Válassza ki a súlyosságot", "mild": "<PERSON><PERSON><PERSON>", "moderate": "Közepes", "severe": "<PERSON><PERSON><PERSON><PERSON>"}, "addButton": "Allergia hozzáadása", "noAllergiesButton": "<PERSON><PERSON><PERSON>ek allergiám"}, "medications": {"title": "Gyógyszerek és táplálékkiegészítők", "subtitle": "Mon<PERSON><PERSON> el nekünk, milyen gyógyszereket vagy táplálékkiegészítőket szed jelenleg", "medicationIndex": "Gyógyszer {{index}}", "name": {"label": "Gyógyszer neve", "placeholder": "Adja meg a gyógyszer nevét"}, "startDate": {"question": "<PERSON><PERSON> kezdte el szedni?", "placeholder": "Válassza ki a dátumot"}, "type": {"label": "Gyógyszer típusa", "shortTerm": "<PERSON><PERSON><PERSON>", "longTerm": "Hosszú távú"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Mennyiség"}, "unit": {"label": "Mértékegység"}, "frequency": {"label": "Szedési gyakoriság", "placeholder": "Szor", "perDay": "naponta", "perWeek": "hetente", "perMonth": "<PERSON><PERSON><PERSON>", "perYear": "évente"}, "units": {"mg": "mg", "ml": "ml", "iu": "NE", "puffs": "f<PERSON>j<PERSON>", "drops": "csepp", "tsp": "tk", "tbsp": "evk", "cups": "<PERSON><PERSON><PERSON><PERSON>"}, "addButton": "Gyógyszer hozzáadása", "noMedicationsButton": "<PERSON><PERSON> s<PERSON> gyógyszert", "calendar": {"title": "Válassza ki a kezdő dátumot"}}, "conditions": {"title": "Egészségügyi állapotok", "subtitle": "<PERSON><PERSON><PERSON>ü<PERSON>, mi<PERSON><PERSON> egészségügyi állapotai vannak vagy voltak a múltban", "conditionIndex": "Állapot {{index}}", "name": {"label": "<PERSON><PERSON><PERSON> neve", "placeholder": "<PERSON>ja meg az <PERSON> (pl.: Asztma, stb.)"}, "since": {"question": "<PERSON><PERSON> j<PERSON> ez az állapot?", "placeholder": "Válassza ki a dátumot"}, "current": {"question": "<PERSON><PERSON><PERSON> is panaszo<PERSON> okoz?"}, "medicated": {"question": "Szed rá gyógyszert?"}, "addButton": "<PERSON><PERSON><PERSON>", "noConditionsButton": "Nincsenek egészségügyi állapotaim", "calendar": {"title": "Válassza ki a dátumot"}}, "reproductive": {"title": "Reproduktív Egészség", "subtitle": "Ez az információ segít nekünk személyre s<PERSON>bb egészségügyi ajánlások nyújtásában", "menstruation": {"question": "Volt már menstruációja?", "detailsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "regularity": {"question": "<PERSON><PERSON><PERSON> rends<PERSON>es a ciklusa?", "regular": "Rendszeres", "irregular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notSure": "<PERSON><PERSON> tudom"}, "cycleLength": {"label": "Átl<PERSON><PERSON> (napok)", "placeholder": "<PERSON><PERSON><PERSON> be a ciklus ho<PERSON>t"}, "flowDays": {"label": "Vérzéses napok: {{flowDays}}", "min": "1 nap", "max": "15 nap"}, "padsPerDay": {"label": "Betét/tampon naponta: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON> tünet j<PERSON>ntkezik a menstruációja során?", "placeholder": "<PERSON><PERSON><PERSON> be a tüneteket (pl.: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fej<PERSON><PERSON><PERSON><PERSON>)"}}, "childbirth": {"question": "<PERSON><PERSON><PERSON><PERSON> már g<PERSON>?", "detailsTitle": "Szülészeti Részletek", "children": {"label": "Gyermekek száma"}, "pregnancies": {"label": "Terhességek száma"}, "complications": {"question": "B<PERSON>rmilyen szövődmény történt a terhesség vagy a szülés során?", "placeholder": "<PERSON><PERSON><PERSON> be a szövődményeket (ha vannak)"}}}, "review": {"title": "Információi <PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tekintse át a megadott információkat a beküldés előtt", "sections": {"basicInfo": "Alapinformációk", "lifestyle": "Életmód", "allergies": "Allergi<PERSON>", "medications": "Gyógyszerek és táplálékkiegészítők", "conditions": "Egészségügyi állapotok", "reproductive": "Reproduktív Egészség", "menstruationDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childbirthDetails": "Szülészeti Részletek"}, "fields": {"age": "Kor:", "sex": "Nem:", "height": "Magasság:", "weight": "Súly:", "diet": "Étrend:", "exercise": "Testmozgás:", "drinking": "Alkohol:", "smoking": "Dohányzás:", "sleep": "Alvás:", "hydration": "Hidratáció:", "allergyIndex": "{{index}}. allergia:", "dose": "Adag:", "frequency": "Gyakoriság:", "type": "Típus:", "since": "Miertől:", "currentlyActive": "Jelenleg aktív:", "takingMedication": "Szed gyógyszert:", "hasMenstruated": "Volt már <PERSON><PERSON><PERSON>:", "regularity": "Rendszeresség:", "cycleLength": "Ciklushossz:", "flowDays": "Vérzéses napok:", "padsPerDay": "Betét/tampon naponta:", "hasChildbirth": "<PERSON><PERSON><PERSON>lt már g<PERSON>:", "children": "Gyermekek:", "pregnancies": "Terhességek:"}, "notProvided": "<PERSON><PERSON><PERSON> megadva", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} <PERSON><PERSON>a", "hydration": "{{hydration}} p<PERSON><PERSON><PERSON> ({{liters}}L) naponta", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} nap"}, "noData": {"allergies": "Nincsenek megadva allergiák", "medications": "Nincsenek megadva gyógyszerek", "conditions": "Nincsenek megadva egészségügyi állapotok"}, "submitButton": "Információk beküldése"}, "success": {"title": "Információk frissítve!", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy megadta az egészségügyi információit. Ezeket felhasználjuk a személyre szabott élmény és a jobb ajánlások nyújtásához.", "benefits": {"insights": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> egészségügyi információk", "reminders": "Jobb gyógyszeremlékeztetők", "recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> egészségügyi ajánlások"}, "continueButton": "Folytatás a kezdőképernyőhöz"}, "permissions": {"microphonePermissionDenied": "A mikrofon engedélye elutasítva", "microphoneAccessDescription": "Az August alkalmazásnak hozzá kell férnie a mikrofonhoz a hangfelvételek rögzítéséhez és a hangüzenetek küldéséhez", "permissionDenied": "<PERSON><PERSON><PERSON><PERSON>", "cameraPermissionRequired": "Ehhez a kamerához való hozzáférésre van szükség!", "mediaLibraryPermissionRequired": "Ehhez a média könyvtárhoz való hozzáférésre van szükség!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON> ho<PERSON> felv<PERSON>tel", "recordingTooLongMessage": "A hangfelvételeknek 5 percnél rövidebbeknek kell lenniük. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vegyen fel egy rövidebb üzenetet."}, "errors": {"uploadFailed": "Feltöltés sikertelen", "voiceUploadFailed": "A hangfelvétel feltöltése sikertelen.", "voiceRecordingFailed": "A hangfelvétel elküldése sikertelen", "failedToStopRecording": "A felvétel leállítása sikertelen", "photoUploadFailed": "A fotó feltöltése sikertelen.", "failedToTakePhoto": "A fotó készítése sikertelen", "imageUploadFailed": "A kép feltöltése sikertelen: {{fileName}}", "failedToPickImage": "<PERSON><PERSON><PERSON> k<PERSON>lasztása si<PERSON>en", "documentUploadFailed": "A dokumentum feltöltése sikertelen: {{fileName}}", "failedToPickDocument": "Dokumentum kiválasztása sikertelen"}, "audioPlayer": {"downloadingAudio": "Hang letöltése...", "loadingAudio": "Hang betöltése..."}, "mediaProcessing": {"processingFile": "Fájl feldolgozása", "uploadingSecuring": "Fájl feltöltése és biztosítása...", "analyzingContent": "Dokumentum tartalmának elemzése...", "extractingInfo": "Fontos információk kinyerése...", "processingInsights": "Észlelések feldolgozása...", "preparingResponse": "Részletes válasz előkészítése...", "finalizingResponse": "Válasz véglegesítése..."}, "attachments": {"voiceMessage": "Hangüzenet", "image": "[KÉP]", "pdf": "[PDF]", "voice": "[HANGÜZENET]"}, "pdf": {"loadingPdf": "PDF betöltése..."}, "dateTime": {"yesterday": "Tegnap, "}, "navbar": {"defaultTitle": "august", "selectedCount": "kiválasztva"}, "mediaUpload": {"photoLibrary": "Fotókönyvtár", "takePhoto": "<PERSON>ot<PERSON>", "chooseFile": "Fájl kiválasztása"}, "comingSoon": {"title": "Hamarosan!", "description": " jelenleg fejlesztés alatt áll. Kövesse nyomon a frissítéseket!", "buttonText": "Rendben!"}, "clipboard": {"success": "Link a vágólapra másolva"}, "mediaPhotos": {"emptyState": "Még nincs bejegyz<PERSON>."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nutrition": {"totalCalories": "Összes kalória", "proteins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "fat": "Zsír", "sugars": "<PERSON><PERSON><PERSON>", "fibers": "Rostok"}}, "reports": {"defaultTitle": "Médiaelem", "defaultFoodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultName": "Dokumentum", "openButton": "Megnyitás külső nézegetőben", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "<PERSON><PERSON><PERSON><PERSON>", "headerRefRange": "Referenciatartomány", "headerStatus": "<PERSON><PERSON><PERSON>"}, "noData": "<PERSON>ncs elérhető biomarker adat"}, "setup": {"title": "Beállítjuk az egészet az Ön számára", "inProgress": "Folyamatban...", "progressMessages": {"0": "<PERSON><PERSON> ka<PERSON><PERSON><PERSON> kiszámítása", "1": "<PERSON><PERSON><PERSON><PERSON>", "2": "Étrendterv létrehozása", "3": "Egészségügyi pontszám kiszámítása", "4": "Beállítás véglegesítése"}, "checklistItems": {"0": "Egészségügyi adatainak elemzése", "1": "Optimális táplálkozási terv kiszámítása", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> szabott ajánlások létrehozása", "3": "Ételjavaslatok létrehozása", "4": "Beállítás véglegesítése"}}, "foodEntry": {"emptyState": "Még nincsenek ételbejegyzések. Készíts képet az étkezésedről, hogy hozzáadd!"}, "nutritionReview": {"congratulations": "Gratulálok!", "subtitle": "Elkészült az egyéni táplálkozási terve.", "submitButton": "Kezdjük!", "dailyTargetsTitle": "<PERSON><PERSON> t<PERSON>lkozási célok", "macroLabels": {"calories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "Z<PERSON>írok"}, "recommendations": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>élo<PERSON>:", "healthScores": "Egészségügyi pontszámok használata a rutinod javítására", "trackFood": "Kövesd folyamatosan az ételfogyasztásodat.", "followCalories": "Kövesd a napi kalória ajánlásodat!", "balanceMacros": "Egyensúlyoz<PERSON>, feh<PERSON><PERSON>je és zsírbeviteled!"}}, "editModal": {"titlePrefix": "Szerkesztés", "cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "saveButton": "Következő"}, "processing": {"stages": {"scanning": "Élelmiszer szkennelése...", "identifying": "Összetevők azonosítása...", "extracting": "Tápanyagok kinyerése...", "finalizing": "Eredmények véglegesítése..."}, "error": {"defaultMessage": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitle": "Próbálj más szögből."}, "retakeButton": "Érintsd meg a kép újrafelvételéhez", "notification": "Készülve értesítünk!"}, "chart": {"title": "Tápanyagkövetés idővel", "selectNutrient": "Tápanyag kiválasztása:", "emptyState": "Még nincsenek elérhető tápérték adatok.", "dropdown": {"calories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "fat": "Zsír", "sugars": "<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultDate": "Ma", "defaultTime": "Ismeretlen idő", "saveChanges": "Men<PERSON>s", "error": {"title": "Hiba", "message": "Sikertelen volt a tápérték adatok frissítése. K<PERSON><PERSON>j<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON><PERSON>!"}, "nutrition": {"calories": "🔥 <PERSON><PERSON><PERSON><PERSON>", "proteins": "🥩 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "🍞 sz<PERSON>hidrátok", "sugars": "🍬 <PERSON><PERSON><PERSON>", "fat": "🥑 Zs<PERSON><PERSON>"}, "macroBreakdown": {"title": "Makrotápanyag-összetétel", "noData": "Nincsenek elérhető makrotápanyag-adatok ehhez az élelmiszerhez."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "fat": "Zsír", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Részletes információ", "edit": "Szerkesztés", "save": "Men<PERSON>s", "saving": "Mentés...", "enterValue": "Érték megadása", "notSet": "<PERSON><PERSON><PERSON>", "age": "<PERSON><PERSON>", "heightCm": "Magasság (cm)", "weightKg": "Súly (kg)", "targetWeight": "Céltestsúly", "nutritionTargets": "Tápanyagcélok", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "fats": "Z<PERSON>írok", "gm": "gm", "editNote": "<PERSON><PERSON><PERSON> be értékeket, vagy hagyja üresen az automatikus számításhoz.", "autoCalculateNote": "A makrók adataid alapján automatikusan számolódnak.", "validation": {"ageMin": "Legalább 18 évesnek kell lenni.", "ageMax": "125 évn<PERSON><PERSON> fiatalabbnak kell lenni", "heightMin": "A magasságnak legalább 50 cm-nek kell lennie.", "heightMax": "A magasságnak 250 cm alatt kell lennie.", "weightMin": "A súlynak legalább 30 kg-nak kell lennie.", "weightMax": "A súlynak 500 kg alatt kell lennie.", "targetWeightMin": "A cél s<PERSON><PERSON>k legalább 30 kg-nak kell lennie.", "targetWeightMax": "A cél s<PERSON>k 500 kg alatt kell lennie.", "proteinMin": "A fehérje mennyisége 0 vagy több kell legyen.", "carbsMin": "A szénhidrát mennyisége 0 vagy több kell, hogy legyen.", "fatsMin": "A zsíroknak 0 vagy többnek kell lenniük."}}, "tracker": {"calories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carbs": "Szénhidrátok", "fat": "Zsír", "excess": "<PERSON><PERSON><PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>"}, "specialistConstants": {"nutritionist": {"name": "Táplálkozási szakértő", "description": "Szakértői taná<PERSON>adás étrenddel, táplálkozással és egészséges étkezési szokásokkal kapcsolatban", "featureName": "Táplálkozási szakértő"}, "cardiologist": {"name": "Kardiológus", "description": "Szív egészségére és keringési betegségekre szakosodott", "featureName": "Kardiológiai szakértő"}, "neurologist": {"name": "Neurológus", "description": "<PERSON>z agy, a gerincvelő és az idegrendszeri betegségekre koncentrál", "featureName": "Neurológiai s<PERSON>ő"}, "oncologist": {"name": "Onkológus", "description": "Rák diagnosztizálására és kezelési lehetőségeire szakosodott", "featureName": "Onkológiai szakértő"}, "endocrinologist": {"name": "Endokrinológus", "description": "Hormonális betegségekben és anyagcserezavarokban já<PERSON>", "featureName": "Endokrinológiai szakértő"}}, "discoverCards": {"categories": {"nutrition": "Táplálkozás", "heartHealth": "Szív egészség", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "Fitnessz", "wellness": "Wellness"}, "titles": {"vitaminB12Recovery": "<PERSON><PERSON><PERSON> idő alatt lehet felépülni a B12-vitamin-hiányból?", "vitaminDeficiencyGanglion": "<PERSON><PERSON><PERSON> ok<PERSON>?", "vitaminDeficiencyHairFall": "Melyik <PERSON> ok<PERSON>?", "vitaminWaters": "<PERSON><PERSON> neked a vitaminvíz?", "cholesterolHeadaches": "Magas koleszterinszint okozhat fejfájást?", "cholesterolEyes": "<PERSON><PERSON><PERSON> s<PERSON> l<PERSON>tó tünetei vannak a magas koleszterinszintnek?", "diabetesHeadaches": "Vajon a cukorbetegség okozhat fejfájást?", "chestPainDrinking": "<PERSON><PERSON><PERSON> fáj a mellkas ivás után?", "stressDizziness": "Stress okozhat s<PERSON>dülést?", "bulimiaFace": "Mi az a bulimia arc?", "kneeTwitch": "<PERSON><PERSON><PERSON> rándul a térdem?", "noseTwitching": "<PERSON><PERSON><PERSON> r<PERSON> az orr?", "piriformisVsSciatica": "<PERSON><PERSON><PERSON>nbségek vannak a piriformis szindróma és az isiász között?", "shoulderBladePinched": "Hogyan szabadítsd fel a csimpáncsízületi ideget", "shoulderPinched": "<PERSON>gyan szabadítsd fel a becsípődött ideget a válladban", "meniscusTorn": "Hogyan gyógyítsunk sérült meniszkuszt természetesen", "hydrateQuickly": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "periodConstipation": "Normális a székrekedés menstruáció alatt?", "acneScars": "<PERSON>gyan szabaduljunk meg az akné hegeitől természetesen egy héten belül", "perimenopausePregnancy": "Terhes lehet a perimenopauza alatt?"}, "descriptions": {"vitaminB12Recovery": "Fedezd fel a B12-vitamin hiány kezelési idejét és a hatékony gyógymódokat az energiaszinted növelésére!", "vitaminDeficiencyGanglion": "Fedezd fel a vitaminhiány és a ganglion ciszták kialakulása közötti kapcsolatot a szervezetben!", "vitaminDeficiencyHairFall": "Tudj meg tö<PERSON>t a<PERSON>, hogyan vezethet a nélkülözhetetlen vitaminok hiánya haj<PERSON>, és mit tehetsz a megelőzéséért!", "vitaminWaters": "Ismerd meg a vitaminvizek előnyeit és lehetséges hátrányait a napi táplálkozásod részeként!", "cholesterolHeadaches": "Vizsgáld meg a magas koleszterinszint és a fejfájás kialakulása közötti lehetséges kapcsolatot!", "cholesterolEyes": "<PERSON><PERSON> meg, ho<PERSON>an j<PERSON> a magas koleszterinszint a szemed<PERSON>, és milyen tüneteket kell figyelned!", "diabetesHeadaches": "Vizsgáld meg a cukorbetegség és a mindennapi életben jelentkező fejfájás kö<PERSON> kapcsolatot!", "chestPainDrinking": "Fedezd fel az okokat, amelyek bizonyos italok fogyasztása után mellkasi fájdalmat okoz<PERSON>nak!", "stressDizziness": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON>an befo<PERSON>ásolhatja a <PERSON>z az egyensúlyodat és az általános jó<PERSON>tedet, szédüléshez vezetve!", "bulimiaFace": "Értsd meg a bulimia fizikai jeleit, beleértve az arc megjelenésére gyakorolt hatásokat is!", "kneeTwitch": "Vizsgáld meg a térd akaratlan rángásának lehetséges okait, és annak kap<PERSON>olat<PERSON>t a <PERSON>zel vagy a fáradtsággal!", "noseTwitching": "Tudj meg többet az orr rángásának lehetséges okairól és kapcsolatáról a szorongással vagy más tényezőkkel!", "piriformisVsSciatica": "Hasonlítsd össze a piriformis szindróma és az isiász tüneteit, hogy jobban megértsd az állapotodat!", "shoulderBladePinched": "Fedezz fel hatékony technikákat a lapockádba szorult ideg enyhítésére és a mozgás helyreállítására!", "shoulderPinched": "Tanulj meg egyszerű gyakorlatokat és nyújtásokat a váll területén lévő idegösszenyomódás enyhítésére!", "meniscusTorn": "Fedezz fel természetes módszereket és gyakorlatokat a sérült meniscus gyógyulását támogatóan!", "hydrateQuickly": "Ta<PERSON><PERSON>ld meg a gyors és hatékony módszereket az újrahidratálásra és az optimális testnedvesség fenntartására!", "periodConstipation": "Értsd meg a menstruáció alatti székrekedés okait, és ismerd meg a természetes gyógymódokat!", "acneScars": "Fedezz fel természetes gyógymódokat és bőrápolási tippeket az aknés hegek megjelenésének gyors csökkentésére!", "perimenopausePregnancy": "Tudj meg többet a perimenopauza, a termékenységi szempontok és az élet ezen szakaszában várható dolgokról!"}}}