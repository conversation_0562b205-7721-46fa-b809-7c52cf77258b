import { useState, useEffect, useCallback } from "react";
import { Tabs } from "expo-router";
import { MessageSquare, Compass, Utensils, User } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { StatusBar } from "expo-status-bar";
import { Platform, View } from "react-native";
import * as Haptics from "expo-haptics";
import { trackClarityEvent } from "@/utils/clarity/clarity-utils";
import { tabPressHandler } from "@/utils/navigation/tabPressHandler";
import { moderateScale, moderateVerticalScale} from "react-native-size-matters";
import { softHaptic } from "@/utils/haptics/haptics";
import { useTranslation } from "react-i18next";
const TAB_BAR_HEIGHT = Platform.OS == "ios" ? 70 : 64;
export default function TabLayout() {
  const { t } = useTranslation();
  const [tabBarStyle, setTabBarStyle] = useState({
    height: TAB_BAR_HEIGHT,
    paddingTop: Platform.OS === 'ios' ? moderateVerticalScale(4) : moderateVerticalScale(4),
    borderTopWidth: 0,
   // marginBottom: moderateVerticalScale(4)
  });
  // Optimized tab press handler
  const handleTabPress = useCallback((e: any) => {
    // Provide haptic feedback
     if(Platform.OS === 'ios'){
                     Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                   }else {
                   softHaptic()
                   }
    // Get the tab name from the event
    const tabName = e.target?.split("/").pop();
    if (tabName) {
      const processedTabName = tabName.split("-")[0].trim();
      tabPressHandler(processedTabName);
      // Track tab navigation in Clarity
      trackClarityEvent("Tab_Navigation", {
        tabName: tabName,
        previousTab: global.currentScreen,
        timestamp: new Date().toISOString(),
      });
    }
  }, []);
  return (
    <View style={{ flex: 1 }}>
      <StatusBar style="dark" backgroundColor={colors.white} />
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: colors.primary,
          tabBarInactiveTintColor: colors.gray[400],
          tabBarStyle: tabBarStyle,
          lazy: false,
          freezeOnBlur: false,
          tabBarLabelStyle: {
            fontSize: 12,
          },
          headerShown: false,
        }}
        screenListeners={{
          tabPress: handleTabPress,
        }}
      >
        <Tabs.Screen
          name="chat"
          options={{
            title: t('tabs.chat'),
            tabBarIcon: ({ color, size }) => (
              <MessageSquare size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="discover"
          options={{
            title: t('tabs.discover'),
            tabBarIcon: ({ color, size }) => (
              <Compass size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="nutrition"
          options={{
            title: t('tabs.nutrition'),
            tabBarIcon: ({ color, size }) => (
              <Utensils size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="personalize"
          options={{
            title: t('tabs.personalize'),
            tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
          }}
        />
      </Tabs>
    </View>
  );
}
