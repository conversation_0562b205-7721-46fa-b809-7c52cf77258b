import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Animated,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  SafeAreaView,
  Image
} from 'react-native';
import {
  Svg,
  Path,
  Rect,
  G,
  <PERSON>lip<PERSON><PERSON>,
  Defs,
  SvgUri,
  Mask,
} from "react-native-svg";
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { colors } from "@/constants/colors";
import { useAuthStore } from "@/store/auth-store";
import {
  updateUserMetadata,
} from "@/services/userService";
import { getUserData, updateUserData } from "@/services/userService";
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import logger from "@/utils/logger/logger";
import { useTranslation } from 'react-i18next';
import { AppEventsLogger } from 'react-native-fbsdk-next';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const trackOnboardingStep = (
  stepNumber: number,
  stepName: string,
  additionalProps?: any
) => {
  trackUserInteraction("Onboarding Step Viewed", {
    step_number: stepNumber,
    step_name: stepName,
    ...additionalProps,
  });
};

const trackOnboardingAction = (action: string, additionalProps?: any) => {
  trackUserInteraction("Onboarding Action", {
    action_type: action,
    ...additionalProps,
  });
};

const PostAuthOnboardingWrapper = () => {
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        setIsLoading(true);
      const { user } = useAuthStore.getState();
      const checkMetaData = await getUserData();
      if (user?.isMobileOnboardingDone && checkMetaData?.user?.meta?.isMobileOnboardingDone === true
      ) {
        router.replace("/(tabs)/chat");
        }
      } catch (error) {
        // Prevent navigation on error, just log it
        logger.error("Error checking onboarding status:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkOnboardingStatus();
  }, []);

  if (isLoading) {
    return null;
  }
  
  return <OnboardingScreen />;
};

// Step 1 Component
//@ts-ignore
const FirstPageStep = ({ userName, setUserName, handleNext, fadeAnim1, fadeAnim2, fadeAnim3, translateY1, translateY2, translateY3 
}) => {
  const { t } = useTranslation();
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.innerContainer}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.firstPageScrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Illustration */}
          <View style={styles.illustrationContainer}>
            <View style={styles.illustration}>
              <SvgUri
                uri="https://augustbuckets.blob.core.windows.net/mobile-app-assets/Onboarding/medical-advice.svg"
                style={styles.illustrationImage}
                width="100%"
                height="100%"
              />
            </View>
          </View>

          {/* Main content */}
          <View style={styles.contentContainer}>
            <Text style={styles.greeting}>{t('onboarding.postAuth.step1.title')}</Text>
            <Text style={styles.title}>{t('onboarding.postAuth.step1.subtitle')}</Text>

            <Text style={styles.description}>
              {t('onboarding.postAuth.step1.description')}
            </Text>

            <Text style={styles.subDescription}>
              {t('onboarding.postAuth.step1.subdescription')}
            </Text>

            {/* Animated pills */}
            <View style={styles.pillsContainer}>
              <Animated.View
                style={[
                  styles.pill,
                  {
                    opacity: fadeAnim1,
                    transform: [{ translateY: translateY1 }]
                  }
                ]}
              >
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.pillText}>{t('onboarding.postAuth.pills.thoughtful')}</Text>
              </Animated.View>

              <Animated.View
                style={[
                  styles.pill,
                  {
                    opacity: fadeAnim2,
                    transform: [{ translateY: translateY2 }]
                  }
                ]}
              >
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.pillText}>{t('onboarding.postAuth.pills.careful')}</Text>
              </Animated.View>

              <Animated.View
                style={[
                  styles.pill,
                  {
                    opacity: fadeAnim3,
                    transform: [{ translateY: translateY3 }]
                  }
                ]}
              >
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.pillText}>{t('onboarding.postAuth.pills.accurate')}</Text>
              </Animated.View>
            </View>
          </View>
        </ScrollView>

        {/* Input section wrapped in KeyboardAvoidingView */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 25 : 20}
        >
          <View style={styles.inputSection}>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder={t('onboarding.postAuth.step1.placeholder')}
                placeholderTextColor="#999"
                value={userName}
                onChangeText={setUserName}
                onSubmitEditing={handleNext}
                returnKeyType="send"
              />
              <TouchableOpacity
                onPress={handleNext}
                disabled={!userName.trim()}
                style={[styles.sendButton, !userName.trim() && styles.sendButtonDisabled]}
              >
                <Svg width={35} height={40} viewBox="0 0 40 40" fill="none">
                  <Rect
                    width={40}
                    height={40}
                    rx={20}
                    fill={userName.trim() ? "#0A432B" : "#D9D9D9"}
                  />
                  <G clipPath="url(#clip0_1_8406)">
                    <Path
                      d="M28.3334 11.6666L19.1667 20.8333M28.3334 11.6666L22.5001 28.3333L19.1667 20.8333M28.3334 11.6666L11.6667 17.5L19.1667 20.8333"
                      stroke={userName.trim() ? "#FFFFFF" : "#545454"}
                      strokeWidth="1.69384"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </G>
                  <Defs>
                    <ClipPath id="clip0_1_8406">
                      <Rect
                        width={20}
                        height={20}
                        fill="white"
                        transform="translate(10 10)"
                      />
                    </ClipPath>
                  </Defs>
                </Svg>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </View>
    </SafeAreaView>
  );
};

// Step 2 Component
//@ts-ignore
const SecondPageStep = ({ userName, handleBack, handleGetStarted, card1Anim, card2Anim, card3Anim, card4Anim, cardTranslateY1, cardTranslateY2, cardTranslateY3, cardTranslateY4 
}) => {
  const { t } = useTranslation();
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.secondPageContainer}>
        <Text style={styles.pageTitle}>{t('onboarding.postAuth.step2.title', { userName: userName || 'there' })}</Text>
        <Text style={styles.pageSubtitle}>{t('onboarding.postAuth.step2.subtitle')}</Text>

        {/* Animated Cards */}
        <Animated.View style={[styles.card, {
              opacity: card1Anim,
              transform: [{ translateY: cardTranslateY1 }]
            }]}>
          <View style={styles.cardTitleRow}>
            <Text style={styles.cardIcon}>✨</Text>
            <Text style={[styles.cardTitle, { color: '#1E88E5' }]}>{t('onboarding.postAuth.features.symptoms.title')}</Text>
          </View>
          <Text style={styles.cardDescription}>
            {t('onboarding.postAuth.features.symptoms.description')}
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.card,
            {
              opacity: card2Anim,
              transform: [{ translateY: cardTranslateY2 }]
            }
          ]}
        >
          <View style={styles.cardTitleRow}>
            <Text style={styles.cardIcon}>🌿</Text>
            <Text style={[styles.cardTitle, { color: '#43A047' }]}>{t('onboarding.postAuth.features.prescriptions.title')}</Text>
          </View>
          <Text style={styles.cardDescription}>
              {t('onboarding.postAuth.features.prescriptions.description')}
            </Text>
            <View style={styles.prescriptionPreview}>
              <Image
                source={require("@/assets/images/sample-prescription.png")}
                style={{
                  width: '100%',
                  height: verticalScale(100),
                  borderRadius: 8,
                  resizeMode: 'cover',
                }}

              />
            </View>
        </Animated.View>

        <Animated.View style={[styles.card, {
              opacity: card3Anim,
              transform: [{ translateY: cardTranslateY3 }]
            }]}>
          <View style={styles.cardTitleRow}>
            <Text style={styles.cardIcon}>⭐</Text>
            <Text style={[styles.cardTitle, { color: '#F9A825' }]}>{t('onboarding.postAuth.features.medicine.title')}</Text>
          </View>
          <Text style={styles.cardDescription}>
          {t('onboarding.postAuth.features.medicine.description')}
          </Text>
        </Animated.View>

        <Animated.View style={[
            styles.card,
            {
              opacity: card4Anim,
              transform: [{ translateY: cardTranslateY4 }]
            }
          ]}>
          <View style={styles.cardTitleRow}>
            <Text style={styles.cardIcon}>❤️</Text>
            <Text style={[styles.cardTitle, { color: '#E53935' }]}>{t('onboarding.postAuth.features.plans.title')}</Text>
          </View>
          <Text style={styles.cardDescription}>
          {t('onboarding.postAuth.features.plans.description')}
          </Text>
        </Animated.View>
      </View>

      {/* Navigation buttons */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <MaterialIcons name="arrow-back" size={24} color="#545454" />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleGetStarted} style={styles.getStartedButton}>
          <Text style={styles.getStartedText}>{t('onboarding.postAuth.buttons.getStarted')}</Text>
          <MaterialIcons name="arrow-forward" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const OnboardingScreen = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const [userName, setUserName] = useState('');
  const { t } = useTranslation();
  const startTime = useRef(Date.now()).current;
  const [currentStep, setCurrentStep] = useState(0);
  const [inputError, setInputError] = useState(false);
  const [resetFeatureAnimations, setResetFeatureAnimations] = useState(0);
  useEffect(() => {
    async function fetchUser() {
      const username = await getUserData();
      if (username?.user?.name.length > 0) {
        setCurrentStep(1);
        setUserName(username?.user?.name);
      } else {
        setCurrentStep(0);
        setUserName("");
      }
    }
    fetchUser();
  }, []);
  const { getAccessToken } = useAuthStore();

  // Animation values for first page
  const fadeAnim1 = useRef(new Animated.Value(0)).current;
  const fadeAnim2 = useRef(new Animated.Value(0)).current;
  const fadeAnim3 = useRef(new Animated.Value(0)).current;
  const translateY1 = useRef(new Animated.Value(50)).current;
  const translateY2 = useRef(new Animated.Value(50)).current;
  const translateY3 = useRef(new Animated.Value(50)).current;

  // Animation values for second page
  const card1Anim = useRef(new Animated.Value(0)).current;
  const card2Anim = useRef(new Animated.Value(0)).current;
  const card3Anim = useRef(new Animated.Value(0)).current;
  const card4Anim = useRef(new Animated.Value(0)).current;
  const cardTranslateY1 = useRef(new Animated.Value(100)).current;
  const cardTranslateY2 = useRef(new Animated.Value(100)).current;
  const cardTranslateY3 = useRef(new Animated.Value(100)).current;
  const cardTranslateY4 = useRef(new Animated.Value(100)).current;

  const getStepData = useCallback((stepIndex: number) => {
    if (stepIndex === 0) {
      return {
        title: t('onboarding.postAuth.step1.title'),
        subtitle: t('onboarding.postAuth.step1.subtitle'),
        type: "input",
        description: t('onboarding.postAuth.step1.description'),
        subdescription: t('onboarding.postAuth.step1.subdescription'),
        placeholder: t('onboarding.postAuth.step1.placeholder'),
      };
    } else {
      return {
        title: t('onboarding.postAuth.step2.title', { userName }),
        subtitle: t('onboarding.postAuth.step2.subtitle'),
        type: "features",
        description: "",
        features: [
          {
            title: t('onboarding.postAuth.step2.features.health.title'),
            subtitle: t('onboarding.postAuth.step2.features.health.subtitle'),
            type: "main",
          },
          {
            title: t('onboarding.postAuth.step2.features.nutrition.title'),
            subtitle: t('onboarding.postAuth.step2.features.nutrition.subtitle'),
            type: "nutrition",
          },
          {
            title: t('onboarding.postAuth.step2.features.reports.title'),
            subtitle: t('onboarding.postAuth.step2.features.reports.subtitle'),
            type: "reports",
          },
        ]
      };
    }
  }, [t, userName]);  

  useEffect(() => {
    const step = getStepData(currentStep);
    trackOnboardingAction("Onboarding Page viewed", {page:step.title})
    trackOnboardingStep(currentStep + 1, step.title, {
      version: "New Onboarding",
    });
  }, [currentStep, getStepData]);  

  // Animate pills on first page
  useEffect(() => {
    if (currentPage === 0) {
      Animated.sequence([
        Animated.parallel([
          Animated.timing(fadeAnim1, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(translateY1, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(fadeAnim2, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(translateY2, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(fadeAnim3, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(translateY3, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    }
  }, [currentPage]);

  // Animate cards on second page
  useEffect(() => {
    if (currentPage === 1) {
      Animated.stagger(300, [
        Animated.parallel([
          Animated.timing(card1Anim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(cardTranslateY1, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(card2Anim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(cardTranslateY2, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(card3Anim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(cardTranslateY3, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(card4Anim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(cardTranslateY4, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    }
  }, [currentPage]);

  const handleNext = async () => {
    if (currentPage === 0 && userName.trim()) {
      // Backend call to update user name
      try {
        await updateUserData(userName.trim());
        trackOnboardingAction("Name Submitted", { 
          name: userName.trim(), 
          version: "New Onboarding"
        });
        logger.info("User name updated successfully during onboarding", {
          name: userName,
        });
      } catch (error:any) {
        if (error?.response?.status !== "401") {
          logger.error("Failed to update user name during onboarding:", error);
        }
      }
      
      trackOnboardingAction("Next Button Clicked", {
        from_step: currentPage,
        to_step: currentPage + 1,
        step_name: getStepData(currentPage).title,
        version: "New Onboarding"
      });
      
      setCurrentPage(1);
      setCurrentStep(1);
    }
  };

  const handleBack = () => {
    if (currentPage === 1) {
      trackOnboardingAction("Back button clicked", {
        version: "New Onboarding",
      });
      setCurrentPage(0);
      setCurrentStep(0);
    }
  };

  const handleGetStarted = async () => {
    try {
      trackOnboardingAction("Onboarding Completed", {
        total_steps_taken: currentStep + 1,
        time_spent: Date.now() - startTime,
        version: "New Onboarding"
      });

      trackUserInteraction("Onboarding Complete", {
        total_time_spent: Date.now() - startTime,
        steps_completed: currentStep + 1,
        user_name: userName,
        version: "New Onboarding"
      });

      AppEventsLogger.logEvent('completed_onboarding');

      await updateUserMetadata("isMobileOnboardingDone", true);
      const { setUser, user } = useAuthStore.getState();
      if (user) {
        setUser({
          ...user,
          isMobileOnboardingDone: true,
        });
      }
      router.replace("/(tabs)/chat");
    } catch (error:any) {
      trackUserInteraction("Onboarding Complete Failure", {
        error_message: error.message,
      });
      if (error?.response?.status !== "401") {
        logger.error("Error updating onboarding status:", error);
      }
      const token = await getAccessToken();
      router.replace(token ? "/(tabs)/chat" : "/auth/phone");
    }
  };

  return currentPage === 0 ? (
    <FirstPageStep 
      userName={userName}
      setUserName={setUserName}
      handleNext={handleNext}
      fadeAnim1={fadeAnim1}
      fadeAnim2={fadeAnim2}
      fadeAnim3={fadeAnim3}
      translateY1={translateY1}
      translateY2={translateY2}
      translateY3={translateY3}
    />
  ) : (
    <SecondPageStep 
      userName={userName}
      handleBack={handleBack}
      handleGetStarted={handleGetStarted}
      card1Anim={card1Anim}
      card2Anim={card2Anim}
      card3Anim={card3Anim}
      card4Anim={card4Anim}
      cardTranslateY1={cardTranslateY1}
      cardTranslateY2={cardTranslateY2}
      cardTranslateY3={cardTranslateY3}
      cardTranslateY4={cardTranslateY4}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  innerContainer: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  firstPageScrollContent: {
    flexGrow: 1,
  },
  secondPageContainer: {
    flex: 1,
    paddingHorizontal: moderateScale(24),
    paddingTop: verticalScale(20),
  },
  illustrationContainer: {
    height: screenHeight * 0.25, // 25% of screen height
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: verticalScale(10),
  },
  illustration: {
    width: '90%',
    height: '100%',
    borderRadius: moderateScale(20),
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  illustrationImage: {
    width: '80%',
    height: '80%',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: moderateScale(24),
    paddingTop: verticalScale(10),
    paddingBottom: verticalScale(10),
  },
  greeting: {
    fontSize: moderateScale(30),
    fontWeight: 'bold',
    color: '#1a5f3f',
    marginBottom: verticalScale(5),
  },
  title: {
    fontSize: moderateScale(30),
    fontWeight: 'bold',
    color: '#1a5f3f',
    marginBottom: verticalScale(25),
  },
  description: {
    fontSize: moderateScale(15),
    color: '#333',
    lineHeight: moderateScale(24),
    marginBottom: verticalScale(15),
  },
  subDescription: {
    fontSize: moderateScale(14),
    color: '#666',
    lineHeight: moderateScale(22),
    marginBottom: verticalScale(30),
  },
  pillsContainer: {
    gap: verticalScale(10),
  },
  pill: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(6),
    backgroundColor: '#e8f5e9',
    borderRadius: moderateScale(20),
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(6),
    alignSelf: 'flex-start'
  },
  checkmark: {
    fontSize: moderateScale(18),
    color: '#1a5f3f',
    marginRight: moderateScale(10),
  },
  pillText: {
    fontSize: moderateScale(14),
    paddingHorizontal: moderateScale(6),
    color: '#333',
  },
  inputSection: {
    paddingHorizontal: moderateScale(20),
    paddingBottom: verticalScale(20),
    paddingTop: verticalScale(10),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: moderateScale(32),
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(8),

  },
  input: {
    flex: 1,
    fontSize: moderateScale(16),
    color: '#333',
  },
  sendButton: {
    marginLeft: moderateScale(10),
  },
  sendButtonDisabled: {
    opacity: 0.3,
  },
  sendButtonText: {
    fontSize: moderateScale(24),
    color: '#1a5f3f',
  },
  scrollContent: {
    paddingHorizontal: moderateScale(24),
    paddingTop: verticalScale(20),
  },
  pageTitle: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: '#1a5f3f',
    marginBottom: verticalScale(8),
  },
  pageSubtitle: {
    fontSize: moderateScale(24),
    fontWeight: '600',
    color: '#333',
    marginBottom: verticalScale(20),
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: verticalScale(12),
    paddingHorizontal: moderateScale(12),
    marginBottom: verticalScale(16),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  cardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(6),
  },
  cardIcon: {
    fontSize: verticalScale(18),
  },
  cardTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    marginLeft: 6,
  },
  cardDescription: {
    fontSize: moderateScale(14),
    color: '#444',
  },
  prescriptionPreview: {
    width: '100%',
    height: verticalScale(100),
    marginTop: verticalScale(10),
    borderRadius: 10,
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  prescriptionText: {
    fontSize: moderateScale(12),
    color: '#333',
    lineHeight: moderateScale(18),
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(24),
    paddingTop: verticalScale(6),
  },
  backButton: {
    padding: moderateScale(10),
  },
  backButtonText: {
    fontSize: moderateScale(20),
    color: '#333',
  },
  getStartedButton: {
    backgroundColor: '#1a5f3f',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
    paddingVertical: moderateScale(10),
    borderRadius: moderateScale(30),
  },
  getStartedText: {
    color: '#fff',
    fontSize: moderateScale(16),
    fontWeight: '600',
    marginRight: moderateScale(10),
  },
  getStartedArrow: {
    color: '#fff',
    fontSize: moderateScale(16),
  },
});

export default PostAuthOnboardingWrapper;