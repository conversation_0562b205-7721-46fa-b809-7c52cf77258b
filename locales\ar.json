{"common": {"error": "خطأ", "yes": "نعم", "no": "لا", "sometimes": "أحيانًا", "close": "إغلاق", "cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "next": "التالي", "loading": "جارٍ التحميل...", "version": "الإصدار *******"}, "welcome": "سجّل الدخول للبدء بالحديث مع أوغست", "notFound": {"title": "عفواً!", "message": "هذه الشاشة غير موجودة.", "goHome": "العودة إلى الشاشة الرئيسية!"}, "library": {"title": "مكتبة الصحة"}, "specialists": {"title": "الأخصائيون", "description": "استشر أخصائيين صحيين متخصصين لمزيد من الاهتمامات الصحية المحددة. اختر أخصائيًا أدناه:", "generalPhysician": {"title": "ط<PERSON>يب عام", "description": "للمشاكل الصحية العامة والرعاية الأولية."}, "nutritionist": {"title": "أخصائي تغذية", "description": "للحصول على نصائح حول النظام الغذائي والتغذية وإدارة الوزن."}, "cardiologist": {"title": "أخصائي أمراض القلب", "description": "للمشاكل المتعلقة بالقلب وصحة القلب والأوعية الدموية."}, "neurologist": {"title": "أخصائي أعصاب", "description": "لمشاكل الدماغ والحبل الشوكي والجهاز العصبي."}, "oncologist": {"title": "أخصائي أورام", "description": "للمشاكل وال علاجات المتعلقة بالسرطان."}, "endocrinologist": {"title": "أخصائي الغدد الصماء", "description": "لاضطرابات الهرمونات وإدارة مرض السكري."}, "dermatologist": {"title": "أخصائي أمراض جلدية", "description": "لحالات الجلد والشعر والأظافر."}, "psychiatrist": {"title": "طبيب نفسي", "description": "للمشاكل الصحية النفسية والرفاه النفسي."}}, "profile": {"title": "الملف الشخصي", "defaultName": "ضيف", "namePlaceholder": "أ<PERSON><PERSON>ل اسمك", "saving": "جارٍ الحفظ...", "noPhoneNumber": "رقم هاتف غير موجود", "loggingOut": "جارٍ تسجيل الخروج...", "about": {"title": "حو<PERSON>", "description": "تعرف على المزيد حول أوغست"}, "whatsapp": {"title": "واتساب", "description": "دردش مع أوغست على واتساب"}, "refer": {"title": "إحالة", "description": "أعجبك أوغست؟ شاركه مع أصدقائك"}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "description": "نأسف لرؤيتك ترحل"}, "logout": {"title": "تسجيل الخروج", "description": "عد قريباً. سنفتقدك"}, "shareMessage": "👋مرحباً، تحقق من هذا التطبيق الرائع الذي أستخدمه!\n\n\n\n➡️لقد استخدمت أوغست للحصول على معلومات وإرشادات صحية سريعة وموثوقة. إنه مثل وجود طبيب في جيبك! تحقق منه هنا:", "error": {"loadFailed": "فشل تحميل بيانات المستخدم", "fetchError": "حد<PERSON> خطأ أثناء جلب بيانات المستخدم", "updateNameFailed": "فشل تحديث الاسم", "updateNameError": "حد<PERSON> خطأ أثناء تحديث الاسم", "loadFoodData": "فشل تحميل بيانات الطعام", "logoutError": "خطأ أثناء تسجيل الخروج:", "shareError": "خطأ في مشاركة الرسائل:"}}, "error": {"title": "حد<PERSON> خطأ ما", "checkLogs": "يرجى التحقق من سجلات جهازك لمزيد من التفاصيل.", "unknown": "خطأ غير معروف", "unknownFile": "ملف غير معروف", "unknownLine": "سطر غير معروف", "unknownColumn": "عمود غير معروف"}, "auth": {"phone": {"selectCountry": "ا<PERSON><PERSON><PERSON> البلد", "searchCountries": "بح<PERSON> عن الدول", "validation": {"invalidPhone": "الرجاء إدخال رقم هاتف صحيح", "invalidDigits": "الرجاء إدخال رقم هاتف صحيح (7-15 رقمًا)"}}, "header": {"title": "احصل على وضوح بشأن مخاوفك الصحية على الفور وبشكل خاص", "subtitle": "إرشادات مدروسة. بدون عجلة. بدون ارتباك.", "emphasis": "فقط الوضوح."}, "greeting": "مرحباً 👋", "phoneNumber": "رقم الهاتف", "requestOTP": "طل<PERSON> رمز التحقق", "otp": {"title": "رمز التحقق مرة واحدة", "verify": "التحقق من رمز التحقق", "sending": "جارٍ الإرسال...", "countdown": "إعادة إرسال رمز التحقق في {{countdown}} ثانية", "resend": "إعادة إرسال رمز التحقق", "sentTo": "تم إرسال رمز التحقق إلى ", "whatsappSuffix": "على واتساب"}, "disclaimer": {"prefix": "بالتسجيل، أنت توافق على ", "continuePrefix": "بالمتابعة، أنت توافق على ", "termsOfService": "شروط الخدمة", "and": "و", "privacyPolicy": "سياسة الخصوصية", "whatsappConsent": "، وتوافق على تلقي التحديثات والتذكيرات منا عبر واتساب."}}, "onboarding": {"preAuth": {"welcome": {"title": "أهلًا بك في أوغست!", "buttonText": "لنبدأ"}}, "postAuth": {"step1": {"title": "مرحباً!", "subtitle": "أنا أوغست 👋", "description": "اعتبرني الزاوية المريحة على جهازك حيث تستكشف كل فضولك الصحية.", "subdescription": "لا تتردد في طرح أي شيء يدور في ذهنك.\nبدون أحكام، بدون حدود!", "placeholder": "ماذا تريد أن أناديك؟"}, "step2": {"title": "مرحباً {{userName}}،", "subtitle": "إليك ما يمكنني فعله:", "features": {"health": {"title": "<PERSON><PERSON><PERSON> عن", "subtitle": "استفساراتك الصحية"}, "nutrition": {"title": "تتبع", "subtitle": "السعرات الحرارية"}, "reports": {"title": "تحليل", "subtitle": "التقارير"}}}}, "pills": {"thoughtful": "مدروس", "careful": "دقيق", "accurate": "دقيق"}, "features": {"symptoms": {"title": "تحقق من أعراضك", "description": "أشعر بالغثيان منذ أسبوع. ماذا يحدث لي؟"}, "prescriptions": {"title": "حلل وصفاتك الطبية", "description": "قم بتحميل وفهم الوصفات الطبية مثل الطبيب."}, "medicine": {"title": "تعرف على دوائك", "description": "هل يتفاعل الميتفورمين لعلاج متلازمة تكيس المبايض مع حبوب أدوية فرط الحركة ونقص الانتباه؟"}, "plans": {"title": "احصل على خطط شخصية", "description": "هل يمكنك إعطائي خطة تغذية ولياقة بدنية لخفض مستويات HbA1c؟"}}, "buttons": {"getStarted": "ابد<PERSON>", "next": "التالي"}, "errors": {"nameRequired": "الرجاء إدخال اسمك"}}, "tabs": {"chat": "الدردشة", "discover": "اكتشاف", "nutrition": "التغذية", "personalize": "تخصيص"}, "chat": {"nav": {"title": "أغسطس"}, "me": "أنا", "augustName": "أغسطس", "input": {"placeholder": "اسأل أغسطس...", "disclaimer": "قد يخطئ أغسطس. تأكد من الطبيب"}, "list": {"loadingMessages": "جاري تحميل الرسائل...", "noMessages": "لا توجد رسائل بعد. ابدأ محادثة!"}, "connection": {"offlineMessage": "يبدو أنك غير متصل بالإنترنت. أعد الاتصال لإرسال الرسائل.", "connecting": "جاري الاتصال...", "tryAgain": "حاول مرة أخرى"}, "prompts": {"uploadReport": "رفع التقرير", "speakInHindi": "تحدث بالهندية", "notFeelingWell": "أنا لست على ما يرام", "whatIsMyBMI": "ما هو مؤشر كتلة جسمي؟", "nutritionAdvice": "نصائح غذائية", "sleepBetter": "النوم بشكل أفضل"}, "citations": {"referenceText": "ل<PERSON>زيد من التفاصيل حول هذه المحادثة، يرجى الرجوع إلى:"}, "actions": {"copiedToClipboard": "تم النسخ إلى الحافظة", "copied": "تم النسخ"}, "share": {"introText": "👋مرحباً، انظر إلى المحادثة التي أجريتها مع أغسطس:\n\n", "downloadText": "\n\n➡️قم بتنزيل أغسطس للدردشة مع رفيقك الصحي من الذكاء الاصطناعي:\n"}}, "discover": {"nav": {"title": "اكتشف"}, "categories": {"all": "الكل", "heartHealth": "ص<PERSON><PERSON> القلب", "nutrition": "التغذية", "mentalHealth": "الصحة العقلية", "fitness": "اللياقة البدنية", "wellness": "الرفاهية"}, "cards": {"empty": "لا توجد بطاقات متاحة لهذه الفئة"}, "sections": {"features": "الميزات"}, "features": {"healthLibrary": {"title": "مكتبة الصحة", "description": "إمكانية الوصول إلى معلومات طبية موثوقة وحديثة مجاناً تماماً."}, "nutritionTracker": {"title": "متتبع التغذية", "description": "هل تساءلت يومًا ما إذا كان بإمكانك فقط تحميل صورة طعامك وتتبع جميع أهدافك الغذائية؟ يمكن لأغسطس أن يفعل ذلك!"}, "multilingualSupport": {"title": "دعم متعدد اللغات", "description": "يمكنك التواصل مع أغسطس بأي لغة تشعر بالراحة معها! أغسطس هنا دائمًا للاستماع والدعم والرد عليك في أي وقت تحتاج إليه.", "samplePrompt": "تحدث بالهندية"}, "labReportAnalysis": {"title": "تحليل تقارير المختبر", "description": "عندما تتحدث إلى أغسطس حول تقارير مختبرك، تحصل على دقة عالية. قام أغسطس بمعالجة أكثر من 4.7 مليون تقرير بدقة استخراج علامات حيوية تبلغ 98.4%."}}}, "nutrition": {"nav": {"title": "التغذية"}, "meals": {"title": "وجباتك", "subtitle": "اضغط للاطلاع على العناصر الغذائية في كل وجبة"}, "upload": {"loading": "جاري تحميل الصورة..."}, "defaultFoodName": "عنصر غذائي", "today": "اليوم", "unknownTime": "وقت غير معروف", "calories": "🔥 سعرات حرارية", "proteins": "🥩 بروتينات", "carbs": "🍞 كربوهيدرات", "sugars": "🍬 سكريات", "fat": "🥑 دهون", "caloriesLabel": "السعرات الحرارية", "proteinLabel": "البروتين", "carbohydratesLabel": "الكربوهيدرات", "fatLabel": "الدهون", "sugarLabel": "السكر", "tips": "نصائح:", "macroBreakdown": "تحليل العناصر الغذائية الكبيرة", "noMacroData": "لا توجد بيانات عن العناصر الغذائية الكبيرة متاحة لهذا العنصر الغذائي.", "disclaimer": "للاستخدام التعليمي فقط.  اعرف أكثر", "disclaimerLink": "هنا", "unit": {"kcal": "سعرة حرارية", "g": "غرام"}, "form": {"gender": {"title": "ما هو جنسك؟", "subtitle": "سيتم استخدام هذا لمعايرة خطتك المخصصة.", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "other": "أ<PERSON><PERSON><PERSON>"}, "age": {"title": "ما هو عمرك؟", "subtitle": "سيتم استخدام هذا لحساب احتياجاتك اليومية."}, "measurements": {"title": "الطول والوزن", "subtitle": "الرجاء إدخال طولك بالسنتيمترات ووزنك بالكيلوغرامات."}, "activity": {"title": "مستوى النشاط", "subtitle": "كم مرة تمارس الرياضة؟", "none": "لا رياضة", "moderate": "متوسط", "high": "عالي"}, "goal": {"title": "<PERSON><PERSON><PERSON> الوزن", "subtitle": "ماذا تريد أن تحقق؟", "increase": "زيادة", "maintain": "الحفاظ", "decrease": "إنقاص"}, "targetWeight": {"title": "وزن الهدف", "subtitle": "ما هو وزنك المستهدف بالكيلوغرامات؟"}, "setup": {"title": "<PERSON>عد<PERSON> خطتك", "subtitle": "الرجاء الانتظار أثناء إعداد خطة التغذية الخاصة بك."}, "review": {"title": "را<PERSON><PERSON> خطتك", "subtitle": "راجع وخصص خطة التغذية الخاصة بك."}, "height": {"label": "الطول (سم)"}, "weight": {"label": "الوزن (كغم)"}}, "error": {"updateFailed": "فشل تحديث بيانات التغذية.  الرجاء المحاولة مرة أخرى.", "parsingError": "خطأ في تحليل بيانات الطعام:", "fetchReportsFailed": "فشل جلب بيانات التقارير.  الرجاء المحاولة مرة أخرى.", "missingReportId": "رقم تعريف التقرير مفقود"}}, "personalize": {"nav": {"title": "تخصيص"}, "button": {"saving": "<PERSON><PERSON><PERSON>", "review": "مراجعة", "saveNext": "حفظ ومتابعة"}}, "basicInfo": {"title": "دعنا نتعرف عليك بشكل أفضل", "subtitle": "تساعدنا هذه المعلومات على تخصيص توصياتك الصحية", "age": {"question": "كم عمرك؟", "placeholder": "أ<PERSON><PERSON>ل عمرك"}, "sex": {"question": "ما هو جنسك؟", "placeholder": "<PERSON><PERSON><PERSON> جن<PERSON>", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "other": "أ<PERSON><PERSON><PERSON>"}, "height": {"question": "ما هو طولك؟ (سم)", "placeholder": "أد<PERSON>ل طولك"}, "weight": {"question": "ما هو وزنك؟ (كغم)", "placeholder": "أدخل وزنك"}}, "lifestyle": {"title": "عادات نمط حياتك", "subtitle": "يساعدنا فهم عاداتك اليومية على تقديم توصيات أفضل", "diet": {"question": "ما نوع النظام الغذائي الذي تتبعه؟", "placeholder": "اختر نظامك الغذائي", "vegetarian": "نباتي", "nonVegetarian": "غير نباتي", "vegan": "نباتي صارم", "pescatarian": "نباتي يأكل السمك", "keto": "كيتو", "paleo": "باليو"}, "exercise": {"question": "هل تمارس الرياضة بانتظام؟"}, "drinking": {"question": "هل تتناول الكحول؟"}, "smoking": {"question": "هل تدخن؟"}, "sleep": {"question": "كم ساعة تنام في الليلة؟", "value": "{{sleep}} ساعة"}, "hydration": {"question": "كم كوبًا من الماء تشرب يوميًا؟", "value": "{{hydration}} كو<PERSON> ({{liters}} لتر)"}}, "allergies": {"title": "هل لديك أي حساسية؟", "subtitle": "إن معرفة حساسيتك يساعدنا على تقديم توصيات أكثر أمانًا", "allergyIndex": "حساسية {{index}}", "name": {"question": "ما الذي لديك حساسية تجاهه؟", "placeholder": "أدخل الحساسية (مثل: الفول السوداني، الغبار)"}, "severity": {"question": "ما مدى شدة هذه الحساسية؟", "placeholder": "اختر الشدة", "mild": "خفيفة", "moderate": "متوسطة", "severe": "شديدة"}, "addButton": "إضافة حساسية أخرى", "noAllergiesButton": "ليس لدي أي حساسية"}, "medications": {"title": "الأدوية والمكملات الغذائية", "subtitle": "أخبرنا عن أي أدوية أو مكملات غذائية تتناولها حاليًا", "medicationIndex": "دواء {{index}}", "name": {"label": "اسم الدواء", "placeholder": "أد<PERSON>ل اسم الدواء"}, "startDate": {"question": "متى بدأت بتناوله؟", "placeholder": "اختر التاريخ"}, "type": {"label": "نوع الدواء", "shortTerm": "قصير المدى", "longTerm": "طويل المدى"}, "dose": {"label": "الجرعة", "placeholder": "الكمية"}, "unit": {"label": "وحدة القياس"}, "frequency": {"label": "التكرار", "placeholder": "مرات", "perDay": "في اليوم", "perWeek": "في الأسبوع", "perMonth": "في الشهر", "perYear": "في السنة"}, "units": {"mg": "ملغ", "ml": "مل", "iu": "وحدة دولية", "puffs": "نفثات", "drops": "قطرات", "tsp": "ملعقة صغيرة", "tbsp": "ملعقة كبيرة", "cups": "أكواب"}, "addButton": "إضافة دواء آخر", "noMedicationsButton": "لا أتناول أي أدوية", "calendar": {"title": "اختر تاريخ البدء"}}, "conditions": {"title": "الحالات الطبية", "subtitle": "أخبرنا عن أي حالات طبية لديك أو كنت تعاني منها في الماضي", "conditionIndex": "حالة {{index}}", "name": {"label": "اسم الحالة", "placeholder": "أدخل الحالة (مثل: الربو، إلخ)"}, "since": {"question": "منذ متى تعاني من هذه الحالة؟", "placeholder": "اختر التاريخ"}, "current": {"question": "هل هي تزعجك حاليًا؟"}, "medicated": {"question": "هل تتناول أي أدوية لهذه الحالة؟"}, "addButton": "إضافة حالة أخرى", "noConditionsButton": "ليس لدي أي حالات طبية", "calendar": {"title": "اختر التاريخ"}}, "reproductive": {"title": "الصحة الإنجابية", "subtitle": "تساعدنا هذه المعلومات على تقديم توصيات صحية أكثر تخصيصًا", "menstruation": {"question": "هل سبق لكِ أن نزفتِ دم الحيض؟", "detailsTitle": "تفاصيل الدورة الشهرية", "regularity": {"question": "ما مدى انتظام دورتكِ الشهرية؟", "regular": "منتظمة", "irregular": "غير منتظمة", "notSure": "لست متأكدة"}, "cycleLength": {"label": "متوسط طول الدورة (أيام)", "placeholder": "أد<PERSON>ل طول الدورة"}, "flowDays": {"label": "أيام النزيف: {{flowDays}}", "min": "يوم واحد", "max": "15 يومًا"}, "padsPerDay": {"label": "عدد الفوط/السدادات يوميًا: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "هل تعانين من أي أعراض أثناء دورتكِ؟", "placeholder": "أدخلي الأعراض (مثل: تقلصات، صداع)"}}, "childbirth": {"question": "هل سبق لكِ أن مررتِ بتجربة الولادة؟", "detailsTitle": "تفاصيل الولادة", "children": {"label": "ع<PERSON><PERSON> الأطفال"}, "pregnancies": {"label": "عد<PERSON> مرات الحمل"}, "complications": {"question": "هل واجهتِ أي مضاعفات أثناء الحمل أو الولادة؟", "placeholder": "أدخلي المضاعفات (إن وجدت)"}}}, "review": {"title": "راجع معلوماتك", "subtitle": "يرجى مراجعة المعلومات التي قدمتها قبل الإرسال", "sections": {"basicInfo": "معلومات أساسية", "lifestyle": "نمط الحياة", "allergies": "الحساسية", "medications": "الأدوية والمكملات الغذائية", "conditions": "الحالات الطبية", "reproductive": "الصحة الإنجابية", "menstruationDetails": "تفاصيل الدورة الشهرية", "childbirthDetails": "تفاصيل الولادة"}, "fields": {"age": "العمر:", "sex": "الجنس:", "height": "الطول:", "weight": "الوزن:", "diet": "النظام الغذائي:", "exercise": "التمارين الرياضية:", "drinking": "شرب الكحول:", "smoking": "التدخين:", "sleep": "النوم:", "hydration": "ترطيب الجسم:", "allergyIndex": "الحساسية {{index}}:", "dose": "الجرعة:", "frequency": "التكرار:", "type": "النوع:", "since": "منذ:", "currentlyActive": "نشط حاليًا:", "takingMedication": "يتناول الدواء:", "hasMenstruated": "هل نزفت دم الحيض؟", "regularity": "الانتظام:", "cycleLength": "طول الدورة:", "flowDays": "أيام النزيف:", "padsPerDay": "عدد الفوط/السدادات يوميًا:", "hasChildbirth": "هل سبق لكِ أن مررتِ بتجربة الولادة؟", "children": "الأطفال:", "pregnancies": "عدد مرات الحمل:"}, "notProvided": "لم يتم تقديمها", "units": {"cm": "{{height}} سم", "kg": "{{weight}} <PERSON>غم"}, "values": {"sleepHours": "{{sleep}} ساعة يوميًا", "hydration": "{{hydration}} كوب ({{liters}} لتر) يوميًا", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} يومًا"}, "noData": {"allergies": "لم يتم تقديم أي معلومات عن الحساسية", "medications": "لم يتم تقديم أي معلومات عن الأدوية", "conditions": "لم يتم تقديم أي معلومات عن الحالات الطبية"}, "submitButton": "إرسال المعلومات"}, "success": {"title": "تم تحديث المعلومات!", "message": "شكرًا لكِ على تقديم معلوماتكِ الصحية. سنستخدم هذه المعلومات لتخصيص تجربتكِ وتقديم توصيات أفضل.", "benefits": {"insights": "رؤى صحية مخصصة", "reminders": "تذكيرات أدوية أفضل", "recommendations": "توصيات صحية مصممة خصيصًا"}, "continueButton": "متابعة إلى لوحة التحكم"}, "permissions": {"microphonePermissionDenied": "تم رفض إذن الميكروفون", "microphoneAccessDescription": "يتطلب أغسطس الوصول إلى ميكروفونك لتسجيل الصوت وإرسال الملاحظات الصوتية", "permissionDenied": "تم رفض الإذن", "cameraPermissionRequired": "نحتاج إلى أذونات الكاميرا لجعل هذا يعمل!", "mediaLibraryPermissionRequired": "نحتاج إلى أذونات مكتبة الوسائط لجعل هذا يعمل!"}, "voiceRecording": {"recordingTooLong": "التسجيل طويل جدًا", "recordingTooLongMessage": "يجب ألا تتجاوز التسجيلات الصوتية 5 دقائق. يرجى تسجيل رسالة أقصر."}, "errors": {"uploadFailed": "فشل التحميل", "voiceUploadFailed": "تعذر تحميل التسجيل الصوتي.", "voiceRecordingFailed": "فشل إرسال التسجيل الصوتي", "failedToStopRecording": "فشل إيقا<PERSON> التسجيل", "photoUploadFailed": "تعذر تحميل الصورة.", "failedToTakePhoto": "فشل التقاط الصورة", "imageUploadFailed": "تعذر تحميل الصورة: {{fileName}}", "failedToPickImage": "فشل اختيار الصورة", "documentUploadFailed": "تعذر تحميل المستند: {{fileName}}", "failedToPickDocument": "فشل اختيار المستند"}, "audioPlayer": {"downloadingAudio": "جاري تنزيل الصوت...", "loadingAudio": "جاري تحميل الصوت..."}, "mediaProcessing": {"processingFile": "جاري معالجة ملفك", "uploadingSecuring": "جاري تحميل وتأمين الملف...", "analyzingContent": "جاري تحليل محتوى المستند...", "extractingInfo": "جاري استخراج المعلومات الرئيسية...", "processingInsights": "جاري معالجة الأفكار...", "preparingResponse": "جاري إعداد استجابة مفصلة...", "finalizingResponse": "جاري وضع اللمسات الأخيرة على الاستجابة..."}, "attachments": {"voiceMessage": "رسالة صوتية", "image": "[صورة]", "pdf": "[ملف PDF]", "voice": "[ملاحظة صوتية]"}, "pdf": {"loadingPdf": "جاري تحميل ملف PDF..."}, "dateTime": {"yesterday": "أمس، "}, "navbar": {"defaultTitle": "أغسطس", "selectedCount": "<PERSON><PERSON><PERSON><PERSON>"}, "mediaUpload": {"photoLibrary": "مكتبة الصور", "takePhoto": "التقاط صورة", "chooseFile": "اختيار ملف"}, "comingSoon": {"title": "قريباً!", "description": "قيد التطوير حاليًا. ترقبوا التحديثات!", "buttonText": "حصلت عليه!"}, "clipboard": {"success": "تم نسخ الرابط إلى الحافظة"}, "mediaPhotos": {"emptyState": "لا يوجد مدخلات حتى الآن."}, "foodDetail": {"defaultFoodName": "عنصر غذائي", "nutrition": {"totalCalories": "السعرات الحرارية الكلية", "proteins": "البروتينات", "carbs": "الكربوهيدرات", "fat": "الدهون", "sugars": "السكريات", "fibers": "الألياف"}}, "reports": {"defaultTitle": "عنصر وسائط", "defaultFoodName": "عنصر غذائي", "defaultName": "مستند", "openButton": "فتح في عارض خارجي", "biomarker": {"headerBiomarker": "المؤشر الحيوي", "headerValue": "القيمة", "headerRefRange": "النطاق المرجعي", "headerStatus": "الحالة"}, "noData": "لا توجد بيانات مؤشرات حيوية متاحة"}, "setup": {"title": "نقوم بإعداد كل شيء من أجلك", "inProgress": "جاري...", "progressMessages": {"0": "حساب السعرات الحرارية اليومية", "1": "تحسين تقسيم الماكرو", "2": "إنشاء خطة الوجبات", "3": "حساب درجة الصحة", "4": "وضع اللمسات الأخيرة على الإعداد"}, "checklistItems": {"0": "تحليل بيانات صحتك", "1": "حسا<PERSON> خطة التغذية المثلى", "2": "تخصيص توصياتك", "3": "إنشاء اقتراحات وجباتك", "4": "وضع اللمسات الأخيرة على إعدادك"}}, "foodEntry": {"emptyState": "لا يوجد مدخلات طعام حتى الآن. التقط صورة لوجبتك لإضافتها!"}, "nutritionReview": {"congratulations": "تهانينا!", "subtitle": "خطة التغذية المخصصة لك جاهزة", "submitButton": "لنبدأ!", "dailyTargetsTitle": "أهدافك الغذائية اليومية", "macroLabels": {"calories": "السعرات الحرارية", "carbs": "الكربوهيدرات", "protein": "البروتين", "fats": "الدهون"}, "recommendations": {"title": "كيفية تحقيق أهدافك:", "healthScores": "استخدم مؤشرات الصحة لتحسين روتينك", "trackFood": "تتبع تناولك للطعام باستمرار", "followCalories": "اتبع توصية السعرات الحرارية اليومية الخاصة بك", "balanceMacros": "وازِن بين الكربوهيدرات والبروتين والدهون"}}, "editModal": {"titlePrefix": "تعديل ", "cancelButton": "إلغاء", "saveButton": "التالي"}, "processing": {"stages": {"scanning": "فحص الطعام...", "identifying": "تحديد المكونات...", "extracting": "استخراج العناصر الغذائية...", "finalizing": "إنهاء النتائج..."}, "error": {"defaultMessage": "لم يتم الكشف عن أي طعام", "subtitle": "جرب زاوية مختلفة"}, "retakeButton": "اضغط لإعادة التقاط الصورة", "notification": "سنعلمك عند الانتهاء!"}, "chart": {"title": "تتبع التغذية بمرور الوقت", "selectNutrient": "اختر العناصر الغذائية:", "emptyState": "لا توجد بيانات غذائية متاحة حتى الآن.", "dropdown": {"calories": "السعرات الحرارية", "protein": "البروتين", "carbs": "الكربوهيدرات", "fat": "الدهون", "sugars": "السكريات"}}, "foodModal": {"defaultName": "عنصر غذائي", "defaultDate": "اليوم", "defaultTime": "وقت غير معروف", "saveChanges": "حفظ التغييرات", "error": {"title": "خطأ", "message": "فشل تحديث بيانات التغذية. يرجى المحاولة مرة أخرى."}, "nutrition": {"calories": "🔥 السعرات الحرارية", "proteins": "🥩 البروتينات", "carbs": "🍞 الكربوهيدرات", "sugars": "🍬 السكريات", "fat": "🥑 الدهون"}, "macroBreakdown": {"title": "تفصيل العناصر الغذائية الكبيرة", "noData": "لا توجد بيانات عن العناصر الغذائية الكبيرة متاحة لهذا العنصر الغذائي."}, "macroLabels": {"calories": "السعرات الحرارية", "protein": "البروتين", "carbs": "الكربوهيدرات", "fat": "الدهون", "sugar": "السكر"}}, "infoModal": {"title": "معلومات مفصلة", "edit": "تعديل", "save": "<PERSON><PERSON><PERSON>", "saving": "يتم الحفظ..", "enterValue": "أد<PERSON>ل القيمة", "notSet": "<PERSON>ي<PERSON> محدد", "age": "العمر", "heightCm": "الطول (سم)", "weightKg": "الوزن (كجم)", "targetWeight": "الوزن المستهدف", "nutritionTargets": "أ<PERSON><PERSON><PERSON><PERSON> التغذية", "protein": "البروتين", "carbs": "الكربوهيدرات", "fats": "الدهون", "gm": "غرام", "editNote": "أدخل القيم أو اتركها فارغة للحساب التلقائي.", "autoCalculateNote": "يتم حساب العناصر الغذائية الكبيرة تلقائيًا بناءً على بياناتك.", "validation": {"ageMin": "يجب أن يكون العمر 18 عامًا على الأقل", "ageMax": "يجب أن يكون العمر أقل من 125 عامًا", "heightMin": "يج<PERSON> أن يكون الطول 50 سم على الأقل", "heightMax": "يج<PERSON> أن يكون الطول أقل من 250 سم", "weightMin": "يجب أن يكون الوزن 30 كجم على الأقل", "weightMax": "يجب أن يكون الوزن أقل من 500 كجم", "targetWeightMin": "يجب أن يكون الوزن المستهدف 30 كجم على الأقل", "targetWeightMax": "يجب أن يكون الوزن المستهدف أقل من 500 كجم", "proteinMin": "يجب أن يكون البروتين 0 أو أكثر", "carbsMin": "يجب أن تكون الكربوهيدرات 0 أو أكثر", "fatsMin": "يجب أن تكون الدهون 0 أو أكثر"}}, "tracker": {"calories": "السعرات الحرارية", "protein": "البروتين", "carbs": "الكربوهيدرات", "fat": "الدهون", "excess": "زيادة", "remaining": "متبقي"}, "specialistConstants": {"nutritionist": {"name": "أخصائي تغذية", "description": "نصائح الخبراء حول النظام الغذائي والتغذية وعادات الأكل الصحية", "featureName": "أخصائي تغذية"}, "cardiologist": {"name": "أخصائي قلب", "description": "متخصص في صحة القلب والأمراض القلبية الوعائية", "featureName": "أخصائي أمراض القلب"}, "neurologist": {"name": "أخصائي أعصاب", "description": "يركز على اضطرابات الدماغ والحبل الشوكي والجهاز العصبي", "featureName": "أخصائي أمراض الأعصاب"}, "oncologist": {"name": "أخصائي أورام", "description": "متخصص في تشخيص السرطان وخيارات العلاج", "featureName": "أخصائي الأورام"}, "endocrinologist": {"name": "أخصائي الغدد الصماء", "description": "خبير في الحالات الهرمونية واضطرابات التمثيل الغذائي", "featureName": "أخصائي الغدد الصماء"}}, "discoverCards": {"categories": {"nutrition": "تغذية", "heartHealth": "ص<PERSON><PERSON> القلب", "mentalHealth": "الصحة العقلية", "fitness": "لياقة بدنية", "wellness": "الرفاهية"}, "titles": {"vitaminB12Recovery": "كم من الوقت يستغرق التعافي من نقص فيتامين ب 12", "vitaminDeficiencyGanglion": "أي نقص في الفيتامينات يسبب أكياس العقد العصبية", "vitaminDeficiencyHairFall": "أي نقص في الفيتامينات يسبب تساقط الشعر", "vitaminWaters": "هل مياه الفيتامينات مفيدة لك", "cholesterolHeadaches": "هل ارتفاع الكوليسترول يسبب الصداع", "cholesterolEyes": "ما هي أعراض ارتفاع الكوليسترول التي يمكن رؤيتها في العينين", "diabetesHeadaches": "هل يمكن أن يسبب مرض السكري الصداع", "chestPainDrinking": "لماذا يؤلم الصدر بعد الشرب", "stressDizziness": "هل يمكن أن يسبب التوتر الدوار", "bulimiaFace": "ما هو وجه الشره المرضي", "kneeTwitch": "لماذا ترتعش ركبتي", "noseTwitching": "لماذا يحدث ارتعاش الأنف", "piriformisVsSciatica": "ما هي الاختلافات بين متلازمة البيريفورميس والعرق النسا", "shoulderBladePinched": "كيفية علاج العصب الملتهب في لوح الكتف", "shoulderPinched": "كيفية علاج العصب الملتهب في الكتف", "meniscusTorn": "كيفية علاج الغضروف المفصلي المسيل للدموع بشكل طبيعي", "hydrateQuickly": "كيفية الترطيب بسرعة", "periodConstipation": "هل من الطبيعي الإصابة بالإمساك أثناء الدورة الشهرية", "acneScars": "كيفية التخلص من ندبات حب الشباب بشكل طبيعي في غضون أسبوع", "perimenopausePregnancy": "هل يمكنك الحمل أثناء انقطاع الطمث المبكر"}, "descriptions": {"vitaminB12Recovery": "اكتشف الجدول الزمني للتعافي من نقص فيتامين ب 12 والعلاجات الفعالة لتعزيز مستويات طاقتك.", "vitaminDeficiencyGanglion": "استكشف العلاقة بين نقص الفيتامينات وتطور أكياس العقد العصبية في الجسم.", "vitaminDeficiencyHairFall": "تعرف على كيفية تسبب نقص الفيتامينات الأساسية في تساقط الشعر وما يمكنك فعله لمنعه.", "vitaminWaters": "اكتشف فوائد ومضار مياه الفيتامينات كجزء من تغذيتك اليومية.", "cholesterolHeadaches": "افحص العلاقة المحتملة بين ارتفاع مستويات الكوليسترول وظهور الصداع.", "cholesterolEyes": "تعرف على كيفية ظهور ارتفاع الكوليسترول في عينيك وما هي الأعراض التي يجب مراقبتها.", "diabetesHeadaches": "ابحث في العلاقة بين مرض السكري وظهور الصداع في الحياة اليومية.", "chestPainDrinking": "استكشف أسبا<PERSON> ألم الصدر بعد تناول مشروبات معينة.", "stressDizziness": "تعمق في كيفية تأثير الإجهاد على توازنك وصحتك العامة، مما يؤدي إلى الدوار.", "bulimiaFace": "افهم العلامات الجسدية لمرض الشره العصبي، بما في ذلك آثاره على مظهر الوجه.", "kneeTwitch": "ابحث عن الأسباب المحتملة وراء ارتعاش الركبة اللاإرادي وعلاقته بالإجهاد أو الإرهاق.", "noseTwitching": "تعرف على الأسباب المحتملة لارتعاش الأنف وارتباطه بالقلق أو عوامل أخرى.", "piriformisVsSciatica": "قارن بين أعراض متلازمة البيريفورميس والعرق النسا لفهم حالتك بشكل أفضل.", "shoulderBladePinched": "اكتشف تقنيات فعالة لتخفيف ضغط العصب في لوح الكتف واستعادة الحركة.", "shoulderPinched": "تعلم تمارين بسيطة وامتدادات لتخفيف ضغط الأعصاب في منطقة الكتف.", "meniscusTorn": "استكشف طرقًا و تمارين طبيعية لدعم التئام الغضروف الهلالي الممزق.", "hydrateQuickly": "اكتشف طرقًا سريعة وفعالة لإعادة الترطيب والحفاظ على ترطيب الجسم الأمثل.", "periodConstipation": "افهم أسباب الإمساك أثناء الدورة الشهرية وتعلم العلاجات الطبيعية.", "acneScars": "اكتشف العلاجات الطبيعية ونصائح العناية بالبشرة لتقليل ظهور ندبات حب الشباب بسرعة.", "perimenopausePregnancy": "تعرف على انقطاع الطمث، واعتبارات الخصوبة، وما يمكن توقعه خلال هذه المرحلة من الحياة."}}}