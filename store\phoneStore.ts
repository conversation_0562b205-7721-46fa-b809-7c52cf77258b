import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

type PhoneState = {
  phoneNumber: string | null;
  setPhoneNumber: (phone: string | null) => void;
};

export const usePhoneStore = create<PhoneState>()(
  persist(
    (set) => ({
      phoneNumber: null,
      setPhoneNumber: (phone) => set({ phoneNumber: phone }),
    }),
    {
      name: 'phone-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
