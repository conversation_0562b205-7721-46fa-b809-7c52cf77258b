import type React from "react";
import { useState, useCallback, useMemo, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  StyleSheet,
  PanResponder,
  Animated,
  Dimensions,
} from "react-native";
import {
  Pill,
  ChevronDown,
  Plus,
  Minus,
  Calendar,
  X,
} from "lucide-react-native";
import { useTranslation } from "react-i18next";
import { colors } from "@/constants/colors";
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils";
import { Calendar as CalendarPicker } from "react-native-calendars";
import { useFormDataStore } from "@/store/formDataStore";
import logger from "@/utils/logger/logger";
import { scale, verticalScale, moderateScale, moderateVerticalScale } from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface MedicationsStepProps {
  stepIndex: number;
  isActive?: boolean;
}
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: scale(16),
    },
    iconContainer: {
      width: scale(64),
      height: scale(64),
      borderRadius: scale(theme.radii["4xl"]),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    medicationItem: {
      marginBottom: verticalScale(24),
      backgroundColor: theme.colors.gray[50],
      borderRadius: scale(theme.radii.md),
      padding: scale(14),
      borderWidth: 1,
      borderColor: theme.colors.gray[200],
    },
    medicationHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: verticalScale(14),
    },
    label: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: theme.colors.gray[800],
    },
    formField: {
      marginBottom: verticalScale(14),
    },
    formRow: {
      flexDirection: "row",
      gap: scale(12),
    },
    doseField: {
      flex: 2,
    },
    unitField: {
      flex: 1,
    },
    fieldLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      marginBottom: verticalScale(8),
      color: theme.colors.gray[700],
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      padding: scale(14),
      backgroundColor: theme.colors.secondary[50],
      fontSize: moderateScale(theme.fontSize.sm),
    },
    dateInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      backgroundColor: theme.colors.secondary[50],
    },
    dateInput: {
      flex: 1,
      padding: scale(14),
      fontSize: moderateScale(14),
    },
    calendarIcon: {
      marginRight: scale(14),
    },
    selectInput: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      padding: scale(14),
      backgroundColor: theme.colors.secondary[50],
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    selectText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
    placeholderText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[400],
    },
    dropdownOptionsContainer: {
      position: "absolute",
      top: verticalScale(50),
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: scale(theme.radii.md),
      zIndex: 9999,
      maxHeight: verticalScale(160),
      elevation: 10,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: verticalScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: scale(4),
    },
    dropdownOption: {
      padding: scale(14),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    dropdownOptionText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
    buttonGroup: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: theme.colors.primary.main,
      borderRadius: scale(theme.radii.md),
      overflow: "hidden",
    },
    button: {
      flex: 1,
      padding: scale(14),
      alignItems: "center",
      justifyContent: "center",
    },
    activeButton: {
      backgroundColor: theme.colors.primary.main,
    },
    buttonText: {
      color: theme.colors.primary.main,
      fontSize: moderateScale(theme.fontSize.sm),
    },
    activeButtonText: {
      color: theme.colors.secondary[50],
    },
    removeButton: {
      width: scale(36),
      height: scale(36),
      borderRadius: scale(theme.radii.lg),
      backgroundColor: theme.colors.gray[100],
      alignItems: "center",
      justifyContent: "center",
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: scale(14),
      backgroundColor: theme.colors.secondary[50],
      marginBottom: verticalScale(14),
    },
    addButtonText: {
      color: theme.colors.primary.main,
      marginLeft: scale(8),
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "500",
    },
    noMedicationsButton: {
      padding: scale(14),
      alignItems: "center",
    },
    noMedicationsText: {
      color: theme.colors.gray[600],
      fontSize: moderateScale(theme.fontSize.sm),
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      padding: scale(20),
    },
    calendarContainer: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: scale(theme.radii.md),
      width: "100%",
      maxWidth: scale(350),
      padding: scale(16),
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: verticalScale(2) },
      shadowOpacity: 0.25,
      shadowRadius: scale(3.84),
      elevation: 5,
    },
    calendarHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: verticalScale(16),
    },
    calendarTitle: {
      fontSize: moderateScale(theme.fontSize.lg),
      fontWeight: "600",
      color: colors.black,
    },
    closeButton: {
      padding: scale(4),
    }
  });
export const MedicationsStep: React.FC<MedicationsStepProps> = ({
  isActive = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const {
    formData,
    updateMedications,
    currentStep,
    updateCurrentStep,
    submitForm,
    isLoading,
  } = useFormDataStore();
  const [doseUnitDropdowns, setDoseUnitDropdowns] = useState<
    Record<number, boolean>
  >({});
  const [frequencyUnitDropdowns, setFrequencyUnitDropdowns] = useState<
    Record<number, boolean>
  >({});
  
  const { height: SCREEN_HEIGHT } = Dimensions.get('window');
  const DISMISS_THRESHOLD = SCREEN_HEIGHT * 0.15; // 15% of screen height
  const VELOCITY_THRESHOLD = 0.7; // Velocity threshold for quick flick

  const [calendarVisible, setCalendarVisible] = useState(false);
  const [activeMedicationIndex, setActiveMedicationIndex] = useState<
    number | null
  >(null);
  const [isDragging, setIsDragging] = useState(false);
  const translateY = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  // Create PanResponder to handle swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to significant vertical movement
        const isVerticalSwipe = Math.abs(gestureState.dy) > 10 &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx * 1.5);
        return isVerticalSwipe;
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
        translateY.setValue(0);
        translateY.setOffset(0);
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
          
          const progress = Math.min(Math.abs(gestureState.dy) / (SCREEN_HEIGHT * 0.5), 1);
          scaleValue.setValue(1 - (0.1 * progress));
          opacityValue.setValue(1 - (0.5 * progress));
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        setIsDragging(false);
        
        const shouldDismiss =
          gestureState.dy > DISMISS_THRESHOLD ||
          gestureState.vy > VELOCITY_THRESHOLD;

        if (shouldDismiss) {
          Animated.timing(translateY, {
            toValue: SCREEN_HEIGHT,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            closeCalendar();
          });
        } else {
          Animated.parallel([
            Animated.spring(translateY, {
              toValue: 0,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            }),
            Animated.spring(scaleValue, {
              toValue: 1,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            }),
            Animated.spring(opacityValue, {
              toValue: 1,
              tension: 40,
              friction: 7,
              useNativeDriver: true,
            })
          ]).start();
        }
      },
      onPanResponderTerminate: () => {
        setIsDragging(false);
        Animated.parallel([
          Animated.spring(translateY, {
            toValue: 0,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.spring(scaleValue, {
            toValue: 1,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.spring(opacityValue, {
            toValue: 1,
            tension: 40,
            friction: 7,
            useNativeDriver: true,
          })
        ]).start();
      },
    })
  ).current;

  const doseUnitOptions = useMemo(
    () => ["mg", "ml", "iu", "puffs", "drops", "tsp", "tbsp", "cups"],
    []
  );
  const frequencyUnitOptions = useMemo(
    () => ["per day", "per week", "per month", "per year"],
    []
  );

  const toggleDoseUnitDropdown = useCallback((index: number) => {
    setDoseUnitDropdowns((prev) => {
      const newState = !prev[index];
      trackUserInteraction("Dropdown Toggle", {
        section: "medications",
        field: "doseUnit",
        index,
        state: newState ? "open" : "closed",
      });
      return { ...prev, [index]: newState };
    });
  }, []);

  const toggleFrequencyUnitDropdown = useCallback((index: number) => {
    setFrequencyUnitDropdowns((prev) => {
      const newState = !prev[index];
      trackUserInteraction("Dropdown Toggle", {
        section: "medications",
        field: "frequencyUnit",
        index,
        state: newState ? "open" : "closed",
      });
      return { ...prev, [index]: newState };
    });
  }, []);

  const addMedication = useCallback(() => {
    updateMedications([
      ...formData.medications,
      {
        name: "",
        dose: "",
        doseUnit: "mg",
        frequency: "",
        frequencyUnit: "per day",
        startDate: "",
        type: "",
      },
    ]);
    trackUserInteraction("Form Item Added", {
      section: "medications",
      itemType: "medication",
      newCount: formData.medications.length + 1,
    });
  }, [formData.medications, updateMedications]);

  const removeMedication = useCallback(
    (index: number) => {
      if (formData.medications.length > 1) {
        // Check if the medication has any data filled in
        const medication = formData.medications[index];
        if (
          medication.name ||
          medication.startDate ||
          medication.type ||
          medication.dose ||
          medication.frequency
        ) {
          // If it has data, clear it first
          const newMedications = [...formData.medications];
          newMedications[index] = {
            name: "",
            dose: "",
            doseUnit: "mg",
            frequency: "",
            frequencyUnit: "per day",
            startDate: "",
            type: "",
          };
          updateMedications(newMedications);
          trackUserInteraction("Form Item Cleared", {
            section: "medications",
            itemType: "medication",
            index,
          });
        } else {
          // If already cleared, remove it
          const newMedications = [...formData.medications];
          newMedications.splice(index, 1);
          updateMedications(newMedications);
          trackUserInteraction("Form Item Removed", {
            section: "medications",
            itemType: "medication",
            newCount: formData.medications.length - 1,
          });
        }
      }
    },
    [formData.medications, updateMedications]
  );

  const updateMedication = useCallback(
    (index: number, field: string, value: string) => {
      const newMedications = [...formData.medications];
      newMedications[index] = { ...newMedications[index], [field]: value };
      updateMedications(newMedications);
      if (value) {
        trackUserInteraction("Form Field Update", {
          section: "medications",
          itemType: "medication",
          field,
          index,
        });
      }
    },
    [formData.medications, updateMedications]
  );

  const openCalendar = useCallback((index: number) => {
    setActiveMedicationIndex(index);
    setCalendarVisible(true);
    // Reset animation values
    translateY.setValue(0);
    scaleValue.setValue(1);
    opacityValue.setValue(1);
    trackUserInteraction("Calendar Opened", { section: "medications", index });
  }, [translateY, scaleValue, opacityValue]);

  const handleDateSelect = useCallback(
    (date: any) => {
      if (activeMedicationIndex === null) return;
      const formattedDate = date.dateString;
      updateMedication(activeMedicationIndex, "startDate", formattedDate);
      trackUserInteraction("Date Selected", {
        section: "medications",
        index: activeMedicationIndex,
        date: date.dateString,
        formattedDate,
      });
      setCalendarVisible(false);
      setActiveMedicationIndex(null);
    },
    [activeMedicationIndex, updateMedication]
  );

  const closeCalendar = useCallback(() => {
    trackUserInteraction("Calendar Closed", {
      section: "medications",
      index: activeMedicationIndex,
      withoutSelection: true,
    });
    setCalendarVisible(false);
    setActiveMedicationIndex(null);
  }, [activeMedicationIndex]);

  const handleNoMedications = useCallback(async () => {
    updateMedications([
      {
        name: "No medications",
        dose: "",
        doseUnit: "mg",
        frequency: "",
        frequencyUnit: "per day",
        startDate: "",
        type: "",
      },
    ]);
    trackUserInteraction("No Medications Selected");
    const success = await submitForm();
    if (success) {
      logger.info("Save and Next Button Clicked and Data saved");
      updateCurrentStep(currentStep + 1);
      trackUserInteraction("Form Navigation", {
        action: "next",
        toStep: "Medical Conditions",
      });
    }
  }, [submitForm, updateCurrentStep, currentStep, updateMedications, t]);

  const renderDoseUnitDropdown = useCallback(
    (index: number) => {
      const isOpen = doseUnitDropdowns[index] || false;
      return (
        <View>
          <TouchableOpacity
            style={styles.selectInput}
            onPress={() => toggleDoseUnitDropdown(index)}
          >
            <Text style={styles.selectText}>
              {formData.medications[index].doseUnit}
            </Text>
            {isOpen ? (
              <ChevronDown
                size={16}
                color={colors.gray[400]}
                style={{ transform: [{ rotate: "180deg" }] }}
              />
            ) : (
              <ChevronDown size={16} color={colors.gray[400]} />
            )}
          </TouchableOpacity>
          {isOpen && (
            <View style={styles.dropdownOptionsContainer}>
              <ScrollView showsVerticalScrollIndicator nestedScrollEnabled>
                {doseUnitOptions.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={styles.dropdownOption}
                    onPress={() => {
                      updateMedication(index, "doseUnit", option);
                      toggleDoseUnitDropdown(index);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      );
    },
    [
      doseUnitDropdowns,
      doseUnitOptions,
      formData.medications,
      toggleDoseUnitDropdown,
      updateMedication,
    ]
  );

  const renderFrequencyUnitDropdown = useCallback(
    (index: number) => {
      const isOpen = frequencyUnitDropdowns[index] || false;
      return (
        <View>
          <TouchableOpacity
            style={styles.selectInput}
            onPress={() => toggleFrequencyUnitDropdown(index)}
          >
            <Text style={styles.selectText}>
              {formData.medications[index].frequencyUnit}
            </Text>
            {isOpen ? (
              <ChevronDown
                size={16}
                color={colors.gray[400]}
                style={{ transform: [{ rotate: "180deg" }] }}
              />
            ) : (
              <ChevronDown size={16} color={colors.gray[400]} />
            )}
          </TouchableOpacity>
          {isOpen && (
            <View style={styles.dropdownOptionsContainer}>
              <ScrollView showsVerticalScrollIndicator nestedScrollEnabled>
                {frequencyUnitOptions.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={styles.dropdownOption}
                    onPress={() => {
                      updateMedication(index, "frequencyUnit", option);
                      toggleFrequencyUnitDropdown(index);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      );
    },
    [
      frequencyUnitDropdowns,
      frequencyUnitOptions,
      formData.medications,
      toggleFrequencyUnitDropdown,
      updateMedication,
    ]
  );

  if (!isActive) return null;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <Pill size={32} color={colors.primary} />
      </View>

      <Text style={styles.title}>{t('medications.title')}</Text>
      <Text style={styles.subtitle}>
        {t('medications.subtitle')}
      </Text>

      {formData.medications.map((medication, index) => (
        <View key={`medication-${index}`} style={styles.medicationItem}>
          <View style={styles.medicationHeader}>
            <Text style={styles.label}>{t('medications.medicationIndex', { index: index + 1 })}</Text>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeMedication(index)}
              disabled={formData.medications.length === 1}
            >
              {formData.medications.length > 1 &&
                (medication.name ||
                medication.startDate ||
                medication.type ||
                medication.dose ||
                medication.frequency ? (
                  <Minus size={16} color={colors.gray[500]} />
                ) : (
                  <X size={16} color={colors.gray[500]} />
                ))}
              {formData.medications.length === 1 && (
                <Minus size={16} color={colors.gray[300]} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('medications.name.label')}</Text>
            <TextInput
              style={styles.input}
              placeholder={t('medications.name.placeholder')}
              value={medication.name}
              onChangeText={(text) => updateMedication(index, "name", text)}
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('medications.startDate.question')}</Text>
            <TouchableOpacity
              style={styles.dateInputContainer}
              onPress={() => openCalendar(index)}
            >
              <TextInput
                style={styles.dateInput}
                placeholder={t('medications.startDate.placeholder')}
                value={
                  medication.startDate
                    ? new Date(medication.startDate).toLocaleDateString()
                    : ""
                }
                editable={false}
              />
              <Calendar
                size={20}
                color={colors.gray[500]}
                style={styles.calendarIcon}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.formField}>
            <Text style={styles.fieldLabel}>{t('medications.type.label')}</Text>
            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.button,
                  medication.type === t('medications.type.shortTerm') && styles.activeButton,
                ]}
                onPress={() => updateMedication(index, "type", t('medications.type.shortTerm'))}
              >
                <Text
                  style={[
                    styles.buttonText,
                    medication.type === t('medications.type.shortTerm') && styles.activeButtonText,
                  ]}
                >
                  {t('medications.type.shortTerm')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  medication.type === t('medications.type.longTerm') && styles.activeButton,
                ]}
                onPress={() => updateMedication(index, "type", t('medications.type.longTerm'))}
              >
                <Text
                  style={[
                    styles.buttonText,
                    medication.type === t('medications.type.longTerm') && styles.activeButtonText,
                  ]}
                >
                  {t('medications.type.longTerm')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formField, styles.doseField]}>
              <Text style={styles.fieldLabel}>{t('medications.dose.label')}</Text>
              <TextInput
                style={styles.input}
                placeholder={t('medications.dose.placeholder')}
                keyboardType="numeric"
                value={medication.dose}
                onChangeText={(text) => updateMedication(index, "dose", text)}
              />
            </View>

            <View style={[styles.formField, styles.unitField]}>
              <Text style={styles.fieldLabel}>{t('medications.unit.label')}</Text>
              {renderDoseUnitDropdown(index)}
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formField, styles.doseField]}>
              <Text style={styles.fieldLabel}>{t('medications.frequency.label')}</Text>
              <TextInput
                style={styles.input}
                placeholder={t('medications.frequency.placeholder')}
                keyboardType="numeric"
                value={medication.frequency}
                onChangeText={(text) =>
                  updateMedication(index, "frequency", text)
                }
              />
            </View>

            <View style={[styles.formField, styles.unitField]}>
              <Text style={styles.fieldLabel}>{t('medications.unit.label')}</Text>
              {renderFrequencyUnitDropdown(index)}
            </View>
          </View>
        </View>
      ))}

      <TouchableOpacity style={styles.addButton} onPress={addMedication}>
        <Plus size={20} color={colors.primary} />
        <Text style={styles.addButtonText}>{t('medications.addButton')}</Text>
      </TouchableOpacity>

      {formData.medications.length === 1 &&
        formData.medications[0].name.toLowerCase() !== "no medications" && (
          <TouchableOpacity
            style={styles.noMedicationsButton}
            onPress={handleNoMedications}
          >
            <Text style={styles.noMedicationsText}>
              {t('medications.noMedicationsButton')}
            </Text>
          </TouchableOpacity>
        )}

      {/* Calendar Modal */}
      <Modal
        visible={calendarVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeCalendar}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            {...panResponder.panHandlers}
            style={[
              styles.calendarContainer,
              {
                transform: [
                  { translateY: translateY },
                  { scale: scaleValue }
                ],
                opacity: opacityValue
              }
            ]}
          >
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>{t('medications.calendar.title')}</Text>
              <TouchableOpacity
                onPress={closeCalendar}
                style={styles.closeButton}
              >
                <X size={20} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>
            <CalendarPicker
              onDayPress={handleDateSelect}
              maxDate={new Date().toISOString().split("T")[0]}
              theme={{
                backgroundColor: colors.white,
                calendarBackground: colors.white,
                textSectionTitleColor: theme.colors.gray[600],
                selectedDayBackgroundColor: colors.primary,
                selectedDayTextColor: colors.white,
                todayTextColor: colors.primary,
                dayTextColor: colors.black,
                textDisabledColor: theme.colors.gray[300],
                arrowColor: colors.primary,
                monthTextColor: colors.black,
                indicatorColor: colors.primary,
              }}
            />
          </Animated.View>
        </View>
      </Modal>
    </ScrollView>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   content: {
//     padding: scale(16),
//   },
//   iconContainer: {
//     width: scale(64),
//     height: scale(64),
//     borderRadius: scale(32),
//     backgroundColor: colors.primary + "20",
//     alignItems: "center",
//     justifyContent: "center",
//     marginBottom: verticalScale(24),
//     alignSelf: "center",
//   },
//   title: {
//     fontSize: moderateScale(22),
//     fontWeight: "700",
//     color: theme.colors.gray[800],
//     marginBottom: verticalScale(8),
//     textAlign: "center",
//   },
//   subtitle: {
//     fontSize: moderateScale(14),
//     color: theme.colors.gray[600],
//     marginBottom: verticalScale(32),
//     textAlign: "center",
//   },
//   medicationItem: {
//     marginBottom: verticalScale(24),
//     backgroundColor: theme.colors.gray[50],
//     borderRadius: scale(12),
//     padding: scale(14),
//     borderWidth: 1,
//
