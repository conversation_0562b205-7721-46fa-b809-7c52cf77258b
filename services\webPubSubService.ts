import { WebPubSubClient } from "@azure/web-pubsub-client";
import logger from '@/utils/logger/logger';
import axiosInstance from './axiosInstance';
import axios from 'axios'; // For type checking
import { useAuthStore } from '@/store/auth-store';
import { getEnvironmentVariable } from "@/utils/getEnvironmentVariable";
import { trackOperation, trackEvent, OperationStatus, EventType } from '@/utils/mixpanel/mixpanel-utils';
import { useCallback, useRef, useMemo } from 'react';

const trackSystemEvent = (event: string, properties?: Record<string, any>) => {
  trackEvent(event, properties, EventType.INTERNAL_OPERATION);
}; 

// Safely get environment variables with type checking
const getEnvVar = (name: "GATEKEEPER_URL" | "TENANT" | "MIXPANEL_TOKEN"): string => {
  try {
    const value = getEnvironmentVariable(name);
    if (!value) {
      throw new Error(`Environment variable ${name} is empty or undefined`);
    }
    return value;
  } catch (error) {
    logger.error(`Failed to get environment variable ${name}:`, error);
    throw error;
  }
};

let client: WebPubSubClient | null = null;

// Track if we've already set up event listeners to avoid duplicates
let eventListenersRegistered = false;

export const initializeWebPubSub = async (): Promise<WebPubSubClient> => {
  //throw new Error('WebPubSub: initializeWebPubSub called');
  logger.info('WebPubSub: Initializing connection');
  trackOperation('WebPubSub Connection', OperationStatus.INITIATED, {
    action: 'initialize'
  });
  
  try {
    // If we already have a connected client, return it
    if (client && isWebPubSubConnected()) {
      logger.info('WebPubSub: Already connected, reusing existing client');
      trackSystemEvent('WebPubSub Client Reused', {
        connectionState: (client as any)._state
      });
      return client;
    }
    
    if (client) {
      logger.info('WebPubSub: Closing existing connection');
      trackOperation('WebPubSub Connection', OperationStatus.INITIATED, {
        action: 'close_existing',
        previousState: (client as any)._state
      });
      await client.stop();
      client = null;
      // Reset event listeners flag when we close the connection
      eventListenersRegistered = false;
    }
    
    // Create new client
    client = new WebPubSubClient({
      getClientAccessUrl: async () => {
        let tenant: string;
        let BASE_URL: string;

        try {
          tenant = getEnvVar("TENANT");
        } catch (error) {
          tenant = "august"; // fallback
          logger.error('WebPubSub: Failed to get TENANT, using default:', error);
        }

        try {
          BASE_URL = getEnvVar("GATEKEEPER_URL");
        } catch (error) {
          logger.error('WebPubSub: Failed to get GATEKEEPER_URL:', error);
          throw new Error("GATEKEEPER_URL is not properly configured");
        }
        
        const negotiateEndpoint = `${BASE_URL}/auth/${tenant}/negotiate-azure-token`;
        logger.info(`Fetching client access URL from ${negotiateEndpoint}`);
        trackOperation('WebPubSub Token Fetch', OperationStatus.INITIATED, {
          endpoint: negotiateEndpoint
        });
        
        try {
          const response = await axiosInstance.get(negotiateEndpoint);
          logger.info(`WebPubSub: Fetch response status:`, response.status);
          
          // Axios automatically throws for non-2xx responses, so we don't need to check response.ok
          const data = response.data;
          trackOperation('WebPubSub Token Fetch', OperationStatus.SUCCESS, {
            status: response.status,
            hasUrl: !!data.url
          });
          return data.url;
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const errorText = error.response?.data || error.message;
            
            if (error.response?.status !== 401) {
            
            logger.error(`WebPubSub: Failed to fetch client access URL: ${error.response?.status} ${error.response?.statusText}`, errorText);
            trackOperation('WebPubSub Token Fetch', OperationStatus.FAILURE, {
              status: error.response?.status,
              statusText: error.response?.statusText,
              message: error.message
            });}
            throw new Error(`Failed to fetch client access URL: ${error.response?.status} ${error.response?.statusText}`);
          } else {
            
            logger.error('WebPubSub: Error fetching client access URL:', error);
            trackOperation('WebPubSub Token Fetch', OperationStatus.FAILURE, {
              error: error instanceof Error ? error.message : String(error)
            });
            throw error;
          }
        }
      }
    });

    // Only set up event listeners if they haven't been registered yet
    if (!eventListenersRegistered) {
      logger.info('WebPubSub: Setting up event listeners');
      
      // Set up event listeners
      client.on("connected", () => {
        logger.info('WebPubSub: Connected successfully');
        logger.info('WebPubSub connected successfully');
        trackSystemEvent('WebPubSub State Change', {
          state: 'connected'
        });
      });

      client.on("disconnected", () => {
        logger.info('WebPubSub: Disconnected');
        trackSystemEvent('WebPubSub State Change', {
          state: 'disconnected'
        });
      });

      client.on("server-message", (e: any) => {
        logger.info(`WebPubSub: Received server message:`, e.message.data);
        trackSystemEvent('WebPubSub Message Received', {
          hasData: !!e.message.data
        });
        // This event will be handled by the chat store
      });
      
      eventListenersRegistered = true;
      logger.info('WebPubSub: Event listeners registered');
    } else {
      logger.info('WebPubSub: Event listeners already registered, skipping');
    }

    // Start the connection
    logger.info('WebPubSub: Starting connection');
    trackOperation('WebPubSub Connection', OperationStatus.INITIATED, {
      action: 'start'
    });
    await client.start();
    logger.info('WebPubSub: Client started successfully');
    trackOperation('WebPubSub Connection', OperationStatus.SUCCESS, {
      action: 'initialize'
    });
    
    return client;
  } catch (error : any) {
    const message = String(error?.message || '');
    const is401 = message.includes('401');

    if (!is401) {
      logger.error('WebPubSub: Error initializing client:', JSON.stringify(error));
    }
    throw error;
  }
};

export const getWebPubSubClient = (): WebPubSubClient | null => {
  return client;
};

export const isWebPubSubConnected = (): boolean => {
  // Check if client exists and is connected
  if (!client) {
    logger.info('WebPubSub: Client does not exist');
    return false;
  }
  
  // The WebPubSubClient doesn't have a direct way to check connection status
  // We'll use a property that's set in our event handlers
  const connectionState = (client as any)._state;
  logger.info('CONNECTED STATE IS', connectionState);
  const isConnected = connectionState === 'Connected'; 
  
  logger.info(`WebPubSub: Connection status check 2 - ${isConnected ? 'connected' : 'disconnected'}`);
  return isConnected;
};

export const disconnectWebPubSub = async () => {
  logger.info(' THE CLIENT IN WEB PUB SUB IS', client)
  if (client) {
    logger.info('WebPubSub: Disconnecting client');
    trackOperation('WebPubSub Connection', OperationStatus.INITIATED, {
      action: 'manual_disconnect'
    });
    await client.stop();
    client = null;
    // Reset event listeners flag when we disconnect
    eventListenersRegistered = false;
    logger.info('WebPubSub: Client disconnected');
    logger.info('WebPubSub client disconnected');
    trackOperation('WebPubSub Connection', OperationStatus.SUCCESS, {
      action: 'manual_disconnect'
    });
  }
};

// Track if reconnection handlers are already set up
let reconnectionHandlersRegistered = false;

// Simplified reconnection logic
export const setupReconnection = (client: WebPubSubClient): void => {
  // Only set up reconnection handlers once
  if (reconnectionHandlersRegistered) {
    logger.info('WebPubSub: Reconnection handlers already registered, skipping');
    return;
  }
  
  logger.info('WebPubSub: Setting up reconnection handlers');
  
  // We'll use a simpler approach - just restart the connection if it fails
  // Keep track of connection state
  let isConnected = false;
  
  client.on("connected", () => {
    isConnected = true;
    logger.info('WebPubSub: Connected successfully (reconnection handler)');
    logger.info('WebPubSub connected successfully');
    trackSystemEvent('WebPubSub State Change', {
      state: 'connected',
      context: 'reconnection_handler'
    });
  });
  
  client.on("disconnected", () => {
    isConnected = false;
    logger.info('WebPubSub: Disconnected (reconnection handler)');
    logger.info('WebPubSub disconnected');
    trackSystemEvent('WebPubSub State Change', {
      state: 'disconnected',
      context: 'reconnection_handler'
    });

    // Check if user is authenticated before attempting to reconnect
    const isAuthenticated = useAuthStore.getState().isAuthenticated;
    
    if (!isAuthenticated) {
      logger.info('WebPubSub: User is not authenticated, skipping reconnection');
      logger.info('User is not authenticated, skipping WebPubSub reconnection');
      trackSystemEvent('WebPubSub Reconnection Skipped', {
        reason: 'user_not_authenticated'
      });
      return; // Skip reconnection if user is not authenticated
    }
    
    // Attempt to reconnect with exponential backoff
    let attempts = 0;
    const maxAttempts = 5;
    const backoffTime = 1000; // Start with 1 second
    
    const attemptReconnect = async () => {
      // Check authentication state again before each reconnection attempt
      const isStillAuthenticated = useAuthStore.getState().isAuthenticated;
      
      if (!isStillAuthenticated) {
        logger.info('WebPubSub: User is no longer authenticated, aborting reconnection');
        logger.info('User is no longer authenticated, aborting WebPubSub reconnection');
        trackSystemEvent('WebPubSub Reconnection Aborted', {
          reason: 'user_no_longer_authenticated'
        });
        return;
      }
      if (attempts >= maxAttempts || isConnected) {
        if (!isConnected) {
          logger.error(`Failed to reconnect after ${maxAttempts} attempts`);
          trackOperation('WebPubSub Reconnection', OperationStatus.FAILURE, {
            reason: 'max_attempts_reached',
            attempts: attempts,
            maxAttempts: maxAttempts
          });
        }
        return;
      }
      
      attempts++;
      const delay = backoffTime * Math.pow(2, attempts - 1); // Exponential backoff
      
      logger.info(`WebPubSub: Reconnection attempt ${attempts}/${maxAttempts} in ${delay}ms`);
      logger.info(`Reconnection attempt ${attempts}/${maxAttempts} in ${delay}ms`);
      trackSystemEvent('WebPubSub Reconnection Scheduled', {
        attempt: attempts,
        maxAttempts: maxAttempts,
        delayMs: delay
      });
      
      setTimeout(async () => {
        if (isConnected) return; // Already reconnected

        // Check authentication state again right before reconnecting
        const isStillAuthenticatedBeforeReconnect = useAuthStore.getState().isAuthenticated;
        if (!isStillAuthenticatedBeforeReconnect) {
          logger.info('WebPubSub: User is no longer authenticated, aborting reconnection attempt');
          logger.info('User is no longer authenticated, aborting WebPubSub reconnection attempt');
          trackSystemEvent('WebPubSub Reconnection Attempt Aborted', {
            reason: 'user_no_longer_authenticated',
            attempt: attempts
          });
          return;
        }
        
        try {
          trackOperation('WebPubSub Reconnection', OperationStatus.INITIATED, {
            attempt: attempts,
            maxAttempts: maxAttempts
          });
          await client.start();
          logger.info('WebPubSub: Reconnected successfully');
          logger.info('WebPubSub reconnected successfully');
          isConnected = true;
          trackOperation('WebPubSub Reconnection', OperationStatus.SUCCESS, {
            attempt: attempts,
            totalAttempts: attempts
          });
        } catch (error) {
          if (attempts < maxAttempts) {
            trackOperation('WebPubSub Reconnection Attempt', OperationStatus.FAILURE, {
              attempt: attempts,
              willRetry: true,
              error: error instanceof Error ? error.message : String(error)
            });
          } else {
            trackOperation('WebPubSub Reconnection', OperationStatus.FAILURE, {
              attempt: attempts,
              willRetry: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
          attemptReconnect();
        }
      }, delay);
    };
    
    attemptReconnect();
  });
  
  reconnectionHandlersRegistered = true;
  logger.info('WebPubSub: Reconnection handlers registered');
};

// Custom hook for using WebPubSub in components
export const useWebPubSub = () => {
  // Use refs to maintain references across renders
  const clientRef = useRef<WebPubSubClient | null>(null);
  
  // Memoized function to initialize the connection
  const initialize = useCallback(async () => {
    try {
      const newClient = await initializeWebPubSub();
      clientRef.current = newClient;
      setupReconnection(newClient);
      return newClient;
    } catch (error : any) {
      if ( error?.response?.status !== 401)
      {
        logger.error('Error in useWebPubSub initialize:', error);
      }
      throw error;
    }
  }, []);
  
  // Memoized function to disconnect
  const disconnect = useCallback(async () => {
    await disconnectWebPubSub();
    clientRef.current = null;
  }, []);
  
  // Memoized function to check connection status
  const isConnected = useCallback(() => {
    return isWebPubSubConnected();
  }, []);
  
  // Memoized function to get the client
  const getClient = useCallback(() => {
    return getWebPubSubClient();
  }, []);
  
  // Return memoized API
  return useMemo(() => ({
    initialize,
    disconnect,
    isConnected,
    getClient
  }), [initialize, disconnect, isConnected, getClient]);
};
