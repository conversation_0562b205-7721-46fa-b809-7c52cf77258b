import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import logger from '@/utils/logger/logger';
import { getEnvironmentVariable } from '@/utils/getEnvironmentVariable';
import { trackUserInteraction, trackOperation, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';
import * as Notifications from 'expo-notifications';
import DeviceInfo from 'react-native-device-info';
import i18n from '@/app/i18n';

async function getDeviceId(): Promise<string> {
  try {
    const deviceId = await DeviceInfo.getUniqueId();
    return deviceId;
  } catch (error) {
    logger.error('Error getting device ID:', error);
    return 'unknown-device';
  }
}

// Store OTP request IDs temporarily
let otpRequestIds: Record<string, string> = {};

const MAX_RETRIES = 1;
const RETRY_DELAY_MS = 1000;

export const requestOtp = async (phone: string): Promise<{ success: boolean; error?: string; status?: number }> => {
  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      logger.info(`Requesting OTP for phone: ${phone} (Attempt ${attempt})`);

      const requestUrl = `${getEnvironmentVariable("GATEKEEPER_URL", phone)}/auth/${getEnvironmentVariable("TENANT")}/request-otp`;
      logger.debug('Request URL:', requestUrl);

      const headers = {
        'Content-Type': 'application/json',
        'x-device-id': await getDeviceId(),
      };      
      const channel = attempt === 1 ? "SMS" : "WHATSAPP"; 
      
      const requestBody = {
        phoneNumber: phone,
        expiry: 600, // 10 minutes expiry
        otpLength: 6,
        channel, // dynamically chosen channel
        metadata: {
          app: "August"
        }
      };

      // Log all request details
      logger.debug('Request details:', {
        url: requestUrl,
        method: 'POST',
        headers: headers,
        body: requestBody
      });
      const response = await fetch(requestUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      });

      // Log response details before processing
      logger.debug('Response details:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      logger.debug('OTP request response status:', response.status);

      // Handle rate limit (429) response
      if (response.status === 429) {
        const retryAfterSecs = parseInt(response.headers.get('retry-after') || '0');
        const retryAfterMins = Math.ceil(retryAfterSecs / 60);
        logger.info('Rate limit exceeded:', {
          status: response.status,
          retryAfterSecs,
          retryAfterMins
        });
        return {
          success: false,
          error: `Too many attempts. Please try again after ${retryAfterMins} minute${retryAfterMins > 1 ? 's' : ''}.`,
          status: response.status
        };
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        logger.error('Non-JSON response received:', contentType);
        const text = await response.text();
        logger.error('Response text:', text.substring(0, 200) + '...');

        if (response.status === 404) {
          return {
            success: false,
            error: 'API endpoint not found. Please check the API URL configuration.',
            status: response.status
          };
        }

        throw new Error('Server returned an invalid response');
      }

      const data = await response.json();
      logger.info('OTP request response:', response);
      
      if (!response.ok) {
        logger.error('Failed to request OTP for phone:', phone , JSON.stringify(data));
        throw new Error(data.error || 'Failed to request OTP');
      }

      if (data.requestId) {
        otpRequestIds[phone] = data.requestId;
        logger.debug('Stored requestId for phone:', phone);
      } else {
        throw new Error('No requestId in response');
      }

      return { success: true, status: response.status };

    } catch (error) {
      logger.info(`Error requesting OTP (Attempt ${attempt}):`, error);

      if (attempt < MAX_RETRIES) {
        logger.info(`Retrying OTP request in ${RETRY_DELAY_MS}ms...`);
        await new Promise(res => setTimeout(res, RETRY_DELAY_MS));
      } else {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to request OTP after multiple attempts. Please try again later.'
        };
      }
    }
  }

  // Should never reach here
  return { success: false, error: 'Unexpected error' };
};


export const verifyOtp = async (phone: string, otp: string): Promise<{ 
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
  error?: string 
}> => {
  try {
    logger.info('Verifying OTP:', { phone, otp });
    
    const requestId = otpRequestIds[phone];
    if (!requestId) {
      logger.error('No requestId found for phone:', phone);
      return { 
        success: false, 
        error: 'OTP session expired. Please request a new OTP.' 
      };
    }
    
    // Log the full URL for debugging
    const requestUrl = `${getEnvironmentVariable("GATEKEEPER_URL", phone)}/auth/${getEnvironmentVariable("TENANT")}/verify-otp`;
    logger.debug('Request URL:', requestUrl);
    const prefLanguage = i18n.language || 'en';
    const headers = {
      'Content-Type': 'application/json',
      'x-device-id': await getDeviceId(),
    };    
    
    const requestBody = {
      requestId: requestId,
      otp: otp, 
      phone: phone,
      source: "MOBILE", 
      languageCode: prefLanguage
    };
    
    logger.debug('Request body:', requestBody);
    
    const response = await fetch(requestUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });
    
    // Log the raw response for debugging
    logger.debug('OTP verification response status:', response.status);
    
    // Check if the response is JSON
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      logger.error('Non-JSON response received:', contentType);
      const text = await response.text();
      logger.error('Response text:', text.substring(0, 200) + '...');
      
      // If we got a 404, the endpoint is wrong
      if (response.status === 404) {
        
        return { 
          success: false, 
          error: 'API endpoint not found. Please check the API URL configuration.' 
        };
      }
      
      
      return { 
        success: false, 
        error: 'Server returned an invalid response. Please try again later.' 
      };
    }

    const data = await response.json();
    
    if (!response.ok) {
      logger.error('OTP Verification failed', JSON.stringify(data.message))
      
      return { 
        success: false, 
        error: data.message || 'Invalid OTP' 
      };
    }
    // Clean up the stored requestId
    delete otpRequestIds[phone];
    logger.debug('OTP verification response details:', {
      success: data.success,
      accessToken: data.accessToken ? `${data.accessToken.substring(0, 10)}...` : undefined, // Log partial token for security
      refreshToken: data.refreshToken ? 'Present (hidden for security)' : undefined,
      user: data.user,
      statusCode: response.status,
      headers: Object.fromEntries([...response.headers.entries()])
    });
    
    // Return the tokens and user info from the API response
    return { 
      success: true,
      accessToken: data.accessToken,
      refreshToken: data.refreshToken,
      user: data.user
    };
  } catch (error) {
    logger.error('Error verifying OTP:', error);
    
    return { 
      success: false, 
      error: 'Network error. Please try again.' 
    };
  }
};

export const refreshAccessToken = async (refreshToken: string): Promise<{
  success: boolean;
  accessToken?: string;
  status?: number;
  error?: string;
}> => {
  try {
    logger.info('Refreshing access token');
    trackOperation('Token Refresh', OperationStatus.INITIATED);
    
    const requestUrl = `${getEnvironmentVariable("GATEKEEPER_URL")}/auth/${getEnvironmentVariable("TENANT")}/refresh-token`;
    logger.debug('Request URL:', requestUrl);
    
    const headers = {
      'Authorization': `Bearer ${refreshToken}`,
      'Content-Type': 'application/json'
    };
    
    const response = await fetch(requestUrl, {
      method: 'POST',
      headers: headers
    });
    
    logger.debug('Token refresh response status:', response.status);
    trackOperation('Token Refresh API Call', OperationStatus.INITIATED, { 
      status: response.status
    });
    
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      logger.error('Non-JSON response received:', contentType);
      trackOperation('Token Refresh', OperationStatus.FAILURE, { 
        reason: 'Invalid response format',
        contentType: contentType || 'none'
      });
      return {
        success: false,
        status: response.status,
        error: 'Server returned an invalid response'
      };
    }
    
    const data = await response.json();
    logger.info('Token refresh response:', data);
      
    if (!response.ok) {

      trackOperation('Token Refresh', OperationStatus.FAILURE, { 
        reason: data.message || 'API error',
        status: response.status
      });
      return {
        success: false,
        status: response.status,
        error: data.message || 'Failed to refresh token'
      };
    }
    
    trackOperation('Token Refresh', OperationStatus.SUCCESS, {
      hasAccessToken: !!data.accessToken
    });
    
    return {
      success: true,
      status: response.status,
      accessToken: data.accessToken
    };
  } catch (error) {
    logger.error('Error refreshing token:', error);
    trackOperation('Token Refresh', OperationStatus.FAILURE, { 
      reason: 'Network error',
      error: error instanceof Error ? error.message : String(error)
    });
    return {
      success: false,
      status: 500,
      error: 'Network error. Please try again.'
    };
  }
};

export const logoutFromServer = async (accessToken: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    logger.info('Logging out from server');
    trackOperation('Logout', OperationStatus.INITIATED);
  
    const requestUrl = `${getEnvironmentVariable("GATEKEEPER_URL")}/auth/${getEnvironmentVariable("TENANT")}/logout`;
    logger.debug('Logout URL:', requestUrl);
    
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    };

    // Retrieve the Expo push token directly by calling getExpoPushTokenAsync()
    let expoPushToken;
    try {
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: `${getEnvironmentVariable("PROJECT_ID")}`
      });
      expoPushToken = tokenData.data;
      logger.info('Expo push token:', expoPushToken);
    } catch (tokenError) {
      logger.error('Error fetching push token:', tokenError);
    }

    const requestBody = {
      ...(expoPushToken ? { expoPushToken } : {})
    };

    logger.debug('Logout request body:', requestBody);
    
    const response = await fetch(requestUrl, {
      method: 'POST',
      headers: headers, 
      body: JSON.stringify(requestBody)
    });
    logger.debug('Logout response status:', response.status);
    trackOperation('Logout API Call', OperationStatus.INITIATED, { 
      status: response.status
    });
    
    // Even if the server response is not successful, we consider the logout successful
    // from the client perspective as we'll clear the tokens anyway
    trackOperation('Logout', OperationStatus.SUCCESS, {
      serverSuccess: response.ok
    });
    
    return { success: true };
  } catch (error) {
    logger.error('Error during logout:', error);
    trackOperation('Logout API Call', OperationStatus.FAILURE, { 
      error: error instanceof Error ? error.message : String(error)
    });
    // Still return success as we'll clear tokens locally
    trackOperation('Logout', OperationStatus.SUCCESS, {
      serverSuccess: false,
      reason: 'Network error'
    });
    
    return { success: true };
  }
};