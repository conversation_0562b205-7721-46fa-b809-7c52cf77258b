{"common": {"error": "Lỗi", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "sometimes": "<PERSON><PERSON><PERSON> khi", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "loading": "<PERSON><PERSON> tả<PERSON>...", "version": "v0.0.1.7"}, "welcome": "<PERSON><PERSON><PERSON> nhập để bắt đầu trò chuyện với August", "notFound": {"title": "Ối!", "message": "<PERSON><PERSON><PERSON> hình này không tồn tại.", "goHome": "Trở về màn hình ch<PERSON>h!"}, "library": {"title": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> khỏe"}, "specialists": {"title": "Chuyên gia", "description": "<PERSON><PERSON> vấn với các chuyên gia y tế chuyên khoa để được giải đáp các thắc mắc về sức khỏe cụ thể hơn. Chọn chuyên gia bên dưới:", "generalPhysician": {"title": "<PERSON><PERSON><PERSON> sĩ đa khoa", "description": "<PERSON><PERSON><PERSON> với các vấn đề sức khỏe chung và chăm sóc ban đầu."}, "nutritionist": {"title": "<PERSON><PERSON><PERSON>n gia dinh dưỡng", "description": "<PERSON><PERSON> được tư vấn về chế độ ăn uống, dinh dưỡng và quản lý cân nặng."}, "cardiologist": {"title": "<PERSON><PERSON><PERSON> sĩ tim mạch", "description": "<PERSON><PERSON><PERSON> với các vấn đề liên quan đến tim và sức khỏe tim mạch."}, "neurologist": {"title": "<PERSON><PERSON><PERSON> s<PERSON> thần kinh", "description": "<PERSON><PERSON>i với các vấn đề về não, tủy sống và hệ thần kinh."}, "oncologist": {"title": "<PERSON><PERSON><PERSON> sĩ ung thư", "description": "<PERSON><PERSON><PERSON> với các vấn đề và phương pháp điều trị liên quan đến ung thư."}, "endocrinologist": {"title": "<PERSON><PERSON><PERSON> s<PERSON> nội tiết", "description": "Đối với các rối loạn liên quan đến hormone và quản lý bệnh tiểu đường."}, "dermatologist": {"title": "<PERSON><PERSON><PERSON> li<PERSON>", "description": "<PERSON><PERSON>i với các tình trạng về da, tóc và móng."}, "psychiatrist": {"title": "<PERSON><PERSON><PERSON> sĩ tâm thần", "description": "<PERSON><PERSON><PERSON> với các vấn đề sức khỏe tâm thần và hạnh phúc tâm lý."}}, "profile": {"title": "<PERSON><PERSON> sơ", "defaultName": "<PERSON><PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "saving": "<PERSON><PERSON> l<PERSON>...", "noPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> có số điện thoại", "loggingOut": "<PERSON><PERSON> đăng xuất...", "about": {"title": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "description": "<PERSON><PERSON><PERSON> hiểu thêm về August"}, "whatsapp": {"title": "WhatsApp", "description": "<PERSON>r<PERSON> ch<PERSON> với August trên <PERSON>pp"}, "refer": {"title": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "description": "Thích August? Chia sẻ với bạn bè của bạn"}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON> t<PERSON>", "description": "<PERSON><PERSON><PERSON> tôi rất tiếc khi thấy bạn rời đi"}, "logout": {"title": "<PERSON><PERSON><PERSON> xu<PERSON>", "description": "Hẹn gặp lại sớm. <PERSON><PERSON><PERSON> tôi sẽ nhớ bạn"}, "shareMessage": "👋Chào, Hãy xem ứng dụng tuyệt vời này mà mình đang sử dụng!\n\n\n\n➡️Mình đã sử dụng August để nhận được thông tin và hướng dẫn sức khỏe nhanh chóng, đáng tin cậy. Giống như có một bác sĩ trong túi của bạn! Hãy xem tại đây:", "error": {"loadFailed": "<PERSON><PERSON><PERSON><PERSON> tải đư<PERSON>c dữ liệu người dùng", "fetchError": "<PERSON>ã xảy ra lỗi khi lấy dữ liệu người dùng", "updateNameFailed": "<PERSON><PERSON><PERSON><PERSON> cập nhật đ<PERSON><PERSON><PERSON> tên", "updateNameError": "<PERSON><PERSON> x<PERSON>y ra lỗi khi cập nhật tên", "loadFoodData": "<PERSON><PERSON><PERSON><PERSON> tải đư<PERSON><PERSON> dữ liệu thực phẩm", "logoutError": "Lỗi trong quá trình đăng xuất:", "shareError": "Lỗi chia sẻ tin nhắn:"}}, "error": {"title": "<PERSON><PERSON> lỗi xảy ra", "checkLogs": "<PERSON><PERSON> lòng kiểm tra nhật ký thiết bị của bạn để biết thêm chi tiết.", "unknown": "Lỗi không xác định", "unknownFile": "<PERSON><PERSON><PERSON> kh<PERSON>ng x<PERSON>c <PERSON>", "unknownLine": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>nh", "unknownColumn": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>nh"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON> quốc gia", "searchCountries": "<PERSON><PERSON><PERSON> kiếm quốc gia", "validation": {"invalidPhone": "<PERSON><PERSON> lòng nhập số điện tho<PERSON>i hợp lệ", "invalidDigits": "<PERSON><PERSON> lòng nhập số điện tho<PERSON><PERSON> hợp lệ (7-15 số)"}}, "header": {"title": "Nhận câu trả lời rõ ràng về các vấn đề sức khỏe của bạn ngay lập tức và riêng tư", "subtitle": "Hướng dẫn chu đáo. Không vội vàng. Không khó hiểu.", "emphasis": "Chỉ cần sự rõ ràng."}, "greeting": "Chào 👋", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "requestOTP": "<PERSON><PERSON><PERSON> c<PERSON>", "otp": {"title": "<PERSON><PERSON><PERSON> kh<PERSON> một lần", "verify": "Xác minh OTP", "sending": "<PERSON><PERSON> g<PERSON>...", "countdown": "<PERSON><PERSON>i lại OTP sau {{countdown}}s", "resend": "Gửi lại OTP", "sentTo": "OTP đã đư<PERSON><PERSON> gửi đến ", "whatsappSuffix": "tr<PERSON><PERSON>"}, "disclaimer": {"prefix": "Bằng cách đăng ký, bạn đồng ý với ", "continuePrefix": "Bằng cách ti<PERSON><PERSON> tụ<PERSON>, bạn đồng ý với ", "termsOfService": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "and": " và ", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "whatsappConsent": ", và đồng ý nhận cập nhật & lời nhắc từ chúng tôi qua WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Chào mừng đến với August!", "buttonText": "<PERSON><PERSON><PERSON> đầu thôi nào"}}, "postAuth": {"step1": {"title": "Chào!", "subtitle": "Tôi là August 👋", "description": "<PERSON><PERSON><PERSON> nghĩ về tôi như một góc nhỏ thoải mái trên thiết bị của bạn, nơi bạn khám phá tất cả những điều bạn tò mò về sức khỏe.", "subdescription": "<PERSON><PERSON> thoải mái hỏi bất cứ điều gì bạn đang nghĩ. Không phán xét, không giới hạn!", "placeholder": "Tôi nên gọi bạn là gì?"}, "step2": {"title": "Chào {{userName}},", "subtitle": "<PERSON><PERSON><PERSON> là những gì tôi có thể làm:", "features": {"health": {"title": "<PERSON><PERSON><PERSON> lời các câu hỏi về", "subtitle": "<PERSON><PERSON><PERSON> khỏe của bạn"}, "nutrition": {"title": "<PERSON>", "subtitle": "Macronutrients"}, "reports": {"title": "<PERSON><PERSON> tích", "subtitle": "Báo cáo"}}}}, "pills": {"thoughtful": "<PERSON>o", "careful": "<PERSON><PERSON><PERSON> thận", "accurate": "<PERSON><PERSON><PERSON>"}, "features": {"symptoms": {"title": "<PERSON><PERSON><PERSON> tra các triệu chứng của b<PERSON>n", "description": "T<PERSON><PERSON> bị buồn nôn trong một tuần nay. Chuyện gì đang xảy ra với tôi vậy?"}, "prescriptions": {"title": "<PERSON><PERSON> tích đơn thuốc của bạn", "description": "<PERSON><PERSON><PERSON> lên và hiểu đơn thu<PERSON>c như một bác sĩ."}, "medicine": {"title": "<PERSON><PERSON><PERSON> hi<PERSON> về thuốc của bạn", "description": "<PERSON><PERSON><PERSON><PERSON>in cho PCOS của tôi có tương tác với thuốc ADHD của tôi không?"}, "plans": {"title": "<PERSON><PERSON><PERSON><PERSON> các kế hoạch cá nhân hóa", "description": "Bạn có thể cho tôi một kế hoạch dinh dưỡng & thể dục để giảm mức HbA1c của tôi không?"}}, "buttons": {"getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "next": "<PERSON><PERSON><PERSON><PERSON> theo"}, "errors": {"nameRequired": "<PERSON><PERSON> lòng nhập tên của bạn"}}, "tabs": {"chat": "<PERSON><PERSON><PERSON>", "discover": "Khám phá", "nutrition": "<PERSON>h dưỡng", "personalize": "Cá nhân hóa"}, "chat": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "me": "<PERSON><PERSON><PERSON>", "augustName": "<PERSON><PERSON><PERSON><PERSON>", "input": {"placeholder": "Hỏi <PERSON>...", "disclaimer": "<PERSON><PERSON><PERSON><PERSON> có thể mắc lỗi. <PERSON>ui lòng xác nhận với bác sĩ"}, "list": {"loadingMessages": "<PERSON><PERSON> tải tin nhắn...", "noMessages": "<PERSON><PERSON><PERSON> có tin nhắn nào. <PERSON><PERSON><PERSON> bắt đầu cuộc trò chuyện!"}, "connection": {"offlineMessage": "<PERSON><PERSON> vẻ như bạn đang ngoại tuyến. Kết nối lại để gửi tin nhắn.", "connecting": "<PERSON><PERSON> kết nối...", "tryAgain": "<PERSON><PERSON><PERSON> lại"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON> l<PERSON>n b<PERSON>o c<PERSON>o", "speakInHindi": "Nói bằng tiếng Hindi", "notFeelingWell": "T<PERSON><PERSON> không khỏe", "whatIsMyBMI": "Chỉ số BMI của tôi là bao nhiêu?", "nutritionAdvice": "<PERSON><PERSON><PERSON> k<PERSON> về dinh dưỡng", "sleepBetter": "<PERSON><PERSON> ngon h<PERSON>n"}, "citations": {"referenceText": "<PERSON><PERSON> biết thêm chi tiết về cuộc trò chuyện nà<PERSON>, vui lòng tham kh<PERSON>o:"}, "actions": {"copiedToClipboard": "Đã sao chép vào clipboard", "copied": "Đã sao chép"}, "share": {"introText": "👋 <PERSON><PERSON>, hãy xem cuộc trò chuyện của tôi với <PERSON> Tám:\n\n", "downloadText": "\n\n➡️Tải xuống <PERSON>háng Tám để trò chuyện với người bạn đồng hành sức khỏe AI thân thiện của bạn:\n"}}, "discover": {"nav": {"title": "Khám phá"}, "categories": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "heartHealth": "<PERSON><PERSON><PERSON> khỏe tim mạch", "nutrition": "<PERSON>h dưỡng", "mentalHealth": "<PERSON><PERSON><PERSON> khỏe tinh thần", "fitness": "<PERSON><PERSON><PERSON> d<PERSON>", "wellness": "<PERSON><PERSON><PERSON> khỏe và thể chất"}, "cards": {"empty": "<PERSON>h<PERSON>ng có thẻ nào khả dụng cho danh mục này"}, "sections": {"features": "<PERSON><PERSON><PERSON>"}, "features": {"healthLibrary": {"title": "<PERSON><PERSON><PERSON> viện sức khỏe", "description": "<PERSON><PERSON><PERSON> cập thông tin y tế đáng tin cậy, ch<PERSON><PERSON> xác và cập nhật hoàn toàn miễn phí."}, "nutritionTracker": {"title": "<PERSON><PERSON><PERSON> cụ theo dõi dinh dưỡng", "description": "Bạn đã bao giờ tự hỏi liệu bạn chỉ cần tải lên ảnh thức ăn của mình và theo dõi tất cả các mục tiêu dinh dưỡng của mình chưa? Tháng Tám có thể làm điều đó!"}, "multilingualSupport": {"title": "Hỗ trợ đa ngôn ngữ", "description": "Bạn có thể giao tiếp với Tháng Tám bằng bất kỳ ngôn ngữ nào bạn cảm thấy thoải mái! Tháng Tám luôn ở đây để lắng nghe, hỗ trợ và phản hồi bạn bất cứ khi nào bạn cần.", "samplePrompt": "Nói bằng tiếng Hindi"}, "labReportAnalysis": {"title": "<PERSON><PERSON> tích báo cáo xét ng<PERSON>m", "description": "<PERSON>hi bạn nói chuyện với Tháng Tám về báo cáo xét nghiệm của mình, bạn sẽ nhận được độ chính xác cao. Th<PERSON>g Tám đã xử lý hơn 4,7 triệu báo cáo với độ chính xác trích xuất sinh học là 98,4%."}}}, "nutrition": {"nav": {"title": "<PERSON>h dưỡng"}, "meals": {"title": "<PERSON><PERSON><PERSON> b<PERSON>a ăn của bạn", "subtitle": "Nhấn để xem hàm lượng macro trong mỗi bữa ăn"}, "upload": {"loading": "<PERSON><PERSON> tả<PERSON>..."}, "defaultFoodName": "<PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON> nay", "unknownTime": "<PERSON>h<PERSON><PERSON> gian không x<PERSON>c đ<PERSON>nh", "calories": "🔥 Calo", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 Carbohydrate", "sugars": "🍬 Đường", "fat": "🥑 <PERSON><PERSON>t béo", "caloriesLabel": "Calo", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "Carbohydrate", "fatLabel": "<PERSON><PERSON><PERSON> b<PERSON>o", "sugarLabel": "Đ<PERSON><PERSON><PERSON>", "tips": "Mẹo:", "macroBreakdown": "Phân tích Macronutrient", "noMacroData": "Không có dữ liệu macronutrient nào khả dụng cho món ăn này.", "disclaimer": "Chỉ để sử dụng cho mục đích giáo dục. <PERSON><PERSON><PERSON> hiểu thêm", "disclaimerLink": "tại đây", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Giới tính của bạn là gì?", "subtitle": "Thông tin này sẽ được sử dụng để hiệu chỉnh kế hoạch tùy chỉnh của bạn.", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "age": {"title": "Tuổi của bạn là bao nhiêu?", "subtitle": "Thông tin này sẽ được sử dụng để tính toán nhu cầu hàng ngày của bạn."}, "measurements": {"title": "Chiều cao & Cân nặng", "subtitle": "<PERSON><PERSON> lòng nhập chiều cao của bạn bằng centimet và cân nặng bằng kilogam."}, "activity": {"title": "<PERSON><PERSON><PERSON> độ hoạt động", "subtitle": "<PERSON>ạn tập thể dục thường xuyên như thế nào?", "none": "<PERSON><PERSON><PERSON><PERSON> tập thể dục", "moderate": "Vừa phải", "high": "<PERSON>"}, "goal": {"title": "<PERSON><PERSON><PERSON> tiêu cân nặng", "subtitle": "Bạn muốn đạt được điều gì?", "increase": "<PERSON><PERSON><PERSON> cân", "maintain": "<PERSON><PERSON><PERSON> c<PERSON>", "decrease": "<PERSON><PERSON><PERSON><PERSON> cân"}, "targetWeight": {"title": "<PERSON><PERSON> nặng mục tiêu", "subtitle": "Cân nặng mục tiêu của bạn là bao nhiêu kilogam?"}, "setup": {"title": "<PERSON><PERSON><PERSON><PERSON> lập kế hoạch của bạn", "subtitle": "<PERSON><PERSON> lòng chờ trong khi chúng tôi chuẩn bị kế hoạch dinh dưỡng của bạn."}, "review": {"title": "<PERSON><PERSON> lại kế hoạch của bạn", "subtitle": "<PERSON>em lại và tùy chỉnh kế hoạch dinh dưỡng của bạn."}, "height": {"label": "<PERSON><PERSON><PERSON> cao (cm)"}, "weight": {"label": "Cân nặng (kg)"}}, "error": {"updateFailed": "<PERSON><PERSON><PERSON> nhật dữ liệu dinh dưỡng không thành công. <PERSON><PERSON> lòng thử lại.", "parsingError": "Lỗi phân tích dữ liệu thực phẩm:", "fetchReportsFailed": "<PERSON><PERSON><PERSON> dữ liệu báo cáo không thành công. <PERSON><PERSON> lòng thử lại.", "missingReportId": "<PERSON> báo c<PERSON>o bị thi<PERSON>u"}}, "personalize": {"nav": {"title": "Cá nhân hóa"}, "button": {"saving": "<PERSON><PERSON>", "review": "<PERSON><PERSON>", "saveNext": "Lưu & Tiếp theo"}}, "basicInfo": {"title": "<PERSON><PERSON><PERSON> cùng tìm hiểu thêm về bạn", "subtitle": "Thông tin này gi<PERSON>p chúng tôi cá nhân hóa các khuyến nghị sức khỏe của bạn", "age": {"question": "Bạn bao nhiêu tuổi?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON>i của bạn"}, "sex": {"question": "Giới tính của bạn là gì?", "placeholder": "<PERSON><PERSON><PERSON> gi<PERSON>i t<PERSON>h của bạn", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "height": {"question": "<PERSON><PERSON>u cao của bạn là bao nhiêu? (cm)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON> cao của bạn"}, "weight": {"question": "Cân nặng của bạn là bao nhiêu? (kg)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> cân nặng của bạn"}}, "lifestyle": {"title": "<PERSON>h<PERSON>i quen lối sống của bạn", "subtitle": "<PERSON><PERSON><PERSON> rõ thói quen hàng ngày của bạn gi<PERSON>p chúng tôi đưa ra lời khuyên tốt hơn", "diet": {"question": "Bạn ăn kiêng theo kiểu nào?", "placeholder": "<PERSON><PERSON>n chế độ ăn của bạn", "vegetarian": "Ăn chay", "nonVegetarian": "Ăn mặn", "vegan": "Ăn chay trường", "pescatarian": "Ăn chay thuần", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Bạn có thường xuyên tập thể dục không?"}, "drinking": {"question": "Bạn có uống rượu không?"}, "smoking": {"question": "Bạn có hút thuốc không?"}, "sleep": {"question": "Bạn ngủ bao nhiêu giờ mỗi đêm?", "value": "{{sleep}} giờ"}, "hydration": {"question": "Bạn uống bao nhiêu cốc nước mỗi ngày?", "value": "{{hydration}} cốc ({{liters}}L)"}}, "allergies": {"title": "Bạn có bị dị ứng gì không?", "subtitle": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> dị <PERSON>ng của bạn gi<PERSON><PERSON> chúng tôi đưa ra những lời khu<PERSON>ên an toàn hơn", "allergyIndex": "Dị ứng {{index}}", "name": {"question": "Bạn bị dị ứng với gì?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> (ví dụ: <PERSON>ạc, Bụi)"}, "severity": {"question": "<PERSON><PERSON><PERSON> độ nghiêm trọng của dị ứng này như thế nào?", "placeholder": "<PERSON><PERSON><PERSON> mức độ nghiêm trọng", "mild": "Nhẹ", "moderate": "<PERSON>rung bình", "severe": "Nặng"}, "addButton": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>h<PERSON>c", "noAllergiesButton": "<PERSON><PERSON><PERSON> không bị dị ứng gì cả"}, "medications": {"title": "<PERSON><PERSON><PERSON><PERSON> và thực phẩm b<PERSON> sung", "subtitle": "<PERSON><PERSON><PERSON> cho chúng tôi biết về bất kỳ loại thuốc hoặc thực phẩm bổ sung nào bạn đang dùng", "medicationIndex": "<PERSON><PERSON><PERSON><PERSON> {{index}}", "name": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên thu<PERSON>c"}, "startDate": {"question": "<PERSON><PERSON>n bắt đầu dùng thuốc từ khi nào?", "placeholder": "<PERSON><PERSON><PERSON>"}, "type": {"label": "<PERSON><PERSON><PERSON>", "shortTerm": "<PERSON><PERSON><PERSON> h<PERSON>n", "longTerm": "<PERSON><PERSON><PERSON> h<PERSON>n"}, "dose": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Số lượng"}, "unit": {"label": "Đơn vị"}, "frequency": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "mỗi ngày", "perWeek": "mỗi tuần", "perMonth": "mỗi tháng", "perYear": "mỗi năm"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "<PERSON><PERSON><PERSON><PERSON>", "tsp": "tsp", "tbsp": "tbsp", "cups": "c<PERSON>c"}, "addButton": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>c", "noMedicationsButton": "<PERSON><PERSON><PERSON> không dùng bất kỳ loại thuốc nào", "calendar": {"title": "<PERSON><PERSON><PERSON> ng<PERSON> b<PERSON><PERSON> đầu"}}, "conditions": {"title": "<PERSON>ình trạng sức khỏe", "subtitle": "<PERSON><PERSON><PERSON> cho chúng tôi biết về bất kỳ tình trạng sức khỏe nào bạn đang mắc phải hoặc đã từng mắc phải trong quá khứ", "conditionIndex": "Tình trạng {{index}}", "name": {"label": "<PERSON><PERSON><PERSON> tình trạng", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tình trạng (ví dụ: <PERSON><PERSON>, v.v.)"}, "since": {"question": "Bạn bị tình trạng này từ khi nào?", "placeholder": "<PERSON><PERSON><PERSON>"}, "current": {"question": "Hiện tại nó có đang gây khó chịu cho bạn không?"}, "medicated": {"question": "Bạn có đang dùng thuốc cho tình trạng này không?"}, "addButton": "<PERSON>hê<PERSON> tình trạng kh<PERSON>c", "noConditionsButton": "<PERSON><PERSON><PERSON> không có bất kỳ tình trạng sức khỏe nào", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "<PERSON><PERSON><PERSON> khỏe sinh sản", "subtitle": "Thông tin này gi<PERSON>p chúng tôi đưa ra các khuyến nghị sức khỏe cá nhân hóa hơn", "menstruation": {"question": "Bạn đã từng có kinh nguyệt chưa?", "detailsTitle": "<PERSON> ti<PERSON> về kinh nguy<PERSON>t", "regularity": {"question": "Chu kỳ kinh nguyệt của bạn đều đặn như thế nào?", "regular": "<PERSON><PERSON>u đặn", "irregular": "<PERSON><PERSON><PERSON><PERSON> đều đặn", "notSure": "<PERSON><PERSON><PERSON><PERSON> chắc chắn"}, "cycleLength": {"label": "<PERSON><PERSON> dài chu kỳ trung bình (ngày)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> độ dài chu kỳ"}, "flowDays": {"label": "<PERSON><PERSON> ng<PERSON>y hành kinh: {{flowDays}}", "min": "1 ngày", "max": "15 ngày"}, "padsPerDay": {"label": "B<PERSON>ng vệ sinh/tampon mỗi ngày: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Bất kỳ triệu chứng nào trong kỳ kinh của bạn?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tri<PERSON><PERSON> ch<PERSON> (ví dụ: chu<PERSON><PERSON> rút, đau đầu)"}}, "childbirth": {"question": "Bạn đã từng sinh con chưa?", "detailsTitle": "<PERSON> tiết về sinh nở", "children": {"label": "Số con"}, "pregnancies": {"label": "Số lần mang thai"}, "complications": {"question": "B<PERSON>t kỳ biến chứng nào trong quá trình mang thai hoặc sinh nở?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> (n<PERSON><PERSON> c<PERSON>)"}}}, "review": {"title": "<PERSON>em lại thông tin của bạn", "subtitle": "<PERSON><PERSON> lòng xem lại thông tin bạn đã cung cấp tr<PERSON><PERSON><PERSON> khi gửi", "sections": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "lifestyle": "<PERSON><PERSON><PERSON>", "allergies": "Dị <PERSON>", "medications": "<PERSON><PERSON><PERSON><PERSON> và thực phẩm b<PERSON> sung", "conditions": "<PERSON><PERSON><PERSON> tr<PERSON>ng b<PERSON>nh", "reproductive": "<PERSON><PERSON><PERSON> khỏe sinh sản", "menstruationDetails": "<PERSON> ti<PERSON> về kinh nguy<PERSON>t", "childbirthDetails": "<PERSON> tiết về sinh nở"}, "fields": {"age": "Tuổi:", "sex": "Gi<PERSON>i tính:", "height": "<PERSON><PERSON><PERSON> cao:", "weight": "Cân nặng:", "diet": "<PERSON><PERSON> độ ăn:", "exercise": "<PERSON>ậ<PERSON> thể dục:", "drinking": "<PERSON><PERSON><PERSON>:", "smoking": "<PERSON><PERSON><PERSON>:", "sleep": "Ngủ:", "hydration": "<PERSON><PERSON><PERSON><PERSON> phần:", "allergyIndex": "Dị ứng {{index}}:", "dose": "<PERSON><PERSON><PERSON>:", "frequency": "<PERSON><PERSON><PERSON> su<PERSON>:", "type": "Loại:", "since": "<PERSON><PERSON> khi nào:", "currentlyActive": "Hiện đang hoạt động:", "takingMedication": "<PERSON><PERSON> dùng thuốc:", "hasMenstruated": "<PERSON><PERSON> từng có kinh nguyệ<PERSON>:", "regularity": "Sự đều đặn:", "cycleLength": "<PERSON><PERSON> dài chu kỳ:", "flowDays": "<PERSON><PERSON> ng<PERSON>y hành <PERSON>h:", "padsPerDay": "Băng vệ sinh/tampon mỗi ngày:", "hasChildbirth": "<PERSON><PERSON> từng sinh con:", "children": "Số con:", "pregnancies": "Số lần mang thai:"}, "notProvided": "<PERSON><PERSON><PERSON> cung cấp", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} giờ mỗi ngày", "hydration": "{{hydration}} cốc ({{liters}}L) mỗi ngày", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} ngày"}, "noData": {"allergies": "<PERSON><PERSON><PERSON> cung cấp thông tin dị ứng", "medications": "<PERSON><PERSON><PERSON> cung cấp thông tin thuốc", "conditions": "<PERSON><PERSON><PERSON> cung cấp thông tin về tình trạng bệnh"}, "submitButton": "<PERSON><PERSON><PERSON> thông tin"}, "success": {"title": "Thông tin đã đư<PERSON><PERSON> cập nhật!", "message": "Cảm ơn bạn đã cung cấp thông tin sức khỏe của mình. Chúng tôi sẽ sử dụng thông tin này để cá nhân hóa trải nghiệm của bạn và đưa ra các khuyến nghị tốt hơn.", "benefits": {"insights": "Thông tin sức khỏe cá nhân hóa", "reminders": "Nhắc nhở dùng thuốc tốt hơn", "recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị sức khỏe phù hợp"}, "continueButton": "<PERSON><PERSON><PERSON><PERSON> tục đến bảng điều khiển"}, "permissions": {"microphonePermissionDenied": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập micro bị từ chối", "microphoneAccessDescription": "August cần t<PERSON>y cập micro của bạn để ghi âm và gửi ghi chú thoại", "permissionDenied": "<PERSON><PERSON><PERSON><PERSON> bị từ chối", "cameraPermissionRequired": "<PERSON><PERSON><PERSON> tôi cần quyền truy cập camera để ứng dụng hoạt động!", "mediaLibraryPermissionRequired": "<PERSON><PERSON>g tôi cần quyền truy cập thư viện phương tiện để ứng dụng hoạt động!"}, "voiceRecording": {"recordingTooLong": "Ghi âm quá dài", "recordingTooLongMessage": "<PERSON>hi âm thoại phải dưới 5 phút. <PERSON><PERSON> lòng ghi lại một tin nhắn ngắn hơn."}, "errors": {"uploadFailed": "<PERSON><PERSON><PERSON> lên thất bại", "voiceUploadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên bản ghi âm thoại.", "voiceRecordingFailed": "<PERSON><PERSON><PERSON> bản ghi âm thoại thất bại", "failedToStopRecording": "<PERSON><PERSON><PERSON><PERSON> thể dừng ghi <PERSON>m", "photoUploadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên <PERSON>.", "failedToTakePhoto": "<PERSON><PERSON><PERSON><PERSON> thể ch<PERSON>p <PERSON>", "imageUploadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên <PERSON>nh: {{fileName}}", "failedToPickImage": "<PERSON><PERSON><PERSON><PERSON> thể chọn <PERSON>nh", "documentUploadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lên tài liệu: {{fileName}}", "failedToPickDocument": "<PERSON><PERSON><PERSON><PERSON> thể chọn tài liệu"}, "audioPlayer": {"downloadingAudio": "<PERSON><PERSON> tải xuống âm thanh...", "loadingAudio": "<PERSON><PERSON> tải âm thanh..."}, "mediaProcessing": {"processingFile": "<PERSON><PERSON><PERSON> lý tệp c<PERSON><PERSON> b<PERSON>n", "uploadingSecuring": "<PERSON><PERSON> tải lên và bảo mật tệp...", "analyzingContent": "<PERSON><PERSON> phân tích nội dung tài liệu...", "extractingInfo": "<PERSON><PERSON> trích xuất thông tin ch<PERSON>h...", "processingInsights": "<PERSON><PERSON> x<PERSON> lý thông tin chi tiết...", "preparingResponse": "<PERSON><PERSON> chu<PERSON>n bị phản hồi chi tiết...", "finalizingResponse": "<PERSON><PERSON> hoàn thiện phản hồi..."}, "attachments": {"voiceMessage": "<PERSON> tho<PERSON>i", "image": "[ẢNH]", "pdf": "[TÀI LIỆU PDF]", "voice": "[GHI CHÚ THOẠI]"}, "pdf": {"loadingPdf": "<PERSON><PERSON> t<PERSON>i PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON> qua, "}, "navbar": {"defaultTitle": "august", "selectedCount": "<PERSON><PERSON> ch<PERSON>n"}, "mediaUpload": {"photoLibrary": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "takePhoto": "<PERSON><PERSON><PERSON>", "chooseFile": "<PERSON><PERSON><PERSON>"}, "comingSoon": {"title": "Sắp ra mắt!", "description": " hiện đang đư<PERSON><PERSON> phát triển. <PERSON><PERSON><PERSON> theo dõi để cập nhật!", "buttonText": "<PERSON>ã hiểu!"}, "clipboard": {"success": "Đã sao chép liên kết vào clipboard"}, "mediaPhotos": {"emptyState": "<PERSON><PERSON>a có mục nào."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON>", "nutrition": {"totalCalories": "Tổng calo", "proteins": "<PERSON><PERSON>", "carbs": "Carbohydrate", "fat": "<PERSON><PERSON><PERSON> b<PERSON>o", "sugars": "Đ<PERSON><PERSON><PERSON>", "fibers": "<PERSON><PERSON><PERSON> x<PERSON>"}}, "reports": {"defaultTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiện", "defaultFoodName": "<PERSON><PERSON>", "defaultName": "<PERSON><PERSON><PERSON> l<PERSON>", "openButton": "Mở trong trình xem bên ngoài", "biomarker": {"headerBiomarker": "Chỉ s<PERSON> sinh học", "headerValue": "<PERSON><PERSON><PERSON> trị", "headerRefRange": "<PERSON><PERSON><PERSON><PERSON> tham chi<PERSON>u", "headerStatus": "<PERSON><PERSON><PERSON><PERSON> thái"}, "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu chỉ số sinh học nào."}, "setup": {"title": "<PERSON><PERSON><PERSON> tôi đang thiết lập mọi thứ cho bạn", "inProgress": "<PERSON><PERSON> xử lý...", "progressMessages": {"0": "<PERSON><PERSON> t<PERSON> toán lư<PERSON>ng calo hàng ngày", "1": "<PERSON><PERSON> tối ưu hóa tỷ l<PERSON>", "2": "<PERSON><PERSON> tạo kế hoạch b<PERSON>a ăn", "3": "<PERSON><PERSON> t<PERSON>h điểm sức khỏe", "4": "<PERSON><PERSON> hoàn thi<PERSON><PERSON> thiết lập"}, "checklistItems": {"0": "<PERSON><PERSON> phân tích dữ liệu sức khỏe của bạn", "1": "<PERSON><PERSON> t<PERSON>h toán kế hoạch dinh dưỡng tối ưu", "2": "<PERSON><PERSON> cá nhân hóa các đề xuất của bạn", "3": "<PERSON><PERSON> tạo gợi ý bữa ăn của bạn", "4": "<PERSON><PERSON> hoàn thi<PERSON>n thiết lập của bạn"}}, "foodEntry": {"emptyState": "Ch<PERSON>a có mục nhập thức ăn nào. Ch<PERSON><PERSON> ảnh bữa ăn của bạn để thêm vào!"}, "nutritionReview": {"congratulations": "<PERSON><PERSON>c mừng!", "subtitle": "<PERSON><PERSON> hoạch dinh dưỡng tùy chỉnh của bạn đã sẵn sàng", "submitButton": "<PERSON><PERSON>t đầu thôi!", "dailyTargetsTitle": "<PERSON><PERSON><PERSON> ti<PERSON>u dinh dưỡng hàng ngày của bạn", "macroLabels": {"calories": "Calo", "carbs": "<PERSON><PERSON>", "protein": "<PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON> b<PERSON>o"}, "recommendations": {"title": "<PERSON><PERSON><PERSON> thế nào để đạt đư<PERSON><PERSON> mục tiêu của bạn:", "healthScores": "Sử dụng điểm số sức khỏe để cải thiện thói quen của bạn", "trackFood": "<PERSON> l<PERSON> thức ăn nạp vào một cách nhất quán", "followCalories": "<PERSON><PERSON><PERSON> tuân theo k<PERSON>n nghị lượng calo hàng ngày của bạn", "balanceMacros": "Cân bằng lượng carbohydrate, protein và chất béo nạp vào."}}, "editModal": {"titlePrefix": "<PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON><PERSON>"}, "processing": {"stages": {"scanning": "<PERSON><PERSON> quét thức ăn...", "identifying": "<PERSON><PERSON> xác định thành phần...", "extracting": "<PERSON><PERSON><PERSON> xuất chất dinh dưỡng...", "finalizing": "<PERSON><PERSON> hoàn tất kết quả..."}, "error": {"defaultMessage": "<PERSON><PERSON><PERSON><PERSON> phát hiện thấy thức ăn", "subtitle": "Thử một góc độ khác"}, "retakeButton": "<PERSON><PERSON><PERSON> để chụp lại <PERSON>nh", "notification": "<PERSON><PERSON>g tôi sẽ thông báo cho bạn khi hoàn tất!"}, "chart": {"title": "<PERSON>h dưỡng theo thời gian", "selectNutrient": "<PERSON><PERSON><PERSON> chất dinh dưỡng:", "emptyState": "<PERSON><PERSON><PERSON> có dữ liệu dinh dưỡng.", "dropdown": {"calories": "Calo", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Mỡ", "sugars": "Đ<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON>", "defaultDate": "<PERSON><PERSON><PERSON> nay", "defaultTime": "<PERSON>h<PERSON><PERSON> gian không x<PERSON>c đ<PERSON>nh", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "error": {"title": "Lỗi", "message": "<PERSON><PERSON><PERSON><PERSON> cập nhật được dữ liệu dinh dưỡng. <PERSON><PERSON> lòng thử lại."}, "nutrition": {"calories": "🔥 Calo", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 Carb", "sugars": "🍬 Đường", "fat": "🥑 Mỡ"}, "macroBreakdown": {"title": "Phân tích Macronutrient", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu về chất dinh dưỡng đa lượng cho mặt hàng thực phẩm này."}, "macroLabels": {"calories": "Calo", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Mỡ", "sugar": "Đ<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Th<PERSON>ng tin chi tiết", "edit": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "saving": "<PERSON><PERSON> l<PERSON>...", "enterValue": "<PERSON><PERSON><PERSON><PERSON> giá trị", "notSet": "Chưa đặt", "age": "<PERSON><PERSON><PERSON>", "heightCm": "<PERSON><PERSON><PERSON> cao (cm)", "weightKg": "Cân nặng (kg)", "targetWeight": "<PERSON><PERSON> nặng mục tiêu", "nutritionTargets": "<PERSON><PERSON><PERSON> ti<PERSON>u dinh dưỡng", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON> b<PERSON>o", "gm": "gm", "editNote": "<PERSON><PERSON><PERSON><PERSON> giá trị hoặc để trống để tự động tính toán.", "autoCalculateNote": "<PERSON><PERSON> đượ<PERSON> tự động tính toán dựa trên dữ liệu của bạn.", "validation": {"ageMin": "<PERSON><PERSON>i phải từ 18 trở lên", "ageMax": "<PERSON><PERSON><PERSON> ph<PERSON>i d<PERSON> 125", "heightMin": "<PERSON><PERSON><PERSON> cao phải tối thiểu 50cm", "heightMax": "<PERSON><PERSON><PERSON> cao phải dưới 250cm", "weightMin": "<PERSON>ân nặng phải tối thiểu 30kg", "weightMax": "Tr<PERSON><PERSON> lư<PERSON>ng phải dưới 500kg", "targetWeightMin": "Cân nặng mục tiêu phải ít nhất 30kg", "targetWeightMax": "Tr<PERSON><PERSON> lư<PERSON><PERSON> mục tiêu phải dưới 500kg", "proteinMin": "Protein phải bằng 0 trở lên", "carbsMin": "Carbohydrate phải bằng 0 trở lên", "fatsMin": "<PERSON><PERSON>t béo phải bằng 0 trở lên"}}, "tracker": {"calories": "Calo", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Mỡ", "excess": "thừa", "remaining": "còn lại"}, "specialistConstants": {"nutritionist": {"name": "<PERSON><PERSON><PERSON>n gia dinh dưỡng", "description": "<PERSON><PERSON><PERSON> khuyên chuyên môn về chế độ ăn uống, dinh dưỡng và thói quen ăn uống lành mạnh", "featureName": "<PERSON><PERSON><PERSON>n gia dinh dưỡng"}, "cardiologist": {"name": "<PERSON><PERSON><PERSON> sĩ tim mạch", "description": "<PERSON><PERSON><PERSON><PERSON> khoa về sức khỏe tim mạch và các bệnh lý tim mạch", "featureName": "<PERSON><PERSON><PERSON>n gia tim mạch"}, "neurologist": {"name": "<PERSON><PERSON><PERSON> s<PERSON> thần kinh", "description": "Tập trung vào các r<PERSON>i lo<PERSON>n về não, tủy sống và hệ thần kinh", "featureName": "<PERSON><PERSON><PERSON>n gia thần kinh"}, "oncologist": {"name": "<PERSON><PERSON><PERSON> sĩ ung thư", "description": "<PERSON><PERSON>ên khoa về chẩn đoán và điều trị ung thư", "featureName": "<PERSON><PERSON><PERSON><PERSON> gia ung thư"}, "endocrinologist": {"name": "<PERSON><PERSON><PERSON> s<PERSON> nội tiết", "description": "Chuyên gia về các bệnh lý nội tiết và rối loạn chuyển hóa", "featureName": "<PERSON><PERSON><PERSON>n gia nội tiết"}}, "discoverCards": {"categories": {"nutrition": "<PERSON>h dưỡng", "heartHealth": "<PERSON><PERSON><PERSON> khỏe tim mạch", "mentalHealth": "<PERSON><PERSON><PERSON> khỏe tinh thần", "fitness": "<PERSON><PERSON><PERSON> d<PERSON>", "wellness": "<PERSON><PERSON><PERSON> khỏe và thể chất"}, "titles": {"vitaminB12Recovery": "<PERSON><PERSON><PERSON> mất bao lâu để hồi phục từ chứng thiếu vitamin B12", "vitaminDeficiencyGanglion": "Thiếu vitamin nào gây ra u nang ganglion", "vitaminDeficiencyHairFall": "Thiếu vitamin nào gây rụng tóc", "vitaminWaters": "Nước vitamin có tốt cho bạn không", "cholesterolHeadaches": "Cholesterol cao có gây đau đầu không", "cholesterolEyes": "<PERSON><PERSON><PERSON> triệu chứng của cholesterol cao có thể nhìn thấy ở mắt là gì", "diabetesHeadaches": "<PERSON><PERSON><PERSON> tiểu đường có thể gây đau đầu không", "chestPainDrinking": "Tại sao ngực đau sau khi uống rượu", "stressDizziness": "<PERSON>ăng thẳng có thể gây chóng mặt không", "bulimiaFace": "Khuôn mặt của chứng cuồng ăn là gì", "kneeTwitch": "Tại sao đầu gối tôi bị giật", "noseTwitching": "Tại sao mũi bị gi<PERSON>t", "piriformisVsSciatica": "<PERSON>ự khác biệt gi<PERSON>a hội chứng Piriformis và đau thần kinh tọa là gì", "shoulderBladePinched": "<PERSON><PERSON><PERSON> thế nào để giải phóng dây thần kinh bị kẹt ở xương bả vai", "shoulderPinched": "<PERSON><PERSON><PERSON> thế nào để giải phóng dây thần kinh bị kẹt ở vai", "meniscusTorn": "<PERSON><PERSON><PERSON> thế nào để chữa lành sụn chêm bị rách một cách tự nhiên", "hydrateQuickly": "<PERSON><PERSON><PERSON> thế nào để cấp nư<PERSON><PERSON> n<PERSON>h chóng", "periodConstipation": "<PERSON><PERSON> bình thường không khi bị táo bón trong kỳ kinh", "acneScars": "<PERSON><PERSON><PERSON> thế nào để loại bỏ sẹo mụn một cách tự nhiên trong vòng một tuần", "perimenopausePregnancy": "Bạn có thể mang thai trong thời kỳ tiền mãn kinh không"}, "descriptions": {"vitaminB12Recovery": "<PERSON><PERSON><PERSON><PERSON> phá thời gian phục hồi đối với thiếu vitamin B12 và các biện pháp hiệu quả để tăng cường năng lượng.", "vitaminDeficiencyGanglion": "<PERSON><PERSON><PERSON><PERSON> phá mối liên hệ giữa thiếu vitamin và sự phát triển của u nang ganglion trong cơ thể.", "vitaminDeficiencyHairFall": "Tìm hiểu về cách thiếu vitamin thiết yếu có thể dẫn đến rụng tóc và những gì bạn có thể làm để ngăn ngừa nó.", "vitaminWaters": "Khám phá những lợi ích và tác hại tiềm tàng của nước vitamin như một phần trong chế độ dinh dưỡng hàng ngày của bạn.", "cholesterolHeadaches": "<PERSON><PERSON>m tra mối liên hệ có thể có giữa mức cholesterol cao và sự khởi phát của chứng đau đầu.", "cholesterolEyes": "T<PERSON>m hiểu cách cholesterol cao có thể biểu hiện ở mắt và các triệu chứng cần chú ý.", "diabetesHeadaches": "Điều tra mối quan hệ giữa bệnh tiểu đường và sự xuất hiện đau đầu trong cuộc sống hàng ngày.", "chestPainDrinking": "<PERSON><PERSON><PERSON><PERSON> phá những lý do dẫn đến đau ngực sau khi uống một số loại đồ uống.", "stressDizziness": "<PERSON><PERSON> sâu vào cách căng thẳng có thể ảnh hưởng đến sự cân bằng và sức khỏe tổng thể của bạn, dẫn đến chóng mặt.", "bulimiaFace": "<PERSON><PERSON><PERSON> các dấu hiệu thể chất của chứng cuồng ăn, bao gồm cả ảnh hưởng đến ngoại hình khuôn mặt.", "kneeTwitch": "Điều tra các nguyên nhân tiềm ẩn đằng sau chứng giật đầu gối không tự chủ và mối liên hệ của nó với căng thẳng hoặc mệt mỏi.", "noseTwitching": "Tìm hiểu về những lý do có thể dẫn đến chứng giật mũi và mối liên hệ của nó với lo lắng hoặc các yếu tố khác.", "piriformisVsSciatica": "So s<PERSON>h các triệu chứng của hội chứng piriformis và đau thần kinh tọa để hiểu rõ hơn về tình trạng của bạn.", "shoulderBladePinched": "<PERSON>h<PERSON><PERSON> phá các kỹ thuật hiệu quả để làm giảm chứng thần kinh bị kẹt ở xương bả vai và khôi phục khả năng vận động.", "shoulderPinched": "T<PERSON>m hiểu các bài tập và động tác kéo giãn đơn giản để giảm bớt tình trạng chèn ép dây thần kinh ở vùng vai.", "meniscusTorn": "<PERSON>h<PERSON><PERSON> phá các phương pháp tự nhiên và bài tập để hỗ trợ quá trình chữa lành sụn chêm bị rách.", "hydrateQuickly": "T<PERSON>m hiểu nh<PERSON>ng cách nhanh chóng và hiệu quả để bù nước và duy trì độ ẩm tối ưu cho cơ thể.", "periodConstipation": "<PERSON><PERSON><PERSON> những lý do dẫn đến táo bón trong thời kỳ kinh nguyệt và tìm hiểu các biện pháp khắc phục tự nhiên.", "acneScars": "<PERSON>h<PERSON>m phá các biện pháp khắc phục tự nhiên và lời khuyên chăm sóc da để làm giảm sự xuất hiện của sẹo mụn nhanh chóng.", "perimenopausePregnancy": "<PERSON><PERSON><PERSON> hiểu về thời kỳ tiền mãn kinh, nh<PERSON><PERSON> cân nhắc về khả năng sinh sản và những gì cần mong đợi trong giai đoạn này của cuộc đời."}}}