{"common": {"error": "<PERSON><PERSON>", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "sometimes": "Ibland", "close": "Stäng", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Spara", "next": "<PERSON><PERSON><PERSON>", "loading": "Laddar...", "version": "v0.0.1.7"}, "welcome": "Logga in för att börja prata med August", "notFound": {"title": "Oj!", "message": "Den här skärmen finns inte.", "goHome": "Gå till startskärmen!"}, "library": {"title": "Hälsobibliotek"}, "specialists": {"title": "<PERSON>er", "description": "Konsultera specialiserade sjukvårdspersonal för mer specifika hälsoproblem. Välj en specialist nedan:", "generalPhysician": {"title": "Allmänläkare", "description": "<PERSON><PERSON><PERSON> all<PERSON> hälsoproblem och primärvård."}, "nutritionist": {"title": "Dietist", "description": "<PERSON><PERSON><PERSON> råd om kost, nä<PERSON> och vik<PERSON>."}, "cardiologist": {"title": "Kardiolog", "description": "<PERSON><PERSON>r hjärtrelaterade problem och kardiovaskulär hälsa."}, "neurologist": {"title": "Neurolog", "description": "<PERSON><PERSON><PERSON> problem med hjärna, ry<PERSON><PERSON><PERSON><PERSON> och nervsystem."}, "oncologist": {"title": "Onkolog", "description": "För cancerrelaterade problem och behandlingar."}, "endocrinologist": {"title": "Endokrinolog", "description": "<PERSON><PERSON>r hormonrelaterade sjukdomar och diabeteshantering."}, "dermatologist": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hud-, hår- och nagelbesv<PERSON>."}, "psychiatrist": {"title": "Psykiater", "description": "För psykisk hälsa och psykologiskt välbefinnande."}}, "profile": {"title": "Profil", "defaultName": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON> ditt namn", "saving": "Sparar...", "noPhoneNumber": "Inget telefonnummer", "loggingOut": "Loggar ut...", "about": {"title": "Om", "description": "<PERSON><PERSON><PERSON> kä<PERSON> b<PERSON>tre"}, "whatsapp": {"title": "WhatsApp", "description": "Chatta med August på WhatsApp"}, "refer": {"title": "Re<PERSON>mmender<PERSON>", "description": "Tyckte du om August? Dela med dina vänner"}, "deleteAccount": {"title": "Ta bort konto", "description": "<PERSON><PERSON> <PERSON>r ledsna att du går"}, "logout": {"title": "Logga ut", "description": "<PERSON>m tillbaka snart. Vi kommer att sakna dig"}, "shareMessage": "👋Hej, kolla in den här fantastiska appen jag använder!\n\n\n\n➡️Jag har använt August för att få snabb och tillförlitlig hälsoinformation och vägledning. Det är som att ha en läkare i fickan! <PERSON><PERSON> in den här:", "error": {"loadFailed": "Misslyckades med att ladda användardata", "fetchError": "Ett fel uppstod vid hämtning av användardata", "updateNameFailed": "<PERSON><PERSON><PERSON><PERSON> med att uppdatera namn", "updateNameError": "Ett fel uppstod vid uppdatering av namn", "loadFoodData": "<PERSON><PERSON><PERSON><PERSON> med att ladda matdata", "logoutError": "Fel vid utloggning:", "shareError": "Fel vid delning av meddelanden:"}}, "error": {"title": "<PERSON><PERSON><PERSON> gick fel", "checkLogs": "Kontrollera enhetens loggar för mer information.", "unknown": "<PERSON><PERSON><PERSON> fel", "unknownFile": "Okänd fil", "unknownLine": "Okänd rad", "unknownColumn": "Okänd kolumn"}, "auth": {"phone": {"selectCountry": "Välj land", "searchCountries": "<PERSON><PERSON><PERSON>", "validation": {"invalidPhone": "<PERSON>e ett giltigt telefonnummer", "invalidDigits": "<PERSON><PERSON> ett giltigt telefonnummer (7-15 siffror)"}}, "header": {"title": "Få klarhet i dina hälsoproblem direkt och privat", "subtitle": "Omtänksam vägledning. Ingen stress. Ingen förvirring.", "emphasis": "<PERSON><PERSON> klar<PERSON>."}, "greeting": "<PERSON><PERSON> 👋", "phoneNumber": "Telefonnummer", "requestOTP": "Begär OTP", "otp": {"title": "Engångskod", "verify": "Verifiera OTP", "sending": "<PERSON><PERSON><PERSON>...", "countdown": "Skicka om OTP om {{countdown}}s", "resend": "Skicka om OTP", "sentTo": "OTP skickad till ", "whatsappSuffix": " via WhatsApp"}, "disclaimer": {"prefix": "Genom att registrera dig godkänner du våra ", "continuePrefix": "Genom att fortsätta godkänner du våra ", "termsOfService": "Användarvillkor", "and": " och ", "privacyPolicy": "Integritetspolicy", "whatsappConsent": ", och samtycker till att ta emot uppdateringar och påminnelser från oss via WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Välkommen till August!", "buttonText": "<PERSON><PERSON><PERSON> oss bö<PERSON>"}}, "postAuth": {"step1": {"title": "Hej!", "subtitle": "Jag är August 👋", "description": "Tänk på mig som den mysiga hörnan på din\nenhet där du utforskar alla dina hälsofrågor.", "subdescription": "<PERSON><PERSON>nn dig fri att fråga vad som helst som du funderar på.\nIngen dömande, inga gränser!", "placeholder": "Vad ska jag kalla dig?"}, "step2": {"title": "Hej {{userName}},", "subtitle": "<PERSON><PERSON>r är vad jag kan göra:", "features": {"health": {"title": "<PERSON><PERSON><PERSON><PERSON> dina", "subtitle": "<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>"}, "nutrition": {"title": "<PERSON><PERSON><PERSON><PERSON> dina", "subtitle": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "Analysera", "subtitle": "Rapporter"}}}}, "pills": {"thoughtful": "Omtänksam", "careful": "Noggrann", "accurate": "Noggrann"}, "features": {"symptoms": {"title": "<PERSON><PERSON> dina symtom", "description": "Jag har mått illa i en vecka. Vad händer med mig?"}, "prescriptions": {"title": "Analysera dina recept", "description": "Ladda upp och förstå recept som en läkare."}, "medicine": {"title": "<PERSON><PERSON>nn till dina mediciner", "description": "Interagerar Metformin för min PCOS med mina ADHD-piller?"}, "plans": {"title": "Få personliga planer", "description": "Kan du ge mig en närings- och träningsplan för att minska mina HbA1c-nivåer?"}}, "buttons": {"getStarted": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>"}, "errors": {"nameRequired": "<PERSON><PERSON> ditt namn"}}, "tabs": {"chat": "<PERSON><PERSON>", "discover": "<PERSON><PERSON><PERSON><PERSON>", "nutrition": "<PERSON><PERSON><PERSON>", "personalize": "Personanpassa"}, "chat": {"nav": {"title": "August"}, "me": "Jag", "augustName": "August", "input": {"placeholder": "<PERSON><PERSON><PERSON> August...", "disclaimer": "August kan göra misstag. Bekräfta med en läkare"}, "list": {"loadingMessages": "Laddar meddelanden...", "noMessages": "Inga meddelanden än. Starta en konversation!"}, "connection": {"offlineMessage": "Det ser ut som att du är offline. Återanslut för att skicka meddelanden.", "connecting": "Ansluter...", "tryAgain": "Försök igen"}, "prompts": {"uploadReport": "Ladda upp rapport", "speakInHindi": "Hindi mein baat karo", "notFeelingWell": "<PERSON><PERSON> mår inte bra", "whatIsMyBMI": "Vad är mitt BMI?", "nutritionAdvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sleepBetter": "<PERSON><PERSON>"}, "citations": {"referenceText": "<PERSON><PERSON>r mer information om denna konversation, vänligen se:"}, "actions": {"copiedToClipboard": "Kopierat till urklipp", "copied": "<PERSON><PERSON><PERSON>"}, "share": {"introText": "👋Hej, kolla in konversationen jag hade med August:\n\n", "downloadText": "\n\n➡️Ladda ner August för att chatta med din vänliga AI-hälsoguide:\n"}}, "discover": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"all": "<PERSON>a", "heartHealth": "HjärtHälsa", "nutrition": "<PERSON><PERSON><PERSON>", "mentalHealth": "<PERSON>", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "cards": {"empty": "Inga kort tillgängliga för denna kate<PERSON>i"}, "sections": {"features": "<PERSON><PERSON><PERSON>"}, "features": {"healthLibrary": {"title": "Hälsobibliotek", "description": "Tillgång till pålitlig och aktuell medicinsk information helt gratis."}, "nutritionTracker": {"title": "<PERSON><PERSON><PERSON> Tracker", "description": "Har du nå<PERSON>sin undrat om du bara kunde ladda upp en bild av din mat och spåra alla dina närings<PERSON>ål? August kan göra just det!"}, "multilingualSupport": {"title": "Flerspr<PERSON><PERSON><PERSON> stöd", "description": "Du kan kommunicera med August på vilket språk du känner dig bekväm med! August finns alltid här för att lyssna, ge stöd och svara när du behöver.", "samplePrompt": "Hindi mein baat karo"}, "labReportAnalysis": {"title": "<PERSON><PERSON><PERSON> av labrapporter", "description": "När du pratar med August om dina labrapporter får du extrem precision. August har bearbetat över 4,7 miljoner rapporter med en noggrannhet för biomarkörutvinning på 98,4%."}}}, "nutrition": {"nav": {"title": "Nutrition"}, "meals": {"title": "<PERSON><PERSON>", "subtitle": "Tryck för att se makronäringsinnehållet i varje måltid"}, "upload": {"loading": "Laddar upp bild..."}, "defaultFoodName": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "unknownTime": "<PERSON><PERSON>nd tid", "calories": "🔥 Kalorier", "proteins": "🥩 Proteiner", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "🍬 So<PERSON>", "fat": "🥑 <PERSON>tt", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "<PERSON><PERSON>", "sugarLabel": "Socker", "tips": "Tips:", "macroBreakdown": "Makronäringsinnehåll", "noMacroData": "Inga makronäringsdata tillgängliga för denna mat<PERSON>.", "disclaimer": "Endast för utbildningsändamål. Lä<PERSON> mer", "disclaimerLink": "<PERSON>är", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Vad är ditt kön?", "subtitle": "Detta används för att kalibrera din anpassade plan.", "male": "Man", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "age": {"title": "Vad är din ålder?", "subtitle": "<PERSON>ta används för att beräkna dina dagliga behov."}, "measurements": {"title": "Längd & Vikt", "subtitle": "Ange din längd i centimeter och vikt i kilogram."}, "activity": {"title": "Aktivitetsnivå", "subtitle": "Hur ofta tränar du?", "none": "Ingen träning", "moderate": "<PERSON><PERSON><PERSON><PERSON>g", "high": "<PERSON><PERSON><PERSON>"}, "goal": {"title": "Viktmål", "subtitle": "Vad vill du uppnå?", "increase": "Öka", "maintain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decrease": "Minska"}, "targetWeight": {"title": "M<PERSON>lvikt", "subtitle": "Vad är din målvikt i kilogram?"}, "setup": {"title": "Inställning av din plan", "subtitle": "Vänta medan vi förbereder din kostplan."}, "review": {"title": "Granska din plan", "subtitle": "Granska och anpassa din kostplan."}, "height": {"label": "Längd (cm)"}, "weight": {"label": "Vikt (kg)"}}, "error": {"updateFailed": "Misslyckades med att uppdatera näringsdata. Försök igen.", "parsingError": "Fel vid parsning av matdata:", "fetchReportsFailed": "Misslyckades med att hämta rapportdata. Försök igen.", "missingReportId": "Rapport-ID saknas"}}, "personalize": {"nav": {"title": "Anpass<PERSON>"}, "button": {"saving": "<PERSON><PERSON>", "review": "Granska", "saveNext": "Spara & Nästa"}}, "basicInfo": {"title": "<PERSON><PERSON><PERSON> oss lära känna dig bättre", "subtitle": "Denna information hjä<PERSON>per oss att anpassa dina hälso rekommendationer", "age": {"question": "Hur gammal är du?", "placeholder": "<PERSON><PERSON>"}, "sex": {"question": "Vad är ditt kön?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>tt kön", "male": "Man", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "height": {"question": "Vad är din längd? (cm)", "placeholder": "<PERSON><PERSON> din längd"}, "weight": {"question": "Vad är din vikt? (kg)", "placeholder": "<PERSON><PERSON> din vikt"}}, "lifestyle": {"title": "<PERSON><PERSON>", "subtitle": "Att förstå dina dagliga vanor hjälper oss att ge bättre rekommendationer", "diet": {"question": "Vilken typ av diet följer du?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "vegetarian": "Vegetarisk", "nonVegetarian": "<PERSON><PERSON>-veget<PERSON><PERSON>", "vegan": "Vegan", "pescatarian": "Pescetarisk", "keto": "Keto", "paleo": "<PERSON><PERSON>"}}, "exercise": {"question": "<PERSON>r<PERSON><PERSON> du regelbundet?"}, "drinking": {"question": "Konsumerar du alkohol?"}, "smoking": {"question": "<PERSON><PERSON><PERSON> du?"}, "sleep": {"question": "<PERSON>r många timmar sover du per natt?", "value": "{{sleep}} timmar"}, "hydration": {"question": "Hur många glas vatten dricker du dagligen?", "value": "{{hydration}} glas ({{liters}}L)}"}, "allergies": {"title": "Har du några allergier?", "subtitle": "Att känna till dina allergier hjälper oss att ge säkrare rekommendationer", "allergyIndex": "Allergi {{index}}", "name": {"question": "Vad är du allergisk mot?", "placeholder": "<PERSON><PERSON> aller<PERSON> (t.ex. <PERSON>, Damm)"}, "severity": {"question": "Hur allvarlig är denna allergi?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "mild": "Mild", "moderate": "<PERSON><PERSON><PERSON><PERSON>g", "severe": "<PERSON><PERSON><PERSON><PERSON>"}, "addButton": "<PERSON><PERSON><PERSON> till en annan allergi", "noAllergiesButton": "Jag har inga allergier"}, "medications": {"title": "Mediciner & Tillskott", "subtitle": "Berätta om eventuella mediciner eller till<PERSON>tt du tar för närva<PERSON>e", "medicationIndex": "Medicin {{index}}", "name": {"label": "Medicinnamn", "placeholder": "<PERSON>e medicinn<PERSON>n"}, "startDate": {"question": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> du ta den?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> datum"}, "type": {"label": "<PERSON>p av medicin", "shortTerm": "<PERSON><PERSON><PERSON><PERSON>", "longTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "Mängd"}, "unit": {"label": "<PERSON><PERSON><PERSON>"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "per dag", "perWeek": "per vecka", "perMonth": "per månad", "perYear": "per år"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffar", "drops": "droppar", "tsp": "tsk", "tbsp": "msk", "cups": "koppar"}, "addButton": "<PERSON><PERSON><PERSON> till en annan medicin", "noMedicationsButton": "Jag tar inga mediciner", "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}, "conditions": {"title": "Medicinska Tillstånd", "subtitle": "Berätta om eventuella medicinska tillstånd du har eller har haft tidigare", "conditionIndex": "Tillstånd {{index}}", "name": {"label": "Tillståndets namn", "placeholder": "<PERSON><PERSON> (t.ex. Astma, etc.)"}, "since": {"question": "Sedan när har du haft detta till<PERSON>ånd?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> datum"}, "current": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON> det dig för nä<PERSON>?"}, "medicated": {"question": "Tar du några mediciner för de<PERSON>?"}, "addButton": "Lägg till ett annat tillstånd", "noConditionsButton": "Jag har inga medicinska tillstånd", "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON> datum"}}, "reproductive": {"title": "Reproduktiv Hälsa", "subtitle": "Denna information hjälper oss att ge mer personliga hälso rekommendationer", "menstruation": {"question": "Har du n<PERSON><PERSON>sin haft menstruation?", "detailsTitle": "Menstruationsdetaljer", "regularity": {"question": "Hur regelbunden är din cykel?", "regular": "Regelbunden", "irregular": "Oregelbunden", "notSure": "Vet ej"}, "cycleLength": {"label": "Genomsnittlig cykellängd (dagar)", "placeholder": "<PERSON><PERSON>"}, "flowDays": {"label": "Blödningsdagar: {{flowDays}}", "min": "1 dag", "max": "15 dagar"}, "padsPerDay": {"label": "Bindor/tamponger per dag: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "<PERSON><PERSON><PERSON> symtom under din mens?", "placeholder": "<PERSON><PERSON> s<PERSON> (t.ex., kramper, huvudvärk)"}}, "childbirth": {"question": "Har du fött barn?", "detailsTitle": "Förlossningsdetaljer", "children": {"label": "Antal barn"}, "pregnancies": {"label": "<PERSON><PERSON> gravid<PERSON>"}, "complications": {"question": "Några komplikationer under gravid<PERSON><PERSON> eller förl<PERSON>ning?", "placeholder": "<PERSON><PERSON> komplikationer (om några)"}}}, "review": {"title": "Granska din information", "subtitle": "Granska informationen du angett innan du skickar in den", "sections": {"basicInfo": "Grundläggande information", "lifestyle": "Liv<PERSON><PERSON>", "allergies": "Allergier", "medications": "Mediciner & Tillskott", "conditions": "Medicinska tillstånd", "reproductive": "Reproduktiv Hälsa", "menstruationDetails": "Menstruationsdetaljer", "childbirthDetails": "Förlossningsdetaljer"}, "fields": {"age": "Ålder:", "sex": "Kön:", "height": "Längd:", "weight": "Vik<PERSON>:", "diet": "Kost:", "exercise": "Träning:", "drinking": "Alkohol:", "smoking": "Rökning:", "sleep": "Sömn:", "hydration": "Hydrering:", "allergyIndex": "Allergi {{index}}:", "dose": "Dos:", "frequency": "Frekvens:", "type": "Typ:", "since": "Sedan:", "currentlyActive": "Aktivt för n<PERSON>:", "takingMedication": "Tar medicin:", "hasMenstruated": "Har haft menstruation:", "regularity": "Regelbundenhet:", "cycleLength": "Cykellängd:", "flowDays": "Blödningsdagar:", "padsPerDay": "Bindor/tamponger per dag:", "hasChildbirth": "Har fött barn:", "children": "Barn:", "pregnancies": "Graviditeter:"}, "notProvided": "<PERSON><PERSON>", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} timmar per dag", "hydration": "{{hydration}} koppar ({{liters}}L) per dag", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} dagar"}, "noData": {"allergies": "Inga allergier angivna", "medications": "Inga mediciner angivna", "conditions": "Inga medicinska tillstånd angivna"}, "submitButton": "Ski<PERSON>a in information"}, "success": {"title": "Information Uppdaterad!", "message": "Tack för att du angav din hälsoinformation. Vi kommer att använda detta för att anpassa din upplevelse och ge bättre rekommendationer.", "benefits": {"insights": "Personliga hälsoinsikter", "reminders": "<PERSON>ättre påminnelser om medicinering", "recommendations": "Skräddarsydda hälsorekommendationer"}, "continueButton": "Fortsätt till instrumentpanelen"}, "permissions": {"microphonePermissionDenied": "Mikrofonåtkomst nekad", "microphoneAccessDescription": "August behöver åtkomst till din mikrofon för att spela in ljud och skicka röstmeddelanden", "permissionDenied": "Åtkomst nekad", "cameraPermissionRequired": "Vi behöver kameratillstånd för att detta ska fungera!", "mediaLibraryPermissionRequired": "Vi behöver åtkomst till mediebiblioteket för att detta ska fungera!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON><PERSON> inspelning", "recordingTooLongMessage": "Röstinspelningar får vara högst 5 minuter. <PERSON><PERSON><PERSON> in ett kortare meddelande."}, "errors": {"uploadFailed": "Uppladdning misslyckades", "voiceUploadFailed": "Kunde inte ladda upp röstinspelningen.", "voiceRecordingFailed": "Kunde inte skicka röstinspelningen", "failedToStopRecording": "Kunde inte stoppa inspelningen", "photoUploadFailed": "Kunde inte ladda upp fotot.", "failedToTakePhoto": "Kunde inte ta fotot", "imageUploadFailed": "Kunde inte ladda upp bilden: {{fileName}}", "failedToPickImage": "Kunde inte välja bild", "documentUploadFailed": "Kunde inte ladda upp dokumentet: {{fileName}}", "failedToPickDocument": "Kunde inte välja dokument"}, "audioPlayer": {"downloadingAudio": "Laddar ner ljud...", "loadingAudio": "Laddar ljud..."}, "mediaProcessing": {"processingFile": "Bearbet<PERSON> din fil", "uploadingSecuring": "<PERSON>ddar upp och säkrar fil...", "analyzingContent": "Analyserar dokumentinnehåll...", "extractingInfo": "Extraherar viktig information...", "processingInsights": "Bearbetar insikter...", "preparingResponse": "Förbereder detaljerat svar...", "finalizingResponse": "Av<PERSON><PERSON>ar svar..."}, "attachments": {"voiceMessage": "Röstmeddelande", "image": "[BILD]", "pdf": "[PDF]", "voice": "[RÖSTNOT]"}, "pdf": {"loadingPdf": "Laddar PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "valda"}, "mediaUpload": {"photoLibrary": "Fotobibliotek", "takePhoto": "Ta foto", "chooseFile": "<PERSON><PERSON><PERSON><PERSON> fil"}, "comingSoon": {"title": "Kommer snart!", "description": " är för n<PERSON> under utveckling. Håll utkik efter uppdateringar!", "buttonText": "Okej!"}, "clipboard": {"success": "Länk kopierad till urklipp"}, "mediaPhotos": {"emptyState": "Inga poster än."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON><PERSON>", "nutrition": {"totalCalories": "<PERSON><PERSON> ka<PERSON>", "proteins": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON>", "sugars": "Socker", "fibers": "Fibrer"}}, "reports": {"defaultTitle": "Mediaobjekt", "defaultFoodName": "<PERSON><PERSON><PERSON>", "defaultName": "Dokument", "openButton": "Öppna i extern visare", "biomarker": {"headerBiomarker": "Biomarkör", "headerValue": "<PERSON><PERSON><PERSON>", "headerRefRange": "Referensintervall", "headerStatus": "Status"}, "noData": "Inga biomarkördata tillgängliga"}, "setup": {"title": "Vi konfigurerar allt åt dig", "inProgress": "Pågår...", "progressMessages": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> dagliga kalorier", "1": "<PERSON><PERSON>rar makronfördelning", "2": "Skapar måltidsplan", "3": "Beräk<PERSON> h<PERSON>", "4": "Avslutar konfiguration"}, "checklistItems": {"0": "<PERSON><PERSON><PERSON><PERSON> dina h<PERSON>", "1": "Beräknar optimal närings plan", "2": "Personanpassar dina rekommendationer", "3": "Skapar dina måltidsförslag", "4": "Avslutar din konfiguration"}}, "foodEntry": {"emptyState": "Inga matposter ännu. Ta ett foto av din måltid för att lägga till den!"}, "nutritionReview": {"congratulations": "Grattis!", "subtitle": "Din anpassade närings plan är klar", "submitButton": "<PERSON><PERSON><PERSON> oss bör<PERSON>!", "dailyTargetsTitle": "<PERSON><PERSON> da<PERSON>", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "fats": "<PERSON><PERSON>"}}, "editModal": {"titlePrefix": "Rediger<PERSON> ", "cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON>"}, "processing": {"stages": {"scanning": "<PERSON><PERSON><PERSON> mat...", "identifying": "Identifierar ingredienser...", "extracting": "Extraherar näringsämnen...", "finalizing": "Slutför resultat..."}, "error": {"defaultMessage": "Ingen mat detekterad", "subtitle": "<PERSON><PERSON> en annan vinkel"}, "retakeButton": "<PERSON><PERSON> för att ta om bilden", "notification": "Vi meddelar dig när det är klart!"}, "chart": {"title": "Näringsuppföljning över tid", "selectNutrient": "Välj näringsämne:", "emptyState": "Inga näringsdata tillgängliga ännu.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON>", "sugars": "Socker"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON>", "defaultDate": "<PERSON><PERSON>", "defaultTime": "<PERSON><PERSON>nd tid", "saveChanges": "<PERSON><PERSON>", "error": {"title": "<PERSON><PERSON>", "message": "Misslyckades med att uppdatera näringsdata. Försök igen."}, "nutrition": {"calories": "🔥 Kalorier", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "🍬 So<PERSON>", "fat": "🥑 <PERSON>tt"}, "macroBreakdown": {"title": "Makronäringsfördelning", "noData": "Inga makronäringsdata tillgängliga för denna mat<PERSON>."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON>", "sugar": "Socker"}}, "infoModal": {"title": "Detaljerad information", "edit": "Rediger<PERSON>", "save": "Spara", "saving": "Sparar...", "enterValue": "Ange värde", "notSet": "<PERSON><PERSON>", "age": "<PERSON><PERSON>", "heightCm": "Längd (cm)", "weightKg": "Vikt (kg)", "targetWeight": "M<PERSON>lvikt", "nutritionTargets": "Näringsmål", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON>", "gm": "g", "editNote": "Ange värden eller lämna tomt för automatisk beräkning.", "autoCalculateNote": "Makron är automatiskt beräknade baserat på dina data.", "validation": {"ageMin": "<PERSON><PERSON><PERSON> måste vara minst 18", "ageMax": "<PERSON><PERSON><PERSON> måste vara under 125", "heightMin": "<PERSON>ängden måste vara minst 50 cm", "heightMax": "Längden måste vara under 250 cm", "weightMin": "<PERSON><PERSON><PERSON> m<PERSON> vara minst 30 kg", "weightMax": "<PERSON><PERSON><PERSON> m<PERSON> vara under 500 kg", "targetWeightMin": "<PERSON><PERSON><PERSON><PERSON><PERSON> måste vara minst 30 kg", "targetWeightMax": "Målvikten måste vara under 500 kg", "proteinMin": "Protein måste vara 0 eller mer", "carbsMin": "Kolhydrater måste vara 0 eller mer", "fatsMin": "Fetter måste vara 0 eller mer"}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON>", "excess": "överskott", "remaining": "återstående"}, "specialistConstants": {"nutritionist": {"name": "Dietist", "description": "Expertråd om kost, näring och hälsosamma matvanor", "featureName": "Dietist"}, "cardiologist": {"name": "Kardiolog", "description": "Specialiserad på hjärthälsa och hjärt-kärlsjukdomar", "featureName": "Kardiolog"}, "neurologist": {"name": "Neurolog", "description": "Fokuserad på hj<PERSON>rnan, ryggmärgen och nervsystemets sjukdomar", "featureName": "Neurolog"}, "oncologist": {"name": "Onkolog", "description": "Specialiserad på cancerdiagnostik och behandlingsalternativ", "featureName": "Onkolog"}, "endocrinologist": {"name": "Endokrinolog", "description": "Expert på <PERSON><PERSON> tillstånd och ämnesomsättningssjukdomar", "featureName": "Endokrinolog"}}, "discoverCards": {"categories": {"nutrition": "<PERSON><PERSON><PERSON>", "heartHealth": "<PERSON><PERSON><PERSON><PERSON> hälsa", "mentalHealth": "<PERSON> hälsa", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON><PERSON>"}, "titles": {"vitaminB12Recovery": "Hur lång tid tar det att återhämta sig från B12-brist?", "vitaminDeficiencyGanglion": "Vilken vitaminbrist or<PERSON>kar ganglioncystor?", "vitaminDeficiencyHairFall": "Vilken vitaminbrist or<PERSON><PERSON> hå<PERSON>?", "vitaminWaters": "<PERSON>r <PERSON>ten bra för dig?", "cholesterolHeadaches": "<PERSON><PERSON><PERSON> högt kolesterol huvudvärk?", "cholesterolEyes": "Vilka är symptomen på högt kolesterol som kan ses i ögonen?", "diabetesHeadaches": "Kan diabetes orsaka huvudvärk?", "chestPainDrinking": "<PERSON><PERSON><PERSON><PERSON><PERSON> gör det ont i bröstet efter att ha druckit?", "stressDizziness": "Kan stress orsaka yrsel?", "bulimiaFace": "<PERSON><PERSON> <PERSON>r bulimia face?", "kneeTwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON> rycker mitt knä?", "noseTwitching": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>cker nä<PERSON>?", "piriformisVsSciatica": "Vad är skillnaden mellan piriformis syndrom och ischias?", "shoulderBladePinched": "<PERSON>r man löser en klämd nerv i skulderbladet", "shoulderPinched": "<PERSON>r man löser en klämd nerv i axeln", "meniscusTorn": "<PERSON>r man läker en sönderriven menisk naturligt", "hydrateQuickly": "<PERSON>r man <PERSON><PERSON><PERSON><PERSON><PERSON> snabbt", "periodConstipation": "Är det normalt att ha förstoppning under mensen?", "acneScars": "<PERSON>r man blir av med akneärr naturligt inom en vecka", "perimenopausePregnancy": "Kan man bli gravid under perimenopausen?"}, "descriptions": {"vitaminB12Recovery": "Upptäck återhämtningsförloppet för vitamin B12-brist och effektiva botemedel för att öka dina energinivåer.", "vitaminDeficiencyGanglion": "Utforska kopplingen mellan vitaminbrist och utvecklingen av ganglioncystor i kroppen.", "vitaminDeficiencyHairFall": "<PERSON><PERSON><PERSON> dig hur brist på essentiella vitaminer kan leda till håravfall och vad du kan göra för att förhindra det.", "vitaminWaters": "<PERSON><PERSON><PERSON><PERSON> fördelarna och potentiella nackdelarna med vitamin<PERSON>ten som en del av din dagliga näring.", "cholesterolHeadaches": "Undersök den möjliga kopplingen mellan höga kolesterolvärden och uppkomsten av huvudvärk.", "cholesterolEyes": "<PERSON><PERSON><PERSON> dig hur högt kolesterol kan manifestera sig i dina ögon och vilka symtom du ska hålla utkik efter.", "diabetesHeadaches": "Undersök sambandet mellan diabetes och huvudvärk i det dagliga livet.", "chestPainDrinking": "Utforska orsakerna bakom bröstsmärta efter konsumtion av vissa <PERSON>cker.", "stressDizziness": "<PERSON><PERSON> djupare in i hur stress kan påverka din balans och ditt allmänna välbefinnande, vilket leder till yrsel.", "bulimiaFace": "Förstå de fysiska tecknen på bulimi, inklusive effekterna på ansiktsutseendet.", "kneeTwitch": "Undersök potentiella orsaker bakom ofrivilligt knäryckningar och dess samband med stress eller trötthet.", "noseTwitching": "<PERSON><PERSON><PERSON> dig om de möjliga orsakerna till näsryckningar och dess koppling till ångest eller andra faktorer.", "piriformisVsSciatica": "Jämför symtomen på piriformis syndrom och ischias för att bättre förstå ditt tillstånd.", "shoulderBladePinched": "Upptäck effektiva tekniker för att lindra en klämd nerv i ditt skulderblad och återställa rörligheten.", "shoulderPinched": "<PERSON><PERSON>r dig enkla övningar och stretchövningar för att lindra nervkompression i skulderområdet.", "meniscusTorn": "Utforska naturliga metoder och övningar för att stödja läkningen av en sönderriven menisk.", "hydrateQuickly": "Ta reda på snabba och effektiva sätt att återfukta och bibehålla optimal vätskebalans i kroppen.", "periodConstipation": "Förstå orsakerna bakom förstoppning under menstruation och lär dig naturliga botemedel.", "acneScars": "Upptäck naturliga botemedel och hudvårdstips för att minska utseendet på ärr efter akne snabbt.", "perimenopausePregnancy": "<PERSON><PERSON>r dig om perimenopause, fertilitetsöverväganden och vad du kan förvänta dig under denna l<PERSON>."}}}