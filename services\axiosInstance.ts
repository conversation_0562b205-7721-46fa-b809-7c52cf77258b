import axios from 'axios';
import { refreshAccessToken } from '@/services/auth-service';
import { useAuthStore } from '@/store/auth-store';
import logger from '@/utils/logger/logger';

const axiosInstance = axios.create();
let isLoggingOut = false; // Guard flag to prevent multiple logouts

axiosInstance.interceptors.request.use(
  async (config) => {
    const token = await useAuthStore.getState().getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      logger.debug('Added access token to request', { url: config.url });
    }

    return config;
  },
  (error) => {
    logger.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    if (!axios.isAxiosError(error) || !error.response) {
      logger.error('Non-Axios error or no response:', error);
      return Promise.reject(error);
    }
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await useAuthStore.getState().getRefreshToken();

        if (!refreshToken) {
          if (!isLoggingOut) {
            isLoggingOut = true;
            await useAuthStore.getState().logout();
          }
          return Promise.reject(error);
        }

        let retries = 4;
        let result;
        while (true) {
          result = await refreshAccessToken(refreshToken);
          if (!result.success && !(result.status === 401) && retries >= 0) {
            retries--;
            continue;
          }
          break;
        }
        if (result.success && result.accessToken) {
          // Update the access token in secure storage
          await useAuthStore.getState().setAccessToken(result.accessToken);

          // Update authorization header for the original request
          originalRequest.headers['Authorization'] = `Bearer ${result.accessToken}`;

          // Retry the original request with the new token
          return axios(originalRequest);
        } else if (result.status === 401) {
          if (!isLoggingOut) {
            isLoggingOut = true;
            await useAuthStore.getState().logout();
          }
        } 
      } catch (refreshError) {
        // If there was an error during refresh, logout
        logger.error('Error during token refresh:', refreshError);
        if (!isLoggingOut) {
          isLoggingOut = true;
          await useAuthStore.getState().logout();
        }
        return Promise.reject(refreshError);
      }
    }

    // Log other errors (not 401)
    if (error.response.status !== 401) {
      logger.error('API error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        url: error.config?.url
      });
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
