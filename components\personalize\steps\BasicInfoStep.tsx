import type React from "react"
import { useState, useCallback, useMemo } from "react"
import { View, Text, TextInput, TouchableOpacity, ScrollView, StyleSheet } from "react-native"
import { User, ChevronDown } from "lucide-react-native"
import { colors } from "@/constants/colors"
import { trackUserInteraction } from "@/utils/mixpanel/mixpanel-utils"
import { useFormDataStore } from "@/store/formDataStore"
import { scale, verticalScale, moderateScale } from "react-native-size-matters"
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useTranslation } from "react-i18next";

interface BasicInfoStepProps {
  stepIndex: number
  isActive?: boolean
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    content: { padding: moderateScale(20) },
    iconContainer: {
      width: scale(64),
      height: scale(64),
      borderRadius: scale(theme.radii["4xl"]),
      backgroundColor: theme.colors.primary.main + "20",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: verticalScale(24),
      alignSelf: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.xl),
      fontWeight: "700",
      color: theme.colors.gray[800],
      marginBottom: verticalScale(8),
      textAlign: "center",
    },
    subtitle: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
      marginBottom: verticalScale(32),
      textAlign: "center",
    },
    formField: { marginBottom: verticalScale(20) },
    label: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "500",
      marginBottom: verticalScale(8),
      color: theme.colors.gray[700],
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      fontSize: moderateScale(theme.fontSize.sm),
      backgroundColor: theme.colors.secondary[50],
    },
    selectInput: {
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      padding: moderateScale(14),
      backgroundColor: theme.colors.secondary[50],
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    selectText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
    placeholderText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray['main'],
    },
    dropdownOptions: {
      position: "absolute",
      top: verticalScale(50),
      left: 0,
      right: 0,
      backgroundColor: theme.colors.secondary[50],
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: moderateScale(theme.radii.md),
      zIndex: 9999,
      elevation: 10,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    dropdownOption: {
      padding: moderateScale(16),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    },
    dropdownOptionText: {
      fontSize: moderateScale(14),
      color: colors.black,
    },
  });

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ stepIndex, isActive = false }) => {
  const { t } = useTranslation();
  const { formData, updateBasicInfo } = useFormDataStore()
  const [showSexOptions, setShowSexOptions] = useState(false)
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);

  const sexOptions = useMemo(() => [t('basicInfo.sex.male'), t('basicInfo.sex.female'), t('basicInfo.sex.other')], [t])

  const handleSexChange = useCallback((value: string) => {
    updateBasicInfo({ sex: value })
    setShowSexOptions(false)
    trackUserInteraction("Form Field Update", {
      section: "basic",
      field: "sex",
      value,
    })
  }, [updateBasicInfo])

  if (!isActive) return null

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.iconContainer}>
        <User size={scale(theme.radii["4xl"])} color={theme.colors.primary.main} />
      </View>

      <Text style={styles.title}>{t('basicInfo.title')}</Text>
      <Text style={styles.subtitle}>{t('basicInfo.subtitle')}</Text>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('basicInfo.age.question')}</Text>
        <TextInput
          style={styles.input}
          placeholder={t('basicInfo.age.placeholder')}
          keyboardType="numeric"
          value={formData.basicInfo.age}
          onChangeText={(text) => updateBasicInfo({ age: text })}
        />
      </View>
      <View style={styles.formField}>
        <Text style={styles.label}>{t('basicInfo.sex.question')}</Text>
        <View>
          <TouchableOpacity style={styles.selectInput} onPress={() => setShowSexOptions(prev => !prev)}>
            <Text style={formData.basicInfo.sex ? styles.selectText : styles.placeholderText}>
              {formData.basicInfo.sex || t('basicInfo.sex.placeholder')}
            </Text>
            {showSexOptions ? (
              <ChevronDown size={scale(16)} color={colors.gray[400]} style={{transform: [{rotate: '180deg'}]}} />
            ) : (
              <ChevronDown size={scale(16)} color={colors.gray[400]} />
            )}
          </TouchableOpacity>

          {showSexOptions && (
            <View style={styles.dropdownOptions}>
              {sexOptions.map((option) => (
                <TouchableOpacity key={option} style={styles.dropdownOption} onPress={() => handleSexChange(option)}>
                  <Text style={styles.dropdownOptionText}>{option}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('basicInfo.height.question')}</Text>
        <TextInput
          style={styles.input}
          placeholder={t('basicInfo.height.placeholder')}
          keyboardType="numeric"
          value={formData.basicInfo.height}
          onChangeText={(text) => updateBasicInfo({ height: text })}
        />
      </View>

      <View style={styles.formField}>
        <Text style={styles.label}>{t('basicInfo.weight.question')}</Text>
        <TextInput
          style={styles.input}
          placeholder={t('basicInfo.weight.placeholder')}
          keyboardType="numeric"
          value={formData.basicInfo.weight}
          onChangeText={(text) => updateBasicInfo({ weight: text })}
        />
      </View>
    </ScrollView>
  )
}

