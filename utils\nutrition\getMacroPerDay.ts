import logger from '@/utils/logger/logger';

// Create a simple memoization cache
const memoCache = new Map<string, any>();

type NutritionFormData = {
  sex: string;
  age: string | number;
  height: string | number;
  weight: string | number;
  exerciseLevel: string;
  goal: string;
  desiredWeight: string | number;
};

type MacroResult = {
  calories: number;
  macros: {
    protein: number;
    fat: number;
    carbs: number;
  };
  units: {
    calories: string;
    protein: string;
    fat: string;
    carbs: string;
  };
};

// Memoized version of getMacroPerDay
const getMacroPerDay = (formData: NutritionFormData): MacroResult => {
  // Create a cache key from the input data
  const cacheKey = JSON.stringify(formData);
  
  // Check if we have a cached result
  if (memoCache.has(cacheKey)) {
    return memoCache.get(cacheKey);
  }
  
  // If not in cache, calculate the result
  const result = calculateMacros(formData);
  
  // Store in cache (limit cache size to prevent memory leaks)
  if (memoCache.size > 100) {
    // Clear the first item if cache gets too large
    const firstKey = memoCache.keys().next().value;
    if (firstKey !== undefined) {
      memoCache.delete(firstKey);
    }
  }
  memoCache.set(cacheKey, result);
  
  return result;
};

// Extract the calculation logic to a separate function
const calculateMacros = (formData : NutritionFormData) : MacroResult => {
  const { sex, age, height, weight, exerciseLevel, goal, desiredWeight } = formData;
  
  // Convert strings to numbers - using parseFloat for better precision
  const numAge = typeof age === 'string' ? parseFloat(age) : age;
  const numHeight = typeof height === 'string' ? parseFloat(height) : height;
  const numWeight = typeof weight === 'string' ? parseFloat(weight) : weight;
  const numDesiredWeight = typeof desiredWeight === 'string' ? parseFloat(desiredWeight) : desiredWeight;
  
  // Changed the logic here a bit
  if (!numAge || !numHeight || !numWeight || !numDesiredWeight || !sex || !exerciseLevel || !goal ||
      isNaN(Number(numAge)) || isNaN(Number(numHeight)) || isNaN(Number(numWeight)) || isNaN(Number(numDesiredWeight)) ||
      numAge < 10 || numAge > 120 || // Realistic age range
      numHeight < 100 || numHeight > 250 || // Realistic height range (cm)
      numWeight < 30 || numWeight > 300 || // Realistic weight range (kg)
      numDesiredWeight < 30 || numDesiredWeight > 300) { // Realistic desired weight range
    
    // Return default values if data is invalid
    return {
      calories: 0,
      macros: {
        protein: 0,
        fat: 0,
        carbs: 0
      },
      units: {
        calories: 'kcal',
        protein: 'g',
        fat: 'g',
        carbs: 'g'
      }
    };
  }
  
  // Calculate BMR using Mifflin-St Jeor Equation (most accurate for general population
  let bmr;
  if (sex === 'male') {
    bmr = 10 * numWeight + 6.25 * numHeight - 5 * numAge + 5;
  } else {
    bmr = 10 * numWeight + 6.25 * numHeight - 5 * numAge - 161;
  }
  
  // Activity multipliers based on medical literature
  const activityMultipliers = {
    'no exercise': 1.2,      // Sedentary (desk job, no exercise)
    'moderate': 1.55,        // Moderate exercise (3-5 days/week)
    'high': 1.725           // Very active (6-7 days/week, intense exercise)
  };
  
  // Calculate TDEE (Total Daily Energy Expenditure)
  const tdee = bmr * activityMultipliers[exerciseLevel as keyof typeof activityMultipliers];
  
  // Medically accurate calorie adjustment
  // 1 kg of fat = approximately 7700 kcal (not 7700 as in original)
  // Safe weight loss/gain: 0.5-1 kg per week maximum
  const weightDiff = Math.abs(numDesiredWeight - numWeight);
  const weeklyRate = Math.min(weightDiff, 0.5); // Cap at 0.5kg per week for safety
  const caloriesPerKgFat = 7700; // Medically accurate value
  const dailyCalorieAdjustment = (caloriesPerKgFat * weeklyRate) / 7;
  
  // Adjust calories based on goal
  let calories;
  switch (goal) {
    case 'increase':
      // For weight gain, add calories but ensure it's reasonable
      calories = tdee + Math.min(dailyCalorieAdjustment, 500); // Max 500 kcal surplus
      break;
    case 'decrease':
      // For weight loss, subtract calories but maintain minimum for health
      const minCalories = bmr * 1.1; // Never go below 110% of BMR
      calories = Math.max(tdee - dailyCalorieAdjustment, minCalories);
      break;
    case 'maintain':
      calories = tdee;
      break;
    default:
      calories = tdee;
  }
  
  // Medically recommended macro distribution
  // Protein: 1.2-2.0g per kg body weight depending on activity and goals
  let proteinPerKg;
  switch (exerciseLevel) {
    case 'no exercise':
      proteinPerKg = goal === 'decrease' ? 1.6 : 1.2; // Higher protein for weight loss
      break;
    case 'moderate':
      proteinPerKg = goal === 'increase' ? 1.8 : 1.6;
      break;
    case 'high':
      proteinPerKg = goal === 'increase' ? 2.0 : 1.8;
      break;
    default:
      proteinPerKg = 1.2;
  }
  
  const proteinGrams = Math.round(proteinPerKg * numWeight);
  const proteinCalories = proteinGrams * 4;
  
  // Fat: 20-35% of total calories (medical recommendation)
  const fatPercentage = 0.25; // 25% as a middle ground
  const fatCalories = calories * fatPercentage;
  const fatGrams = Math.round(fatCalories / 9);
  
  // Carbs: Fill remaining calories
  const remainingCalories = calories - proteinCalories - fatCalories;
  const carbGrams = Math.round(Math.max(0, remainingCalories / 4));
  
  // Ensure minimum carb intake for brain function (minimum 130g/day)
  const finalCarbGrams = Math.max(carbGrams, 130);
  
  return {
    calories: Math.round(calories),
    macros: {
      protein: proteinGrams,
      fat: fatGrams,
      carbs: finalCarbGrams
    },
    units: {
      calories: 'kcal',
      protein: 'g',
      fat: 'g',
      carbs: 'g'
    }
  };
};

export default getMacroPerDay;
