{"build": {"preview": {"android": {"buildType": "apk"}, "env": {"GATEKEEPER_URL": "https://api.getbeyondhealth.com", "TENANT": "august"}, "channel": "preview"}, "preview2": {"android": {"gradleCommand": ":app:assembleRelease"}, "channel": "preview2"}, "preview3": {"developmentClient": true, "autoIncrement": true, "distribution": "internal", "env": {"GATEKEEPER_URL": "https://api.getbeyondhealth.com", "TENANT": "august"}, "channel": "preview3"}, "preview4": {"distribution": "internal", "channel": "preview4"}, "development": {"developmentClient": true, "distribution": "internal"}, "production": {"autoIncrement": true, "distribution": "store", "channel": "production"}, "testflight": {"distribution": "store", "ios": {"enterpriseProvisioning": "universal"}, "autoIncrement": true, "channel": "testflight", "env": {"GATEKEEPER_URL": "https://api.getbeyondhealth.com", "TENANT": "august"}}}, "cli": {"appVersionSource": "remote"}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "9LN6K5BTX5"}}}}