import React, { useEffect } from 'react';
import { router } from 'expo-router';
import { useAuthStore, useAuthLoading } from '@/store/auth-store';
import { isPreAuthOnboardingComplete } from '@/utils/onboarding/onboarding';
import logger from '@/utils/logger/logger';
import { identifyUser } from '@/utils/mixpanel/mixpanel-utils';
import { usePhoneStore } from '@/store/phoneStore';
import { getUserData } from '@/services/userService';
import { AppState, AppStateStatus } from 'react-native';

export default function Index() {
  const { setIsLoading, setAccessToken, getAccessToken } = useAuthStore();
  
  useEffect(() => {
    let appState = AppState.currentState;
  
    const checkAuth = async () => {
      try {
        setIsLoading(true);

        const storedToken =   await getAccessToken();
        //const preAuthOnboardingDone = await isPreAuthOnboardingComplete();
        
        if (storedToken) {
          const phoneNumber = usePhoneStore.getState().phoneNumber;
          if (phoneNumber) {
            identifyUser(phoneNumber);
          
          logger.info("Token found in storage", phoneNumber);
          await setAccessToken(storedToken);
          try {
            const userData = await getUserData();
            const { user } = useAuthStore.getState();
            const isMobileOnboardingDone = userData?.user?.meta?.isMobileOnboardingDone;
            if (isMobileOnboardingDone && user?.isMobileOnboardingDone) {
              router.replace("/(tabs)/chat");
            } else {
              router.replace("/onboarding/PostAuthOnboarding");
            }
          } catch (error) {
            logger.error('Error fetching user data:', error);
            if ((error as any)?.response?.status === 401) {
              logger.info('Invalid or expired token detected, clearing tokens and redirecting to auth');
              await useAuthStore.getState().logout();
            } else {
              // For other errors, still redirect to auth as a safety measure
              logger.error('Error fetching user data', error);
              router.replace("/auth/phone");
            }
            return;
          }
        } else {
          logger.info('Invalid or expired refresh token detected, clearing tokens and redirecting to auth');
          await useAuthStore.getState().logout();
        }
        } else {
          // No token, user needs to authenticate first
          logger.info('No token found, checking pre-auth onboarding status');
          router.replace("/auth/phone");
        }
      } catch (error) {
        logger.error('Error checking authentication:', error);
        router.replace("/auth/phone");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);
  
  return null;
}
