{"common": {"error": "त्रुटि", "yes": "हाँ", "no": "नहीं", "sometimes": "कभी-कभी", "close": "ब<PERSON><PERSON> करें", "cancel": "रद्<PERSON> करें", "save": "सहेजें", "next": "अगला", "loading": "लोड हो रहा है...", "version": "v0.0.1.7"}, "welcome": "ऑगस्ट से बात करना शुरू करने के लिए लॉगिन करें", "notFound": {"title": "ओह!", "message": "यह स्क्रीन मौजूद नहीं है।", "goHome": "होम स्क्रीन पर जाएँ!"}, "library": {"title": "स्वास्थ्य पुस्तकालय"}, "specialists": {"title": "विशेषज्ञ", "description": "अधिक विशिष्ट स्वास्थ्य संबंधी चिंताओं के लिए विशेष स्वास्थ्य पेशेवरों से परामर्श करें। नीचे एक विशेषज्ञ चुनें:", "generalPhysician": {"title": "सामान्य चिकित्सक", "description": "सामान्य स्वास्थ्य संबंधी चिंताओं और प्राथमिक देखभाल के लिए।"}, "nutritionist": {"title": "पोषण विशेषज्ञ", "description": "आहार, पोषण और वजन प्रबंधन सलाह के लिए।"}, "cardiologist": {"title": "कार्डियोलॉजिस्ट", "description": "हृदय संबंधी चिंताओं और हृदय स्वास्थ्य के लिए।"}, "neurologist": {"title": "न्यूरोलॉजिस्ट", "description": "मस्तिष्क, रीढ़ की हड्डी और तंत्रिका तंत्र की समस्याओं के लिए।"}, "oncologist": {"title": "ऑन्कोलॉजिस्ट", "description": "कैंसर संबंधी चिंताओं और उपचारों के लिए।"}, "endocrinologist": {"title": "एंडोक्रिनोलॉजिस्ट", "description": "हार्मोन संबंधी विकारों और मधुमेह प्रबंधन के लिए।"}, "dermatologist": {"title": "त्वचा विशेषज्ञ", "description": "त्वचा, बालों और नाखूनों की स्थिति के लिए।"}, "psychiatrist": {"title": "मनोचिकित्सक", "description": "मानसिक स्वास्थ्य संबंधी चिंताओं और मनोवैज्ञानिक कल्याण के लिए।"}}, "profile": {"title": "प्रोफ़ाइल", "defaultName": "अतिथि", "namePlaceholder": "अपना नाम दर्ज करें", "saving": "सहेज रहा है...", "noPhoneNumber": "कोई फ़ोन नंबर नहीं", "loggingOut": "लॉग आउट हो रहा है...", "about": {"title": "के बारे में", "description": "ऑगस्ट के बारे में और जानें"}, "whatsapp": {"title": "व्हाट्सएप", "description": "व्हाट्सएप पर ऑगस्ट के साथ चैट करें"}, "refer": {"title": "संदर्भित करें", "description": "ऑगस्ट को पसंद किया? अपने दोस्तों के साथ साझा करें"}, "deleteAccount": {"title": "खाता हटाएँ", "description": "हमें आपको जाते हुए देखकर दुख हुआ"}, "logout": {"title": "लॉग आउट करें", "description": "जल्द ही वापस आएँ। हम आपको याद करेंगे"}, "shareMessage": "👋नमस्ते, इस बेहतरीन ऐप को देखें जिसका मैं उपयोग कर रहा हूँ!\n\n\n\n➡️मैं त्वरित, विश्वसनीय स्वास्थ्य जानकारी और मार्गदर्शन प्राप्त करने के लिए ऑगस्ट का उपयोग कर रहा हूँ। यह आपकी जेब में एक डॉक्टर रखने जैसा है! इसे यहाँ देखें:", "error": {"loadFailed": "उपयोगकर्ता डेटा लोड करने में विफल", "fetchError": "उपयोगकर्ता डेटा प्राप्त करते समय एक त्रुटि हुई", "updateNameFailed": "नाम अपडेट करने में विफल", "updateNameError": "नाम अपडेट करते समय एक त्रुटि हुई", "loadFoodData": "भोजन डेटा लोड करने में विफल", "logoutError": "लॉग आउट के दौरान त्रुटि:", "shareError": "संदेश साझा करने में त्रुटि:"}}, "error": {"title": "कुछ गलत हो गया", "checkLogs": "अधिक विवरण के लिए कृपया अपने डिवाइस लॉग देखें।", "unknown": "अज्ञात त्रुटि", "unknownFile": "अज्ञात फ़ाइल", "unknownLine": "अज्ञात पंक्ति", "unknownColumn": "अज्ञात कॉलम"}, "auth": {"phone": {"selectCountry": "देश चुनें", "searchCountries": "देश खोजें", "validation": {"invalidPhone": "कृपया एक मान्य फ़ोन नंबर दर्ज करें", "invalidDigits": "कृपया एक मान्य फ़ोन नंबर दर्ज करें (7-15 अंक)"}}, "header": {"title": "अपनी स्वास्थ्य संबंधी चिंताओं पर तुरंत और निजी तौर पर स्पष्टता प्राप्त करें", "subtitle": "सोच-समझकर दी गई सलाह। कोई जल्दबाज़ी नहीं। कोई भ्रम नहीं।", "emphasis": "बस स्पष्टता।"}, "greeting": "नमस्ते 👋", "phoneNumber": "फ़ोन नंबर", "requestOTP": "OTP का अनुरोध करें", "otp": {"title": "वन-टाइम पासवर्ड", "verify": "OTP सत्यापित करें", "sending": "भेज रहा है...", "countdown": "{{countdown}} सेकंड में OTP पुनः भेजें", "resend": "OTP पुनः भेजें", "sentTo": "OTP भेजा गया ", "whatsappSuffix": "Whatsapp पर"}, "disclaimer": {"prefix": "साइन अप करके, आप हमारी", "continuePrefix": "जारी रखकर, आप हमारी", "termsOfService": "सेवा की शर्तें", "and": "और", "privacyPolicy": "गोपनीयता नीति", "whatsappConsent": "से सहमत हैं, और Whatsapp के माध्यम से हमारे द्वारा अपडेट और रिमाइंडर प्राप्त करने की सहमति देते हैं।"}}, "onboarding": {"preAuth": {"welcome": {"title": "अगस्त में आपका स्वागत है!", "buttonText": "चलिए शुरू करते हैं"}}, "postAuth": {"step1": {"title": "हाय!", "subtitle": "मैं अगस्त हूँ 👋", "description": "मुझे अपने डिवाइस पर आरामदायक कोने के रूप में सोचें जहाँ आप अपनी सभी स्वास्थ्य जिज्ञासाओं का पता लगाते हैं।", "subdescription": "मन में जो भी हो, बेझिझक पूछें। कोई निर्णय नहीं, कोई सीमा नहीं!", "placeholder": "मैं आपको क्या कहूँ?"}, "step2": {"title": "हाय {{userName}},", "subtitle": "यहाँ मैं क्या कर सकता हूँ:", "features": {"health": {"title": "अपने", "subtitle": "स्वास्थ्य प्रश्नों के उत्तर दें"}, "nutrition": {"title": "उन", "subtitle": "मैक्रोज़ को ट्रैक करें"}, "reports": {"title": "विश्लेषण करें", "subtitle": "रिपोर्ट"}}}}, "pills": {"thoughtful": "विचार<PERSON>ील", "careful": "सावधान", "accurate": "सटीक"}, "features": {"symptoms": {"title": "अपने लक्षणों की जाँच करें", "description": "मुझे एक हफ़्ते से जी मिचला रहा है। मेरे साथ क्या हो रहा है?"}, "prescriptions": {"title": "अपनी प्रिस्क्रिप्शन का विश्लेषण करें", "description": "डॉक्टर की तरह प्रिस्क्रिप्शन अपलोड करें और समझें।"}, "medicine": {"title": "अपनी दवा के बारे में जानें", "description": "क्या मेरे PCOS के लिए मेटफॉर्मिन मेरी ADHD गोलियों के साथ परस्पर क्रिया करता है?"}, "plans": {"title": "व्यक्तिगत योजनाएँ प्राप्त करें", "description": "क्या आप मुझे मेरे HbA1c स्तर को कम करने के लिए पोषण और फिटनेस योजना दे सकते हैं?"}}, "buttons": {"getStarted": "शुरू करें", "next": "अगला"}, "errors": {"nameRequired": "कृपया अपना नाम दर्ज करें"}}, "tabs": {"chat": "चैट", "discover": "खोजें", "nutrition": "पोषण", "personalize": "व्यक्तिगत बनाएँ"}, "chat": {"nav": {"title": "अगस्त"}, "me": "मैं", "augustName": "अगस्त", "input": {"placeholder": "अगस्त से पूछें...", "disclaimer": "अगस्त गलतियाँ कर सकता है। डॉक्टर से पुष्टि करें"}, "list": {"loadingMessages": "संदेश लोड हो रहे हैं...", "noMessages": "अभी तक कोई संदेश नहीं है। बातचीत शुरू करें!"}, "connection": {"offlineMessage": "ऐसा लगता है कि आप ऑफ़लाइन हैं। संदेश भेजने के लिए फिर से कनेक्ट करें।", "connecting": "कनेक्ट हो रहा है...", "tryAgain": "फिर से कोशिश करें"}, "prompts": {"uploadReport": "रिपोर्ट अपलोड करें", "speakInHindi": "हिंदी में बात करो", "notFeelingWell": "मैं ठीक महसूस नहीं कर रहा हूँ", "whatIsMyBMI": "मेरा BMI क्या है?", "nutritionAdvice": "पोषण सलाह", "sleepBetter": "बेहतर नींद लें"}, "citations": {"referenceText": "इस बातचीत के बारे में अधिक जानकारी के लिए, कृपया देखें:"}, "actions": {"copiedToClipboard": "क्लिपबोर्ड में कॉपी किया गया", "copied": "कॉपी किया गया"}, "share": {"introText": "👋अरे, अगस्त के साथ मेरी हुई बातचीत देखो:\n\n", "downloadText": "\n\n➡️अपने दोस्ताना AI स्वास्थ्य साथी के साथ चैट करने के लिए अगस्त डाउनलोड करें:\n"}}, "discover": {"nav": {"title": "खोजें"}, "categories": {"all": "सभी", "heartHealth": "हृदय स्वास्थ्य", "nutrition": "पोषण", "mentalHealth": "मानसिक स्वास्थ्य", "fitness": "फिटनेस", "wellness": "स्वास्थ्य"}, "cards": {"empty": "इस श्रेणी के लिए कोई कार्ड उपलब्ध नहीं है"}, "sections": {"features": "सुविधाएँ"}, "features": {"healthLibrary": {"title": "स्वास्थ्य पुस्तकालय", "description": "पूरी तरह से मुफ्त में विश्वसनीय, भरोसेमंद और अप-टू-डेट चिकित्सा जानकारी तक पहुँच।"}, "nutritionTracker": {"title": "पोषण ट्रैकर", "description": "क्या आपने कभी सोचा है कि क्या आप अपने भोजन की एक तस्वीर अपलोड कर सकते हैं और अपने सभी पोषण लक्ष्यों को ट्रैक कर सकते हैं? अगस्त ऐसा ही कर सकता है!"}, "multilingualSupport": {"title": "बहुभाषी समर्थन", "description": "आप अगस्त के साथ किसी भी भाषा में संवाद कर सकते हैं जिसमें आप सहज हैं! अगस्त हमेशा सुनने, समर्थन करने और आपकी आवश्यकता होने पर किसी भी समय आपको जवाब देने के लिए यहां है।", "samplePrompt": "हिंदी में बात करो"}, "labReportAnalysis": {"title": "लैब रिपोर्ट विश्लेषण", "description": "जब आप अगस्त से अपनी लैब रिपोर्ट के बारे में बात करते हैं, तो आपको अत्यधिक सटीकता मिलती है। अगस्त ने 98.4% की बायोमार्कर निष्कर्षण सटीकता के साथ 4.7 मिलियन से अधिक रिपोर्ट संसाधित की हैं।"}}}, "nutrition": {"nav": {"title": "पोषण"}, "meals": {"title": "आपके भोजन", "subtitle": "प्रत्येक भोजन में मैक्रो की जांच करने के लिए टैप करें"}, "upload": {"loading": "इमेज अपलोड हो रही है..."}, "defaultFoodName": "खाद्य पदार्थ", "today": "आज", "unknownTime": "अज्ञात समय", "calories": "🔥 कैलोरी", "proteins": "🥩 प्रोटीन", "carbs": "🍞 कार्ब्स", "sugars": "🍬 शर्करा", "fat": "🥑 वसा", "caloriesLabel": "कैलोरी", "proteinLabel": "प्रोटीन", "carbohydratesLabel": "कार्बोहाइड्रेट", "fatLabel": "वसा", "sugarLabel": "चीनी", "tips": "सुझाव:", "macroBreakdown": "मैक्रोन्यूट्रिएंट ब्रेकडाउन", "noMacroData": "इस खाद्य पदार्थ के लिए कोई मैक्रोन्यूट्रिएंट डेटा उपलब्ध नहीं है।", "disclaimer": "केवल शैक्षिक उपयोग के लिए। अधिक जानें", "disclaimerLink": "यहाँ", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "आपका लिंग क्या है?", "subtitle": "यह आपकी कस्टम योजना को कैलिब्रेट करने के लिए उपयोग किया जाएगा।", "male": "पुरुष", "female": "महिला", "other": "अन्य"}, "age": {"title": "आपकी आयु क्या है?", "subtitle": "यह आपकी दैनिक आवश्यकताओं की गणना करने के लिए उपयोग किया जाएगा।"}, "measurements": {"title": "ऊँचाई और वजन", "subtitle": "कृपया अपनी ऊँचाई सेंटीमीटर में और वजन किलोग्राम में दर्ज करें।"}, "activity": {"title": "गतिविधि स्तर", "subtitle": "आप कितनी बार व्यायाम करते हैं?", "none": "कोई व्यायाम नहीं", "moderate": "मध्यम", "high": "उच्च"}, "goal": {"title": "वजन लक्ष्य", "subtitle": "आप क्या हासिल करना चाहेंगे?", "increase": "बढ़ाएँ", "maintain": "बनाए रखें", "decrease": "घटाएँ"}, "targetWeight": {"title": "लक्ष्य वजन", "subtitle": "आपका लक्ष्य वजन किलोग्राम में क्या है?"}, "setup": {"title": "अपनी योजना स्थापित करना", "subtitle": "कृपया प्रतीक्षा करें जब तक हम आपकी पोषण योजना तैयार नहीं कर लेते।"}, "review": {"title": "अपनी योजना की समीक्षा करें", "subtitle": "अपनी पोषण योजना की समीक्षा करें और उसे अनुकूलित करें।"}, "height": {"label": "ऊँचाई (सेमी)"}, "weight": {"label": "वजन (किग्रा)"}}, "error": {"updateFailed": "पोषण डेटा अपडेट करने में विफल। कृपया पुनः प्रयास करें।", "parsingError": "भोजन डेटा पार्स करने में त्रुटि:", "fetchReportsFailed": "रिपोर्ट डेटा प्राप्त करने में विफल। कृपया पुनः प्रयास करें।", "missingReportId": "रिपोर्ट आईडी गायब है"}}, "personalize": {"nav": {"title": "व्यक्तिगत बनाएँ"}, "button": {"saving": "सहेज रहा है", "review": "समीक्षा करें", "saveNext": "सहेजें और अगला"}}, "basicInfo": {"title": "आइए आपको बेहतर जानते हैं", "subtitle": "यह जानकारी हमें आपकी स्वास्थ्य सिफारिशों को वैयक्तिकृत करने में मदद करती है", "age": {"question": "आपकी उम्र कितनी है?", "placeholder": "अपनी आयु दर्ज करें"}, "sex": {"question": "आपका लिंग क्या है?", "placeholder": "अपना लिंग चुनें", "male": "पुरुष", "female": "महिला", "other": "अन्य"}, "height": {"question": "आपकी ऊँचाई क्या है? (सेमी)", "placeholder": "अपनी ऊँचाई दर्ज करें"}, "weight": {"question": "आपका वजन क्या है? (किग्रा)", "placeholder": "अपना वजन दर्ज करें"}}, "lifestyle": {"title": "आपकी जीवनशैली की आदतें", "subtitle": "आपकी दैनिक आदतों को समझने से हमें बेहतर सुझाव देने में मदद मिलती है", "diet": {"question": "आप किस प्रकार का आहार लेते हैं?", "placeholder": "अपना आहार चुनें", "vegetarian": "<PERSON>ा<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonVegetarian": "मा<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "vegan": "शाक<PERSON><PERSON><PERSON><PERSON><PERSON> (शुद्ध)", "pescatarian": "पेस्केटेरियन", "keto": "कीटो", "paleo": "पेलियो"}, "exercise": {"question": "क्या आप नियमित रूप से व्यायाम करते हैं?"}, "drinking": {"question": "क्या आप शराब का सेवन करते हैं?"}, "smoking": {"question": "क्या आप धूम्रपान करते हैं?"}, "sleep": {"question": "आप प्रति रात कितने घंटे सोते हैं?", "value": "{{sleep}} घंटे"}, "hydration": {"question": "आप प्रतिदिन कितने कप पानी पीते हैं?", "value": "{{hydration}} कप ({{liters}}L)"}}, "allergies": {"title": "क्या आपको कोई एलर्जी है?", "subtitle": "आपकी एलर्जी को जानने से हमें सुरक्षित सुझाव देने में मदद मिलती है", "allergyIndex": "एलर्जी {{index}}", "name": {"question": "आपको किससे एलर्जी है?", "placeholder": "एलर्जी दर्ज करें (जैसे, मूंगफली, धूल)"}, "severity": {"question": "यह एलर्जी कितनी गंभीर है?", "placeholder": "गंभीरता चुनें", "mild": "हल्की", "moderate": "मध्यम", "severe": "गं<PERSON>ीर"}, "addButton": "एक और एलर्जी जोड़ें", "noAllergiesButton": "मुझे कोई एलर्जी नहीं है"}, "medications": {"title": "दवाएँ और पूरक", "subtitle": "हमें उन किसी भी दवा या पूरक के बारे में बताएं जो आप वर्तमान में ले रहे हैं", "medicationIndex": "दवा {{index}}", "name": {"label": "दवा का नाम", "placeholder": "दवा का नाम दर्ज करें"}, "startDate": {"question": "आपने इसे कब लेना शुरू किया?", "placeholder": "तिथि चुनें"}, "type": {"label": "दवा का प्रकार", "shortTerm": "अल्पकालिक", "longTerm": "दीर्घकालिक"}, "dose": {"label": "मात्रा", "placeholder": "मात्रा"}, "unit": {"label": "इकाई"}, "frequency": {"label": "आवृत्ति", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "प्रति दिन", "perWeek": "प्रति सप्ताह", "perMonth": "प्रति माह", "perYear": "प्रति वर्ष"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "एक और दवा जोड़ें", "noMedicationsButton": "मैं कोई दवा नहीं लेता", "calendar": {"title": "प्रारंभ तिथि चुनें"}}, "conditions": {"title": "चिकित्सीय स्थितियाँ", "subtitle": "हमें उन किसी भी चिकित्सीय स्थिति के बारे में बताएं जो आपको पहले थी या अभी है", "conditionIndex": "स्थिति {{index}}", "name": {"label": "स्थिति का नाम", "placeholder": "स्थिति दर्ज करें (जैसे अस्थमा, आदि)"}, "since": {"question": "आपको यह स्थिति कब से है?", "placeholder": "तिथि चुनें"}, "current": {"question": "क्या यह अभी आपको परेशान कर रही है?"}, "medicated": {"question": "क्या आप इसके लिए कोई दवा ले रहे हैं?"}, "addButton": "एक और स्थिति जोड़ें", "noConditionsButton": "मुझे कोई चिकित्सीय स्थिति नहीं है", "calendar": {"title": "तिथि चुनें"}}, "reproductive": {"title": "प्रजनन स्वास्थ्य", "subtitle": "यह जानकारी हमें अधिक व्यक्तिगत स्वास्थ्य सिफारिशें प्रदान करने में मदद करती है", "menstruation": {"question": "क्या आपने कभी मासिक धर्म का अनुभव किया है?", "detailsTitle": "मासिक धर्म विवरण", "regularity": {"question": "आपका चक्र कितना नियमित है?", "regular": "नियमित", "irregular": "अनियमित", "notSure": "पता नहीं"}, "cycleLength": {"label": "औसत चक्र लंबाई (दिन)", "placeholder": "चक्र लंबाई दर्ज करें"}, "flowDays": {"label": "प्रवाह के दिन: {{flowDays}}", "min": "1 दिन", "max": "15 दिन"}, "padsPerDay": {"label": "प्रतिदिन पैड/टैम्पोन: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "आपके पीरियड के दौरान कोई लक्षण?", "placeholder": "लक्षण दर्ज करें (जैसे, ऐंठन, सिरदर्द)"}}, "childbirth": {"question": "क्या आपने प्रसव का अनुभव किया है?", "detailsTitle": "प्रसव विवरण", "children": {"label": "बच्चों की संख्या"}, "pregnancies": {"label": "गर्भावस्था की संख्या"}, "complications": {"question": "गर्भावस्था या प्रसव के दौरान कोई जटिलताएँ?", "placeholder": "जटिलताओं (यदि कोई हो) दर्ज करें"}}}, "review": {"title": "अपनी जानकारी की समीक्षा करें", "subtitle": "जमा करने से पहले आपने जो जानकारी प्रदान की है उसकी समीक्षा करें", "sections": {"basicInfo": "मूल जानकारी", "lifestyle": "जीवनशैली", "allergies": "एलर्जी", "medications": "दवाएँ और पूरक", "conditions": "चिकित्सीय स्थितियाँ", "reproductive": "प्रजनन स्वास्थ्य", "menstruationDetails": "मासिक धर्म विवरण", "childbirthDetails": "प्रसव विवरण"}, "fields": {"age": "आयु:", "sex": "लिंग:", "height": "ऊँचाई:", "weight": "वजन:", "diet": "आहार:", "exercise": "व्यायाम:", "drinking": "शराब पीना:", "smoking": "धूम्रपान:", "sleep": "नींद:", "hydration": "जलजनन:", "allergyIndex": "एलर्जी {{index}}:", "dose": "मात्रा:", "frequency": "आवृत्ति:", "type": "प्रकार:", "since": "से:", "currentlyActive": "वर्तमान में सक्रिय:", "takingMedication": "दवा लेना:", "hasMenstruated": "मासिक धर्म हुआ है:", "regularity": "नियमितता:", "cycleLength": "चक्र लंबाई:", "flowDays": "प्रवाह के दिन:", "padsPerDay": "प्रतिदिन पैड/टैम्पोन:", "hasChildbirth": "प्रसव का अनुभव हुआ है:", "children": "बच्चे:", "pregnancies": "गर्भावस्था:"}, "notProvided": "प्रदान नहीं किया गया", "units": {"cm": "{{height}} सेमी", "kg": "{{weight}} किग्रा"}, "values": {"sleepHours": "{{sleep}} घंटे प्रति दिन", "hydration": "{{hydration}} कप ({{liters}}L) प्रति दिन", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} दिन"}, "noData": {"allergies": "कोई एलर्जी प्रदान नहीं की गई", "medications": "कोई दवाएँ प्रदान नहीं की गईं", "conditions": "कोई चिकित्सीय स्थितियाँ प्रदान नहीं की गईं"}, "submitButton": "जानकारी जमा करें"}, "success": {"title": "जानकारी अपडेट हो गई!", "message": "अपनी स्वास्थ्य जानकारी प्रदान करने के लिए धन्यवाद। हम आपके अनुभव को वैयक्तिकृत करने और बेहतर सिफारिशें प्रदान करने के लिए इसका उपयोग करेंगे।", "benefits": {"insights": "वैयक्तिकृत स्वास्थ्य अंतर्दृष्टि", "reminders": "बेहतर दवा रिमाइंडर", "recommendations": "अनुकूलित स्वास्थ्य सिफारिशें"}, "continueButton": "डैशबोर्ड पर जारी रखें"}, "permissions": {"microphonePermissionDenied": "माइक्रोफ़ोन अनुमति अस्वीकृत", "microphoneAccessDescription": "ऑगस्ट को ऑडियो रिकॉर्ड करने और वॉयस नोट भेजने के लिए आपके माइक्रोफ़ोन तक पहुँच की आवश्यकता है", "permissionDenied": "अनुमति अस्वीकृत", "cameraPermissionRequired": "इसे काम करने के लिए हमें कैमरा अनुमति की आवश्यकता है!", "mediaLibraryPermissionRequired": "इसे काम करने के लिए हमें मीडिया लाइब्रेरी अनुमति की आवश्यकता है!"}, "voiceRecording": {"recordingTooLong": "रिकॉर्डिंग बहुत लंबी है", "recordingTooLongMessage": "वॉयस रिकॉर्डिंग 5 मिनट से कम होनी चाहिए। कृपया एक छोटा संदेश रिकॉर्ड करें।"}, "errors": {"uploadFailed": "अपलोड विफल", "voiceUploadFailed": "वॉयस रिकॉर्डिंग अपलोड नहीं कर सका।", "voiceRecordingFailed": "वॉयस रिकॉर्डिंग भेजने में विफल", "failedToStopRecording": "रिकॉर्डिंग रोकने में विफल", "photoUploadFailed": "फ़ोटो अपलोड नहीं कर सका।", "failedToTakePhoto": "फ़ोटो लेने में विफल", "imageUploadFailed": "छवि अपलोड नहीं कर सका: {{fileName}}", "failedToPickImage": "छवि चुनने में विफल", "documentUploadFailed": "दस्तावेज़ अपलोड नहीं कर सका: {{fileName}}", "failedToPickDocument": "दस्तावेज़ चुनने में विफल"}, "audioPlayer": {"downloadingAudio": "ऑडियो डाउनलोड कर रहा है...", "loadingAudio": "ऑडियो लोड हो रहा है..."}, "mediaProcessing": {"processingFile": "आपकी फ़ाइल संसाधित कर रहा है", "uploadingSecuring": "फ़ाइल अपलोड और सुरक्षित कर रहा है...", "analyzingContent": "दस्तावेज़ सामग्री का विश्लेषण कर रहा है...", "extractingInfo": "मुख्य जानकारी निकाल रहा है...", "processingInsights": "इनसाइट्स संसाधित कर रहा है...", "preparingResponse": "विस्तृत प्रतिक्रिया तैयार कर रहा है...", "finalizingResponse": "प्रतिक्रिया को अंतिम रूप दे रहा है..."}, "attachments": {"voiceMessage": "वॉयस संदेश", "image": "[छवि]", "pdf": "[PDF]", "voice": "[वॉयस नोट]"}, "pdf": {"loadingPdf": "PDF लोड हो रहा है..."}, "dateTime": {"yesterday": "कल,"}, "navbar": {"defaultTitle": "ऑगस्ट", "selectedCount": "चयनित"}, "mediaUpload": {"photoLibrary": "फ़ोटो लाइब्रेरी", "takePhoto": "फ़ोटो लें", "chooseFile": "फ़ाइल चुनें"}, "comingSoon": {"title": "जल्द ही आ रहा है!", "description": "विकास के अधीन है। अपडेट के लिए बने रहें!", "buttonText": "समझ गया!"}, "clipboard": {"success": "लिंक क्लिपबोर्ड में कॉपी किया गया"}, "mediaPhotos": {"emptyState": "अभी तक कोई प्रविष्टि नहीं।"}, "foodDetail": {"defaultFoodName": "खाद्य पदार्थ", "nutrition": {"totalCalories": "कुल कैलोरी", "proteins": "प्रोटीन", "carbs": "कार्ब्स", "fat": "वसा", "sugars": "चीनी", "fibers": "फाइबर"}}, "reports": {"defaultTitle": "मीडिया आइटम", "defaultFoodName": "खाद्य पदार्थ", "defaultName": "दस्तावेज़", "openButton": "बाहरी दर्शक में खोलें", "biomarker": {"headerBiomarker": "बायो<PERSON>ा<PERSON><PERSON><PERSON>र", "headerValue": "मान", "headerRefRange": "संदर<PERSON><PERSON> रेंज", "headerStatus": "स्थिति"}, "noData": "कोई बायोमार्कर डेटा उपलब्ध नहीं है"}, "setup": {"title": "हम आपके लिए सब कुछ सेट कर रहे हैं", "inProgress": "प्रगति पर...", "progressMessages": {"0": "दैनिक कैलोरी की गणना करना", "1": "मैक्रो स्प्लिट का अनुकूलन करना", "2": "भोजन योजना बनाना", "3": "स्वास्थ्य स्कोर की गणना करना", "4": "सेटअप को अंतिम रूप देना"}, "checklistItems": {"0": "आपके स्वास्थ्य डेटा का विश्लेषण करना", "1": "इष्टतम पोषण योजना की गणना करना", "2": "आपकी सिफारिशों को वैयक्तिकृत करना", "3": "आपके भोजन के सुझाव बनाना", "4": "आपके सेटअप को अंतिम रूप देना"}}, "foodEntry": {"emptyState": "अभी तक कोई खाने की एंट्री नहीं है। इसे जोड़ने के लिए अपने भोजन की एक तस्वीर लें!"}, "nutritionReview": {"congratulations": "बधाई हो!", "subtitle": "आपकी कस्टम न्यूट्रिशन योजना तैयार है", "submitButton": "चलिए शुरू करते हैं!", "dailyTargetsTitle": "आपके दैनिक पोषण लक्ष्य", "macroLabels": {"calories": "कैलोरी", "carbs": "कार्ब्स", "protein": "प्रोटीन", "fats": "वसा"}, "recommendations": {"title": "अपने लक्ष्यों तक कैसे पहुँचें:", "healthScores": "अपनी दिनचर्या को बेहतर बनाने के लिए स्वास्थ्य स्कोर का उपयोग करें", "trackFood": "अपने भोजन के सेवन को लगातार ट्रैक करें", "followCalories": "अपनी दैनिक कैलोरी सिफारिश का पालन करें", "balanceMacros": "अपने कार्ब्स, प्रोटीन और वसा के सेवन को संतुलित करें"}}, "editModal": {"titlePrefix": "संपादित करें ", "cancelButton": "रद्<PERSON> करें", "saveButton": "अगला"}, "processing": {"stages": {"0": "भोजन स्कैन कर रहा है...", "1": "सामग्री की पहचान कर रहा है...", "2": "पोषक तत्वों को निकाल रहा है...", "3": "परिणामों को अंतिम रूप दे रहा है..."}, "error": {"defaultMessage": "कोई भोजन नहीं मिला", "subtitle": "एक अलग कोण आज़माएँ"}, "retakeButton": "तस्वीर फिर से लेने के लिए टैप करें", "notification": "हम आपको पूरा होने पर सूचित करेंगे!"}, "chart": {"title": "समय के साथ पोषण ट्रैकिंग", "selectNutrient": "पोषक तत्व चुनें:", "emptyState": "अभी तक कोई पोषण डेटा उपलब्ध नहीं है।", "dropdown": {"calories": "कैलोरी", "protein": "प्रोटीन", "carbs": "कार्ब्स", "fat": "वसा", "sugars": "चीनी"}}, "foodModal": {"defaultName": "खाद्य पदार्थ", "defaultDate": "आज", "defaultTime": "अज्ञात समय", "saveChanges": "परिवर्तन सहेजें", "error": {"title": "त्रुटि", "message": "पोषण डेटा अपडेट करने में विफल। कृपया पुनः प्रयास करें।"}, "nutrition": {"calories": "🔥 कैलोरी", "proteins": "🥩 प्रोटीन", "carbs": "🍞 कार्ब्स", "sugars": "🍬 चीनी", "fat": "🥑 वसा"}, "macroBreakdown": {"title": "मैक्रोन्यूट्रिएंट ब्रेकडाउन", "noData": "इस खाद्य पदार्थ के लिए कोई मैक्रोन्यूट्रिएंट डेटा उपलब्ध नहीं है।"}, "macroLabels": {"calories": "कैलोरी", "protein": "प्रोटीन", "carbs": "कार्ब्स", "fat": "वसा", "sugar": "चीनी"}}, "infoModal": {"title": "विस्तृत जानकारी", "editButton": "संपादित करें", "saveButton": "सहेजें", "savingButton": "सहेज रहा है..", "fields": {"age": "आयु", "height": "ऊँचाई (सेमी)", "weight": "वजन (किग्रा)", "targetWeight": "लक्ष्य वजन", "placeholder": "मान दर्ज करें", "notSet": "सेट नहीं"}, "nutritionTargets": "पोषण लक्ष्य", "macros": {"protein": "प्रोटीन", "carbs": "कार्ब्स", "fats": "वसा"}, "note": {"editing": "मान दर्ज करें या ऑटो-गणना के लिए रिक्त छोड़ दें।", "autoCalculated": "आपके डेटा के आधार पर मैक्रोज़ स्वतः परिकलित किए जाते हैं।"}}, "tracker": {"calories": "कैलोरी", "caloriesRemaining": " शेष कैलोरी", "caloriesExcess": " अतिरिक्त कैलोरी", "macros": {"protein": "प्रोटीन", "carbs": "कार्ब्स", "fat": "वसा"}, "excess": "अतिरिक्त", "remaining": "शेष"}, "specialistConstants": {"nutritionist": {"name": "पोषण विशेषज्ञ", "description": "आहार, पोषण और स्वस्थ खान-पान की आदतों पर विशेषज्ञ सलाह", "featureName": "पोषण विशेषज्ञ"}, "cardiologist": {"name": "हृदय रोग विशेषज्ञ", "description": "हृदय स्वास्थ्य और हृदय संबंधी स्थितियों में विशेषज्ञता", "featureName": "हृदय रोग विशेषज्ञ"}, "neurologist": {"name": "न्यूरोलॉजिस्ट", "description": "मस्तिष्क, स्पाइनल कॉर्ड और तंत्रिका तंत्र के विकारों पर केंद्रित", "featureName": "न्यूरोलॉजी विशेषज्ञ"}, "oncologist": {"name": "ऑन्कोलॉजिस्ट", "description": "कैंसर के निदान और उपचार के विकल्पों में विशेषज्ञता", "featureName": "ऑन्कोलॉजी विशेषज्ञ"}, "endocrinologist": {"name": "एंडोक्रिनोलॉजिस्ट", "description": "हार्मोनल स्थितियों और चयापचय संबंधी विकारों में विशेषज्ञ", "featureName": "एंडोक्रिनोलॉजी विशेषज्ञ"}}, "discoverCards": {"categories": {"nutrition": "पोषण", "heartHealth": "हृदय स्वास्थ्य", "mentalHealth": "मानसिक स्वास्थ्य", "fitness": "फिटनेस", "wellness": "स्वास्थ्य"}, "titles": {"vitaminB12Recovery": "विटामिन B12 की कमी से उबरने में कितना समय लगता है?", "vitaminDeficiencyGanglion": "किस विटामिन की कमी से गैन्ग्लियन सिस्ट होते हैं?", "vitaminDeficiencyHairFall": "किस विटामिन की कमी से बाल झड़ते हैं?", "vitaminWaters": "क्या विटामिन वाटर आपके लिए अच्छे हैं?", "cholesterolHeadaches": "क्या उच्च कोलेस्ट्रॉल से सिरदर्द होता है?", "cholesterolEyes": "उच्च कोलेस्ट्रॉल के ऐसे कौन से लक्षण हैं जो आँखों में दिखाई दे सकते हैं?", "diabetesHeadaches": "क्या मधुमेह से सिरदर्द हो सकता है?", "chestPainDrinking": "शराब पीने के बाद सीने में दर्द क्यों होता है?", "stressDizziness": "क्या तनाव से चक्कर आ सकते हैं?", "bulimiaFace": "बुलिमिया फेस क्या है?", "kneeTwitch": "मेरे घुटने में मरोड़ क्यों आता है?", "noseTwitching": "नाक में मरोड़ क्यों आता है?", "piriformisVsSciatica": "पिरिफॉर्मिस सिंड्रोम और साइटिका में क्या अंतर हैं?", "shoulderBladePinched": "शोल्डर ब्लेड में फंसी नर्व को कैसे रिलीज करें?", "shoulderPinched": "कंधे में फंसी नर्व को कैसे रिलीज करें?", "meniscusTorn": "फटे हुए मेनिस्कस को प्राकृतिक रूप से कैसे ठीक करें?", "hydrateQuickly": "जल्दी से कैसे हाइड्रेट करें?", "periodConstipation": "क्या पीरियड के दौरान कब्ज होना सामान्य है?", "acneScars": "एक हफ्ते के अंदर मुंहासों के निशान से कैसे छुटकारा पाएँ?", "perimenopausePregnancy": "क्या पेरिमेनोपॉज के दौरान गर्भवती होना संभव है?"}, "descriptions": {"vitaminB12Recovery": "विटामिन B12 की कमी से उबरने की समय-सीमा और अपने ऊर्जा स्तर को बढ़ाने के प्रभावी उपचारों का पता लगाएं।", "vitaminDeficiencyGanglion": "शरीर में गैंग्लियन सिस्ट के विकास और विटामिन की कमी के बीच के संबंध का पता लगाएं।", "vitaminDeficiencyHairFall": "जानें कि कैसे आवश्यक विटामिनों की कमी से बालों का झड़ना हो सकता है और इसे रोकने के लिए आप क्या कर सकते हैं।", "vitaminWaters": "अपने दैनिक पोषण के हिस्से के रूप में विटामिन वाटर्स के लाभों और संभावित कमियों का पता लगाएं।", "cholesterolHeadaches": "उच्च कोलेस्ट्रॉल के स्तर और सिरदर्द की शुरुआत के बीच के संभावित संबंध की जांच करें।", "cholesterolEyes": "जानें कि उच्च कोलेस्ट्रॉल आपकी आंखों में कैसे प्रकट हो सकता है और किन लक्षणों पर ध्यान देना चाहिए।", "diabetesHeadaches": "दैनिक जीवन में मधुमेह और सिरदर्द के होने के बीच के संबंध की जांच करें।", "chestPainDrinking": "कुछ पेय पदार्थों के सेवन के बाद सीने में दर्द के पीछे के कारणों का पता लगाएं।", "stressDizziness": "जानें कि कैसे तनाव आपके संतुलन और समग्र कल्याण को प्रभावित कर सकता है, जिससे चक्कर आ सकते हैं।", "bulimiaFace": "बुलिमिया के शारीरिक लक्षणों को समझें, जिसमें चेहरे की बनावट पर प्रभाव भी शामिल है।", "kneeTwitch": "अनैच्छिक घुटने में मरोड़ के पीछे के संभावित कारणों और तनाव या थकान से इसके संबंध की जांच करें।", "noseTwitching": "नाक में मरोड़ के संभावित कारणों और चिंता या अन्य कारकों से इसके संबंध के बारे में जानें।", "piriformisVsSciatica": "अपनी स्थिति को बेहतर ढंग से समझने के लिए पिरिफॉर्मिस सिंड्रोम और साइटिका के लक्षणों की तुलना करें।", "shoulderBladePinched": "अपने शोल्डर ब्लेड में फंसी नर्व को राहत देने और गतिशीलता को बहाल करने के लिए प्रभावी तकनीकों का पता लगाएं।", "shoulderPinched": "कंधे के क्षेत्र में तंत्रिका संपीड़न को कम करने के लिए सरल व्यायाम और स्ट्रेचिंग जानें।", "meniscusTorn": "फटे हुए मेनिस्कस के उपचार में सहायता के लिए प्राकृतिक तरीकों और व्यायामों का पता लगाएं।", "hydrateQuickly": "पुनर्जलयोजन और इष्टतम शरीर के जलयोजन को बनाए रखने के त्वरित और प्रभावी तरीके खोजें।", "periodConstipation": "मासिक धर्म के दौरान कब्ज के पीछे के कारणों को समझें और प्राकृतिक उपचार जानें।", "acneScars": "मुंहासों के निशान की उपस्थिति को तेज़ी से कम करने के लिए प्राकृतिक उपचार और त्वचा देखभाल युक्तियाँ खोजें।", "perimenopausePregnancy": "पेरिमेनोपॉज़, प्रजनन क्षमता पर विचार और जीवन के इस चरण के दौरान क्या अपेक्षा करें, इसके बारे में जानें।"}}, "languageSwitcher": {"title": "भाषा", "searchPlaceholder": "भाषाएँ खोजें...", "useDeviceLanguage": "डिवाइस की भाषा का उपयोग करें", "changingLanguage": "भाषा बदली जा रही है...", "noResults": "कोई भाषा नहीं मिली"}}