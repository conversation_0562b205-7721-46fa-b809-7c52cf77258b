import { createConfig } from '@gluestack-ui/themed';

export const config = createConfig({
  //@ts-ignore
  theme: {
    tokens: {
      colors: {
        primary50: '#E6F0EC',
        primary100: '#C0D9CE',
        primary200: '#97C0AF',
        primary300: '#6DA790',
        primary400: '#4E9478',
        primary500: '#206E55', // Main primary color
        primary600: '#1C6349',
        primary700: '#17573E',
        primary800: '#134A34',
        primary900: '#0E3D2A',
        
        secondary50: '#FFFFFF',
        secondary100: '#FFFFFF',
        secondary200: '#FFFFFF',
        secondary300: '#FFFFFF',
        secondary400: '#FFFFFF',
        secondary500: '#FFFFFF', // Main white color
        secondary600: '#F5F5F5',
        secondary700: '#E0E0E0',
        secondary800: '#CCCCCC',
        secondary900: '#B8B8B8',
        
        light50: '#FFFFFF',
        light100: '#F9FAFB',
        light200: '#F3F4F6',
        light300: '#E5E7EB',
        light400: '#D1D5DB',
        light500: '#9CA3AF',
        light600: '#6B7280',
        light700: '#4B5563',
        light800: '#1F2937',
        light900: '#111827',
      },
      fonts: {
        heading: 'System',
        body: 'System',
      },
    },
    components: {
      Button: {
        theme: {
          variants: {
            solid: {
              bg: '$primary500',
              borderColor: '$primary500',
            },
            outline: {
              borderColor: '$primary500',
              color: '$primary500',
            },
          },
        },
      },
      Text: {
        theme: {
          variants: {
            heading: {
              fontWeight: '700',
              color: '$light800',
            },
            subheading: {
              fontWeight: '600',
              color: '$light700',
            },
            body: {
              fontWeight: '400',
              color: '$light700',
            },
          },
        },
      },
      Input: {
        theme: {
          variants: {
            outline: {
              borderColor: '$light300',
              _focus: {
                borderColor: '$primary500',
              },
            },
          },
        },
      },
    },
  },
});