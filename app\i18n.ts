import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger/logger';
import en from '../locales/en.json';
import hi from '../locales/hi.json';
import mr from '../locales/mr.json';
import pa from '../locales/pa.json';
import kn from '../locales/kn.json';
import ta from '../locales/ta.json';
import es from '../locales/es.json';
import fr from '../locales/fr.json';
import de from '../locales/de.json';
import it from '../locales/it.json';
import ptBR from '../locales/pt-BR.json';
import ptPT from '../locales/pt-PT.json';
import ru from '../locales/ru.json';
import zhCN from '../locales/zh-CN.json';
import zhTW from '../locales/zh-TW.json';
import ja from '../locales/ja.json';
import ko from '../locales/ko.json';
import ar from '../locales/ar.json';
import id from '../locales/id.json';
import fil from '../locales/fil.json';
import tr from '../locales/tr.json';
import bn from '../locales/bn.json';
import te from '../locales/te.json';
import gu from '../locales/gu.json';
import ur from '../locales/ur.json';
import ms from '../locales/ms.json';
import vi from '../locales/vi.json';
import th from '../locales/th.json';
import he from '../locales/he.json';
import pl from '../locales/pl.json';
import nl from '../locales/nl.json';
import uk from '../locales/uk.json';
import el from '../locales/el.json';
import sv from '../locales/sv.json';
import cs from '../locales/cs.json';
import hu from '../locales/hu.json';
import ro from '../locales/ro.json';

const LANGUAGE_KEY = 'user_selected_language';

// Get the initial language (stored > device > fallback)
const getInitialLanguage = async (): Promise<string> => {
  try {
    // Check for stored language preference
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
    if (storedLanguage) {
      return storedLanguage;
    }
    
    // Fall back to device language
    const locales = Localization.getLocales();
    const deviceLanguage = locales?.[0]?.languageCode || 'en';
    
    // Check if device language is supported
    const supportedLanguages = Object.keys(resources);
    return supportedLanguages.includes(deviceLanguage) ? deviceLanguage : 'en';
  } catch (error) {
    logger.error('Error getting initial language:', error);
    return 'en';
  }
};

// Get fallback language
const fallbackLng = 'en';

const resources = {
  en: {
    translation: en,
  },
  hi: {
    translation: hi,
  },
  mr: {
    translation: mr,
  },
  pa: {
    translation: pa,
  },
  ta: {
    translation: ta,
  },
  te: {
    translation: te,
  },
  kn: {
    translation: kn,
  },
  es: {
    translation: es,
  },
  fr: {
    translation: fr,
  },
  de: {
    translation: de,
  },
  it: {
    translation: it,
  },
  'pt-BR': {
    translation: ptBR,
  },
  'pt-PT': {
    translation: ptPT,
  },
  ru: {
    translation: ru,
  },
  'zh-CN': {
    translation: zhCN,
  },
  'zh-TW': {
    translation: zhTW,
  },
  ja: {
    translation: ja,
  },
  ko: {
    translation: ko,
  },
  ar: {
    translation: ar,
  },
  id: {
    translation: id,
  },
  fil: {
    translation: fil,
  },
  tr: {
    translation: tr,
  },
  bn: {
    translation: bn,
  },
  gu: {
    translation: gu,
  },
  ur: {
    translation: ur,
  },
  ms: {
    translation: ms,
  },
  vi: {
    translation: vi,
  },
  th: {
    translation: th,
  },
  he: {
    translation: he,
  },
  pl: {
    translation: pl,
  },
  nl: {
    translation: nl,
  },
  uk: {
    translation: uk,
  },
  el: {
    translation: el,
  },
  sv: {
    translation: sv,
  },
  cs: {
    translation: cs,
  },
  hu: {
    translation: hu,
  },
  ro: {
    translation: ro,
  },
};

// Initialize i18n with device language first, then update with stored preference
const initializeI18n = async () => {
  const initialLanguage = await getInitialLanguage();
  
  await i18n
    .use(initReactI18next)
    .init({
      lng: initialLanguage,
      fallbackLng,
      resources,
      ns: ['translation'],
      defaultNS: 'translation',
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    });
};

// Initialize i18n immediately
initializeI18n().catch(logger.error);

export default i18n;
