{"common": {"error": "<PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non", "sometimes": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "save": "Enregistrer", "next": "Suivant", "loading": "Chargement...", "version": "v0.0.1.7"}, "welcome": "Connectez-vous pour commencer à discuter avec August", "notFound": {"title": "Oups !", "message": "Cet écran n'existe pas.", "goHome": "Retour à l'écran d'accueil !"}, "library": {"title": "Bibliothèque santé"}, "specialists": {"title": "Spécialistes", "description": "Consultez des professionnels de santé spécialisés pour des problèmes de santé plus spécifiques. Choisissez un spécialiste ci-dessous :", "generalPhysician": {"title": "M<PERSON><PERSON>cin <PERSON>", "description": "Pour les problèmes de santé généraux et les soins primaires."}, "nutritionist": {"title": "Nutritionniste", "description": "Pour les conseils en matière d'alimentation, de nutrition et de gestion du poids."}, "cardiologist": {"title": "Cardiologue", "description": "Pour les problèmes cardiaques et la santé cardiovasculaire."}, "neurologist": {"title": "Neurologue", "description": "Pour les problèmes cérébraux, de la moelle épinière et du système nerveux."}, "oncologist": {"title": "Oncologue", "description": "Pour les problèmes et les traitements liés au cancer."}, "endocrinologist": {"title": "Endocrinologue", "description": "Pour les troubles hormonaux et la prise en charge du diabète."}, "dermatologist": {"title": "Dermatologue", "description": "Pour les affections de la peau, des cheveux et des ongles."}, "psychiatrist": {"title": "Psychiatre", "description": "Pour les problèmes de santé mentale et le bien-être psychologique."}}, "profile": {"title": "Profil", "defaultName": "Invi<PERSON>", "namePlaceholder": "Entrez votre nom", "saving": "Enregistrement...", "noPhoneNumber": "Aucun numéro de téléphone", "loggingOut": "Déconnexion...", "about": {"title": "À propos", "description": "En savoir plus sur August"}, "whatsapp": {"title": "WhatsApp", "description": "Discutez avec August sur WhatsApp"}, "refer": {"title": "<PERSON><PERSON><PERSON>", "description": "Vous avez aimé August ? Partagez-le avec vos amis"}, "deleteAccount": {"title": "Supprimer le compte", "description": "Nous sommes désolés de vous voir partir"}, "logout": {"title": "Déconnexion", "description": "Revenez bientôt. Vous nous man<PERSON>"}, "shareMessage": "👋Salut, Découvrez cette application géniale que j'utilise !\n\n\n\n➡️J'utilise August pour obtenir des informations et des conseils de santé rapides et fiables. C'est comme avoir un médecin dans votre poche ! Découvrez-la ici :", "error": {"loadFailed": "Échec du chargement des données utilisateur", "fetchError": "Une erreur s'est produite lors de la récupération des données utilisateur", "updateNameFailed": "Échec de la mise à jour du nom", "updateNameError": "Une erreur s'est produite lors de la mise à jour du nom", "loadFoodData": "Échec du chargement des données alimentaires", "logoutError": "Erreur lors de la déconnexion :", "shareError": "Erreur lors du partage des messages :"}}, "error": {"title": "Une erreur s'est produite", "checkLogs": "<PERSON>euillez consulter les journaux de votre appareil pour plus de détails.", "unknown": "<PERSON><PERSON><PERSON> inconnue", "unknownFile": "<PERSON><PERSON><PERSON> inconnu", "unknownLine": "Ligne inconnue", "unknownColumn": "Colonne inconnue"}, "auth": {"phone": {"selectCountry": "Sélectionner le pays", "searchCountries": "Rechercher des pays", "validation": {"invalidPhone": "Veuillez entrer un numéro de téléphone valide", "invalidDigits": "Veuillez entrer un numéro de téléphone valide (7 à 15 chiffres)"}}, "header": {"title": "Obtenez des réponses claires à vos préoccupations de santé instantanément et en toute confidentialité", "subtitle": "Des conseils avisés. Pas de précipitation. Pas de confusion.", "emphasis": "Juste de la clarté."}, "greeting": "Salut 👋", "phoneNumber": "Numéro de téléphone", "requestOTP": "De<PERSON>er un OTP", "otp": {"title": "Mot de passe à usage unique", "verify": "Vérifier l'OTP", "sending": "Envoi...", "countdown": "Renvoyer l'OTP dans {{countdown}}s", "resend": "Renvoyer l'OTP", "sentTo": "OTP envoyé à ", "whatsappSuffix": " sur WhatsApp"}, "disclaimer": {"prefix": "En vous inscrivant, vous acceptez nos ", "continuePrefix": "En continuant, vous acceptez nos ", "termsOfService": "Conditions d'utilisation", "and": " et ", "privacyPolicy": "Politique de confidentialité", "whatsappConsent": ", et consentez à recevoir des mises à jour et des rappels de notre part via WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Bienvenue sur August !", "buttonText": "Commençons !"}}, "postAuth": {"step1": {"title": "Salut !", "subtitle": "Je suis August 👋", "description": "Considérez-moi comme le coin confortable de votre\nappareil où vous explorez toutes vos\ncuriosités en matière de santé.", "subdescription": "N'hésitez pas à poser toutes les questions qui vous viennent à l'esprit.\nPas de jugement, pas de limites !", "placeholder": "Comment dois-je vous appeler ?"}, "step2": {"title": "Salut {{userName}},", "subtitle": "Voici ce que je peux faire :", "features": {"health": {"title": "Répondre à vos", "subtitle": "Questions de santé"}, "nutrition": {"title": "Suivre vos", "subtitle": "<PERSON><PERSON>"}, "reports": {"title": "Analyser", "subtitle": "Rapports"}}}, "pills": {"thoughtful": "Bienveillant", "careful": "Atten<PERSON><PERSON>", "accurate": "<PERSON><PERSON><PERSON>"}, "features": {"symptoms": {"title": "Vérifiez vos symptômes", "description": "J'ai des nausées depuis une semaine. Que m'arrive-t-il ?"}, "prescriptions": {"title": "Analysez vos ordonnances", "description": "Téléchargez et comprenez vos ordonnances comme un médecin."}, "medicine": {"title": "Connaissez vos médicaments", "description": "La Metformine pour mon SOPK interagit-elle avec mes pilules pour le TDAH ?"}, "plans": {"title": "Obtenez des plans personnalisés", "description": "Pouvez-vous me donner un plan nutritionnel et sportif pour réduire mon taux d'HbA1c ?"}}, "buttons": {"getStarted": "Commencer", "next": "Suivant"}, "errors": {"nameRequired": "Veuillez entrer votre nom"}}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Découvrir", "nutrition": "Nutrition", "personalize": "Personnaliser"}, "chat": {"nav": {"title": "Août"}, "me": "<PERSON><PERSON>", "augustName": "Août", "input": {"placeholder": "<PERSON><PERSON><PERSON> à Août...", "disclaimer": "Août peut faire des erreurs. Confirmez avec un médecin"}, "list": {"loadingMessages": "Chargement des messages...", "noMessages": "Aucun message encore. Commencez une conversation !"}, "connection": {"offlineMessage": "Il semble que vous soyez hors ligne. Reconnectez-vous pour envoyer des messages.", "connecting": "Connexion...", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>"}, "prompts": {"uploadReport": "Télécharger le rapport", "speakInHindi": "Parlez en hindi", "notFeelingWell": "Je ne me sens pas bien", "whatIsMyBMI": "Quel est mon IMC ?", "nutritionAdvice": "Conseils nutritionnels", "sleepBetter": "<PERSON><PERSON> dormir"}, "citations": {"referenceText": "Pour plus de détails concernant cette conversation, veuillez vous référer à :"}, "actions": {"copiedToClipboard": "Copié dans le presse-papiers", "copied": "<PERSON><PERSON><PERSON>"}, "share": {"introText": "👋Salut, regarde la conversation que j'ai eue avec Août :\n\n", "downloadText": "\n\n➡️Téléchargez Août pour discuter avec votre compagnon de santé IA amical :\n"}}, "discover": {"nav": {"title": "Découvrir"}, "categories": {"all": "Tous", "heartHealth": "<PERSON><PERSON>", "nutrition": "Nutrition", "mentalHealth": "Santé <PERSON>e", "fitness": "Fitness", "wellness": "Bien-être"}, "cards": {"empty": "Aucune carte disponible pour cette catégorie"}, "sections": {"features": "Fonctionnalités"}, "features": {"healthLibrary": {"title": "Bibliothèque de santé", "description": "Accès gratuit à des informations médicales fiables, crédibles et à jour."}, "nutritionTracker": {"title": "Suivi nutritionnel", "description": "Vous êtes-vous déjà demandé si vous pouviez simplement télécharger une photo de votre nourriture et suivre tous vos objectifs nutritionnels ? Août peut le faire !"}, "multilingualSupport": {"title": "Support multilingue", "description": "Vous pouvez communiquer avec Août dans n'importe quelle langue avec laquelle vous êtes à l'aise ! Août est toujours là pour vous écouter, vous soutenir et vous répondre à tout moment.", "samplePrompt": "Parlez en hindi"}, "labReportAnalysis": {"title": "Analyse de rapport de laboratoire", "description": "Lorsque vous parlez à Août de vos rapports de laboratoire, vous obtenez une précision extrême. Août a traité plus de 4,7 millions de rapports avec une précision d'extraction des biomarqueurs de 98,4 %."}}}, "nutrition": {"nav": {"title": "Nutrition"}, "meals": {"title": "<PERSON>os repas", "subtitle": "Appuyez pour voir les macros de chaque repas"}, "upload": {"loading": "Téléchargement de l'image..."}, "defaultFoodName": "Article alimentaire", "today": "<PERSON><PERSON><PERSON>'hui", "unknownTime": "Heure inconnue", "calories": "🔥 Calories", "proteins": "🥩 Protéines", "carbs": "🍞 Glucides", "sugars": "🍬 Sucres", "fat": "🥑 Matières grasses", "caloriesLabel": "Calories", "proteinLabel": "Protéines", "carbohydratesLabel": "Glucides", "fatLabel": "Matières grasses", "sugarLabel": "<PERSON><PERSON>", "tips": "Conseils :", "macroBreakdown": "Répartition des macronutriments", "noMacroData": "Aucune donnée sur les macronutriments disponible pour cet aliment.", "disclaimer": "À des fins éducatives seulement. En savoir plus", "disclaimerLink": "ici", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Quel est votre genre ?", "subtitle": "Cela sera utilisé pour calibrer votre plan personnalisé.", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "<PERSON><PERSON>"}, "age": {"title": "Quel est votre âge ?", "subtitle": "Cela sera utilisé pour calculer vos besoins quotidiens."}, "measurements": {"title": "Taille et poids", "subtitle": "Veuillez saisir votre taille en centimètres et votre poids en kilogrammes."}, "activity": {"title": "Niveau d'activité", "subtitle": "À quelle fréquence faites-vous de l'exercice ?", "none": "Pas d'exercice", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>"}, "goal": {"title": "Objectif de poids", "subtitle": "Qu'aimeriez-vous atteindre ?", "increase": "Augmenter", "maintain": "Maintenir", "decrease": "<PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Poids cible", "subtitle": "Quel est votre poids cible en kilogrammes ?"}, "setup": {"title": "Configuration de votre plan", "subtitle": "Veuillez patienter pendant que nous préparons votre plan nutritionnel."}, "review": {"title": "Révisez votre plan", "subtitle": "Révisez et personnalisez votre plan nutritionnel."}, "height": {"label": "Taille (cm)"}, "weight": {"label": "Poids (kg)"}}, "error": {"updateFailed": "Échec de la mise à jour des données nutritionnelles. Veuillez réessayer.", "parsingError": "Erreur d'analyse des données alimentaires :", "fetchReportsFailed": "Échec de la récupération des données des rapports. Veuillez réessayer.", "missingReportId": "L'ID du rapport est manquant"}}, "personalize": {"nav": {"title": "Personnaliser"}, "button": {"saving": "Enregistrement", "review": "<PERSON><PERSON><PERSON><PERSON>", "saveNext": "Enregistrer et suivant"}}, "basicInfo": {"title": "Apprenons à mieux vous connaître", "subtitle": "Ces informations nous aident à personnaliser vos recommandations de santé", "age": {"question": "Quel âge avez-vous ?", "placeholder": "Entrez votre âge"}, "sex": {"question": "Quel est votre sexe ?", "placeholder": "Sélectionnez votre sexe", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "<PERSON><PERSON>"}, "height": {"question": "Quelle est votre taille ? (cm)", "placeholder": "Entrez votre taille"}, "weight": {"question": "Quel est votre poids ? (kg)", "placeholder": "Entrez votre poids"}}, "lifestyle": {"title": "Vos habitudes de vie", "subtitle": "Comprendre vos habitudes quotidiennes nous aide à vous fournir de meilleures recommandations.", "diet": {"question": "Quel type de régime alimentaire suivez-vous ?", "placeholder": "Sélectionnez votre régime", "vegetarian": "Végétarien", "nonVegetarian": "Non-végétarien", "vegan": "Végétalien", "pescatarian": "Pescatarien", "keto": "<PERSON><PERSON><PERSON>", "paleo": "Pa<PERSON><PERSON>"}, "exercise": {"question": "Faites-vous régulièrement de l'exercice ?"}, "drinking": {"question": "Consommez-vous de l'alcool ?"}, "smoking": {"question": "Fu<PERSON>z-vous ?"}, "sleep": {"question": "Co<PERSON>ien d'heures dormez-vous par nuit ?", "value": "{{sleep}} heures"}, "hydration": {"question": "Combien de tasses d'eau buvez-vous par jour ?", "value": "{{hydration}} tasses ({{liters}}L)"}}, "allergies": {"title": "Avez-vous des allergies ?", "subtitle": "Connaître vos allergies nous aide à vous fournir des recommandations plus sûres.", "allergyIndex": "Allergie {{index}}", "name": {"question": "À quoi êtes-vous allergique ?", "placeholder": "Entrez l'allergie (p. ex., Arachides, Poussière)"}, "severity": {"question": "Quelle est la gravité de cette allergie ?", "placeholder": "Sélectionnez la gravité", "mild": "<PERSON><PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "severe": "Sévere"}, "addButton": "Ajouter une autre allergie", "noAllergiesButton": "Je n'ai aucune allergie."}, "medications": {"title": "Médicaments et suppléments", "subtitle": "Parlez-nous des médicaments ou suppléments que vous prenez actuellement.", "medicationIndex": "Médicament {{index}}", "name": {"label": "Nom du médicament", "placeholder": "Saisis<PERSON>z le nom du médicament"}, "startDate": {"question": "Quand as-tu commencé à le prendre ?", "placeholder": "Sélectionner la date"}, "type": {"label": "Type de médicament", "shortTerm": "Court terme", "longTerm": "Long terme"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "unit": {"label": "Unité"}, "frequency": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Times", "perDay": "par jour", "perWeek": "par semaine", "perMonth": "par mois", "perYear": "par an"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "bouffées", "drops": "gouttes", "tsp": "c. à thé", "tbsp": "cuillère à soupe", "cups": "tasses"}, "addButton": "Ajouter un autre médicament", "noMedicationsButton": "Je ne prends aucun médicament.", "calendar": {"title": "Sélectionner la date de début"}}, "conditions": {"title": "Conditions médicales", "subtitle": "Parlez-nous de tout problème de santé que vous avez ou avez eu par le passé.", "conditionIndex": "Condition {{index}}", "name": {"label": "Nom de la condition", "placeholder": "Sai<PERSON><PERSON>z la condition (p. ex. Asthme, etc.)"}, "since": {"question": "Depuis quand souffrez-vous de ce problème ?", "placeholder": "Sélectionner la date"}, "current": {"question": "Est-ce que cela vous trouble actuellement ?"}, "medicated": {"question": "Prenez-vous des médicaments pour ça ?"}, "addButton": "Ajouter une autre condition", "noConditionsButton": "Je n'ai aucun problème de santé.", "calendar": {"title": "Sélectionner la date"}}, "reproductive": {"title": "<PERSON><PERSON>", "subtitle": "Ces informations nous aident à vous fournir des recommandations de santé plus personnalisées", "menstruation": {"question": "<PERSON><PERSON>-vous déjà eu vos règles ?", "detailsTitle": "Détails des règles", "regularity": {"question": "Quelle est la régularité de votre cycle ?", "regular": "Ré<PERSON>lier", "irregular": "Irrégulier", "notSure": "Je ne sais pas"}, "cycleLength": {"label": "Durée moyenne du cycle (jours)", "placeholder": "Entrez la durée du cycle"}, "flowDays": {"label": "Jours de règles : {{flowDays}}", "min": "1 jour", "max": "15 jours"}, "padsPerDay": {"label": "Serviettes/tampons par jour : {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "<PERSON><PERSON>-vous des symptômes pendant vos règles ?", "placeholder": "Entrez les symptômes (ex: crampes, maux de tête)"}}, "childbirth": {"question": "<PERSON><PERSON>-vous déjà accouché ?", "detailsTitle": "<PERSON><PERSON><PERSON> de l'accouchement", "children": {"label": "Nombre d'enfants"}, "pregnancies": {"label": "Nombre de grossesses"}, "complications": {"question": "Y a-t-il eu des complications pendant la grossesse ou l'accouchement ?", "placeholder": "Entrez les complications (le cas échéant)"}}}, "review": {"title": "Vérifiez vos informations", "subtitle": "Veuillez vérifier les informations que vous avez fournies avant de soumettre", "sections": {"basicInfo": "Informations de base", "lifestyle": "Mode de vie", "allergies": "Allergies", "medications": "Médicaments et suppléments", "conditions": "Conditions médicales", "reproductive": "<PERSON><PERSON>", "menstruationDetails": "Détails des règles", "childbirthDetails": "<PERSON><PERSON><PERSON> de l'accouchement"}, "fields": {"age": "Âge :", "sex": "Sexe :", "height": "Taille :", "weight": "Poids :", "diet": "Régime :", "exercise": "Exercice :", "drinking": "Consommation d'alcool :", "smoking": "Tabagisme :", "sleep": "Sommeil :", "hydration": "Hydratation :", "allergyIndex": "Allergie {{index}} :", "dose": "Dose :", "frequency": "Fréquence :", "type": "Type :", "since": "Depuis :", "currentlyActive": "Actuellement actif :", "takingMedication": "Prend des médicaments :", "hasMenstruated": "A eu ses règles :", "regularity": "Régularité :", "cycleLength": "Durée du cycle :", "flowDays": "Jours de règles :", "padsPerDay": "Serviettes/tampons par jour :", "hasChildbirth": "A accouché :", "children": "Enfants :", "pregnancies": "Grossesses :"}, "notProvided": "Non fourni", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} heures par jour", "hydration": "{{hydration}} tasses ({{liters}}L) par jour", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} jours"}, "noData": {"allergies": "Aucune allergie fournie", "medications": "Aucun médicament fourni", "conditions": "Aucune condition médicale fournie"}, "submitButton": "Soumettre les informations"}, "success": {"title": "Informations mises à jour !", "message": "Merci d'avoir fourni vos informations de santé. Nous les utiliserons pour personnaliser votre expérience et vous fournir de meilleures recommandations.", "benefits": {"insights": "Informations personnalisées sur la santé", "reminders": "Meilleurs rappels de médicaments", "recommendations": "Recommandations de santé personnalisées"}, "continueButton": "Continuer vers le tableau de bord"}, "permissions": {"microphonePermissionDenied": "Accès au microphone refusé", "microphoneAccessDescription": "August nécessite l'accès à votre microphone pour enregistrer l'audio et envoyer des notes vocales", "permissionDenied": "Autorisation refusée", "cameraPermissionRequired": "Nous avons besoin de l'autorisation de la caméra pour que cela fonctionne !", "mediaLibraryPermissionRequired": "Nous avons besoin des autorisations de la bibliothèque multimédia pour que cela fonctionne !"}, "voiceRecording": {"recordingTooLong": "Enregistrement trop long", "recordingTooLongMessage": "Les enregistrements vocaux doivent durer moins de 5 minutes. Veuillez enregistrer un message plus court."}, "errors": {"uploadFailed": "Échec du téléchargement", "voiceUploadFailed": "Impossible de télécharger l'enregistrement vocal.", "voiceRecordingFailed": "Échec de l'envoi de l'enregistrement vocal", "failedToStopRecording": "Échec de l'arrêt de l'enregistrement", "photoUploadFailed": "Impossible de télécharger la photo.", "failedToTakePhoto": "Échec de la prise de photo", "imageUploadFailed": "Impossible de télécharger l'image : {{fileName}}", "failedToPickImage": "Échec de la sélection de l'image", "documentUploadFailed": "Impossible de télécharger le document : {{fileName}}", "failedToPickDocument": "Échec de la sélection du document"}, "audioPlayer": {"downloadingAudio": "Téléchargement de l'audio...", "loadingAudio": "Chargement de l'audio..."}, "mediaProcessing": {"processingFile": "Traitement de votre fichier", "uploadingSecuring": "Téléchargement et sécurisation du fichier...", "analyzingContent": "Analyse du contenu du document...", "extractingInfo": "Extraction des informations clés...", "processingInsights": "Traitement des informations...", "preparingResponse": "Préparation d'une réponse détaillée...", "finalizingResponse": "Finalisation de la réponse..."}, "attachments": {"voiceMessage": "Message vocal", "image": "[IMAGE]", "pdf": "[PDF]", "voice": "[NOTE VOCALE]"}, "pdf": {"loadingPdf": "Chargement du PDF..."}, "dateTime": {"yesterday": "Hier, "}, "navbar": {"defaultTitle": "august", "selectedCount": "sélectionné(s)"}, "mediaUpload": {"photoLibrary": "Photothèque", "takePhoto": "<PERSON><PERSON><PERSON> une photo", "chooseFile": "<PERSON><PERSON> un fichier"}, "comingSoon": {"title": "Bientôt disponible !", "description": " est actuellement en cours de développement. Restez à l'écoute pour les mises à jour !", "buttonText": "Compris !"}, "clipboard": {"success": "Lien copié dans le presse-papiers"}, "mediaPhotos": {"emptyState": "Aucune entrée pour le moment."}, "foodDetail": {"defaultFoodName": "Article alimentaire", "nutrition": {"totalCalories": "Calories totales", "proteins": "Protéines", "carbs": "Glucides", "fat": "Matières grasses", "sugars": "<PERSON><PERSON><PERSON>", "fibers": "Fibres"}}, "reports": {"defaultTitle": "Élément multimédia", "defaultFoodName": "Article alimentaire", "defaultName": "Document", "openButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans une visionneuse externe", "biomarker": {"headerBiomarker": "Biomarqueur", "headerValue": "<PERSON><PERSON>", "headerRefRange": "Fourchette de référence", "headerStatus": "Statut"}, "noData": "Aucune donnée de biomarqueur disponible"}, "setup": {"title": "Nous configurons tout pour vous", "inProgress": "En cours...", "progressMessages": {"0": "Calcul des calories quotidiennes", "1": "Optimisation de la répartition des macronutriments", "2": "Création du plan de repas", "3": "Calcul du score de santé", "4": "Finalisation de la configuration"}, "checklistItems": {"0": "Analy<PERSON> de vos donn<PERSON> de sant<PERSON>", "1": "Calcul d'un plan nutritionnel optimal", "2": "Personnalisation de vos recommandations", "3": "Création de vos suggestions de repas", "4": "Finalisation de votre configuration"}}, "foodEntry": {"emptyState": "Aucun enregistrement de repas pour le moment. Prenez une photo de votre repas pour l'ajouter !"}, "nutritionReview": {"congratulations": "Félicitations !", "subtitle": "Votre plan nutritionnel personnalisé est prêt", "submitButton": "C'est parti !", "dailyTargetsTitle": "Vos objectifs nutritionnels quotidiens", "macroLabels": {"calories": "Calories", "carbs": "Glucides", "protein": "<PERSON><PERSON><PERSON>", "fats": "Matières grasses"}, "recommendations": {"title": "Comment atteindre vos objectifs :", "healthScores": "Utilisez les scores de santé pour améliorer votre routine", "trackFood": "<PERSON><PERSON>z régulièrement votre consommation alimentaire", "followCalories": "Su<PERSON>z vos recommandations quotidiennes en calories", "balanceMacros": "Équilibrez votre apport en glucides, protéines et lipides"}}, "editModal": {"titlePrefix": "Modifier", "cancelButton": "Annuler", "saveButton": "Suivant"}, "processing": {"stages": {"scanning": "Analyse de la nourriture...", "identifying": "Identification des ingrédients...", "extracting": "Extraction des nutriments…", "finalizing": "Finalisation des résultats…"}, "error": {"defaultMessage": "Aucun aliment détecté", "subtitle": "Essayez un autre angle"}, "retakeButton": "Touchez pour reprendre la photo", "notification": "Nous vous préviendrons dès que ce sera fait !"}, "chart": {"title": "Suivi nutritionnel au fil du temps", "selectNutrient": "Sélectionner le nutriment :", "emptyState": "Aucune donnée nutritionnelle disponible pour le moment.", "dropdown": {"calories": "Calories", "protein": "<PERSON><PERSON><PERSON>", "carbs": "Glucides", "fat": "Gros", "sugars": "<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "Article alimentaire", "defaultDate": "<PERSON><PERSON><PERSON>'hui", "defaultTime": "Heure inconnue", "saveChanges": "Enregistrer les modifications", "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Échec de la mise à jour des données nutritionnelles. Veuillez réessayer."}, "nutrition": {"calories": "🔥 Calories", "proteins": "🥩 Protéines", "carbs": "🍞 Glucides", "sugars": "🍬 Sucres", "fat": "🥑 Gras"}, "macroBreakdown": {"title": "Répartition des macronutriments", "noData": "Aucune donnée sur les macronutriments n'est disponible pour cet aliment."}, "macroLabels": {"calories": "Calories", "protein": "<PERSON><PERSON><PERSON>", "carbs": "Glucides", "fat": "Gros", "sugar": "<PERSON><PERSON>"}}, "infoModal": {"title": "Informations détaillées", "edit": "Modifier", "save": "Enregistrer", "saving": "Enregistrement…", "enterValue": "Entrez une valeur", "notSet": "Non défini", "age": "Âge", "heightCm": "Taille (cm)", "weightKg": "Poids (kg)", "targetWeight": "Poids cible", "nutritionTargets": "Objectifs nutritionnels", "protein": "<PERSON><PERSON><PERSON>", "carbs": "Glucides", "fats": "Matières grasses", "gm": "gm", "editNote": "Saisissez des valeurs ou laissez les champs vides pour un calcul automatique.", "autoCalculateNote": "Les macros sont calculées automatiquement en fonction de vos données.", "validation": {"ageMin": "L'âge doit être d'au moins 18 ans", "ageMax": "L'âge doit être inférieur à 125", "heightMin": "La hauteur doit être d'au moins 50 cm", "heightMax": "La hauteur doit être inférieure à 250 cm", "weightMin": "Le poids doit être d'au moins 30 kg", "weightMax": "Le poids doit être inférieur à 500 kg", "targetWeightMin": "Le poids cible doit être d'au moins 30 kg", "targetWeightMax": "Le poids cible doit être inférieur à 500 kg", "proteinMin": "Les protéines doivent être égales à 0 ou plus.", "carbsMin": "Les glucides doivent être supérieurs ou égaux à 0.", "fatsMin": "Les matières grasses doivent être de 0 ou plus."}}, "tracker": {"calories": "Calories", "protein": "<PERSON><PERSON><PERSON>", "carbs": "Glucides", "fat": "Gros", "excess": "excès", "remaining": "restant"}, "specialistConstants": {"nutritionist": {"name": "Nutritionniste", "description": "Conseils d'experts sur l'alimentation, la nutrition et les saines habitudes alimentaires", "featureName": "Spécialiste en nutrition"}, "cardiologist": {"name": "Cardiologue", "description": "Spécialisé en santé cardiaque et affections cardiovasculaires", "featureName": "Spécialiste en cardiologie"}, "neurologist": {"name": "Neurologue", "description": "Spécialisé dans les troubles du cerveau, de la moelle épinière et du système nerveux", "featureName": "Spécialiste en neurologie"}, "oncologist": {"name": "Oncologue", "description": "Spécialisé dans le diagnostic du cancer et les options de traitement", "featureName": "Spécialiste en oncologie"}, "endocrinologist": {"name": "Endocrinologue", "description": "Expert en affections hormonales et troubles métaboliques", "featureName": "Spécialiste en endocrinologie"}}, "discoverCards": {"categories": {"nutrition": "Nutrition", "heartHealth": "<PERSON><PERSON>", "mentalHealth": "Santé <PERSON>e", "fitness": "Fitness", "wellness": "Bien-être"}, "titles": {"vitaminB12Recovery": "Combien de temps faut-il pour se remettre d'une carence en vitamine B12 ?", "vitaminDeficiencyGanglion": "Quelle carence en vitamine cause des kystes ganglionnaires ?", "vitaminDeficiencyHairFall": "Quelle carence en vitamine cause la chute des cheveux ?", "vitaminWaters": "Les eaux vitaminées sont-elles bonnes pour la santé ?", "cholesterolHeadaches": "Le cholestérol élevé provoque-t-il des maux de tête ?", "cholesterolEyes": "Quels sont les symptômes d'un taux élevé de cholestérol visibles dans les yeux ?", "diabetesHeadaches": "Le diabète peut-il causer des maux de tête ?", "chestPainDrinking": "Pourquoi la poitrine fait-elle mal après avoir bu ?", "stressDizziness": "Le stress peut-il causer des vertiges ?", "bulimiaFace": "Le visage de la boulimie", "kneeTwitch": "Pourquoi mon genou tremble-t-il ?", "noseTwitching": "Pourquoi les nez se contractent-ils ?", "piriformisVsSciatica": "Quelles sont les différences entre le syndrome du piriforme et la sciatique ?", "shoulderBladePinched": "Comment soulager un nerf pincé dans l'omoplate", "shoulderPinched": "Comment soulager un nerf pincé à l'épaule", "meniscusTorn": "Comment soigner un ménisque déchiré naturellement", "hydrateQuickly": "Comment s'hydrater rapidement", "periodConstipation": "Est-il normal d'être constipée pendant ses règles ?", "acneScars": "Comment se débarrasser naturellement des cicatrices d'acné en une semaine", "perimenopausePregnancy": "Peut-on tomber enceinte pendant la périménopause"}, "descriptions": {"vitaminB12Recovery": "Dé<PERSON>uvrez le délai de récupération d'une carence en vitamine B12 et des remèdes efficaces pour augmenter votre niveau d'énergie.", "vitaminDeficiencyGanglion": "Explorez le lien entre les carences en vitamines et le développement de kystes ganglionnaires dans le corps.", "vitaminDeficiencyHairFall": "Découvrez comment un manque de vitamines essentielles peut entraîner une chute de cheveux et ce que vous pouvez faire pour l'éviter.", "vitaminWaters": "Découvrez les avantages et les inconvénients potentiels des eaux vitaminées dans le cadre de votre alimentation quotidienne.", "cholesterolHeadaches": "Examinez le lien possible entre un taux de cholestérol <PERSON> et l'apparition de maux de tête.", "cholesterolEyes": "Découvrez comment un taux de cholestérol élevé peut se manifester dans vos yeux et quels symptômes surveiller.", "diabetesHeadaches": "Examinez la relation entre le diabète et l'apparition de maux de tête dans la vie quotidienne.", "chestPainDrinking": "Explorez les raisons de la douleur thoracique après la consommation de certaines boissons.", "stressDizziness": "Approfondissez la manière dont le stress peut affecter votre équilibre et votre bien-être général, entraînant des vertiges.", "bulimiaFace": "Comprenez les signes physiques de la boulimie, y compris les effets sur l'apparence du visage.", "kneeTwitch": "Examinez les causes possibles des contractions involontaires du genou et leur lien avec le stress ou la fatigue.", "noseTwitching": "Découvrez les raisons possibles des contractions du nez et leur lien avec l'anxiété ou d'autres facteurs.", "piriformisVsSciatica": "Comparez les symptômes du syndrome du piriforme et de la sciatique pour mieux comprendre votre état.", "shoulderBladePinched": "Découvrez des techniques efficaces pour soulager une compression nerveuse dans votre omoplate et restaurer la mobilité.", "shoulderPinched": "Apprenez des exercices et des étirements simples pour soulager la compression nerveuse dans la région de l'épaule.", "meniscusTorn": "Explorez les méthodes naturelles et les exercices pour soutenir la guérison d'une déchirure du ménisque.", "hydrateQuickly": "Découvrez des moyens rapides et efficaces de vous réhydrater et de maintenir une hydratation corporelle optimale.", "periodConstipation": "Comprenez les raisons de la constipation pendant les menstruations et découvrez des remèdes naturels.", "acneScars": "Découvrez des remèdes naturels et des conseils de soins de la peau pour réduire rapidement l'apparence des cicatrices d'acné.", "perimenopausePregnancy": "Découvrez la périménopause, les considérations en matière de fertilité et à quoi vous attendre pendant cette étape de la vie."}}}