import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  Dimensions
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { FileText } from 'lucide-react-native';
import { Platform } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

type MediaProcessingCardProps = {
  isPdf?: boolean;
  isImage?: boolean;
};

const MediaProcessingCard = ({ isPdf = true, isImage = false }: MediaProcessingCardProps) => {
  const { t } = useTranslation();
  
  const PROCESSING_STAGES = [
    t('mediaProcessing.uploadingSecuring'),
    t('mediaProcessing.analyzingContent'),
    t('mediaProcessing.extractingInfo'),
    t('mediaProcessing.processingInsights'),
    t('mediaProcessing.preparingResponse'),
    t('mediaProcessing.finalizingResponse')
  ];
  const [currentStage, setCurrentStage] = useState(0);
  const [barWidth, setBarWidth] = useState(0);

  // Animation values
  const progressAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Calculate total duration based on document type
  const totalDuration = isPdf ? 70000 : 40000;

  // Calculate individual stage durations
  const stageDurations = [2000, 2000, 3000, 2000, 3000, 15000];
  const [displayedStage, setDisplayedStage] = useState(0);

  // Calculate cumulative durations for stage transitions
  const cumulativeDurations = stageDurations.reduce((acc, duration, index) => {
    const prevTotal = index > 0 ? acc[index - 1] : 0;
    acc.push(prevTotal + duration);
    return acc;
  }, [] as number[]);

  // Normalize to percentage of total duration
  const stagePercentages = cumulativeDurations.map(d => d / cumulativeDurations[cumulativeDurations.length - 1]);

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: 1,
      duration: totalDuration,
      easing: Easing.bezier(0.4, 0.0, 0.2, 1),
      useNativeDriver: false,
    }).start();
  
    const stageTimers: NodeJS.Timeout[] = [];
  
    stagePercentages.forEach((percentage, index) => {
      if (index === 0) return;
  
      const timer = setTimeout(() => {
        // Trigger fade out
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start(() => {
          setDisplayedStage(index);  // update what is shown after fade-out
          setCurrentStage(index);    // logical state update
  
          // Then fade in
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }).start();
        });
      }, percentage * totalDuration - 400);
  
      stageTimers.push(timer);
    });
  
    return () => {
      stageTimers.forEach(timer => clearTimeout(timer));
    };
  }, []);  

  // Interpolate progress value to width percentage
  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  // Interpolate progress value to emoji position
  const emojiPosition = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, Math.max(barWidth - 30, 0)], // subtract icon size
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('mediaProcessing.processingFile')}</Text>

      <View style={styles.progressWrapper}>
        <View style={styles.progressContainer} onLayout={(event) => setBarWidth(event.nativeEvent.layout.width)}>
          <Animated.View style={[styles.progressBar, { width: progressWidth }]}>
            <LinearGradient
              colors={['#206E55', '#2a8c6d']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ flex: 1 }}
            />
          </Animated.View>
        </View>

        {/* Icon container positioned absolutely relative to the wrapper */}
        <Animated.View
          style={[
            styles.emojiContainer,
            { transform: [{ translateX: emojiPosition }] }
          ]}
        >
          <View style={styles.emojiCircle}>
            <FileText size={20} color="#206E55" />
          </View>
        </Animated.View>
      </View>

      <View style={styles.stageContainer}>
        <Animated.Text style={[styles.stageText, { opacity: fadeAnim }]}>
          {PROCESSING_STAGES[displayedStage]}
        </Animated.Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    marginHorizontal: 15,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f8f9fa',
    borderColor: '#e2e8f0',
    borderWidth: 1,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginLeft: 20
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
    textAlign: 'center',
  },
  progressWrapper: {
    position: 'relative',
    marginVertical: 10,
    height: 30, // Increased height to accommodate the icon
    width: '100%',
  },
  progressContainer: {
    height: 12,
    backgroundColor: '#e2e8f0',
    borderRadius: 6,
    overflow: 'hidden',
    width: '100%',
    marginTop: 10, // Center the progress bar vertically in the wrapper
  },
  progressBar: {
    height: '100%',
    width: '100%',
    borderRadius: 6,
    position: 'absolute',
  },
  emojiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  stageContainer: {
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',

  },
  stageText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#334155',
  },
});

export default MediaProcessingCard;