{"common": {"error": "Σφάλμα", "yes": "Ναι", "no": "Όχι", "sometimes": "Μερικές φορές", "close": "Κλείσιμο", "cancel": "Ακύρωση", "save": "Αποθήκευση", "next": "Επόμενο", "loading": "Φόρτωση...", "version": "v0.0.1.7"}, "welcome": "Συνδεθείτε για να ξεκινήσετε να μιλάτε με τον Αύγουστο", "notFound": {"title": "Οups!", "message": "Η οθόνη αυτή δεν υπάρχει.", "goHome": "Πήγαινε στην αρχική οθόνη!"}, "library": {"title": "Βιβλιοθήκη Υγείας"}, "specialists": {"title": "Ειδικοί", "description": "Συμβουλευτείτε εξειδικευμένους επαγγελματίες υγείας για πιο συγκεκριμένα θέματα υγείας. Επιλέξτε έναν ειδικό παρακάτω:", "generalPhysician": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ<PERSON>ς", "description": "Για γενικά θέματα υγείας και πρωτοβάθμια φροντίδα."}, "nutritionist": {"title": "Διατροφολόγος", "description": "Για συμβουλές διατροφής, διατροφής και διαχείρισης βάρους."}, "cardiologist": {"title": "Καρ<PERSON>ιολόγος", "description": "Για καρδιακά προβλήματα και καρδιαγγειακή υγεία."}, "neurologist": {"title": "Νευρ<PERSON>λόγος", "description": "Για προβλήματα εγκεφάλου, νωτιαίου μυελού και νευρικού συστήματος."}, "oncologist": {"title": "Ο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ος", "description": "Για θέματα που σχετίζονται με τον καρκίνο και θεραπείες."}, "endocrinologist": {"title": "Ενδοκρινολόγος", "description": "Για διατ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> που σχετίζονται με ορμόνες και διαχείριση διαβήτη."}, "dermatologist": {"title": "Δερματολόγος", "description": "Για δερματικ<PERSON>ς παθήσεις, μαλλιών και νυχιών."}, "psychiatrist": {"title": "Ψυχ<PERSON>α<PERSON><PERSON>ος", "description": "Για θέματα ψυχικής υγείας και ψυχολογικής ευεξίας."}}, "profile": {"title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "defaultName": "Ξένος", "namePlaceholder": "Εισάγετε το όνομά σας", "saving": "Αποθήκευση...", "noPhoneNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON> αριθμό τηλεφώνου", "loggingOut": "Αποσύνδεση...", "about": {"title": "Σχετικά", "description": "Μάθετε περισσότερα για τον Αύγουστο"}, "whatsapp": {"title": "WhatsApp", "description": "Μιλήστε με τον Αύγουστο στο WhatsApp"}, "refer": {"title": "Προώθηση", "description": "Σας άρεσε ο Αύγουστος; Μοιραστείτε το με τους φίλους σας"}, "deleteAccount": {"title": "Διαγραφή λογαριασμού", "description": "Λυπούμαστε που φεύγετε"}, "logout": {"title": "Αποσύνδεση", "description": "Επιστρέψτε σύντομα. Θα μας λείψετε"}, "shareMessage": "👋Γεια σας, Δείτε αυτή την καταπληκτική εφαρμογή που χρησιμοποιώ!\n\n\n\n➡️Χρησιμοποιώ τον Αύγουστο για να λαμβάνω γρήγορες, αξι<PERSON><PERSON>ιστες πληροφορίες και καθοδήγηση για την υγεία. Είναι σαν να έχεις έναν γιατρό στην τσέπη σου! Δείτε το εδώ:", "error": {"loadFailed": "Αποτυχ<PERSON>α φόρτωσης δεδομένων χρήστη", "fetchError": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά την ανάκτηση δεδομένων χρήστη", "updateNameFailed": "Αποτυχία ενημέρωσης ονόματος", "updateNameError": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά την ενημέρωση ονόματος", "loadFoodData": "Αποτυχία φόρτωσης δεδομένων τροφίμων", "logoutError": "Σφάλμα κατά την αποσύνδεση:", "shareError": "Σφάλμα κοινής χρήσης μηνυμάτων:"}}, "error": {"title": "Κάτι πήγε στραβά", "checkLogs": "Ελέγξτε τα αρχεία καταγραφής της συσκευής σας για περισσότερες λεπτομέρειες.", "unknown": "Άγνωστο σφάλμα", "unknownFile": "Άγνωστο αρχείο", "unknownLine": "Άγνωστη γραμμή", "unknownColumn": "Άγνωστη στήλη"}, "auth": {"phone": {"selectCountry": "Επιλογ<PERSON>", "searchCountries": "Αναζήτη<PERSON>η χωρών", "validation": {"invalidPhone": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγε<PERSON>ε έναν έγκυρο αριθμό τηλεφώνου", "invalidDigits": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισά<PERSON><PERSON><PERSON><PERSON> έναν έγκυρο αριθμό τηλεφώνου (7-15 ψηφία)"}}, "header": {"title": "Αποκτή<PERSON><PERSON><PERSON> άμεσα και ιδιωτικά σαφήνεια για τις ανησυχίες σας για την υγεία σας", "subtitle": "Σοβα<PERSON><PERSON> καθοδήγηση. <PERSON><PERSON><PERSON><PERSON><PERSON> βιασύνη. <PERSON><PERSON><PERSON><PERSON><PERSON> σύγχυση.", "emphasis": "Μόνο σαφήνεια."}, "greeting": "Γεια σου 👋", "phoneNumber": "Αριθ<PERSON><PERSON><PERSON> Τ<PERSON>λεφώνου", "requestOTP": "Αίτηση OTP", "otp": {"title": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> Μια<PERSON>ς", "verify": "Επαλήθευση OTP", "sending": "Αποστολή...", "countdown": "Επανά<PERSON><PERSON><PERSON>τ<PERSON><PERSON><PERSON> OTP σε {{countdown}} δευτερόλεπτα", "resend": "Επανάποστολή OTP", "sentTo": "OTP αποσταλμένο σε ", "whatsappSuffix": " στο <PERSON>"}, "disclaimer": {"prefix": "Εγγραφόμενοι, συμφωνείτε με τους ", "continuePrefix": "Συνε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, συμφωνείτε με τους ", "termsOfService": "Όρους Χρήσης", "and": " και ", "privacyPolicy": "Πολιτική Απορρήτου", "whatsappConsent": ", και δίνετε τη συγκατάθεσή σας για να λαμβάνετε ενημερώσεις & υπενθυμίσεις από εμάς μέσω WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Καλ<PERSON>ς ήρθατε στον Αύγουστο!", "buttonText": "Ας ξεκινήσουμε"}}, "postAuth": {"step1": {"title": "Γεια!", "subtitle": "Είμαι ο Αύγουστος 👋", "description": "Σκεφτείτε με ως την άνετη γωνιά στη συσκευή σας όπου εξερευνάτε όλες τις απορίες σας για την υγεία.", "subdescription": "Μη διστάσετε να ρωτήσετε οτιδήποτε σας απασχολεί.\nΧωρίς κρίση, χωρίς όρια!", "placeholder": "Πώς να σας φωνάζω;"}, "step2": {"title": "Γεια σου {{userName}},", "subtitle": "Αυτό μπορώ να κάνω:", "features": {"health": {"title": "Απαντήστε στις", "subtitle": "Ερωτήσεις υγείας"}, "nutrition": {"title": "Παρακολουθήστε τα", "subtitle": "Μακροθρεπτικά"}, "reports": {"title": "Αναλύστε", "subtitle": "Αναφορές"}}}, "pills": {"thoughtful": "Σοβ<PERSON><PERSON><PERSON>ς", "careful": "Προσεκτικ<PERSON>ς", "accurate": "Ακριβής"}, "features": {"symptoms": {"title": "Ελέγξτε τα συμπτώματά σας", "description": "Νιώθω ναυτία για μια εβδομάδα. Τι μου συμβαίνει;"}, "prescriptions": {"title": "Αναλύστε τις συνταγές σας", "description": "Ανεβάστε και κατανοήστε τις συνταγές σας όπως ένας γιατρός."}, "medicine": {"title": "Μάθετε για τα φάρμακά σας", "description": "Αλληλεπιδρά η Μετφορμίνη για την ΠΟΚΥ με τα χάπια μου για ΔΕΠΥ;"}, "plans": {"title": "Λάβετε εξατομικευμένα προγράμματα", "description": "Μπορείτε να μου δώσετε ένα πρόγραμμα διατροφής & γυμναστικής για τη μείωση των επιπέδων HbA1c;"}}, "buttons": {"getStarted": "Ξεκινήστε", "next": "Επόμενο"}, "errors": {"nameRequired": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε το όνομά σας"}}}, "tabs": {"chat": "Cha<PERSON>", "discover": "Ανακάλυψη", "nutrition": "Διατροφή", "personalize": "Εξατομίκευση"}, "chat": {"nav": {"title": "Αύγουστος"}, "me": "Εγώ", "augustName": "Αύγουστος", "input": {"placeholder": "Ρωτήστε τον Αύγουστο...", "disclaimer": "Ο Αύγουστος μπορεί να κάνει λάθη. Επιβεβαιώστε με έναν γιατρό"}, "list": {"loadingMessages": "Φόρτωση μηνυμάτων...", "noMessages": "Δεν υπάρχουν ακόμη μηνύματα. Ξεκινήστε μια συνομιλία!"}, "connection": {"offlineMessage": "Φαίνετα<PERSON> ότι είστε εκτός σύνδεσης. Συνδεθείτε ξανά για να στείλετε μηνύματα.", "connecting": "Σύνδεση...", "tryAgain": "Προσπαθήστε ξανά"}, "prompts": {"uploadReport": "Ανεβάστε αναφορά", "speakInHindi": "Μιλήστε στα Χίντι", "notFeelingWell": "Δεν αισθάνομαι καλά", "whatIsMyBMI": "Ποιος είναι ο ΔΜΣ μου;", "nutritionAdvice": "Συμβουλές διατροφής", "sleepBetter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "citations": {"referenceText": "Για περισσότερες λεπτομέρειες σχετικά με αυτή τη συνομιλία, ανατρέξτε στο:"}, "actions": {"copiedToClipboard": "Αντιγρά<PERSON>ηκε στο πρόχειρο", "copied": "Αντιγράφηκε"}, "share": {"introText": "👋Γεια σας, δείτε τη συνομιλία που είχα με τον Αύγουστο:\n\n", "downloadText": "\n\n➡️Κατεβάστε τον Αύγουστο για να συνομιλήσετε με τον φιλικό σας βοηθό υγείας AI:\n"}}, "discover": {"nav": {"title": "Ανακαλύψτε"}, "categories": {"all": "Όλα", "heartHealth": "Υγεία καρδιάς", "nutrition": "Διατροφή", "mentalHealth": "Ψυχική υγεία", "fitness": "Φυσική κατάσταση", "wellness": "Ευεξία"}, "cards": {"empty": "Δεν υπάρχουν διαθέσιμες κάρτες για αυτήν την κατηγορία"}, "sections": {"features": "<PERSON><PERSON><PERSON><PERSON><PERSON>τηριστικά"}, "features": {"healthLibrary": {"title": "Ιατρική Βιβλιοθήκη", "description": "Πρόσβαση σε αξιόπιστες, ακριβ<PERSON><PERSON>ς και ενημερωμένες ιατρικές πληροφορίες εντελώς δωρεάν."}, "nutritionTracker": {"title": "Πιστοποιητικό Διατροφής", "description": "Αναρωτηθήκατε ποτέ αν θα μπορούσατε απλώς να ανεβάσετε μια φωτογραφία του φαγητού σας και να παρακολουθήσετε όλους τους διατροφικούς σας στόχους; Ο Αύγουστος μπορεί να το κάνει αυτό!"}, "multilingualSupport": {"title": "Πολυγλωσσική υποστήριξη", "description": "Μπορείτε να επικοινωνήσετε με τον Αύγουστο σε οποιαδήποτε γλώσσα αισθάνεστε άνετα! Ο Αύγουστος είναι πάντα εδώ για να σας ακούσει, να σας στηρίξει και να σας απαντήσει όποτε χρειαστείτε.", "samplePrompt": "Μιλήστε στα Χίντι"}, "labReportAnalysis": {"title": "Ανάλυση εργαστη<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποτελεσμάτων", "description": "Όταν μιλάτε στον Αύγουστο για τις εργαστηριακές σας αναφορές, έχετε εξαιρετική ακρίβεια. Ο Αύγουστος έχει επεξεργαστεί πάνω από 4,7 εκατομμύρια αναφορές με ακρίβεια εξαγωγής βιοδεικτών 98,4%."}}}, "nutrition": {"nav": {"title": "Διατροφή"}, "meals": {"title": "Τα Γεύματά Σας", "subtitle": "Πατήστε για να δείτε τα μακροθρεπτικά συστατικά κάθε γεύματος"}, "upload": {"loading": "Φόρτωση εικόνας..."}, "defaultFoodName": "<PERSON><PERSON><PERSON><PERSON>ροφ<PERSON>ς", "today": "Σήμερα", "unknownTime": "Άγνωστη ώρα", "calories": "🔥 Θερμίδες", "proteins": "🥩 Πρωτεΐνες", "carbs": "🍞 Υδατάνθρακες", "sugars": "🍬 Ζάχαρη", "fat": "🥑 Λίπος", "caloriesLabel": "Θερμίδες", "proteinLabel": "Πρωτεΐνη", "carbohydratesLabel": "Υδατάνθρακες", "fatLabel": "<PERSON><PERSON><PERSON><PERSON>", "sugarLabel": "Ζάχαρη", "tips": "Συμβουλές:", "macroBreakdown": "Ανάλυση Μακροθρεπτικών Συστατικών", "noMacroData": "Δεν υπάρχουν διαθέσιμα δεδομένα μακροθρεπτικών συστατικών για αυτό το είδος τροφής.", "disclaimer": "Μόνο για εκπαιδευτικούς σκοπούς. Μάθετε περισσότερα", "disclaimerLink": "εδώ", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Ποιο είναι το φύλο σας;", "subtitle": "Αυτό θα χρησιμοποιηθεί για να βαθμονομήσουμε το εξατομικευμένο πρόγραμμά σας.", "male": "Άνδρας", "female": "Γυνα<PERSON><PERSON><PERSON>", "other": "Άλλο"}, "age": {"title": "Πόσ<PERSON> χρονών είστε;", "subtitle": "Αυτό θα χρησιμοποιηθεί για να υπολογίσουμε τις ημερήσιες ανάγκες σας."}, "measurements": {"title": "Ύψος & Βάρος", "subtitle": "Παρακαλούμε εισάγετε το ύψος σας σε εκατοστά και το βάρος σας σε κιλά."}, "activity": {"title": "Επίπεδ<PERSON> Δραστηριότητας", "subtitle": "Πόσο συχνά γυμνάζεστε;", "none": "Καμία Άσκηση", "moderate": "Μέτρια", "high": "Υψηλή"}, "goal": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "subtitle": "Τι θέλετε να πετύχετε;", "increase": "Αύξηση", "maintain": "Συντήρηση", "decrease": "Μείωση"}, "targetWeight": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "subtitle": "Ποιο είναι ο στόχος σας σε κιλά;"}, "setup": {"title": "Ρύθμιση του Προγράμματός Σας", "subtitle": "Παρακαλούμε περιμένετε ενώ ετοιμάζουμε το πρόγραμμα διατροφής σας."}, "review": {"title": "Επισκόπηση Προγράμματος", "subtitle": "Επισκόπηση και προσαρμογή του προγράμματος διατροφής σας."}, "height": {"label": "Ύψος (cm)"}, "weight": {"label": "Βάρος (kg)"}}, "error": {"updateFailed": "Αποτυχ<PERSON>α ενημέρωσης δεδομένων διατροφής. Παρακαλούμε δοκιμάστε ξανά.", "parsingError": "Σφάλμα ανάλυσης δεδομένων τροφής:", "fetchReportsFailed": "Αποτυχ<PERSON><PERSON> λήψης δεδομένων αναφορών. Παρακαλούμε δοκιμάστε ξανά.", "missingReportId": "Λείπει το αναγνωριστικ<PERSON> αναφοράς"}}, "personalize": {"nav": {"title": "Εξατομίκευση"}, "button": {"saving": "Αποθήκευση", "review": "Επισκόπηση", "saveNext": "Αποθήκευση & Επόμενο"}}, "basicInfo": {"title": "Ας σας γνωρίσουμε καλύτερα", "subtitle": "Αυτές οι πληροφορίες μας βοηθούν να εξατομικεύσουμε τις συστάσεις υγείας σας", "age": {"question": "Πόσ<PERSON> χρονών είστε;", "placeholder": "Εισάγετε την ηλικία σας"}, "sex": {"question": "Ποιο είναι το φύλο σας;", "placeholder": "Επιλέξτε το φύλο σας", "male": "Άνδρας", "female": "Γυνα<PERSON><PERSON><PERSON>", "other": "Άλλο"}, "height": {"question": "Ποιο είναι το ύψος σας; (cm)", "placeholder": "Εισάγετε το ύψος σας"}, "weight": {"question": "Ποιο είναι το βάρος σας; (kg)", "placeholder": "Εισάγετε το βάρος σας"}}, "lifestyle": {"title": "Οι Συνήθειες του Τρόπου Ζωής Σας", "subtitle": "Η κατανόηση των καθημερινών σας συνηθειών μας βοηθά να σας δώσουμε καλύτερες συστάσεις", "diet": {"question": "Τι είδους διατροφή ακολουθείτε;", "placeholder": "Επιλέξτε τη διατροφή σας", "vegetarian": "Χορτοφαγική", "nonVegetarian": "Παντοφαγική", "vegan": "Vegan", "pescatarian": "Πεσκαταριανή", "keto": "Keto", "paleo": "Παλαιολιθική"}, "exercise": {"question": "Γυμνάζεστε τακτικά;"}, "drinking": {"question": "Καταν<PERSON>λών<PERSON><PERSON>ε αλκοόλ;"}, "smoking": {"question": "Καπνίζετε;"}, "sleep": {"question": "Πόσες ώρες κοιμάστε κάθε βράδυ;", "value": "{{sleep}} ώρες"}, "hydration": {"question": "Πόσα ποτήρια νερό πίνετε καθημερινά;", "value": "{{hydration}} ποτήρια ({{liters}}L)"}}, "allergies": {"title": "Έχετε αλλεργίες;", "subtitle": "Η γνώση των αλλερ<PERSON>ιών σας μας βοηθά να σας δώσουμε ασφαλέστερες συστάσεις", "allergyIndex": "Αλλεργία {{index}}", "name": {"question": "Σε τι είστε αλλεργικοί;", "placeholder": "Εισάγετε αλλεργία (π.χ., <PERSON><PERSON><PERSON>τ<PERSON><PERSON><PERSON><PERSON>, Σκόνη)"}, "severity": {"question": "Πόσο σοβαρή είναι αυτή η αλλεργία;", "placeholder": "Επιλέξτε σοβαρότητα", "mild": "Ήπια", "moderate": "Μέτρια", "severe": "Σοβαρή"}, "addButton": "Προσθήκη άλλης αλλεργίας", "noAllergiesButton": "Δεν έχω αλλεργίες"}, "medications": {"title": "Φάρμακα & Συμπληρώματα", "subtitle": "Πείτε μας για τυχόν φάρμακα ή συμπληρώματα που παίρνετε αυτή τη στιγμή", "medicationIndex": "Φάρμακ<PERSON> {{index}}", "name": {"label": "Όνομα Φαρμάκου", "placeholder": "Εισάγετε το όνομα του φαρμάκου"}, "startDate": {"question": "Πότε αρχίσατε να το παίρνετε;", "placeholder": "Επιλέξτε ημερομηνία"}, "type": {"label": "<PERSON><PERSON><PERSON><PERSON>κου", "shortTerm": "Βραχυπρόθεσμο", "longTerm": "Μακροπρόθεσμο"}, "dose": {"label": "Δόση", "placeholder": "Ποσότητα"}, "unit": {"label": "Μονάδα"}, "frequency": {"label": "Συχνότητα", "placeholder": "<PERSON>ορ<PERSON>ς", "perDay": "ημερησίως", "perWeek": "εβδομαδιαίως", "perMonth": "μηνιαίως", "perYear": "ετησίως"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "puffs", "drops": "drops", "tsp": "tsp", "tbsp": "tbsp", "cups": "cups"}, "addButton": "Προσθήκη άλλου φαρμάκου", "noMedicationsButton": "Δεν παίρνω φάρμακα", "calendar": {"title": "Επιλέξτε Ημερομηνία Έναρξης"}}, "conditions": {"title": "Ιατρικές Παθήσεις", "subtitle": "Πείτε μας για τυχόν ιατρικές παθήσεις που έχετε ή είχατε στο παρελθόν", "conditionIndex": "Παθήση {{index}}", "name": {"label": "Όνομα Παθήσης", "placeholder": "Εισάγετε πάθηση (π.χ. Άσθμα, κλπ)"}, "since": {"question": "Από πότε έχετε αυτή την πάθηση;", "placeholder": "Επιλέξτε ημερομηνία"}, "current": {"question": "Σας ενοχλεί αυτή τη στιγμή;"}, "medicated": {"question": "Πάρετε φάρμακα για αυτό;"}, "addButton": "Προσθήκη άλλης πάθησης", "noConditionsButton": "Δεν έχω ιατρικές παθήσεις", "calendar": {"title": "Επιλέξτε Ημερομηνία"}}, "reproductive": {"title": "Υγεία Αναπαραγωγής", "subtitle": "Αυτές οι πληροφορίες μας βοηθούν να σας παρέχουμε πιο εξατομικευμένες συστάσεις υγείας", "menstruation": {"question": "Έχετε ποτέ έμμηνο ρύση;", "detailsTitle": "Λεπτομέρειες Έμμηνης Ρύσης", "regularity": {"question": "Πόσο τακτικ<PERSON>ς είναι ο κύκλος σας;", "regular": "Τακτικ<PERSON>ς", "irregular": "Ακαν<PERSON>νιστος", "notSure": "Δεν είμαι σίγουρος/η"}, "cycleLength": {"label": "<PERSON><PERSON><PERSON><PERSON> μέσου κύκλου (ημέρες)", "placeholder": "Εισάγετε το μήκος του κύκλου"}, "flowDays": {"label": "Ημέρες ροής: {{flowDays}}", "min": "1 ημέρα", "max": "15 ημέρες"}, "padsPerDay": {"label": "Σερβιέτες/ταμπόν ανά ημέρα: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Οποιαδήποτε συμπτώματα κατά την περίοδο σας;", "placeholder": "Εισάγετε συμπτώματα (π.χ., κράμπες, πονοκέφαλοι)"}}, "childbirth": {"question": "Έχετε περάσει από τοκετό;", "detailsTitle": "Λεπτομέρειες Τοκετού", "children": {"label": "Αριθμ<PERSON>ς <PERSON>ι<PERSON>ν"}, "pregnancies": {"label": "Αριθ<PERSON><PERSON>ς <PERSON>ν"}, "complications": {"question": "Οποιεσδήποτε επιπλοκές κατά την εγκυμοσύνη ή τον τοκετό;", "placeholder": "Εισάγετε επιπλοκές (αν υπάρχουν)"}}}, "review": {"title": "Ελέγξτε τις Πληροφορίες Σας", "subtitle": "Παρακαλούμε ελέγξτε τις πληροφορίες που δώσατε πριν υποβάλετε", "sections": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "lifestyle": "Τρόπος Ζωής", "allergies": "Αλλεργ<PERSON>ες", "medications": "Φάρμακα & Συμπληρώματα", "conditions": "Ιατρικ<PERSON><PERSON> Καταστάσεις", "reproductive": "Υγεία Αναπαραγωγής", "menstruationDetails": "Λεπτομέρειες Έμμηνης Ρύσης", "childbirthDetails": "Λεπτομέρειες Τοκετού"}, "fields": {"age": "Ηλικία:", "sex": "Φύλο:", "height": "Ύψος:", "weight": "Βάρος:", "diet": "Διατροφή:", "exercise": "Άσκηση:", "drinking": "Κατανάλω<PERSON>η αλκοόλ:", "smoking": "Κάπνισμα:", "sleep": "Ύπνος:", "hydration": "Ενυδάτωση:", "allergyIndex": "Αλλεργία {{index}}:", "dose": "Δόση:", "frequency": "Συχνότητα:", "type": "Τύπος:", "since": "Από:", "currentlyActive": "Ενεργό:", "takingMedication": "Λαμβάνετε φάρμακα:", "hasMenstruated": "Έχει έμμηνο ρύση:", "regularity": "Κανονικότητα:", "cycleLength": "<PERSON><PERSON><PERSON><PERSON> κύκλου:", "flowDays": "Ημέρες ροής:", "padsPerDay": "Σερβιέτες/ταμπόν ανά ημέρα:", "hasChildbirth": "Έχει περάσει από τοκετό:", "children": "Παιδιά:", "pregnancies": "Κυήσεις:"}, "notProvided": "Δεν έχει δοθεί", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} ώρες την ημέρα", "hydration": "{{hydration}} φλιτζάνια ({{liters}}L) την ημέρα", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} ημέρες"}, "noData": {"allergies": "Δεν έχουν δοθεί αλλεργίες", "medications": "Δεν έχουν δοθεί φάρμακα", "conditions": "Δεν έχουν δοθεί ιατρικές καταστάσεις"}, "submitButton": "Υποβολή Πληροφοριών"}, "success": {"title": "Πληροφορίες Ενημερώθηκαν!", "message": "Σας ευχαριστούμε που μας δώσατε τις πληροφορίες υγείας σας. Θα τις χρησιμοποιήσουμε για να εξατομικεύσουμε την εμπειρία σας και να σας παρέχουμε καλύτερες συστάσεις.", "benefits": {"insights": "Εξατομικευμένες πληροφορίες υγείας", "reminders": "Καλύτερες υπενθυμίσεις φαρμάκων", "recommendations": "Προσαρμοσμένες συστάσεις υγείας"}, "continueButton": "Συνέχεια στον Πίνακα Ελέγχου"}, "permissions": {"microphonePermissionDenied": "Αρνήθηκε η άδεια χρήσης μικροφώνου", "microphoneAccessDescription": "Η εφαρμογή August χρειάζεται πρόσβαση στο μικρόφωνό σας για να ηχογραφήσει ήχο και να στείλει φωνητικά μηνύματα", "permissionDenied": "Άρνηση Άδειας", "cameraPermissionRequired": "Χρει<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε άδεια κάμερας για να λειτουργήσει αυτό!", "mediaLibraryPermissionRequired": "Χρει<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε άδεια πρόσβασης στη βιβλιοθήκη πολυμέσων για να λειτουργήσει αυτό!"}, "voiceRecording": {"recordingTooLong": "Πολύ μεγάλη ηχογράφηση", "recordingTooLongMessage": "Οι φωνητικές ηχογραφήσεις πρέπει να διαρκούν λιγότερο από 5 λεπτά. Παρακαλούμε ηχογραφήστε ένα πιο σύντομο μήνυμα."}, "errors": {"uploadFailed": "Αποτυχία αποστολής", "voiceUploadFailed": "Δεν ήταν δυνα<PERSON>ή η αποστολή της φωνητικής ηχογράφησης.", "voiceRecordingFailed": "Αποτυχ<PERSON><PERSON> αποστολής φωνητικής ηχογράφησης", "failedToStopRecording": "Αποτυχία διακο<PERSON>ής ηχογράφησης", "photoUploadFailed": "Δεν ήταν δυνα<PERSON>ή η αποστολή της φωτογραφίας.", "failedToTakePhoto": "Αποτυχία λήψης φωτογραφίας", "imageUploadFailed": "Δεν ήταν δυνατή η αποστολή της εικόνας: {{fileName}}", "failedToPickImage": "Αποτυχία επιλογής εικόνας", "documentUploadFailed": "Δεν ήταν δυνατή η αποστολή του εγγράφου: {{fileName}}", "failedToPickDocument": "Αποτυχία επιλογής εγγράφου"}, "audioPlayer": {"downloadingAudio": "Λή<PERSON>η ήχου...", "loadingAudio": "Φόρτωση ήχου..."}, "mediaProcessing": {"processingFile": "Επεξεργασία του αρχείου σας", "uploadingSecuring": "Αποστολή και ασφάλιση αρχείου...", "analyzingContent": "Ανάλυση περιεχομένου εγγράφου...", "extractingInfo": "Εξαγωγ<PERSON> βα<PERSON>ικών πληροφοριών...", "processingInsights": "Επεξεργασία πληροφοριών...", "preparingResponse": "Προετοιμασία λεπτομερούς απάντησης...", "finalizingResponse": "Ολοκλήρωση απάντησης..."}, "attachments": {"voiceMessage": "Φωνητικό μήνυμα", "image": "[ΕΙΚΟΝΑ]", "pdf": "[PDF]", "voice": "[ΦΩΝΗΤΙΚΟ ΣΗΜΕΙΩΜΑ]"}, "pdf": {"loadingPdf": "Φόρτωση PDF..."}, "dateTime": {"yesterday": "Χθε<PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "επιλεγμένα"}, "mediaUpload": {"photoLibrary": "Φωτογραφική Βιβλιοθήκη", "takePhoto": "Λήψη Φωτογραφίας", "chooseFile": "Επιλογή Αρχείου"}, "comingSoon": {"title": "Σύντομα!", "description": " βρίσκετ<PERSON><PERSON> υπό ανάπτυξη. Μείνετε συντονισμένοι για ενημερώσεις!", "buttonText": "Το κατάλαβα!"}, "clipboard": {"success": "Ο σύνδεσμος αντιγρά<PERSON>ηκε στο πρόχειρο"}, "mediaPhotos": {"emptyState": "Δεν υπάρχουν ακόμα καταχωρήσεις."}, "foodDetail": {"defaultFoodName": "Είδος τροφής", "nutrition": {"totalCalories": "Σύνολο θερμίδων", "proteins": "Πρωτεΐνες", "carbs": "Υδατάνθρακες", "fat": "Λίπη", "sugars": "Σάκχαρα", "fibers": "Ίνες"}}, "reports": {"defaultTitle": "Μέσο", "defaultFoodName": "Είδος τροφής", "defaultName": "Έγγραφο", "openButton": "Άνοιγμα σε εξωτερικό πρόγραμμα προβολής", "biomarker": {"headerBiomarker": "Βιοδείκτης", "headerValue": "Τιμή", "headerRefRange": "Εμβέλεια αναφοράς", "headerStatus": "Κατάσταση"}, "noData": "Δεν υπάρχουν διαθέσιμα δεδομένα βιοδεικτών"}, "setup": {"title": "Ρυθμίζουμε τα πάντα για εσάς", "inProgress": "Σε εξέλιξη...", "progressMessages": {"0": "Υπολογισμός ημερήσιων θερμίδων", "1": "Βελτιστοποίηση αναλογίας μακροθρεπτικών συστατικών", "2": "Δημιουργ<PERSON>α σχεδίου γευμάτων", "3": "Υπολογισμός βαθμολογίας υγείας", "4": "Ολοκλήρωση ρύθμισης"}, "checklistItems": {"0": "Ανάλυση των δεδομένων υγείας σας", "1": "Υπολογισμός βέλτιστου διατροφικού σχεδίου", "2": "Προσωποποίηση των προτάσεων σας", "3": "Δημιουργ<PERSON>α προτάσεων γευμάτων", "4": "Ολοκλήρωση της ρύθμισης"}}, "foodEntry": {"emptyState": "Δεν υπάρχουν ακόμα καταχωρήσεις φαγητού. Τραβήξτε μια φωτογραφία του γεύματός σας για να την προσθέσετε!"}, "nutritionReview": {"congratulations": "Συγχαρητήρια!", "subtitle": "Το εξατομικευμένο διατροφικό σας πλάνο είναι έτοιμο.", "submitButton": "Ας αρχίσουμε!", "dailyTargetsTitle": "Οι Ημερήσιοι Στόχοι Διατροφής σας", "macroLabels": {"calories": "Θερμίδες", "carbs": "Υδατάνθρακες", "protein": "Πρωτεΐνη", "fats": "Λίπη"}, "recommendations": {"title": "Πώς να πετύχετε τους στόχους σας:", "healthScores": "Χρησιμοποιήστε τις βαθμολογίες υγείας για να βελτιώσετε την καθημερινή σας ρουτίνα.", "trackFood": "Παρακολουθήστε συνεπώς την πρόσληψη τροφής σας.", "followCalories": "Ακολουθήστε την ημερήσια συνιστώμενη πρόσληψη θερμίδων σας.", "balanceMacros": "Ισορροπήστε την πρόσληψη υδατανθράκων, πρωτεϊνών και λιπών."}}, "editModal": {"titlePrefix": "Επεξεργασία", "cancelButton": "Ακύρωση", "saveButton": "Επόμενο"}, "processing": {"stages": {"scanning": "Σάρωση τροφίμων...", "identifying": "Προσδιορισμός συστατικών...", "extracting": "Εξαγωγή θρεπτικών συστατικών...", "finalizing": "Ολοκλήρωση αποτελεσμάτων..."}, "error": {"defaultMessage": "Δεν ανιχνεύθηκε τροφή", "subtitle": "Δοκίμασε μια διαφορετική οπτική γωνία"}, "retakeButton": "Πατήστε για να τραβήξετε ξανά την φωτογραφία", "notification": "Θα σας ειδοποιήσουμε όταν ολοκληρωθεί!"}, "chart": {"title": "Παρα<PERSON><PERSON><PERSON>ούθηση Διατροφής με την πάροδο του Χρόνου", "selectNutrient": "Επιλογή Θρεπτικού Συστατικού:", "emptyState": "Δεν υπάρχουν ακόμη διαθέσιμα δεδομένα διατροφής.", "dropdown": {"calories": "Θερμίδες", "protein": "Πρωτεΐνη", "carbs": "Υδατάνθρακες", "fat": "<PERSON><PERSON><PERSON><PERSON>", "sugars": "Σάκχαρα"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON><PERSON> Τροφίμων", "defaultDate": "Σήμερα", "defaultTime": "Άγνωστος χρόνος", "saveChanges": "Αποθήκευση αλλαγών", "error": {"title": "Σφάλμα", "message": "Απέτυχε η ενημέρωση των διατροφικών δεδομένων. Παρακαλώ προσπαθήστε ξανά."}, "nutrition": {"calories": "🔥 Θερμίδες", "proteins": "Πρωτεΐνες", "carbs": "Ψωμί Υδατάνθρακες", "sugars": "Ζάχαρη 🍬", "fat": "🥑 Λίπος"}, "macroBreakdown": {"title": "Ανάλυση Μακροθρεπτικών Συστατικών", "noData": "Δεν υπάρχουν διαθέσιμα δεδομένα για τα μακροθρεπτικά συστατικά αυτού του είδους τροφής."}, "macroLabels": {"calories": "Θερμίδες", "protein": "Πρωτεΐνη", "carbs": "Υδατάνθρακες", "fat": "<PERSON><PERSON><PERSON><PERSON>", "sugar": "Ζάχαρη"}}, "infoModal": {"title": "Λεπτο<PERSON><PERSON><PERSON><PERSON><PERSON>ς Πληροφορίες", "edit": "Επεξεργασία", "save": "Αποθήκευση", "saving": "Αποθήκευση...", "enterValue": "Εισάγετε τιμή", "notSet": "Δεν έχει οριστεί", "age": "Ηλικία", "heightCm": "Ύψος (cm)", "weightKg": "Βάρος (kg)", "targetWeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "nutritionTargets": "Στόχοι Διατροφής", "protein": "Πρωτεΐνη", "carbs": "Υδατάνθρακες", "fats": "Λίπη", "gm": "gm", "editNote": "Εισάγετε τιμές ή αφήστε κενά για αυτόματο υπολογισμό.", "autoCalculateNote": "Οι μακροεντολές υπολογίζονται αυτόματα με βάση τα δεδομένα σας.", "validation": {"ageMin": "Η ηλικία πρέπει να είναι τουλάχιστον 18.", "ageMax": "Η ηλικία πρέπει να είναι κάτω των 125.", "heightMin": "Το <PERSON><PERSON>ος πρέπει να είναι τουλάχιστον 50cm.", "heightMax": "Το ύ<PERSON>ος πρέπει να είναι κάτω από 250cm", "weightMin": "Το βάρος πρέπει να είναι τουλάχιστον 30kg", "weightMax": "Το βάρος πρέπει να είναι κάτω από 500kg", "targetWeightMin": "Ο στόχος βάρους πρέπει να είναι τουλάχιστον 30 κιλά.", "targetWeightMax": "Το βά<PERSON>-στ<PERSON><PERSON><PERSON> πρέπει να είναι κάτω από 500 κιλά.", "proteinMin": "Η πρωτεΐνη πρέπει να είναι 0 ή περισσότερο.", "carbsMin": "Οι υδατάνθρακες πρέπει να είναι 0 ή περισσότεροι.", "fatsMin": "Τα λιπαρά πρέπει να είναι 0 ή περισσότερα."}}, "tracker": {"calories": "Θερμίδες", "protein": "Πρωτεΐνη", "carbs": "Υδατάνθρακες", "fat": "<PERSON><PERSON><PERSON><PERSON>", "excess": "υπερβολή", "remaining": "υπόλοιπο"}, "specialistConstants": {"nutritionist": {"name": "Διατροφολόγος", "description": "Εξειδικευμένη συμβουλή σε θέματα διατροφής, υγι<PERSON>ινών διατροφικών συνηθειών", "featureName": "Σύμβουλος Διατροφής"}, "cardiologist": {"name": "Καρ<PERSON>ιολόγος", "description": "Εξειδίκευση στην υγεία της καρδιάς και στις καρδιαγγειακές παθήσεις", "featureName": "Σύμβ<PERSON><PERSON><PERSON><PERSON>λογ<PERSON>ας"}, "neurologist": {"name": "Νευρ<PERSON>λόγος", "description": "Εστίαση σε διαταραχές του εγκεφάλου, του νωτιαίου μυελού και του νευρικού συστήματος", "featureName": "Σύμβ<PERSON><PERSON><PERSON><PERSON>ολογ<PERSON>ας"}, "oncologist": {"name": "Ο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ος", "description": "Εξειδίκευση στη διάγνωση και θεραπεία του καρκίνου", "featureName": "Σύμβου<PERSON><PERSON>ς"}, "endocrinologist": {"name": "Ενδοκρινολόγος", "description": "Εξειδίκευση σε ορμονικές παθήσεις και μεταβολικές διαταραχές", "featureName": "Σύμβ<PERSON><PERSON><PERSON><PERSON>κρινολογίας"}}, "discoverCards": {"categories": {"nutrition": "Διατροφή", "heartHealth": "Υγεία Καρδιάς", "mentalHealth": "Ψυχική Υγεία", "fitness": "Φόρμα", "wellness": "Ευεξία"}, "titles": {"vitaminB12Recovery": "Πόσο καιρό χρειάζεται για να αναρρώσει κάποιος από έλλειψη βιταμίνης Β12;", "vitaminDeficiencyGanglion": "Ποια Βιταμίνη Έλλειψη Προκαλεί Κύστεις Γαγγλίων", "vitaminDeficiencyHairFall": "Ποια Βιταμίνη Έλλειψη Προκαλεί Τριχόπτωση", "vitaminWaters": "Είναι τα βιταμινούχα νερά καλά για την υγεία;", "cholesterolHeadaches": "Προκαλεί η υψηλή χοληστερόλη πονοκεφάλους;", "cholesterolEyes": "Ποια είναι τα συμπτώματα της υψηλής χοληστερόλης που μπορούν να φανούν στα μάτια;", "diabetesHeadaches": "Μπορεί ο Διαβήτης να Προκαλε<PERSON> Κεφαλαλγίες;", "chestPainDrinking": "Γιατ<PERSON> πονάει το στήθος μετά από το ποτό;", "stressDizziness": "Μπορεί το Στρες να Προκαλέσει Ζάλη;", "bulimiaFace": "Τι είναι το πρόσωπο της βουλιμίας", "kneeTwitch": "Γιατί Τρέμει Το Γόνατό Μου", "noseTwitching": "Γιατί Τρέμει η Μύτη;", "piriformisVsSciatica": "Ποιες είναι οι διαφορές μεταξύ του συνδρόμου του πυριφόρμου μυός και της ισχιαλγίας;", "shoulderBladePinched": "Πώς να απελευθερώσετε ένα νεύρο που πιέζεται στην ωμοπλάτη", "shoulderPinched": "Πώς να απελευθερώσετε ένα νεύρο που πιέζεται στον ώμο", "meniscusTorn": "Πώς να θεραπεύσετε ένα σχισμένο μηνίσκο φυσικά", "hydrateQuickly": "Πώς να ενυδατωθείτε γρήγορα", "periodConstipation": "Είναι φυσιολογικό να έχω δυσκοιλιότητα κατά την περίοδο;", "acneScars": "Πώς να απαλλαγείτε από τις ουλές ακμής φυσικά μέσα σε μια εβδομάδα", "perimenopausePregnancy": "Μπορείς να μείνεις έγκυος κατά την εμμηνόπαυση;"}, "descriptions": {"vitaminB12Recovery": "Ανακαλύψτε το χρονοδιάγραμμα ανάρρωσης από την ανεπάρκεια βιταμίνης Β12 και αποτελεσματικές θεραπείες για την ενίσχυση των επιπέδων ενέργειάς σας.", "vitaminDeficiencyGanglion": "Εξερευνήστε τη σχέση μεταξύ των ελλείψεων βιταμινών και της ανάπτυξης κύστεων γαγγλίων στο σώμα.", "vitaminDeficiencyHairFall": "Μάθετε πώς η έλλειψη απαραίτητων βιταμινών μπορεί να οδηγήσει σε τριχόπτωση και τι μπορείτε να κάνετε για να το αποτρέψετε.", "vitaminWaters": "Αποκαλύψτε τα οφέλη και τα πιθανά μειονεκτήματα των βιταμινούχων νερών ως μέρος της καθημερινής σας διατροφής.", "cholesterolHeadaches": "Εξετάστε την πιθανή σχέση μεταξύ υψηλών επιπέδων χοληστερόλης και της εμφάνισης πονοκεφάλων.", "cholesterolEyes": "Μάθετε πώς η υψηλή χοληστερόλη μπορεί να εκδηλωθεί στα μάτια σας και ποια συμπτώματα να προσέξετε.", "diabetesHeadaches": "Ερευνήστε τη σχέση μεταξύ διαβήτη και εμφάνισης πονοκεφάλων στην καθημερινή ζωή.", "chestPainDrinking": "Εξερευνήστε τους λόγους πίσω από τον πόνο στο στήθος μετά την κατανάλωση ορισμένων ποτών.", "stressDizziness": "Βυθιστείτε στο πώς το στρες μπορεί να επηρεάσει την ισορροπία σας και την γενική ευεξία σας, οδηγώντας σε ζάλη.", "bulimiaFace": "Κατανοήστε τα φυσικά σημάδια της βουλιμίας, συμπεριλαμβανομένων των επιπτώσεων στην εμφάνιση του προσώπου.", "kneeTwitch": "Ερευνήστε τις πιθανές αιτίες πίσω από τις ακούσιες συσπάσεις του γόνατος και τη σχέση τους με το στρες ή την κόπωση.", "noseTwitching": "Μάθετε για τους πιθανούς λόγους για τις συσπάσεις της μύτης και τη σχέση τους με το άγχος ή άλλους παράγοντες.", "piriformisVsSciatica": "Συγκρίνετε τα συμπτώματα του συνδρόμου πυριφόρμου μυός και της ισχιαλγίας για να κατανοήσετε καλύτερα την πάθησή σας.", "shoulderBladePinched": "Ανακαλύψτε αποτελεσματικές τεχνικές για να ανακουφίσετε ένα τσιμπημένο νεύρο στην ωμοπλάτη σας και να αποκαταστήσετε την κινητικότητα.", "shoulderPinched": "Μάθετε απλές ασκήσεις και διατάσεις για να ανακουφίσετε την πίεση στα νεύρα στην περιοχή του ώμου.", "meniscusTorn": "Εξερευνήστε φυσικές μεθόδους και ασκήσεις για να υποστηρίξετε την επούλωση ενός σχισμένου μηνίσκου.", "hydrateQuickly": "Βρείτε γρήγορους και αποτελεσματικούς τρόπους για να ενυδατωθείτε και να διατηρήσετε την βέλτιστη ενυδάτωση του σώματος.", "periodConstipation": "Κατανοήστε τους λόγους πίσω από τη δυσκοιλιότητα κατά την έμμηνο ρύση και μάθετε φυσικές θεραπείες.", "acneScars": "Ανακαλύψτε φυσικές θεραπείες και συμβουλές περιποίησης του δέρματος για να μειώσετε την εμφάνιση ουλών ακμής γρήγορα.", "perimenopausePregnancy": "Μάθετε για την εμμηνόπαυση, τις σκέψεις για γονιμότητα και τι να περιμένετε σε αυτό το στάδιο της ζωής."}}}