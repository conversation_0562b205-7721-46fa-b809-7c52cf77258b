import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '@/utils/logger/logger';

const ONBOARDING_COMPLETE_KEY = 'onboarding_complete';
const PRE_AUTH_ONBOARDING_COMPLETE_KEY = 'pre_auth_onboarding_complete';

export const isUserOnboarded = async (): Promise<boolean> => {
  try {
    const value = await AsyncStorage.getItem(ONBOARDING_COMPLETE_KEY);
    return value === 'true';
  } catch (error) {
    logger.error('Error reading onboarding status:', error);
    return false;
  }
};
   
export const setUserOnboarded = async (onboarded: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(ONBOARDING_COMPLETE_KEY, onboarded ? 'true' : 'false');
    
  } catch (error) {
    logger.error('Error saving onboarding status:', error);
  }
};

export const isPreAuthOnboardingComplete = async (): Promise<boolean> => {
  try {
    const value = await AsyncStorage.getItem(PRE_AUTH_ONBOARDING_COMPLETE_KEY);
    return value === 'true';
  } catch (error) {
    logger.error('Error reading pre-auth onboarding status:', error);
    return false;
  }
};

export const setPreAuthOnboardingComplete = async (complete: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(PRE_AUTH_ONBOARDING_COMPLETE_KEY, complete ? 'true' : 'false');
  } catch (error) {
    logger.error('Error saving pre-auth onboarding status:', error);
  }
};
