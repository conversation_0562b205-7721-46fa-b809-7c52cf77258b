import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import { fetchChatHistory, sendMessage } from '@/services/chatService';
import { initializeWebPubSub, setupReconnection, isWebPubSubConnected } from '@/services/webPubSubService';
import logger from '@/utils/logger/logger';
import { useAuthStore } from './auth-store';
import { usePhoneStore } from './phoneStore';
import { trackUserInteraction, trackOperation, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';
import { useMemo } from 'react';
import { getReportsData } from '@/services/nutritionService';
import NetInfo from '@react-native-community/netinfo';
export type Message = {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: number;
  attachments?: Array<{
    type: 'image' | 'file' | 'pdf' | 'voice';
    uri: string;
    name?: string;
    serverUrl?: string; // URL of the file on the server after upload
    signedUrl?: string; // Signed URL for accessing the file (especially for PDFs)
  }>;
};

type ChatState = {
  messages: Message[];
  isLoading: boolean;
  isWaitingForResponse: boolean;  // New state for tracking response waiting
  lastUserMessageId: string | null;  // Track ID of the last user message
  lastUserMessageTimestamp: number | null;  // Track timestamp of the last user message
  lastMessageId?: string;
  selectedMessages: string[];
  chatListRef: React.RefObject<any> | null;

  // Actions
  setChatListRef: (ref: React.RefObject<any>) => void;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  clearMessages: () => void;
  setLoading: (loading: boolean) => void;
  setWaitingForResponse: (waiting: boolean, messageId?: string) => void;  // New action
  setSelectedMessages: (messageIds: string[]) => void;
  toggleMessageSelection: (messageId: string) => void;
  clearSelectedMessages: () => void;

  // API actions
  fetchChatHistory: (limit?: number) => Promise<void>;
  fetchOlderMessages: (limit?: number) => Promise<void>; // New function to fetch older messages
  sendMessageToApi: (text: string, attachments?: Message['attachments']) => Promise<void>;

  setupNetworkListener: () => void;
  removeOfflineMessage: () => void;

  // WebPubSub actions
  initializeWebPubSub: () => Promise<void>;
  handleWebPubSubMessage: (messageData: any) => void;
};

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      messages: [],
      isLoading: false,
      isWaitingForResponse: false,
      lastUserMessageId: null,
      lastUserMessageTimestamp: null,
      lastMessageId: undefined,
      selectedMessages: [],
      chatListRef: null,

      setChatListRef: (ref) => set({ chatListRef: ref }),

      addMessage: (message) => {
        const messageId = uuidv4();
        const timestamp = Date.now();

        // Track message added to store
        trackOperation('Message Store', OperationStatus.SUCCESS, {
          sender: message.sender,
          hasText: !!message.text,
          hasAttachments: !!(message.attachments && message.attachments.length > 0),
          attachmentCount: message.attachments?.length || 0,
          messageId
        });

        set((state) => ({
          messages: [
            ...state.messages,
            {
              ...message,
              id: messageId,
              timestamp,
            },
          ],
        }));
      },

      clearMessages: () => set({ messages: [] }),

      setLoading: (loading) => set({ isLoading: loading }),

      setWaitingForResponse: (waiting, messageId) => set(state => ({
        isWaitingForResponse: waiting,
        lastUserMessageId: messageId ? messageId : state.lastUserMessageId,
        lastUserMessageTimestamp: waiting ? Date.now() : state.lastUserMessageTimestamp
      })),

      fetchChatHistory: async (limit = 100) => {
        const { lastMessageId } = get();

        logger.info(`Initiating chat history fetch with limit: ${limit}, lastMessageId: ${lastMessageId || 'none'}`);
        trackOperation('Chat History Fetch', OperationStatus.INITIATED, {
          limit,
          hasLastMessageId: !!lastMessageId
        });

        set({ isLoading: true });

        try {
          const response = await fetchChatHistory(limit, lastMessageId);

          // Get the number of messages from the length property
          const messageCount = response.chats.length as number;
          logger.info(`Successfully received chat history with ${messageCount} messages`);
          trackOperation('Chat History Fetch', OperationStatus.SUCCESS, {
            messageCount,
            hasLastMessageId: !!response.last_message_id
          });

          // Transform the response to match our Message type
          const transformedMessages = Object.entries(response.chats)
            .filter(([key, value]) => key !== 'length' && typeof value === 'object' && value !== null)
            .map(([_, chat]) => {
              const typedChat = chat as {
                message: string;
                id: string;
                role: 'human' | 'assistant';
                timestamp: string;
                data?: string;
                message_type?: string;
                meta?: {
                  type?: string;
                  fileURI?: string;
                };
              };

              // Map 'human' to 'user' and 'assistant' to 'bot' for sender
              const mappedSender: 'user' | 'bot' = typedChat.role === 'human' ? 'user' : 'bot';

              // Check for image and PDF type messages
              const isImage = typedChat.message_type === 'image' || typedChat.meta?.type === 'image';
              const isPdf = typedChat.message_type === 'document' || typedChat.message_type === 'pdf' || typedChat.meta?.type === 'pdf';
              const isVoice = typedChat.message_type === 'voice';


              // logger.debug(`Transforming message ${typedChat.id} from sender: ${typedChat.role} to ${mappedSender}`);

              // Extract filename from URL for PDF files
              const extractFilenameFromUrl = (url: string): string => {
                if (!url) return 'PDF Document';
                try {
                  // Get the last part of the URL (after the last slash)
                  const filename = url.split('/').pop() || '';
                  // Remove any query parameters
                  const cleanFilename = filename.split('?')[0];
                  // Decode URL-encoded characters
                  const decodedFilename = decodeURIComponent(cleanFilename);
                  // Return the filename or a default if it's empty
                  return decodedFilename || 'PDF Document';
                } catch (e) {
                  return 'PDF Document';
                }
              };

              return {
                id: typedChat.id,
                text: typedChat.message, // Use message instead of text
                sender: mappedSender,
                timestamp: new Date(typedChat.timestamp).getTime(),
                attachments: typedChat.data && (isImage || isPdf || isVoice) ? [
                  {
                    type: isImage ? 'image' : isPdf ? 'pdf' : 'voice' as 'image' | 'file' | 'pdf' | 'voice',
                    uri: typedChat.data,
                    ...(isPdf && { name: extractFilenameFromUrl(typedChat.data) })
                  }
                ] : undefined
              };
            })
            // Sort messages by timestamp (oldest first, newest last)
            .sort((a, b) => a.timestamp - b.timestamp);

          logger.info(`Messages sorted by timestamp, oldest first (${transformedMessages.length} messages)`);

          logger.info('Setting transformed messages to store');

          set({
            messages: transformedMessages,
            lastMessageId: response.last_message_id,
            isLoading: false
          });
        } catch (error) {
          if ((error as any)?.response?.status !== 401) {
            logger.error('Failed to fetch chat history:', error);
          }
          set({ isLoading: false });
        }
      },

      fetchOlderMessages: async (limit = 30) => {
        const { messages } = get();

        if (messages.length === 0) {
          logger.info('No existing messages, fetching initial chat history');
          trackOperation('Older Messages Fetch', OperationStatus.INITIATED, {
            reason: 'No existing messages'
          });
          return get().fetchChatHistory(limit);
        }

        // Get the oldest message ID (first message in the array since they're sorted)
        const oldestMessageId = messages[0]?.id;

        if (!oldestMessageId) {
          logger.warn('No oldest message ID found, fetching initial chat history');
          trackOperation('Fetch Older Messages Redirected', OperationStatus.INITIATED, {
            reason: 'No oldest message ID'
          });
          return get().fetchChatHistory(limit);
        }

        logger.info(`Fetching older messages with limit: ${limit}, oldestMessageId: ${oldestMessageId}`);
        trackOperation('Older Messages Fetch', OperationStatus.INITIATED, {
          limit,
          currentMessageCount: messages.length
        });

        set({ isLoading: true });

        try {
          const response = await fetchChatHistory(limit, oldestMessageId);

          // Get the number of messages from the length property
          const messageCount = response.chats.length as number;
          logger.info(`Successfully received older messages with ${messageCount} messages`);

          // If no older messages were found
          if (messageCount === 0) {
            logger.info('No older messages found');
            trackOperation('Fetch Older Messages Completed', OperationStatus.INITIATED, {
              messageCount: 0,
              reason: 'No older messages found'
            });
            set({ isLoading: false });
            return;
          }
          // Transform the response to match our Message type (same logic as fetchChatHistory)
          const transformedMessages = Object.entries(response.chats)
            .filter(([key, value]) => key !== 'length' && typeof value === 'object' && value !== null)
            .map(([_, chat]) => {
              const typedChat = chat as {
                message: string;
                id: string;
                role: 'human' | 'assistant';
                timestamp: string;
                data?: string;
                message_type?: string;
                meta?: {
                  type?: string;
                  fileURI?: string;
                };
              };

              // Map 'human' to 'user' and 'assistant' to 'bot' for sender
              const mappedSender: 'user' | 'bot' = typedChat.role === 'human' ? 'user' : 'bot';

              // Check for image and PDF type messages
              const isImage = typedChat.message_type === 'image' || typedChat.meta?.type === 'image';
              const isPdf = typedChat.message_type === 'pdf' || typedChat.meta?.type === 'pdf';
              const isVoice = typedChat.message_type === 'voice'

              logger.debug(`Transforming older message ${typedChat.id} from sender: ${typedChat.role} to ${mappedSender}`);

              // Extract filename from URL for PDF files
              const extractFilenameFromUrl = (url: string): string => {
                if (!url) return 'PDF Document';
                try {
                  // Get the last part of the URL (after the last slash)
                  const filename = url.split('/').pop() || '';
                  // Remove any query parameters
                  const cleanFilename = filename.split('?')[0];
                  // Decode URL-encoded characters
                  const decodedFilename = decodeURIComponent(cleanFilename);
                  // Return the filename or a default if it's empty
                  return decodedFilename || 'PDF Document';
                } catch (e) {
                  return 'PDF Document';
                }
              };

              return {
                id: typedChat.id,
                text: typedChat.message, // Use message instead of text
                sender: mappedSender,
                timestamp: new Date(typedChat.timestamp).getTime(),
                attachments: isImage && typedChat.data ? [
                  {
                    type: 'image' as 'image' | 'file' | 'pdf' | 'voice',
                    uri: typedChat.data
                  }
                ] : isPdf && typedChat.data ? [
                  {
                    type: 'pdf' as 'image' | 'file' | 'pdf' | 'voice',
                    uri: typedChat.data,
                    name: extractFilenameFromUrl(typedChat.data)
                  }
                ] : isVoice && typedChat.data ? [
                  {
                    type: 'voice' as 'image' | 'file' | 'pdf' | 'voice',
                    uri: typedChat.data,
                    name: extractFilenameFromUrl(typedChat.data)
                  }
                ] : undefined

              };
            })
            // Sort messages by timestamp (oldest first, newest last)
            .sort((a, b) => a.timestamp - b.timestamp);
          logger.info(`Older messages sorted by timestamp, oldest first (${transformedMessages.length} messages)`);

          // Prepend the older messages to the existing messages
          set((state) => ({
            messages: [...transformedMessages, ...state.messages],
            lastMessageId: response.last_message_id || state.lastMessageId,
            isLoading: false
          }));

          logger.info('Older messages prepended to existing messages');
          trackOperation('Fetch Older Messages Completed', OperationStatus.SUCCESS, {
            messageCount: transformedMessages.length,
            totalMessageCount: transformedMessages.length + messages.length
          });
        } catch (error: unknown) {
          if ((error as any).response?.status !== 401) {
            logger.error('Failed to fetch older messages:', error);
          }
          set({ isLoading: false });
        }
      },

      sendMessageToApi: async (text, attachments) => {
        logger.info('Preparing to send message to API', { hasText: !!text, attachmentsCount: attachments?.length || 0 });
        trackOperation('API Message Send Initiated', OperationStatus.INITIATED, {
          hasText: !!text,
          attachmentsCount: attachments?.length || 0,
          attachmentTypes: attachments?.map(att => att.type) || []
        });

        set({ isLoading: true });
        // if (Math.random() < 0.5) {
        //   throw new Error("Simulated failure for testing");
        // }
        try {
          // Check if WebPubSub is connected - only try to reconnect if we're on the chat page
          // This avoids initializing WebPubSub in multiple places
          const isConnected = isWebPubSubConnected();
          logger.info(`WebPubSub connection status before sending: ${isConnected ? 'connected' : 'disconnected'}`);

          // Only try to reconnect if we're not connected
          if (!isConnected) {
            logger.info('WebPubSub not connected, message will be sent without WebPubSub');
            // Show offline message to user
            try {
              trackOperation('WebPubSub Reconnection Attempt', OperationStatus.INITIATED);
              await get().initializeWebPubSub();
              logger.info('WebPubSub reconnection successful');
              trackOperation('WebPubSub Reconnection Succeeded', OperationStatus.SUCCESS);
            } catch (reconnectError) {
              if(!isWebPubSubConnected()){
                // Check if reconnection message already exists
                const reconnectMessageExists = get().messages.some(
                  msg => msg.sender === "bot" && msg.text === "Looks like you're offline. Reconnect to send messages."
                );
                
                // Only add the message if it doesn't already exist
                if (!reconnectMessageExists) {
                  setTimeout(() => {
                    set((state) => ({
                      messages: [
                        ...state.messages,
                        {
                          id: uuidv4(),
                          text: "Looks like you're offline. Reconnect to send messages.",
                          sender: "bot",
                          timestamp: Date.now(),
                        },
                      ],
                    }));
                  }, 1500);
                }
              }
              logger.warn('WebPubSub reconnection failed, message will be sent without WebPubSub', reconnectError);
              trackOperation('WebPubSub Reconnection Failed', OperationStatus.FAILURE);
            }
          }

          // Generate a common requestId for all related messages
          const requestId = uuidv4();
          logger.debug('Generated requestId for message group', { requestId });
          trackOperation('Message Request ID Generated', OperationStatus.INITIATED, { requestId });

          // Get phone number from phoneStore first, then fall back to auth store, then use a default
          const phoneStoreNumber = usePhoneStore.getState().phoneNumber;
          const user = useAuthStore.getState().user;
          const phoneNumber = phoneStoreNumber || user?.phone || 'unknown';
          logger.info('Using phone number for message:', phoneNumber);

          // Check if we have both text and attachments
          const hasBothTextAndMedia = text.trim() && attachments && attachments.length > 0;

          if (hasBothTextAndMedia) {
            logger.info('Message contains both text and media, splitting into separate messages');

            // First, add and send each attachment as a separate message
            for (let i = 0; i < attachments.length; i++) {
              const attachment = attachments[i];
              const mediaMessageId = uuidv4();
              const timestamp = Date.now() + i * 100; // Slightly increment timestamp for ordering

              // Add media message to local state
              set((state) => ({
                messages: [
                  ...state.messages,
                  {
                    id: mediaMessageId,
                    text: '',
                    sender: 'user',
                    timestamp,
                    attachments: [attachment]
                  },
                ],
              }));

              // Prepare attachment data for API
              let attachmentUrl = null;
              let fileExtension = null;

              // Always use the server URL if available
              if (attachment.serverUrl) {
                attachmentUrl = String(attachment.serverUrl);
                fileExtension = attachment.type === 'image'
                  ? '.jpg'
                  : attachment.type === 'voice'
                    ? '.m4a'
                    : '.pdf';
              }
              // Fall back to local URI only if server URL is not available
              else if (attachment.uri) {
                attachmentUrl = String(attachment.uri);
                fileExtension = attachment.type === 'image'
                  ? '.jpg'
                  : attachment.type === 'voice'
                    ? '.m4a'
                    : '.pdf';
              }
              // Send media message to API
              logger.info('Sending media message to API', {
                mediaMessageId,
                mediaType: attachment.type
              });
              trackOperation('Media Message Send', OperationStatus.INITIATED, {
                mediaType: attachment.type,
                hasServerUrl: !!attachment.serverUrl,
                messageId: mediaMessageId
              });

              await sendMessage({
                text: '',
                providerMessageId: mediaMessageId,
                attachment: attachmentUrl,
                fileExtension,
                messageType: attachment.type === 'image' ? 'image' : (attachment.type === 'voice' ? 'voice' : 'pdf'),
                sender: 'human',
                source: 'MOBILE',
                phoneNumber,
                timestamp,
                requestId
              });

              trackOperation('Media Message Send', OperationStatus.SUCCESS, {
                mediaType: attachment.type,
                messageId: mediaMessageId
              });
            }

            // Then, add and send the text message
            const textMessageId = uuidv4();
            const textTimestamp = Date.now() + attachments.length * 100; // Ensure text comes after all media

            // Add text message to local state without setting isWaitingForResponse
            set((state) => ({
              messages: [
                ...state.messages,
                {
                  id: textMessageId,
                  text,
                  sender: 'user',
                  timestamp: textTimestamp,
                  attachments: undefined
                },
              ],
              lastUserMessageId: textMessageId
            }));

            // Send text message to API
            logger.info('Sending text message to API', { textMessageId });
            trackOperation('Text Message Send', OperationStatus.INITIATED, {
              textLength: text.length,
              messageId: textMessageId
            });

            const response = await sendMessage({
              text,
              providerMessageId: textMessageId,
              attachment: null,
              fileExtension: null,
              messageType: 'text',
              sender: 'human',
              source: 'MOBILE',
              phoneNumber,
              timestamp: textTimestamp,
              requestId
            });

            // Only set isWaitingForResponse to true after successful API call
            if (response) {
              set({ isWaitingForResponse: true });
              trackOperation('Text Message Send', OperationStatus.SUCCESS, {
                messageId: textMessageId
              });
            }
          } else {
            // Handle the case where we have only text or multiple attachments without text
            // Check if we have multiple attachments without text
            const hasMultipleAttachmentsNoText = !text.trim() && attachments && attachments.length > 1;

            if (hasMultipleAttachmentsNoText) {
              logger.info('Message contains multiple media attachments without text, sending as separate messages');

              // Send each attachment as a separate message
              for (let i = 0; i < attachments.length; i++) {
                const attachment = attachments[i];
                const mediaMessageId = uuidv4();
                const timestamp = Date.now() + i * 100; // Slightly increment timestamp for ordering

                // Add media message to local state without setting isWaitingForResponse
                set((state) => ({
                  messages: [
                    ...state.messages,
                    {
                      id: mediaMessageId,
                      text: '',
                      sender: 'user',
                      timestamp,
                      attachments: [attachment]
                    },
                  ],
                  lastUserMessageId: i === attachments.length - 1 ? mediaMessageId : state.lastUserMessageId
                }));

                // Prepare attachment data for API
                let attachmentUrl = null;
                let fileExtension = null;

                // Always use the server URL if available
                if (attachment.serverUrl) {
                  attachmentUrl = String(attachment.serverUrl);
                  fileExtension = attachment.type === 'image'
                    ? '.jpg'
                    : attachment.type === 'voice'
                      ? '.m4a'
                      : '.pdf';
                }
                // Fall back to local URI only if server URL is not available
                else if (attachment.uri) {
                  attachmentUrl = String(attachment.uri);
                  fileExtension = attachment.type === 'image'
                    ? '.jpg'
                    : attachment.type === 'voice'
                      ? '.m4a'
                      : '.pdf';
                }

                // Send media message to API
                logger.info('Sending media message to API', {
                  mediaMessageId,
                  mediaType: attachment.type,
                  isLastAttachment: i === attachments.length - 1
                });
                trackOperation('Media Message Send', OperationStatus.INITIATED, {
                  mediaType: attachment.type,
                  isLastAttachment: i === attachments.length - 1,
                  hasServerUrl: !!attachment.serverUrl,
                  messageId: mediaMessageId
                });

                const response = await sendMessage({
                  text: '',
                  providerMessageId: mediaMessageId,
                  attachment: attachmentUrl,
                  fileExtension,
                  messageType: attachment.type === 'image' ? 'image' : (attachment.type === 'voice' ? 'voice' : 'pdf'),
                  sender: 'human',
                  source: 'MOBILE',
                  phoneNumber,
                  timestamp,
                  requestId
                });

                // Only set isWaitingForResponse to true after successful API call for the last attachment
                if (response && i === attachments.length - 1) {
                  set({ isWaitingForResponse: true });
                  trackOperation('Media Message Send', OperationStatus.SUCCESS, {
                    mediaType: attachment.type,
                    isLastAttachment: true,
                    messageId: mediaMessageId
                  });
                } else if (response) {
                  trackOperation('Media Message Send', OperationStatus.SUCCESS, {
                    mediaType: attachment.type,
                    isLastAttachment: false,
                    messageId: mediaMessageId
                  });
                }
              }
            } else {     // Handle the case where we have only text or a single attachment (original behavior)
              const messageId = uuidv4();

              logger.debug('Generated ID for single message', { messageId, requestId });

              // Add message to local state without setting isWaitingForResponse
              set((state) => ({
                messages: [
                  ...state.messages,
                  {
                    id: messageId,
                    text,
                    sender: 'user',
                    timestamp: Date.now(),
                    attachments
                  },
                ],
                lastUserMessageId: messageId
              }));

              // Prepare attachment if any
              let attachment = null;
              let fileExtension = null;

              if (attachments && attachments.length > 0) {
                // Always use the server URL if available
                if (attachments[0].serverUrl) {
                  setTimeout(async () => {
                    try {
                      logger.info('Running scheduled report check 3 minutes after message send');
                      const messageTimestamp = new Date().getTime();
                      const reportsResponse = await getReportsData();
                      if (reportsResponse?.success && reportsResponse?.files) {
                        const files = reportsResponse.files;

                        let mostRecentDate = new Date(0);
                        let mostRecentType = null;

                        Object.keys(files).forEach((key) => {
                          if (key === "length") return; // Skip length field

                          const file = files[key];
                          const timestamp = new Date(file.timestamp);

                          if (!isNaN(timestamp.getTime()) && timestamp > mostRecentDate) {
                            mostRecentDate = timestamp;
                            mostRecentType = file.type || null;
                          }
                        });

                        if (mostRecentType) {
                          const timeDifference = messageTimestamp - mostRecentDate.getTime();;
                          logger.info('THE DIFFERENCE IS', timeDifference, mostRecentDate, messageTimestamp)
                          if (timeDifference >= 0 && timeDifference <= 180000) { // 3.5 minutes = 210000 ms
                            trackUserInteraction('Document Uploaded', { type: mostRecentType })
                            logger.info('Found report within window, tracking event', {
                              mostRecentType,
                              timeDifference
                            });
                          } else {
                            logger.info('Latest report outside window');
                          }
                        }
                      }
                    } catch (reportError) {
                      logger.error('Failed to check reports in scheduled task:', reportError);
                    }
                  }, 120000);
                  // Use type assertion to tell TypeScript this is a string
                  attachment = String(attachments[0].serverUrl);
                  fileExtension = attachments[0].type === 'image' ? '.jpg' : attachments[0].type === 'voice' ? '.m4a' : '.pdf';
                  logger.debug('Using server URL for attachment', {
                    type: attachments[0].type,
                    fileExtension,
                    serverUrl: attachments[0].serverUrl
                  });
                }
                // Fall back to local URI only if server URL is not available
                else if (attachments[0].uri) {
                  // Use type assertion to tell TypeScript this is a string
                  attachment = String(attachments[0].uri);
                  fileExtension = attachments[0].type === 'image' ? '.jpg' : attachments[0].type === 'voice' ? '.m4a' : '.pdf';
                  logger.debug('Using local URI for attachment (no server URL available)', {
                    type: attachments[0].type,
                    fileExtension
                  });
                }
              }

              // Send to API
              logger.info('Sending single message to API', { messageId, hasAttachment: !!attachment });
              trackOperation('Message Send', OperationStatus.INITIATED, {
                messageId,
                hasText: !!text.trim(),
                hasAttachment: !!attachment,
                attachmentType: attachments && attachments.length > 0 ? attachments[0].type : null
              });

              const response = await sendMessage({
                text,
                providerMessageId: messageId,
                attachment,
                fileExtension,
                messageType: attachment
                  ? (attachments && attachments[0].type === 'image'
                    ? 'image'
                    : (attachments && attachments[0].type === 'pdf' ? 'pdf' : (
                      attachments && attachments[0].type === 'voice' ? 'voice' : 'attachment'
                    )))
                  : 'text',
                sender: 'human',
                source: 'MOBILE',
                phoneNumber,
                timestamp: Date.now(),
                requestId
              });

              // Only set isWaitingForResponse to true after successful API call
              if (response) {
                set({ isWaitingForResponse: true });
              }
            }
          }

          logger.info('Message sent successfully');
          trackOperation('Message Send', OperationStatus.SUCCESS);
          set({ isLoading: false });
          // Note: We don't reset isWaitingForResponse here
        } catch (error: unknown) {
          if (error instanceof Error) {
            // Check if error has response property before accessing it
            const errorResponse = (error as any).response;
            if (errorResponse?.status !== 401) {
              logger.error('Failed to send message:', error);
            }
          }
          trackOperation('Message Send', OperationStatus.FAILURE, {
            error: error instanceof Error ? error.message : String(error)
          });
          set({
            isLoading: false,
            isWaitingForResponse: false  // Reset on error
          });
        }
      },

      // Initialize WebPubSub connection
      initializeWebPubSub: async () => {
        try {
          logger.info('Initializing WebPubSub connection');

          // Initialize the WebPubSub client
          const client = await initializeWebPubSub();

          setupReconnection(client);

          // Set up message handler - we need to register this handler here
          // to ensure we're using the store's handleWebPubSubMessage function
          client.on("server-message", (e: any) => {
            logger.info('Received server message, passing to handler');
            get().handleWebPubSubMessage(e.message.data);
          });

          logger.info('WebPubSub initialized successfully');
        } catch (error : any) {
          const message = String(error?.message || '');
          const is401 = message.includes('401');

          if (!is401) {
            logger.error('Failed to initialize WebPubSub:', JSON.stringify(error));
          }
          trackOperation('WebPubSub Connection', OperationStatus.FAILURE, {
            error: error instanceof Error ? error.message : String(error)
          });
          throw error;
        }
      },

      // Handle incoming WebPubSub messages
      handleWebPubSubMessage: (messageData) => {
        logger.webPubSubMessage(messageData);

        try {
          // Parse the message if it's a string
          const message = typeof messageData === 'string' ? JSON.parse(messageData) : messageData;

          // Check for image and PDF type messages
          const isImage = message.message_type === 'image' || message.meta?.type === 'image';
          const isPdf = message.message_type === 'document' || message.message_type === 'pdf' || message.meta?.type === 'pdf';
          const isVoice = message.message_type == 'voice';
          // Log PDF messages for debugging
          if (isPdf) {
            logger.info('Received PDF message from WebPubSub:', message);
          }

          // Extract filename from URL for PDF files
          const extractFilenameFromUrl = (url: string): string => {
            if (!url) return 'PDF Document';
            try {
              // Get the last part of the URL (after the last slash)
              const filename = url.split('/').pop() || '';
              // Remove any query parameters
              const cleanFilename = filename.split('?')[0];
              // Decode URL-encoded characters
              const decodedFilename = decodeURIComponent(cleanFilename);
              // Return the filename or a default if it's empty
              return decodedFilename || 'PDF Document';
            } catch (e) {
              return 'PDF Document';
            }
          };

          // Add the message to the store
          const newMessage: Message = {
            id: message.id || uuidv4(),
            text: message.messageText || message.message || message.text || '',
            sender: 'bot', // Assuming server messages are from the bot
            timestamp: message.timestamp ? new Date(message.timestamp).getTime() : Date.now(),
            attachments: isImage && message.data ? [
              {
                type: 'image' as 'image' | 'file' | 'pdf' | 'voice',
                uri: message.data
              }
            ] : isPdf && message.data ? [
              {
                type: 'pdf' as 'image' | 'file' | 'pdf' | 'voice',
                uri: message.data,
                name: extractFilenameFromUrl(message.data),
                signedUrl: message.signedURL || message.signed_url || message.fileURL || message.file_url || message.data
              }
            ] : isVoice && message.data ? [
              {
                type: 'voice' as 'image' | 'file' | 'pdf' | 'voice',
                uri: message.data,
                name: extractFilenameFromUrl(message.data),
                signedUrl: message.signedURL || message.signed_url || message.fileURL || message.file_url || message.data
              }
            ] : undefined
            
          };


          set((state) => ({
            messages: [
              ...state.messages,
              newMessage,
            ],
            // Reset waiting state when we receive a bot message
            isWaitingForResponse: false
          }));

        } catch (error) {

          if ((error as any)?.response?.status !== 401) {
            logger.error('Error processing WebPubSub message:', error);
          }
        }
      },
      toggleMessageSelection: (messageId: string) =>
        set((state) => {
          const selectedSet = new Set(state.selectedMessages);
          if (selectedSet.has(messageId)) {
            selectedSet.delete(messageId);
          } else {
            selectedSet.add(messageId);
          }
          return {
            selectedMessages: Array.from(selectedSet)
          };
        }),

      setSelectedMessages: (messageIds: string[]) =>
        set({ selectedMessages: [...new Set(messageIds)] }),

      clearSelectedMessages: () =>
        set({ selectedMessages: [] }),

      setupNetworkListener: () => {
        // Set up network status listener
        const unsubscribe = NetInfo.addEventListener(state => {
          logger.info('Network state changed:', state);
          
          // If we're back online and connected
          if (state.isConnected && state.isInternetReachable) {
            // Remove the offline message if it exists
            get().removeOfflineMessage();
            
            // WebPubSub should handle its own reconnection logic
            // We're only responsible for cleaning up the UI message
          }
        });
        
        // Store the unsubscribe function for cleanup if needed
        (window as any).__networkUnsubscribe = unsubscribe;
      },
      
      removeOfflineMessage: () => {
        set((state) => ({
          messages: state.messages.filter(
            msg => !(msg.sender === "bot" && msg.text === "Looks like you're offline. Reconnect to send messages.")
          )
        }));
        logger.info('Removed offline message from chat');
      },
      
    }),
    {
      name: 'chat-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // Don't persist the ref
        ...state,
        chatListRef: null,
      }),
    }
  )
);

// Custom hooks for memoized selectors
export const useMessages = () => {
  const messages = useChatStore(state => state.messages);
  return useMemo(() => messages, [JSON.stringify(messages)]);
};

export const useIsLoading = () => {
  const isLoading = useChatStore(state => state.isLoading);
  return useMemo(() => isLoading, [isLoading]);
};

export const useIsWaitingForResponse = () => {
  const isWaitingForResponse = useChatStore(state => state.isWaitingForResponse);
  return useMemo(() => isWaitingForResponse, [isWaitingForResponse]);
};

export const useLatestMessage = () => {
  const messages = useChatStore(state => state.messages);
  return useMemo(() =>
    messages.length > 0 ? messages[messages.length - 1] : null,
    [messages.length > 0 ? messages[messages.length - 1]?.id : 'empty']
  );
};

export const useMessagesBySender = (sender: 'user' | 'bot') => {
  const messages = useChatStore(state => state.messages);
  return useMemo(() =>
    messages.filter(message => message.sender === sender),
    [messages.filter(message => message.sender === sender).length]
  );
};
