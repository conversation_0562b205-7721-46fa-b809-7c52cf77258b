import { Mixpanel } from 'mixpanel-react-native';
import { getEnvironmentVariable } from '../getEnvironmentVariable';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import { useCallback } from 'react';
import logger from '@/utils/logger/logger';
import { getUserData } from '@/services/userService';
/**
 * Enum for categorizing event types
 */
export enum EventType {
  USER_INTERACTION = 'user_interaction',
  INTERNAL_OPERATION = 'internal_operation'
}

/**
 * Enum for tracking operation status
 */
export enum OperationStatus {
  INITIATED = 'initiated',
  SUCCESS = 'success',
  FAILURE = 'failure'
}

const mixpanelToken = getEnvironmentVariable("MIXPANEL_TOKEN")

const trackAutomaticEvents = false; //disable legacy mobile autotrack
const useNative = false;  //disable Native Mode, use Javascript Mode

// create new Mixpanel class
const mixpanel = new Mixpanel(
  mixpanelToken,
  trackAutomaticEvents,
  useNative
);

/**
 * Track an event in Mixpanel with enhanced properties
 * @param event Event name
 * @param properties Event properties
 * @param eventType Type of event (user interaction or internal operation)
 */
export const trackEvent = (
  event: string,
  properties?: Record<string, any>,
  eventType?: EventType
) => {
  try {
    // Add common properties to all events
    // Safely access global.currentScreen
    let currentScreen = 'unknown';
    try {
      // @ts-ignore - Access global.currentScreen which is set in app/_layout.tsx
      if (global && global.currentScreen) {
        // @ts-ignore
        currentScreen = global.currentScreen;
      }
    } catch (e) {
      // Ignore errors accessing global
    }

    const enhancedProperties = {
      ...properties,
      event_type: eventType,
      timestamp: new Date().toISOString(),
      screen: currentScreen
    };

    mixpanel.track(event, enhancedProperties);
  } catch (error) {
    logger.error('Mixpanel event tracking failed:', error);
  }
};

/**
 * Track a user interaction event
 * This includes all user-facing events like:
 * - Button presses
 * - Screen views
 * - Keyboard interactions
 * - Gestures (swipes, scrolls)
 * - Form inputs
 * - Modal interactions
 * - Navigation events
 * 
 * @param event Event name
 * @param properties Event properties
 */
export const trackUserInteraction = (
  event: string,
  properties?: Record<string, any>
) => {
  trackEvent(event, properties, EventType.USER_INTERACTION);
};

/**
 * Track an internal operation event with status
 * This includes all backend/system events like:
 * - API calls
 * - Data storage operations
 * - Authentication flows
 * - WebPubSub connections
 * - Error handling
 * - Background processes
 * 
 * @param operation Operation name
 * @param status Operation status (initiated, success, failure)
 * @param properties Additional properties
 */
export const trackOperation = (
  operation: string,
  status: OperationStatus,
  properties?: Record<string, any>
) => {
  const event = `${operation}_${status.toLowerCase()}`;
  trackEvent(
    event,
    properties,
    EventType.INTERNAL_OPERATION
  );
};

mixpanel.init();

/**
 * Identify a user in Mixpanel using their phone number
 * This registers the user's unique device with their phone number
 * 
 * @param phoneNumber The user's phone number (including country code)
 */
// Create a custom hook for tracking events in components
export const useMixpanelTracking = () => {
  // Memoized version of trackUserInteraction
  const trackUserInteractionMemo = useCallback(
    (event: string, properties?: Record<string, any>) => {
      trackUserInteraction(event, properties);
    },
    []
  );

  // Memoized version of trackOperation
  const trackOperationMemo = useCallback(
    (operation: string, status: OperationStatus, properties?: Record<string, any>) => {
      trackOperation(operation, status, properties);
    },
    []
  );

  // Memoized version of identifyUser
  const identifyUserMemo = useCallback(
    async (phoneNumber: string) => {
      await identifyUser(phoneNumber);
    },
    []
  );

  return {
    trackUserInteraction: trackUserInteractionMemo,
    trackOperation: trackOperationMemo,
    identifyUser: identifyUserMemo
  };
};

export const identifyUser = async (phoneNumber: string) => {
  try {
    if (!phoneNumber) {
      logger.error('Cannot identify user: Phone number is required');
      return;
    }
    // Clean the phone number to use as a unique identifier
    // Remove any non-numeric characters except the + at the beginning
    const cleanedPhoneNumber = phoneNumber.replace(/[^\d+]/g, '');

    // Use the phone number as the distinct ID
    mixpanel.identify(cleanedPhoneNumber);

    // Get device information
    let deviceName = 'Unknown Device';
    try {
      deviceName = Device.modelName || 'Unknown Device';
    } catch (e) {
      logger.error('Error getting device name:', e);
    }

    let deviceType = 'Unknown';
    try {
      // Device.DeviceType is an enum with values like PHONE, TABLET, etc.
      deviceType = Device.deviceType === Device.DeviceType.PHONE ? 'Phone' :
        Device.deviceType === Device.DeviceType.TABLET ? 'Tablet' : 'Unknown';
    } catch (e) {
      logger.error('Error getting device type:', e);
    }

    // Set user properties
    mixpanel.getPeople().set({
      '$phone': cleanedPhoneNumber,
      'phone_number': cleanedPhoneNumber,
      'device_type': deviceType,
      'device_brand': Device.brand,
      'device_model': Device.modelName,
      'os_name': Platform.OS,
      'os_version': Platform.Version,
      'first_login_date': new Date().toISOString(),
    });

    logger.info('User identified in Mixpanel with phone number:', cleanedPhoneNumber);
    const response = await getUserData();
    if (response.success) {
      trackUserInteraction('SESSION START')
      // Track the identification event
      trackOperation('User Identification', OperationStatus.SUCCESS, {
        device_type: deviceType,
        os_name: Platform.OS
      });
    }
  } catch (error: any) {
    const statusCode = error?.response?.status;
    const is401 = statusCode === 401 || String(error?.message || '').includes('401');

    if (!is401) {
      logger.error('Mixpanel user identification failed:', error);
    }
  }
};
