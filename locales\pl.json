{"common": {"error": "Błąd", "yes": "Tak", "no": "<PERSON><PERSON>", "sometimes": "<PERSON><PERSON><PERSON>", "close": "Zamknij", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "loading": "Ładowanie...", "version": "v0.0.1.7"}, "welcome": "<PERSON><PERSON><PERSON><PERSON>, aby roz<PERSON> rozmowę z Augustem", "notFound": {"title": "Ups!", "message": "Ten ekran nie istnieje.", "goHome": "Przejdź do ekranu głównego!"}, "library": {"title": "Biblioteka Zdrowia"}, "specialists": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Skonsultuj się ze specjalistami służby zdrowia w celu uzyskania bardziej szczegółowych informacji na temat zdrowia. Wybierz specjalistę poniżej:", "generalPhysician": {"title": "<PERSON><PERSON><PERSON>", "description": "W sprawach ogólnego stanu zdrowia i opieki podstawowej."}, "nutritionist": {"title": "Dietetyk", "description": "W sprawie diety, odżywiania i porad dotyczących kontroli wagi."}, "cardiologist": {"title": "Kardiolog", "description": "W sprawach dotyczących serca i zdrowia układu krążenia."}, "neurologist": {"title": "Neurolog", "description": "W sprawach dotyczących mózgu, rdzenia kręgowego i układu nerwowego."}, "oncologist": {"title": "Onkolog", "description": "W sprawach dotyczących nowotworów i ich leczenia."}, "endocrinologist": {"title": "Endokrynolog", "description": "W sprawach zaburzeń hormonalnych i cukrzycy."}, "dermatologist": {"title": "Dermatolog", "description": "W sprawach dotyczących skóry, włosów i paznokci."}, "psychiatrist": {"title": "Psychiatria", "description": "W sprawach zdrowia psychicznego i dobrego samopoczucia psychicznego."}}, "profile": {"title": "Profil", "defaultName": "<PERSON><PERSON><PERSON>", "namePlaceholder": "Wprowadź swoje imię", "saving": "Zapisywanie...", "noPhoneNumber": "Brak numeru telefonu", "loggingOut": "Wylogowywanie...", "about": {"title": "O ap<PERSON>ji", "description": "Dowiedz się więcej o Auguście"}, "whatsapp": {"title": "WhatsApp", "description": "Porozmawiaj z Augustem na WhatsApp"}, "refer": {"title": "<PERSON><PERSON>", "description": "Polubiłeś Augusta? Podziel się z przyjaciółmi!"}, "deleteAccount": {"title": "Us<PERSON>ń konto", "description": "<PERSON>rz<PERSON>ro nam, że odchodzisz"}, "logout": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wracaj wkrótce. Będziemy Cię tęsknić"}, "shareMessage": "👋Hej, sprawdź tę niesamowitą aplikację, z której korzystam!\n\n\n\n➡️Korzystam z Augusta, aby uzyskać szybkie i wiarygodne informacje i wskazówki dotyczące zdrowia. To jak mieć lekarza w kieszeni! Sprawdź to tutaj:", "error": {"loadFailed": "Nie udało się załadować danych użytkownika", "fetchError": "Wystą<PERSON>ł błąd podczas pobierania danych użytkownika", "updateNameFailed": "Nie udało się zaktualizować imienia", "updateNameError": "Wys<PERSON>ą<PERSON>ł błąd podczas aktualizacji imienia", "loadFoodData": "Nie udało się załadować danych żywieniowych", "logoutError": "Błąd podczas wylogowywania:", "shareError": "Błąd udostępniania wiadomości:"}}, "error": {"title": "Coś poszło nie tak", "checkLogs": "Sprawdź dzienniki urządzenia, aby uzyskać więcej szczegółów.", "unknown": "Nieznany błąd", "unknownFile": "Nieznany plik", "unknownLine": "Nieznana linia", "unknownColumn": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>na"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON>rz kraj", "searchCountries": "Szukaj krajów", "validation": {"invalidPhone": "Wprowadź poprawny numer telefonu", "invalidDigits": "Wprowadź poprawny numer telefonu (7-15 cyfr)"}}, "header": {"title": "Uzyskaj natychmiastową i prywatną odpowiedź na swoje pytania dotyczące zdrowia", "subtitle": "Przemyślane wskazówki. Bez pośpiechu. Bez zamieszania.", "emphasis": "Po prostu j<PERSON>."}, "greeting": "Cześć 👋", "phoneNumber": "Numer telefonu", "requestOTP": "Wyślij kod OTP", "otp": {"title": "Jednorazowe hasło", "verify": "Zweryfikuj kod OTP", "sending": "Wysyłanie...", "countdown": "Ponowna wysyłka kodu OTP za {{countdown}}s", "resend": "Wyślij ponownie kod OTP", "sentTo": "Kod OTP wysłany na ", "whatsappSuffix": " przez <PERSON>"}, "disclaimer": {"prefix": "Rejestrując się, zgadzasz się na nasze ", "continuePrefix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zgadzasz się na nasze ", "termsOfService": "Warunki świadczenia usług", "and": " i ", "privacyPolicy": "Politykę <PERSON>", "whatsappConsent": ", oraz wyrażasz zgodę na otrzymywanie aktualizacji i przypomnień od nas za pośrednictwem WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Witamy w August!", "buttonText": "Zacznijmy!"}}, "postAuth": {"step1": {"title": "Hej!", "subtitle": "Jestem August 👋", "description": "Myśl o mnie jak o przytulnym kąciku na Twoim\nurządzeniu, gdzie możesz odkrywać wszystkie swoje\nzdrowotne ciekawości.", "subdescription": "Zadawaj wszystko, co Ci na myśli.\nBez oceniania, bez ograniczeń!", "placeholder": "Jak mam C<PERSON>ę na<PERSON>?"}, "step2": {"title": "<PERSON><PERSON><PERSON><PERSON> {{userName}},", "subtitle": "Oto co potrafię:", "features": {"health": {"title": "Odpowiedź na Twoje", "subtitle": "Pytania zdrowotne"}, "nutrition": {"title": "Śledź swoje", "subtitle": "Makroskładniki"}, "reports": {"title": "Analizuj", "subtitle": "<PERSON><PERSON><PERSON>"}}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "careful": "Dokładne", "accurate": "Precyzyjne"}, "features": {"symptoms": {"title": "Sprawdź swoje objawy", "description": "Od tygodnia mam nudności. Co się ze mną dzieje?"}, "prescriptions": {"title": "Przeanalizuj swoje recepty", "description": "Prześlij i zrozum recepty jak lekarz."}, "medicine": {"title": "Poznaj swoje leki", "description": "<PERSON>zy Metformina na mój PCOS wchodzi w interakcję z moimi tabletkami na ADHD?"}, "plans": {"title": "Uzyskaj spersonalizowane plany", "description": "<PERSON><PERSON> moż<PERSON>z stworzyć dla mnie plan żywieniowy i fitness, aby obniżyć poziom HbA1c?"}}, "buttons": {"getStarted": "Zacznij", "next": "<PERSON><PERSON>"}, "errors": {"nameRequired": "Wprowadź swoje imię"}}, "tabs": {"chat": "<PERSON><PERSON><PERSON>", "discover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nutrition": "Odżywianie", "personalize": "<PERSON><PERSON><PERSON><PERSON>"}, "chat": {"nav": {"title": "August"}, "me": "<PERSON>a", "augustName": "August", "input": {"placeholder": "<PERSON>apytaj Augusta...", "disclaimer": "August może się mylić. Potwierdź u lekarza"}, "list": {"loadingMessages": "Ładowanie wiadomości...", "noMessages": "Brak jeszcze wiadomości. Rozpocznij rozmowę!"}, "connection": {"offlineMessage": "Wygląda na to, że jeste<PERSON> offline. Połącz się ponownie, aby w<PERSON><PERSON><PERSON> wiadom<PERSON>.", "connecting": "Łączenie...", "tryAgain": "Spróbuj ponownie"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> raport", "speakInHindi": "Roz<PERSON><PERSON><PERSON> po hindi", "notFeelingWell": "<PERSON>e czuję się dobrze", "whatIsMyBMI": "<PERSON><PERSON>e jest moje BMI?", "nutritionAdvice": "Porady żywieniowe", "sleepBetter": "<PERSON><PERSON><PERSON>"}, "citations": {"referenceText": "Aby uzyskać więcej szczegółów na temat tej rozmowy, zapoznaj się z:"}, "actions": {"copiedToClipboard": "Skopiowano do schowka", "copied": "Skopiowano"}, "share": {"introText": "👋Hej, spójrz na rozmowę, którą prowadziłem z Augustem:\n\n", "downloadText": "\n\n➡️Pobierz Augusta, aby p<PERSON><PERSON><PERSON> ze swoim przyjaznym asystentem AI ds. zdrowia:\n"}}, "discover": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "categories": {"all": "Wszystkie", "heartHealth": "Zdrowie serca", "nutrition": "Odżywianie", "mentalHealth": "Zdrowie psychiczne", "fitness": "Fitness", "wellness": "Zdrowie i wellness"}, "cards": {"empty": "Brak kart dostępnych dla tej kategorii"}, "sections": {"features": "<PERSON><PERSON><PERSON>"}, "features": {"healthLibrary": {"title": "Biblioteka zdrowia", "description": "Dostęp do sprawdzonych, wiarygodnych i aktualnych informacji medycznych całkowicie za darmo."}, "nutritionTracker": {"title": "Monitor odżywiania", "description": "Zastanawiałeś się kiedyś, czy możesz po prostu przesłać zdjęcie swojego jedzenia i śledzić wszystkie swoje cele żywieniowe? August może to zrobić!"}, "multilingualSupport": {"title": "Wsparcie wielojęzyczne", "description": "Możesz komunikować się z Augustem w dowolnym języku, którym się posługujesz! August jest zawsze gotowy słuchać, wspierać i odpowiadać w każdej chwili.", "samplePrompt": "Roz<PERSON><PERSON><PERSON> po hindi"}, "labReportAnalysis": {"title": "<PERSON><PERSON><PERSON>", "description": "Kiedy rozmawiasz z Augustem o swoich wynikach badań laboratoryjnych, uzyskujesz ekstremalną precyzję. August przetworzył ponad 4,7 miliona raportów z dokładnością ekstrakcji biomarkerów wynoszącą 98,4%."}}}, "nutrition": {"nav": {"title": "Odżywianie"}, "meals": {"title": "<PERSON><PERSON> posiłki", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, aby sprawdzić makroelementy w każdym posiłku"}, "upload": {"loading": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>..."}, "defaultFoodName": "Produkt spożywczy", "today": "<PERSON><PERSON><PERSON><PERSON>", "unknownTime": "<PERSON><PERSON><PERSON><PERSON>", "calories": "🔥 Kalorie", "proteins": "🥩 Białka", "carbs": "🍞 Węglowodany", "sugars": "🍬 <PERSON><PERSON><PERSON>", "fat": "🥑 Tłuszcze", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "Białko", "carbohydratesLabel": "Węglowodany", "fatLabel": "Tłuszcz", "sugarLabel": "<PERSON><PERSON><PERSON>", "tips": "Wskazówki:", "macroBreakdown": "Podział makroskładników", "noMacroData": "Brak dostępnych danych o makroskładnikach dla tego produktu.", "disclaimer": "Tylko do celów edukacyjnych. Dowiedz się więcej", "disclaimerLink": "tutaj", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "<PERSON><PERSON> jest <PERSON>ja płeć?", "subtitle": "To będzie użyte do kalibracji Twojego spersonalizowanego planu.", "male": "Mężczyzna", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "age": {"title": "Ile masz lat?", "subtitle": "To będzie użyte do obliczenia Twoich dziennych potrzeb."}, "measurements": {"title": "Wzrost i waga", "subtitle": "Wprowadź swój wzrost w centymetrach i wagę w kilogramach."}, "activity": {"title": "Poziom aktywności", "subtitle": "Jak c<PERSON>?", "none": "Brak <PERSON>wicz<PERSON>ń", "moderate": "Umiarkowany", "high": "<PERSON><PERSON><PERSON>"}, "goal": {"title": "<PERSON><PERSON> wagowy", "subtitle": "Czego chcesz osiągnąć?", "increase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maintain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decrease": "Zm<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Waga docelowa", "subtitle": "J<PERSON> jest <PERSON>ja waga docelowa w kilogramach?"}, "setup": {"title": "Us<PERSON>wianie planu", "subtitle": "<PERSON><PERSON><PERSON>, przygotowujemy Twój plan żywieniowy."}, "review": {"title": "Przegląd planu", "subtitle": "Sprawdź i dostosuj swój plan żywieniowy."}, "height": {"label": "W<PERSON><PERSON>t (cm)"}, "weight": {"label": "Waga (kg)"}}, "error": {"updateFailed": "Nie udało się zaktualizować danych żywieniowych. Spróbuj ponownie.", "parsingError": "Błąd parsowania danych żywieniowych:", "fetchReportsFailed": "Nie udało się pobrać danych raportów. Spróbuj ponownie.", "missingReportId": "Brakuje identyfikatora raportu"}}, "personalize": {"nav": {"title": "Personalizacja"}, "button": {"saving": "Zapisywanie", "review": "Przegląd", "saveNext": "Zapisz i dalej"}}, "basicInfo": {"title": "Poznajmy si<PERSON> le<PERSON>j", "subtitle": "Te informacje pomogą nam spersonalizować Twoje rekomendacje zdrowotne", "age": {"question": "Ile masz lat?", "placeholder": "Wprowadź swój wiek"}, "sex": {"question": "<PERSON><PERSON> jest <PERSON>ja płeć?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> swoją płeć", "male": "Mężczyzna", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "height": {"question": "<PERSON><PERSON> jest <PERSON><PERSON><PERSON>j wz<PERSON>t? (cm)", "placeholder": "Wprowadź swój wzrost"}, "weight": {"question": "<PERSON><PERSON> jest <PERSON>ja waga? (kg)", "placeholder": "Wprowadź swoją wagę"}}, "lifestyle": {"title": "Twoje nawyki <PERSON>cio<PERSON>", "subtitle": "Zrozumienie Twoich codziennych nawyków pomaga nam w dostarczaniu lepszych rekomendacji.", "diet": {"question": "<PERSON><PERSON><PERSON>?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> swoj<PERSON>", "vegetarian": "Weget<PERSON>ński", "nonVegetarian": "<PERSON><PERSON>", "vegan": "<PERSON><PERSON><PERSON>", "pescatarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "<PERSON><PERSON> <PERSON>wicz<PERSON>z?"}, "drinking": {"question": "<PERSON><PERSON> spo<PERSON><PERSON> al<PERSON>?"}, "smoking": {"question": "<PERSON><PERSON> p<PERSON>?"}, "sleep": {"question": "Ile godzin śpisz w nocy?", "value": "{{sleep}} godzin"}, "hydration": {"question": "Ile szklanek wody pijesz dziennie?", "value": "{{hydration}} szklanek ({{liters}}L)"}}, "allergies": {"title": "<PERSON><PERSON> masz jakieś alergie?", "subtitle": "Z<PERSON><PERSON><PERSON><PERSON><PERSON> Twoich alergii pomaga nam w bezpieczniejszym dostarczaniu rekomendacji.", "allergyIndex": "Alergia {{index}}", "name": {"question": "Na co jesteś uczulony/uczulona?", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> alergię (np. Or<PERSON><PERSON><PERSON> ziem<PERSON>, Kurz)"}, "severity": {"question": "Jak silna jest ta alergia?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "mild": "Łagodny", "moderate": "Umiarkowany", "severe": "Ciężki"}, "addButton": "<PERSON><PERSON><PERSON> alergi<PERSON>", "noAllergiesButton": "<PERSON>e mam żadnych alergii."}, "medications": {"title": "Leki i suplementy", "subtitle": "Powiedz nam o wszystkich lekach lub suplementach, które obecnie przyjmujesz.", "medicationIndex": "Lek {{index}}", "name": {"label": "<PERSON><PERSON>wa leku", "placeholder": "Wprowadź nazwę leku"}, "startDate": {"question": "<PERSON><PERSON>y zacząłeś/aś to brać?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"label": "<PERSON><PERSON><PERSON>", "shortTerm": "Krótkoterminowy", "longTerm": "Długoterminowy"}, "dose": {"label": "Dawka", "placeholder": "K<PERSON><PERSON>"}, "unit": {"label": "Jednostka"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "<PERSON><PERSON>nn<PERSON>", "perWeek": "tygodniowo", "perMonth": "miesię<PERSON>nie", "perYear": "rocznie"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "<PERSON><PERSON><PERSON>i", "drops": "krople", "tsp": "łyżeczka", "tbsp": "łyżka stołowa", "cups": "kubki"}, "addButton": "<PERSON><PERSON>j k<PERSON> lek", "noMedicationsButton": "Nie biorę żadnych leków.", "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON> roz<PERSON>"}}, "conditions": {"title": "<PERSON>", "subtitle": "Opowiedz nam o wszelkich schorzeniach, na które cierpisz lub cierpiałeś w przeszłości", "conditionIndex": "Warunek {{index}}", "name": {"label": "Nazwa schorzenia", "placeholder": "Wprowad<PERSON> schorzenie (np. astma itp.)"}, "since": {"question": "Od kiedy masz ten problem?", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "current": {"question": "<PERSON><PERSON> to cię obecnie niepokoi?"}, "medicated": {"question": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> jaki<PERSON> leki na to?"}, "addButton": "<PERSON><PERSON><PERSON>", "noConditionsButton": "<PERSON>e mam żadnych schorzeń.", "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "Zdrowie reprodukcyjne", "subtitle": "Te informacje pomogą nam w dostarczaniu spersonalizowanych zaleceń zdrowotnych", "menstruation": {"question": "<PERSON>zy miałaś kiedykolwiek miesiączkę?", "detailsTitle": "Szczegóły dotyczące miesiączki", "regularity": {"question": "Jak regularny jest twój cykl?", "regular": "Regularny", "irregular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notSure": "<PERSON><PERSON> wiem"}, "cycleLength": {"label": "Średnia długość cyklu (dni)", "placeholder": "Wprowadź długość cyklu"}, "flowDays": {"label": "Dni krwawienia: {{flowDays}}", "min": "1 dzień", "max": "15 dni"}, "padsPerDay": {"label": "Podpasek/tamponów dziennie: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Jakiekolwiek objawy podczas okresu?", "placeholder": "W<PERSON><PERSON><PERSON><PERSON> objawy (np. <PERSON><PERSON><PERSON><PERSON>, bóle głowy)"}}, "childbirth": {"question": "<PERSON><PERSON> doświadcz<PERSON>ła<PERSON> porodu?", "detailsTitle": "Szczegóły dotyczące porodu", "children": {"label": "Liczba dzieci"}, "pregnancies": {"label": "Liczba ciąż"}, "complications": {"question": "Jakiekolwiek powikłania podczas ciąży lub porodu?", "placeholder": "Wprowadź powikłania (je<PERSON><PERSON> jakieś)"}}}, "review": {"title": "Sprawdź swoje informacje", "subtitle": "Przed wysłaniem sprawdź wprowadzone informacje", "sections": {"basicInfo": "Informacje podstawowe", "lifestyle": "Styl życia", "allergies": "Alergie", "medications": "Leki i suplementy", "conditions": "Sc<PERSON><PERSON><PERSON>", "reproductive": "Zdrowie reprodukcyjne", "menstruationDetails": "Szczegóły dotyczące miesiączki", "childbirthDetails": "Szczegóły dotyczące porodu"}, "fields": {"age": "Wiek:", "sex": "Płeć:", "height": "Wzrost:", "weight": "Waga:", "diet": "Dieta:", "exercise": "Ćwiczenia:", "drinking": "Alkohol:", "smoking": "Palenie:", "sleep": "Sen:", "hydration": "Nawodnienie:", "allergyIndex": "Alergia {{index}}:", "dose": "Dawka:", "frequency": "C<PERSON>ęsto<PERSON><PERSON><PERSON>ść:", "type": "Rodzaj:", "since": "Od:", "currentlyActive": "Obecnie aktywne:", "takingMedication": "Przyjmowanie leków:", "hasMenstruated": "<PERSON><PERSON> miesiączkę:", "regularity": "Regularność:", "cycleLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cyklu:", "flowDays": "Dni krwawienia:", "padsPerDay": "Podpasek/tamponów dziennie:", "hasChildbirth": "Doświadczyła porodu:", "children": "Dzieci:", "pregnancies": "Ciąż:"}, "notProvided": "<PERSON><PERSON> podano", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} godzin dziennie", "hydration": "{{hydration}} k<PERSON><PERSON><PERSON> ({{liters}}L) dziennie", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} dni"}, "noData": {"allergies": "Brak podanych alergii", "medications": "Brak podanych leków", "conditions": "Brak podanych schorzeń"}, "submitButton": "Wyślij informacje"}, "success": {"title": "Informacje zaktualizowane!", "message": "Dziękujemy za podanie informacji o swoim zdrowiu. Użyjemy ich do spersonalizowania Twojego doświadczenia i dostarczenia lepszych zaleceń.", "benefits": {"insights": "Spersonalizowane wskazówki zdrowotne", "reminders": "Lepsze przypomnienia o lekach", "recommendations": "Dopasowane zalecenia zdrowotne"}, "continueButton": "Przejdź do pulpitu nawigacyjnego"}, "permissions": {"microphonePermissionDenied": "Odmowa dostępu do mikrofonu", "microphoneAccessDescription": "August wymaga dostępu do mikrofonu, aby nagrywać dźwięk i wysyłać notatki głosowe", "permissionDenied": "Odmo<PERSON> dos<PERSON>ępu", "cameraPermissionRequired": "Potrzebujemy dostępu do kamery, aby to zadziałało!", "mediaLibraryPermissionRequired": "Potrzebujemy dostępu do biblioteki multimediów, aby to zadziałało!"}, "voiceRecording": {"recordingTooLong": "Nagranie za długie", "recordingTooLongMessage": "Nagrania głosowe muszą trwać krócej niż 5 minut. Nagraj krótszą wiadomość."}, "errors": {"uploadFailed": "<PERSON><PERSON> udało się przesłać", "voiceUploadFailed": "<PERSON>e udało się przesłać nagrania głosowego.", "voiceRecordingFailed": "<PERSON>e udało się wysłać nagrania głosowego", "failedToStopRecording": "Nie udało się zatrzymać nagrywania", "photoUploadFailed": "<PERSON>e udało się przesłać zdjęcia.", "failedToTakePhoto": "<PERSON>e udało się zrobić zdjęcia", "imageUploadFailed": "<PERSON><PERSON> udało się przesłać obrazu: {{fileName}}", "failedToPickImage": "<PERSON>e udało się wybrać obrazu", "documentUploadFailed": "<PERSON>e udało się przesłać dokumentu: {{fileName}}", "failedToPickDocument": "Nie udało się wybrać dokumentu"}, "audioPlayer": {"downloadingAudio": "Pobieranie dźwięku...", "loadingAudio": "Wczytywanie dźwięku..."}, "mediaProcessing": {"processingFile": "Przetwarzanie pliku", "uploadingSecuring": "Wysyłanie i zabezpieczanie pliku...", "analyzingContent": "Analizowanie zawartości dokumentu...", "extractingInfo": "Ekstrahowanie kluczowych informacji...", "processingInsights": "Przetwarzanie informacji...", "preparingResponse": "Przygotowywanie szczegółowej odpowiedzi...", "finalizingResponse": "Finalizowanie odpowiedzi..."}, "attachments": {"voiceMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć głosowa", "image": "[OBRAZ]", "pdf": "[PDF]", "voice": "[NOTATKA GŁOSOWA]"}, "pdf": {"loadingPdf": "Wczytywanie pliku PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "w<PERSON><PERSON><PERSON><PERSON>"}, "mediaUpload": {"photoLibrary": "Galeria zdjęć", "takePhoto": "Zrób zdjęcie", "chooseFile": "<PERSON><PERSON><PERSON>rz plik"}, "comingSoon": {"title": "Wkrótce!", "description": " jest obecnie w fazie rozwoju. Bądź na bieżąco z aktualizacjami!", "buttonText": "Rozumiem!"}, "clipboard": {"success": "Link skopiowany do schowka"}, "mediaPhotos": {"emptyState": "Brak wpisów."}, "foodDetail": {"defaultFoodName": "Produkt spożywczy", "nutrition": {"totalCalories": "Całkowita kaloryczność", "proteins": "Białka", "carbs": "Węglowodany", "fat": "Tłuszcze", "sugars": "<PERSON><PERSON><PERSON>", "fibers": "Błonnik"}}, "reports": {"defaultTitle": "Element multimedialny", "defaultFoodName": "Produkt spożywczy", "defaultName": "Dokument", "openButton": "Otwórz w przeglądarce zewnętrznej", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "<PERSON><PERSON><PERSON><PERSON>", "headerRefRange": "Zakres referencyjny", "headerStatus": "Status"}, "noData": "Brak dostępnych danych biomarkerów"}, "setup": {"title": "Konfigurujemy wszystko dla Ciebie", "inProgress": "W trakcie...", "progressMessages": {"0": "Obliczanie dziennego zapotrzebowania kalorycznego", "1": "Optymalizacja podziału makro", "2": "Tworzenie planu posiłków", "3": "Obliczanie wskaźnika zdrowia", "4": "Finalizacja konfiguracji"}, "checklistItems": {"0": "<PERSON><PERSON><PERSON> da<PERSON>ch zdrowotnych", "1": "Obliczanie optymalnego planu żywieniowego", "2": "Personalizacja zaleceń", "3": "Tworzenie sugestii posiłków", "4": "Finalizacja konfiguracji"}}, "foodEntry": {"emptyState": "Brak jeszcze wpisów posiłków. Zrób zdjęcie swojego posiłku, aby je dodać!"}, "nutritionReview": {"congratulations": "G<PERSON>ulacje!", "subtitle": "Twój spersonalizowany plan żywieniowy jest gotowy", "submitButton": "Zaczynamy!", "dailyTargetsTitle": "Twoje dzienne cele żywieniowe", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "Węglowodany", "protein": "Białko", "fats": "Tłuszcze"}, "recommendations": {"title": "<PERSON><PERSON> swoje cele:", "healthScores": "Używaj wskaźników zdrowia, aby <PERSON><PERSON> swoją rutynę.", "trackFood": "Regularnie monitoruj spożycie jedzenia", "followCalories": "Stosuj się do swojego dziennego zalecenia kalorycznego.", "balanceMacros": "Zrównoważ spożycie węglowodanów, białka i tłuszczu."}}, "editModal": {"titlePrefix": "<PERSON><PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON>"}, "processing": {"stages": {"scanning": "Skanowanie jedzenia...", "identifying": "Identyfikowanie składników...", "extracting": "Ekstrakcja składników odżywczych...", "finalizing": "Finalizowanie wyników..."}, "error": {"defaultMessage": "<PERSON><PERSON> w<PERSON><PERSON> j<PERSON>", "subtitle": "Spróbuj innego podejścia."}, "retakeButton": "<PERSON><PERSON><PERSON><PERSON>, aby zrobić zdjęcie ponownie", "notification": "Powiadomimy Cię, gdy skończymy!"}, "chart": {"title": "Monitorowanie Odżywiania w Czasie", "selectNutrient": "Wybierz składnik odżywczy:", "emptyState": "Brak dostępnych danych o wartości odżywczej.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "Białko", "carbs": "Węglowodany", "fat": "Tłuszcz", "sugars": "<PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "Produkt spożywczy", "defaultDate": "<PERSON><PERSON><PERSON><PERSON>", "defaultTime": "<PERSON>ez<PERSON><PERSON> czas", "saveChanges": "Zapisz zmiany", "error": {"title": "Błąd", "message": "Nie udało się zaktualizować danych o wartościach odżywczych. Spróbuj ponownie."}, "nutrition": {"calories": "🔥 Kalorie", "proteins": "🥩 Białka", "carbs": "🍞 Węglowodany", "sugars": "🍬 <PERSON><PERSON><PERSON>", "fat": "🥑 Tłuszcz"}, "macroBreakdown": {"title": "Rozkład makroskładników", "noData": "Brak danych makroskładników dla tego produktu spożywczego."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "Białko", "carbs": "Węglowodany", "fat": "Tłuszcz", "sugar": "<PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "Szczegółowe informacje", "edit": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Zapisywanie...", "enterValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notSet": "<PERSON><PERSON>", "age": "<PERSON><PERSON>", "heightCm": "W<PERSON><PERSON>t (cm)", "weightKg": "Waga (kg)", "targetWeight": "Waga docelowa", "nutritionTargets": "<PERSON><PERSON>eni<PERSON>e", "protein": "Białko", "carbs": "Węglowodany", "fats": "Tłuszcze", "gm": "gm", "editNote": "W<PERSON>rowadź wartości lub pozostaw puste pola dla automatycznego obliczenia.", "autoCalculateNote": "Makra są automatycznie obliczane na podstawie Twoich danych.", "validation": {"ageMin": "Wiek musi wyn<PERSON>ić co najmniej 18 lat", "ageMax": "Wiek musi by<PERSON> poniżej 125", "heightMin": "<PERSON><PERSON><PERSON><PERSON><PERSON> musi wyn<PERSON>ić co najmniej 50 cm", "heightMax": "<PERSON><PERSON><PERSON><PERSON><PERSON> musi być poniżej 250 cm", "weightMin": "Waga musi w<PERSON> co najmniej 30 kg", "weightMax": "Waga musi być poniżej 500 kg", "targetWeightMin": "Docelowa waga musi w<PERSON>ić co najmniej 30 kg", "targetWeightMax": "<PERSON>sa docelowa musi by<PERSON> poniżej 500 kg", "proteinMin": "Białko musi być 0 lub więcej.", "carbsMin": "Węglowodany muszą wynosić 0 lub więcej.", "fatsMin": "Tłuszcze muszą wynosić 0 lub więcej."}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "Białko", "carbs": "Węglowodany", "fat": "Tłuszcz", "excess": "Nadmiar", "remaining": "pozostałe"}, "specialistConstants": {"nutritionist": {"name": "Dietetyk", "description": "Ekspercka porada w zakresie diety, żywienia i zdrowych nawyków żywieniowych", "featureName": "Specjalista ds. Żywienia"}, "cardiologist": {"name": "Kardiolog", "description": "Specjalista w dziedzinie zdrowia serca i chorób układu krążenia", "featureName": "Specjalista Kardiolog"}, "neurologist": {"name": "Neurolog", "description": "Specjalista zajmujący się chorobami mózgu, rdzenia kręgowego i układu nerwowego", "featureName": "Specjalista Neurolog"}, "oncologist": {"name": "Onkolog", "description": "Specjalista w dziedzinie diagnostyki i leczenia raka", "featureName": "Specjalista Onkolog"}, "endocrinologist": {"name": "Endokrynolog", "description": "Ekspert w dziedzinie chorób hormonalnych i zaburzeń metabolicznych", "featureName": "Specjalista Endokrynolog"}}, "discoverCards": {"categories": {"nutrition": "Odżywianie", "heartHealth": "Zdrowie serca", "mentalHealth": "Zdrowie psychiczne", "fitness": "Fitness", "wellness": "Zdrowie i wellness"}, "titles": {"vitaminB12Recovery": "Jak długo trwa regeneracja po niedoborze witaminy B12", "vitaminDeficiencyGanglion": "Który niedobór witaminy powoduje torbiele galaretowate", "vitaminDeficiencyHairFall": "Który niedobór witaminy powoduje wypadanie włosów", "vitaminWaters": "<PERSON><PERSON> wit<PERSON>we wody są dla Ciebie dobre", "cholesterolHeadaches": "<PERSON>zy wysoki cholesterol powoduje bóle głowy", "cholesterolEyes": "<PERSON><PERSON><PERSON> objawy wysokiego cholesterolu można zaobserwować w oczach", "diabetesHeadaches": "<PERSON><PERSON> cukrzyca może powodować bóle głowy", "chestPainDrinking": "Dlaczego boli klatka piersiowa po alkoholu", "stressDizziness": "<PERSON><PERSON> stres może powodować zawroty głowy", "bulimiaFace": "<PERSON><PERSON><PERSON> jest twarz buli<PERSON><PERSON>ki", "kneeTwitch": "Dlaczego drga mi kolano", "noseTwitching": "Dlaczego drga nos", "piriformisVsSciatica": "Jakie są różnice między zespołem mięśnia gruszkowatego a rwa kulszową", "shoulderBladePinched": "<PERSON><PERSON> <PERSON><PERSON>ić uciskany nerw w łopatce", "shoulderPinched": "<PERSON><PERSON> <PERSON><PERSON>ić uciskany nerw w ramieniu", "meniscusTorn": "Jak naturalnie wyleczyć uszkodzony łąkotkę", "hydrateQuickly": "<PERSON>ak szybko się nawodn<PERSON>", "periodConstipation": "Czy zaparcia w czasie miesiączki są normalne", "acneScars": "<PERSON><PERSON> p<PERSON> się blizn potrądzikowych naturalnie w ciągu tygodnia", "perimenopausePregnancy": "Czy można zajść w ciążę w okresie okołomenopauzalnym"}, "descriptions": {"vitaminB12Recovery": "Odkryj przebieg rekonwalescencji po niedoborze witaminy B12 i skuteczne metody na podniesienie poziomu energii.", "vitaminDeficiencyGanglion": "Zbadaj związek między niedoborami witamin a powstawaniem torbieli galaretowatych w organizmie.", "vitaminDeficiencyHairFall": "<PERSON><PERSON><PERSON>, jak brak niezbędnych witamin może prowadzić do wypadania włosów i co możesz zrobić, aby temu zapobiec.", "vitaminWaters": "Odkryj korzyści i potencjalne wady witaminowych napojów w kontekście codziennej diety.", "cholesterolHeadaches": "Przeanalizuj możliwy związek między wysokim poziomem cholesterolu a występowaniem bólu głowy.", "cholesterolEyes": "<PERSON><PERSON><PERSON>, jak wysoki poziom cholesterolu może objawiać się w oczach i na jakie objawy zwracać uwagę.", "diabetesHeadaches": "Zbadaj związek między cukrzycą a występowaniem bólu głowy w życiu codziennym.", "chestPainDrinking": "Zbadaj przyczyny bólu w klatce piersiowej po spożyciu niektórych napojów.", "stressDizziness": "Zagłęb się w to, jak stres może wpływać na równowagę i ogólne samopoczucie, prowadząc do zawrotów głowy.", "bulimiaFace": "Zrozum fizyczne objawy bulimii, w tym wpływ na wygląd twarzy.", "kneeTwitch": "Zbadaj potencjalne przyczyny mimowolnego drgania kolana i jego związek ze stresem lub zmęczeniem.", "noseTwitching": "Dowiedz się o możliwych przyczynach drgania nosa i jego związku z lękiem lub innymi czynnikami.", "piriformisVsSciatica": "Porównaj objawy zespołu mięśnia gruszkowatego i rwy kulszowej, aby lepiej zrozumieć swój stan.", "shoulderBladePinched": "Odkryj skuteczne techniki łagodzenia ucisku nerwu w łopatce i przywracania ruchomości.", "shoulderPinched": "<PERSON>ucz się prostych ćwiczeń i rozciągania, aby złagodzić ucisk nerwu w okolicy barku.", "meniscusTorn": "Zbadaj naturalne metody i ćwiczenia wspierające gojenie uszkodzonego łąkotki.", "hydrateQuickly": "Dowiedz się o szybkich i skutecznych sposobach na nawodnienie i utrzymanie optymalnego nawodnienia organizmu.", "periodConstipation": "Zrozum przyczyny zaparć podczas menstruacji i poznaj naturalne metody leczenia.", "acneScars": "Odkryj naturalne metody i porady dotyczące pielęgnacji skóry, aby szybko zmniejszyć widoczność blizn potrądzikowych.", "perimenopausePregnancy": "Dowiedz się o okresie okołomenopauzalnym, kwestiach płodności i czego można się spodziewać na tym etapie życia."}}}