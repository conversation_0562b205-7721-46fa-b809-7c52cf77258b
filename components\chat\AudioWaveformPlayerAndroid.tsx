import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {
  Waveform,
  type IWaveformRef,
  PlayerState,
} from '@simform_solutions/react-native-audio-waveform';
import { Audio, InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { Play, Pause } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useAudioStore } from '@/store/audioStoreAndroid'; // or wherever it's located

interface AudioWaveformPlayerAndroidProps {
  uri: string;
  path: string;
  messageId: string;
  currentlyPlayingId: string | null;
  setCurrentlyPlayingId: (id: string | null) => void;
}

const VOICE_DIR = `${FileSystem.documentDirectory}voices/`;

const AudioWaveformPlayerAndroid: React.FC<AudioWaveformPlayerAndroidProps> = ({
  uri,
  path,
  messageId,
  currentlyPlayingId, 
  setCurrentlyPlayingId
}) => {
  const waveformRef = useRef<IWaveformRef>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playerState, setPlayerState] = useState<PlayerState>(PlayerState.stopped);
  const [isLoading, setIsLoading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [sampleAudioPath, setSampleAudioPath] = useState<string | null>(null);
  const [downloadedPath, setDownloadedPath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { currentPlayer, setCurrentPlayer } = useAudioStore();

  // Function to process Android paths
  const getAndroidWaveformPath = (uri: string): string => {
    if (Platform.OS === 'android' && uri.startsWith('file://')) {
      return uri.replace('file://', '');
    }
    return uri;
  };

  // Check if path is a local file
  const isLocalFile = (filePath: string): boolean => {
    return filePath.startsWith('file://');
  };

  // Download audio to local storage
  const downloadAudioToLocal = async (remoteUri: string, filename: string): Promise<string | null> => {
    try {
      const dirInfo = await FileSystem.getInfoAsync(VOICE_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(VOICE_DIR, { intermediates: true });
      }
      const fileUri = `${VOICE_DIR}${filename}`;
      const downloadResult = await FileSystem.downloadAsync(remoteUri, fileUri);
      return downloadResult.uri;
    } catch (error) {
      //console.error('Failed to download audio:', error);
      return null;
    }
  };

  useEffect(() => {
    if (
      currentPlayer?.messageId !== messageId &&
      isPlaying
    ) {
      // This component is NOT the currently playing one
      sound?.pauseAsync();
      waveformRef.current?.pausePlayer();
      setIsPlaying(false);
      setPlayerState(PlayerState.paused);
    }
  }, [currentPlayer?.messageId]);

  // Load sample audio for waveform visualization
  useEffect(() => {
    const loadSampleAudio = async () => {
      try {
        const asset = Asset.fromModule(require('@/assets/audio/sample-audio.m4a'));
        await asset.downloadAsync();
        setSampleAudioPath(asset.localUri || asset.uri);
      } catch (err) {
        //console.error('Failed to load sample audio:', err);
      }
    };
    loadSampleAudio();
  }, []);

  useEffect(() => {
    if (currentlyPlayingId !== messageId && isPlaying) {
      // Another waveform started playing — pause this one
      sound?.pauseAsync();
      waveformRef.current?.pausePlayer();
      setIsPlaying(false);
      setPlayerState(PlayerState.paused);
    }
  }, [currentlyPlayingId]);  

  // Configure audio and load sound when we have a local file
  useEffect(() => {
    let isMounted = true;

    const configureAndLoadAudio = async () => {
      try {
        // Configure audio mode for speaker playback on iOS
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          interruptionModeIOS: InterruptionModeIOS.DoNotMix,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
          playThroughEarpieceAndroid: false,
        });

        // Only load sound if we have a local file
        const audioPath = downloadedPath || path;
        if (!isLocalFile(audioPath)) {
          return;
        }

        setIsLoading(true);
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: audioPath },
          { shouldPlay: false }
        );

        if (!isMounted) {
          await newSound.unloadAsync();
          return;
        }

        // Set up status updates
        newSound.setOnPlaybackStatusUpdate((status) => {
          if (!status.isLoaded || !isMounted) return;
          
          // Only update isPlaying if there's an actual change to avoid flicker
          if (status.isPlaying !== undefined && status.isPlaying !== isPlaying) {
            setIsPlaying(status.isPlaying);
          }
          
          if (status.durationMillis) {
            setDuration(status.durationMillis / 1000);
          }
          
          if (status.positionMillis) {
            setCurrentTime(status.positionMillis / 1000);
          }

          if (status.didJustFinish) {
            setIsPlaying(false);
            setPlayerState(PlayerState.stopped);
            setCurrentTime(0);
            // Ensure waveform is also stopped
            if (waveformRef.current) {
              waveformRef.current.stopPlayer();
            }
          }
        });

        setSound(newSound);
      } catch (error) {
        //console.error('Error loading audio:', error);
        if (isMounted) setError('Failed to load audio');
      } finally {
        if (isMounted) setIsLoading(false);
      }
    };

    configureAndLoadAudio();

    return () => {
      isMounted = false;
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [path, downloadedPath]);

  const handlePlayPause = async () => {
    const audioPath = downloadedPath || path;
    if (!isPlaying && currentPlayer?.messageId !== messageId) {
      currentPlayer?.sound.pauseAsync();
      currentPlayer?.waveformRef?.current?.pausePlayer();
    }
  
    // Set this as current player globally
    if (!isPlaying) {
      setCurrentPlayer({
        sound: sound!,
        waveformRef,
        messageId,
      });
    }
    
    // If it's not a local file, download it first
    if (!isLocalFile(audioPath)) {
      setIsDownloading(true);
      try {
        const filename = `voice_${messageId}.m4a`;
        const localPath = await downloadAudioToLocal(uri, filename);
        
        if (localPath) {
          setDownloadedPath(localPath);
          // The useEffect will handle loading the sound
        } else {
          setError('Failed to download audio');
        }
      } catch (err) {
        //console.error('Download error:', err);
        setError('Failed to download audio');
      } finally {
        setIsDownloading(false);
      }
      return;
    }

    // If we have a loaded sound, toggle playback
    if (!sound) return;

    try {
      if (isPlaying) {
        await sound.pauseAsync();
        waveformRef.current?.pausePlayer();
        setPlayerState(PlayerState.paused);
        setIsPlaying(false);
      } else {
        // Re-ensure audio mode for iOS speaker playback
        if (Platform.OS === 'ios') {
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            staysActiveInBackground: false,
            interruptionModeIOS: InterruptionModeIOS.DoNotMix,
            playsInSilentModeIOS: true,
            shouldDuckAndroid: true,
            interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
            playThroughEarpieceAndroid: false,
          });
        }

        // Set playing state immediately to avoid UI flicker
        setIsPlaying(true);
        setPlayerState(PlayerState.playing);

        if (currentTime === 0 || playerState === PlayerState.stopped) {
          await sound.replayAsync();
          waveformRef.current?.startPlayer();
        } else {
          await sound.playAsync();
          waveformRef.current?.resumePlayer();
        }
      }
    } catch (error) {
      //console.error('Error toggling playback:', error);
      setError('Playback error');
      setIsPlaying(false);
      setPlayerState(PlayerState.stopped);
    }
  };

  const formatTime = (seconds: number) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPlayPauseIcon = () => {
    if (isLoading || isDownloading) {
      return <ActivityIndicator size="small" color={colors.primary} />;
    }
    return playerState === PlayerState.playing ? (
      <Pause size={24} color={colors.primary} />
    ) : (
      <Play size={24} color={colors.primary} />
    );
  };

  // Determine which path to use for waveform display
  const getWaveformPath = () => {
    const audioPath = downloadedPath || path;
    
    if (isLocalFile(audioPath)) {
      return getAndroidWaveformPath(audioPath);
    }
    
    // Use sample audio for visualization
    return sampleAudioPath ? getAndroidWaveformPath(sampleAudioPath) : '';
  };

  const waveformPath = getWaveformPath();
  const hasAudio = sound !== null || !isLocalFile(path);

  // Don't render until we have a waveform path
  if (!waveformPath && !error) {
    return (
      <View style={styles.container}>
        <View style={styles.playButton}>
          <ActivityIndicator size="small" color="#075E54" />
        </View>
        <View style={styles.waveformContainer}>
          <Text style={styles.loadingText}>Loading audio...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.playButton}
        onPress={handlePlayPause}
        disabled={!hasAudio || isLoading || isDownloading}
      >
        {getPlayPauseIcon()}
      </TouchableOpacity>

      <View style={styles.waveformContainer}>
        {waveformPath && (
          <Waveform
            key={downloadedPath || path} // Force re-render when downloadedPath changes
            ref={waveformRef}
            mode="static"
            path={waveformPath}
            candleSpace={2}
            candleWidth={2}
            waveColor="#B0B0B0"
            scrubColor="#075E54"
            //@ts-ignore
            autoPlay={false}
            onPlayerStateChange={(state: PlayerState) => {
              //console.log('Waveform state changed:', state);
              // Update playerState when waveform reports stopped (audio finished)
              if (state === PlayerState.stopped) {
                setPlayerState(PlayerState.stopped);
                setIsPlaying(false);
                setCurrentTime(0);
              }
            }}
            onError={(error: any) => {
              //console.error('Waveform error:', error);
            }}
            onCurrentProgressChange={(progress: number, duration: number) => {
              // We use the Sound object for progress tracking
            }}
          />
        )}
      </View>
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    padding: 8,
    minHeight: 60,
    maxWidth: '100%',
  },
  playButton: {
    backgroundColor: '#fff',
    borderRadius: 24,
    padding: 6,
    marginRight: 8,
  },
  waveformContainer: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 12,
    color: '#667781',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 10,
    color: '#FF0000',
    textAlign: 'center',
  },
});

export default AudioWaveformPlayerAndroid;