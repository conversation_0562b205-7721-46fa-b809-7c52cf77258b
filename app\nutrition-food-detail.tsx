import React, { useState, useMemo, useEffect , useCallback, useRef} from "react";
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    SafeAreaView,
    StatusBar,
    TouchableOpacity,
    Alert,
    Platform,
    ActivityIndicator,
    TextInput,
    Linking, 
    BackHandler
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import {
    ArrowLeft,
    ChevronDown,
    Edit2,
    Plus,
    Minus,
    Clock,
    Calendar,
    Save,
    Share2,
} from "lucide-react-native";
import { colors } from "@/constants/colors";
import {
    moderateScale,
    moderateVerticalScale,
} from "react-native-size-matters";
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import EditValueModal from "@/components/nutrition/EditValueModal";
import { getReportsData, updateFoodData } from "@/services/nutritionService";
import logger from "@/utils/logger/logger";
import { parseNutritionalData } from "@/utils/nutrition/parseNutritionalData";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import WhatsAppStyleMarkdownBubble from '@/components/chat/MarkdownBubble';
import { useTranslation } from 'react-i18next';
import FoodShareCard from "@/components/nutrition/FoodShareCard";
import { captureRef } from "react-native-view-shot";
import * as Sharing from "expo-sharing";
import { useUser } from '@/store/auth-store';
import { OperationStatus, trackOperation } from "@/utils/mixpanel/mixpanel-utils";

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    headerOverlay: {
      position: "absolute",
      top: Platform.OS === "ios" ? moderateVerticalScale(50) : moderateVerticalScale(40),
      left: 0,
      right: 0,
      zIndex: 10,
      paddingHorizontal: moderateScale(16),
    },
    headerContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    headerButton: {
      width: moderateScale(80),
      height: moderateScale(40),
      alignItems: "center",
      justifyContent: "center",
      borderRadius: moderateScale(20),
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      borderWidth: 1,
      borderColor: "rgba(255, 255, 255, 0.2)",
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateScale(2) },
      shadowOpacity: 0.2,
      shadowRadius: moderateScale(4),
      elevation: 3,
    },
    headerButtonBack: {
      width: moderateScale(40),
      height: moderateScale(40),
      alignItems: "center",
      justifyContent: "center",
      borderRadius: moderateScale(20),
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      borderWidth: 1,
      borderColor: "rgba(255, 255, 255, 0.2)",
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateScale(2) },
      shadowOpacity: 0.2,
      shadowRadius: moderateScale(4),
      elevation: 3,
    },
    headerButtonText: {
      color: colors.white,
      fontSize: moderateScale(14),
      fontWeight: "600",
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    imageContainer: {
      width: "100%",
      height: moderateVerticalScale(400), // 75% of original image height
      position: "relative",
    },
    image: {
      width: "100%",
      height: "100%", // full image height

    },
    imageGradient: {
      position: "absolute",
      left: 0,
      right: 0,
      bottom: 0,
      height: moderateVerticalScale(150),
    },
    content: {
      marginTop: -moderateVerticalScale(100),
      borderTopLeftRadius: moderateScale(30),
      borderTopRightRadius: moderateScale(30),
      backgroundColor: theme.colors.secondary[50],
      paddingBottom: moderateVerticalScale(32),
      overflow: "hidden",
    },
    detailsContainer: {
      padding: moderateScale(24),
    },
    link: {
      color: '#007bff', // blue link color
    },
    disclaimer: {
      textAlign: "center",
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
      marginTop: moderateScale(16),
      marginBottom: moderateScale(24),
    },
    title: {
      fontSize: moderateScale(theme.fontSize["xl"]),
      fontWeight: "700",
      color: colors.black,
      marginBottom: moderateScale(12),
    },
    metaContainer: {
      flexDirection: "row",
      marginBottom: moderateScale(24),
    },
    metaItem: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: moderateScale(24),
    },
    metaText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray.main,
      marginLeft: moderateScale(6),
    },
    nutritionContainer: {
      paddingVertical: moderateScale(6),
      marginBottom: moderateScale(24),
      backgroundColor: colors.white,
      borderRadius: moderateScale(16),
      padding: moderateScale(6),
    },
    sectionTitle: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      color: colors.black,
      marginBottom: moderateScale(16),
    },
    notes: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "400",
      color: colors.black,
      lineHeight: moderateVerticalScale(21),
      marginBottom: moderateScale(6),
    },
    nutritionItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingVertical: moderateScale(12),
    },
    nutritionLabel: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: colors.black,
    },
    nutritionValue: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: theme.colors.primary.main,
    },
    divider: {
      height: moderateVerticalScale(1),
      backgroundColor: theme.colors.gray[200],
    },
    macroVisualization: {
      backgroundColor: colors.white,
      borderRadius: theme.radii.lg,
      paddingVertical: moderateScale(20),
      paddingHorizontal: moderateScale(16),
      marginBottom: moderateScale(16),
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
    },
    notesSection: {
      backgroundColor: 'rgba(255, 243, 128, 0.5)',
      borderRadius: theme.radii.lg,
      marginBottom: moderateVerticalScale(16),
      padding: moderateScale(20),
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: moderateScale(8),
      elevation: 2,
    },
    macroBarContainer: {
      flexDirection: "row",
      height: moderateVerticalScale(12),
      borderRadius: theme.radii.md,
      overflow: "hidden",
      marginBottom: moderateScale(16),
    },
    macroBar: {
      height: "100%",
    },
    proteinBar: {
      backgroundColor: "#8D6E63",
    },
    carbsBar: {
      backgroundColor: "#F4A261",
    },
    fatBar: {
      backgroundColor: "#FFD166",
    },
    macroLegend: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    legendItem: {
      flexDirection: "row",
      alignItems: "center",
    },
    legendColor: {
      width: moderateScale(12),
      height: moderateScale(12),
      borderRadius: theme.radii.sm,
      marginRight: moderateScale(8),
    },
    legendText: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
      marginStart: moderateScale(-6)
    },
    notFound: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: moderateScale(24),
    },
    notFoundText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: colors.black,
      marginBottom: moderateScale(16),
    },
    backButton: {
      paddingVertical: moderateScale(10),
      paddingHorizontal: moderateScale(20),
      backgroundColor: theme.colors.primary.main,
      borderRadius: theme.radii.sm,
    },
    backButtonText: {
      color: theme.colors.secondary[50],
      fontWeight: "600",
    },
    noDataText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray.main,
      textAlign: "center",
      marginTop: moderateScale(12),
      fontStyle: "italic",
    },
    quantityContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: moderateScale(16),
      backgroundColor: colors.white,
      borderRadius: theme.radii.lg,
      padding: 0,
    },
    componentEditMode: {
      backgroundColor: colors.gray[50],
    },
    componentEditButton: {
      width: moderateScale(24),
      height: moderateScale(24),
      borderRadius: moderateScale(14),
      backgroundColor: colors.white,
      alignItems: "center",
      justifyContent: "center",
      marginLeft: moderateScale(8),
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
    },
    macroInputField: {
      height: moderateVerticalScale(36),
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: colors.black,
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
      borderRadius: theme.radii.sm,
      paddingHorizontal: moderateScale(8),
      paddingVertical: moderateScale(4),
      backgroundColor: colors.white,
    },
    quantityLabel: {
      fontSize: moderateScale(theme.fontSize.md),
      color: colors.black,
      marginRight: moderateScale(12),
    },
    quantityControls: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.white,
      borderRadius: theme.radii.sm,
      overflow: "hidden",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
    },
    quantityButton: {
      padding: moderateScale(8),
      alignItems: "center",
      justifyContent: "center",
      width: moderateScale(26),
      height: moderateScale(26),
    },
    quantityValue: {
      fontSize: moderateScale(theme.fontSize.md),
      fontWeight: "600",
      paddingHorizontal: moderateScale(2),
      minWidth: moderateScale(16),
      textAlign: "center",
    },
    unitText: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray.main,
      marginLeft: moderateScale(8),
    },
    editUnitButton: {
      marginLeft: moderateScale(8),
    },
    editButton: {
      position: "absolute",
      top: moderateScale(8),
      right: moderateScale(8),
      width: moderateScale(24),
      height: moderateScale(24),
      borderRadius: moderateScale(12),
      backgroundColor: colors.white,
      alignItems: "center",
      justifyContent: "center",
    },
    macroItem: {
      width: "48%",
      backgroundColor: colors.gray[100],
      borderRadius: theme.radii.md,
      padding: moderateScale(12),
      marginTop: moderateScale(12),
      position: "relative",
    },
    macroLabel: {
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
      marginBottom: moderateScale(4),
    },
    macroValue: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: colors.black,
    },
    macroUnit: {
      fontSize: moderateScale(theme.fontSize.md),
      color: theme.colors.gray.main,
    },
    macroGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    componentItem: {
      backgroundColor: colors.white,
      borderRadius: theme.radii.xl,
      padding: moderateScale(14),
      marginBottom: moderateScale(12),
      position: "relative",
      borderWidth: 1,
      borderColor: theme.colors.gray[300],
    },
    componentHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: moderateScale(6),
    },
    componentEmoji: {
      fontSize: moderateScale(14),
      marginRight: moderateScale(6),
    },
    componentName: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      color: colors.black,
      flex: 1,
    },
    componentQuantity: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray.main,
      marginLeft: moderateScale(4),
    },
    componentsContainer: {
      backgroundColor: theme.colors.secondary[50],
      borderRadius: theme.radii.lg,
      padding: 0,
      marginBottom: moderateScale(24),
    },
  });

export default function NutritionFoodDetailScreen() {
  const user = useUser();
  const shareCardRef = useRef(null);
  //console.log(`[PERF] NutritionFoodDetailScreen render started: ${performance.now()}`);

    const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const router = useRouter();
    const params = useLocalSearchParams();
    const [food, setFood] = useState<any>(null);
    const [editedFood, setEditedFood] = useState<any>(null);
    const [originalFood, setOriginalFood] = useState<any>(null);
    const [hasChanges, setHasChanges] = useState(false);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isPageLoading, setIsPageLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const [isLoadingReports, setIsLoadingReports] = useState<boolean>(false);
    const [reportsData, setReportsData] = useState<any[]>([]);
    const [reportsError, setReportsError] = useState<string | null>(null);
  
    const [componentsInEditMode, setComponentsInEditMode] = useState<number[]>(
        []
    );
    const [currentEditItem, setCurrentEditItem] = useState<{
        label: string;
        value: string | number;
        key: string;
        unit?: string;
        isComponent?: boolean;
        componentIndex?: number;
        componentKey?: string;
    } | null>(null);

    const updateComponentMacros = useCallback((component: any, ratio: number) => {
      const updatedComponent = { ...component };

      updatedComponent.calories = Math.round(
          parseFloat(updatedComponent.calories || 0) * ratio
      );
      updatedComponent.protein = Math.round(
          parseFloat(updatedComponent.protein || 0) * ratio
      );
      updatedComponent.carbohydrates = Math.round(
          parseFloat(updatedComponent.carbohydrates || 0) * ratio
      );
      updatedComponent.fat = Math.round(
          parseFloat(updatedComponent.fat || 0) * ratio
      );
      updatedComponent.sugar = Math.round(
          parseFloat(updatedComponent.sugar || 0) * ratio
      );

      return updatedComponent;
  }, []);

  const formatFoodDataForApi = useCallback((food: any) => {
    const food_images = {
        quantity: food.quantity.toString(),
        calories: food.calories.toString(),
        protein: food.protein.toString(),
        carbohydrates: food.carbohydrates.toString(),
        fats: food.fat.toString(),
        sugar: food.sugar.toString(),
    };

    const food_components = food.components
        ? food.components.map((component: any) => ({
              id: component.id.toString(),
              quantity: component.quantity.toString(),
              calories: component.calories.toString(),
              protein: component.protein.toString(),
              fats: component.fat.toString(),
              carbohydrates: component.carbohydrates.toString(),
              sugar: component.sugar ? component.sugar.toString() : "0",
          }))
        : [];

    return {
        food_images,
        food_components,
    };
}, []);

    // Recalculate main meal macros based on all components
    const recalculateMainMealMacros = useCallback((food: any) => {
      if (!food.components || food.components.length === 0) return food;

      // Reset main meal macros
      food.calories = 0;
      food.protein = 0;
      food.carbohydrates = 0;
      food.fat = 0;
      food.sugar = 0;

      // Sum up all component macros
      food.components.forEach((component: any) => {
          food.calories += Math.round(parseFloat(component.calories || 0));
          food.protein += Math.round(parseFloat(component.protein || 0));
          food.carbohydrates += Math.round(
              parseFloat(component.carbohydrates || 0)
          );
          food.fat += Math.round(parseFloat(component.fat || 0));
          food.sugar += Math.round(parseFloat(component.sugar || 0));
      });

      return food;
  }, []);
    // Distribute a macro change proportionally among components
    const distributeMacroChange = useCallback(
      (food: any, macroKey: string, newValue: number) => {
          if (!food.components || food.components.length === 0) return food;
          const currentValue = parseFloat(food[macroKey] || 0);

          if (currentValue === 0) {
              const componentCount = food.components.length;
              const valuePerComponent = Math.round(newValue / componentCount);
              food.components = food.components.map((component: any) => {
                  const componentKey =
                      macroKey === "carbohydrates"
                          ? "carbohydrates"
                          : macroKey === "fat"
                          ? "fat"
                          : macroKey;

                  component[componentKey] = valuePerComponent;
                  return component;
              });

              food[macroKey] = Math.round(newValue);
              return food;
          }

          const ratio = newValue / currentValue;

          food.components = food.components.map((component: any) => {
              const componentKey =
                  macroKey === "carbohydrates"
                      ? "carbohydrates"
                      : macroKey === "fat"
                      ? "fat"
                      : macroKey;

              const oldValue = parseFloat(component[componentKey] || 0);
              const newComponentValue = Math.round(oldValue * ratio);

              component[componentKey] = newComponentValue;

              return component;
          });

          food[macroKey] = Math.round(newValue);

          return food;
      },
      []
  );

  const updateProcessedOutput = useCallback((food: any) => {
    if (!food.processed_output) return food;

    const lines = food.processed_output.split("\n");
    const firstLine = lines[0];
    const updatedFirstLine = firstLine.replace(
        /\d+(\.\d+)?\s+plate/,
        `${food.quantity} plate`
    );
    lines[0] = updatedFirstLine;
    if (food.components) {
        food.components.forEach((component: any) => {
            const componentName = component.name;
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].includes(componentName)) {
                    lines[i] = lines[i].replace(
                        /\d+(\.\d+)?/,
                        component.quantity.toString()
                    );
                    break;
                }
            }
        });
    }

    food.processed_output = lines.join("\n");
    return food;
}, []);

    // Initialize food data from params
    useEffect(() => {
      const loadData = async () => {
        const dataLoadStart = performance.now();
        //console.log(`[PERF] loadData() started at ${dataLoadStart}`);
        if (params.foodData) {
            try {
              const foodData = JSON.parse(params.foodData as string);
              //const navStart = params.ts ? parseFloat(params.ts as string) : undefined;
              //if (navStart) {
                //console.log(`[PERF] Total time since tap: ${dataLoadStart - navStart}ms`);
              //}
              setFood(foodData);

                const foodCopy = JSON.parse(JSON.stringify(foodData));

                if (foodCopy.food_components) {
                    const componentsArray = [];
                    const componentsObj = foodCopy.food_components;
                    for (let i = 0; i < componentsObj.length; i++) {
                        if (componentsObj[i]) {
                            const component = { ...componentsObj[i] };
                            // Convert fats to fat
                            if (component.fats !== undefined) {
                                component.fat = component.fats;
                                delete component.fats;
                            }
                            componentsArray.push(component);
                        }
                    }

                    foodCopy.components = componentsArray;
                }

                if (foodCopy.food_images) {
                    Object.keys(foodCopy.food_images).forEach((key) => {
                        if (key !== "ismigrated" && key !== "meta") {
                            // Convert fats to fat in food_images
                            if (key === "fats") {
                                foodCopy.fat = foodCopy.food_images[key];
                            } else {
                                foodCopy[key] = foodCopy.food_images[key];
                            }
                        }
                    });
                }

                // Ensure all macro values are integers
                if (foodCopy.calories)
                    foodCopy.calories = Math.round(parseFloat(foodCopy.calories));
                if (foodCopy.protein)
                    foodCopy.protein = Math.round(parseFloat(foodCopy.protein));
                if (foodCopy.carbohydrates)
                    foodCopy.carbohydrates = Math.round(parseFloat(foodCopy.carbohydrates));
                if (foodCopy.fat) 
                    foodCopy.fat = Math.round(parseFloat(foodCopy.fat));
                if (foodCopy.sugar)
                    foodCopy.sugar = Math.round(parseFloat(foodCopy.sugar));

                // Ensure component macros are integers
                if (foodCopy.components) {
                    foodCopy.components.forEach((component: any) => {
                        if (component.calories)
                            component.calories = Math.round(parseFloat(component.calories));
                        if (component.protein)
                            component.protein = Math.round(parseFloat(component.protein));
                        if (component.carbohydrates)
                            component.carbohydrates = Math.round(parseFloat(component.carbohydrates));
                        if (component.fat)
                            component.fat = Math.round(parseFloat(component.fat));
                        if (component.sugar)
                            component.sugar = Math.round(parseFloat(component.sugar));
                    });
                }

                setEditedFood(foodCopy);
                setOriginalFood(JSON.parse(JSON.stringify(foodCopy))); // Store original for comparison
                setHasChanges(false); // Reset changes flag
            } catch (error) {
                logger.error(t('nutrition.error.parsingError'), JSON.stringify(error));
                Alert.alert(t('common.error'), t('nutrition.error.loadFoodData'));
                router.back();
            }
        }
        setTimeout(() => {
          setIsPageLoading(false);
          //console.log(`[PERF] loadData() finished at ${performance.now()}`);
        }, 100);
      };

      loadData();
    }, [params.foodData, router]);

   

    const foodName = useMemo(() => {
      if (editedFood?.processed_output) {
          const nameText = editedFood.processed_output
              .split("\n")[0]
              .split(/\d/)[0]
              .replace(/^[\s-]+/, "")
              .trim();
          return nameText || t('nutrition.defaultFoodName');
      }
      return editedFood?.name || t('nutrition.defaultFoodName');
  }, [editedFood]);

    // Format date
    const formattedDate = useMemo(() => {
      return editedFood?.timestamp
          ? new Date(editedFood.timestamp).toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
          })
          : t('nutrition.today');
  }, [editedFood]);
  
  const formattedTime = useMemo(() => {
      return editedFood?.timestamp
          ? new Date(editedFood.timestamp).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
          })
          : t('nutrition.unknownTime');
  }, [editedFood]);

  const fetchReportsData = useCallback(async () => {
    setIsLoadingReports(true);
    setReportsError(null);

    try {
      const reportsResponse = await getReportsData();
      if (reportsResponse?.success) {
        const reports = reportsResponse.files ||
          (reportsResponse.files && typeof reportsResponse.files === 'object'
            ? Object.values(reportsResponse.files)
            : []);

        setReportsData(reports);
      }
    } catch (error: any) {
      setReportsError(t('nutrition.error.fetchReportsFailed'));
      if (error?.response?.status !== 401) {
        logger.error('Error fetching reports data:', error);
      }

    } finally {
      setIsLoadingReports(false);
    }
  }, []);

  const handleSaveChanges = useCallback(async () => {
    if (!hasChanges) return;
    try {
        setIsSaving(true);
        const formattedData = formatFoodDataForApi(editedFood);

        const reportId = editedFood.report_id;
        if (!reportId) {
            throw new Error(t('nutrition.error.missingReportId'));
        }
        logger.info('updating data gere', formattedData)
        await updateFoodData(reportId, formattedData);
        
        router.replace('/nutrition?refreshData=true');
        try {
          await fetchReportsData();
        } catch (error) {
          if ((error as any)?.response?.status !== 401) {
            logger.error('Error refreshing data:', error);
          }
        } finally {
          setIsRefreshing(false);
        }
    } catch (error) {
        Alert.alert(
            t('common.error'),
            t('nutrition.error.updateFailed')
        );
        //console.error("Error saving food data:", error);
    } finally {
        setIsSaving(false);
    }
}, [
    hasChanges,
    editedFood,
    formatFoodDataForApi,
    updateFoodData,
    router
]);
    const openEditModal = (
        label: string,
        value: string | number,
        key: string,
        unit?: string,
        isComponent?: boolean,
        componentIndex?: number,
        componentKey?: string
    ) => {
        setCurrentEditItem({
            label,
            value,
            key,
            unit,
            isComponent,
            componentIndex,
            componentKey,
        });
        setEditModalVisible(true);
    };

    const handleSaveEditedValue = useCallback((newValue:string) => {
      if (!currentEditItem) return;
  
      const updatedFood = JSON.parse(JSON.stringify(editedFood));
      const numericMatch = newValue.match(/(\d+(\.\d+)?)/);
      const numericValue = numericMatch
          ? Math.round(parseFloat(numericMatch[0]))
          : 0;
  
      if (
          currentEditItem.isComponent &&
          currentEditItem.componentIndex !== undefined &&
          currentEditItem.componentKey
      ) {
          if (
              updatedFood.components &&
              updatedFood.components[currentEditItem.componentIndex]
          ) {
              const oldValue =
                  updatedFood.components[currentEditItem.componentIndex][
                      currentEditItem.componentKey
                  ];
              updatedFood.components[currentEditItem.componentIndex][
                  currentEditItem.componentKey
              ] = numericValue;
              if (currentEditItem.componentKey !== "quantity") {
                  recalculateMainMealMacros(updatedFood);
              } else {
                  // If updating quantity, update all macros proportionally
                  const ratio = numericValue / parseFloat(oldValue || "1");
                  updatedFood.components[currentEditItem.componentIndex] =
                      updateComponentMacros(
                          updatedFood.components[currentEditItem.componentIndex],
                          ratio
                      );
                  recalculateMainMealMacros(updatedFood);
              }
              updateProcessedOutput(updatedFood);
          }
      } else {
        if (
          ["calories", "protein", "carbohydrates", "fat", "sugar"].includes(
              currentEditItem.key
          )
      ) {
          distributeMacroChange(updatedFood, currentEditItem.key, numericValue);
      } else {
          updatedFood[currentEditItem.key] = numericValue;
      }
      
          updateProcessedOutput(updatedFood);
      }
      setEditedFood({ ...updatedFood });
      setHasChanges(true); // Mark that changes were made
  }, [
      currentEditItem,
      editedFood,
      recalculateMainMealMacros,
      updateComponentMacros,
      updateProcessedOutput,
      distributeMacroChange
  ]);

  const handleQuantityChange = useCallback((increment:boolean) => {
    const updatedFood = JSON.parse(JSON.stringify(editedFood));
    const currentQty = parseInt(updatedFood.quantity || "1");
    const newQty = increment
        ? Math.min(10, currentQty + 1)
        : Math.max(1, currentQty - 1);

    if (newQty === currentQty) return;
    const ratio = newQty / currentQty;
    updatedFood.quantity = newQty.toString();
    updatedFood.calories = Math.round(
        parseFloat(updatedFood.calories || 0) * ratio
    );
    updatedFood.protein = Math.round(
        parseFloat(updatedFood.protein || 0) * ratio
    );
    updatedFood.carbohydrates = Math.round(
        parseFloat(updatedFood.carbohydrates || 0) * ratio
    );
    updatedFood.fat = Math.round(parseFloat(updatedFood.fat || 0) * ratio);
    updatedFood.sugar = Math.round(parseFloat(updatedFood.sugar || 0) * ratio);

    if (updatedFood.components) {
        updatedFood.components = updatedFood.components.map((component:any) => {
            const updatedComponent = updateComponentMacros(component, ratio);
            updatedComponent.quantity = (
                parseFloat(component.quantity || "1") * ratio
            ).toFixed(1);
            return updatedComponent;
        });
    }

    updateProcessedOutput(updatedFood);
    setEditedFood({ ...updatedFood });
    setHasChanges(true);
}, [
    editedFood,
    updateComponentMacros,
    updateProcessedOutput
]);

const handleComponentQuantityChange = useCallback((index:number, increment:boolean) => {
  if (!editedFood.components) return;

  const updatedFood = JSON.parse(JSON.stringify(editedFood));
  const component = updatedFood.components[index];
  const currentQty = parseFloat(component.quantity || "1");

  const newQty = increment ? currentQty + 0.5 : Math.max(0, currentQty - 0.5);
  if (newQty === currentQty) return;

  if (newQty === 0) {
      updatedFood.components[index].quantity = "0";
      updatedFood.components[index].calories = 0;
      updatedFood.components[index].protein = 0;
      updatedFood.components[index].carbohydrates = 0;
      updatedFood.components[index].fat = 0;
      updatedFood.components[index].sugar = 0;
  } else {
      const ratio = currentQty === 0 ? 1 : newQty / currentQty;
      updatedFood.components[index] = updateComponentMacros(component, ratio);
      updatedFood.components[index].quantity = newQty.toString();
  }
  recalculateMainMealMacros(updatedFood);

  updateProcessedOutput(updatedFood);
  setEditedFood({ ...updatedFood });
  setHasChanges(true);
}, [
  editedFood,
  updateComponentMacros,
  recalculateMainMealMacros,
  updateProcessedOutput
]);
const isComponentInEditMode = useCallback((index:number) => {
  return componentsInEditMode.includes(index);
}, [componentsInEditMode]);

const handleShareClick = useCallback(async () => {
  try {
    if (!shareCardRef.current) {
      Alert.alert("Sharing not available", "Sharing feature is not available for this food.");
      return;
    }

    const uri = await captureRef(shareCardRef, {
      format: "png",
      quality: 1,
      result: "tmpfile", // saves to a temporary file
    });
 
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri, {
        dialogTitle: "Share Food Card",
        mimeType: "image/png",
        UTI: "public.png",
      });
      trackOperation("Food Card Share", OperationStatus.INITIATED, {
        content_type : "card"
      })
    } else {
      Alert.alert("Sharing not available on this device");
    }
  } catch (error) {
    Alert.alert("Error", "Could not share the card.");
    logger.error("Share error", error);
  }
}, [shareCardRef, editedFood, foodName, user]);

// Memoize toggleComponentEditMode
const toggleComponentEditMode = useCallback((index:number) => {
  setComponentsInEditMode((prev) => {
      if (prev.includes(index)) {
          return prev.filter((i) => i !== index);
      } else {
          return [...prev, index];
      }
  });
}, []);
    // Memoize selectivelyEncodeAzureUrl
const selectivelyEncodeAzureUrl = useCallback((url:string) => {
  const [baseUrl, queryString] = url.split("?");
  if (!queryString) return url;

  const params = queryString.split("&").map((part) => {
      const indexOfEqual = part.indexOf("=");
      if (indexOfEqual === -1) return part;
      const key = part.slice(0, indexOfEqual);
      const value = part.slice(indexOfEqual + 1);

      if (key === "sig") {
          return `${key}=${encodeURIComponent(value)}`;
      }
      if (key === "st" || key === "se") {
          return `${key}=${value.replace(/:/g, "%3A")}`;
      }
      if (key === "rsct" || key === "rscd") {
          return `${key}=${encodeURIComponent(value)}`;
      }
      return `${key}=${value}`;
  });
  return `${baseUrl}?${params.join("&")}`;
}, []);

if (!food || !editedFood || isPageLoading) {
  return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <ActivityIndicator size="small" color={colors.primary} />
      </View>
  );
}


return (
  <>
    <Stack.Screen
      options={{
        title: foodName,
        headerShown: false,
      }}
    />
    <StatusBar
      barStyle="light-content"
      translucent
      backgroundColor="transparent"
    />

    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={styles.imageContainer}>
        {food?.url && (
          <>
            <Image
              source={{ uri: selectivelyEncodeAzureUrl(food?.url || food?.thumbnail_url) }}
              style={styles.image}
              placeholder={require('@/assets/images/favicon.png')}
              cachePolicy="memory-disk"
              transition={300}
              contentFit="cover"
              onError={(e) => { logger.info(e.error) }}
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.imageGradient}
            />
          </>
        )}
      </View>

      <View style={styles.content}>
        <View style={styles.detailsContainer}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              paddingHorizontal: moderateScale(0),
            }}
          >
            <Text
              style={[
                styles.title,
                { flex: 1, marginRight: moderateScale(6) },
              ]}
            >
              {foodName}
            </Text>

            {/* Only show quantity controls when structured data is available */}
            {food &&
              food.food_images &&
              food.food_images.calories !== null &&
              food.food_images.calories !== undefined &&
              food.food_images.calories !== 0 ? (
              <View style={[styles.quantityContainer, { flexShrink: 0 }]}>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(false)}
                  >
                    <Minus size={16} color={colors.primary} />
                  </TouchableOpacity>

                  <Text style={styles.quantityValue}>
                    {editedFood.quantity}
                  </Text>

                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(true)}
                  >
                    <Plus size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              // When using fallback, just show the quantity as text without controls
              <Text style={styles.quantityValue}>{editedFood.quantity}</Text>
            )}
          </View>

          <View style={styles.metaContainer}>
            <View style={styles.metaItem}>
              <Calendar size={16} color={colors.gray[500]} />
              <Text style={styles.metaText}>{formattedDate}</Text>
            </View>

            <View style={styles.metaItem}>
              <Clock size={16} color={colors.gray[500]} />
              <Text style={styles.metaText}>{formattedTime}</Text>
            </View>
          </View>
          <View style={styles.nutritionContainer}>
            {/* Check if structured data is available in food_images */}
            {food &&
              food.food_images &&
              food.food_images.calories !== null &&
              food.food_images.calories !== undefined &&
              food.food_images.calories !== 0 ? (
              <>
                {/* Use structured data directly */}
                <React.Fragment key="calories">
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>🔥 Calories</Text>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Text
                        style={styles.nutritionValue}
                      >{`${editedFood.calories} kcal 🔥`}</Text>
                      <TouchableOpacity
                        style={[
                          styles.editButton,
                          {
                            position: "relative",
                            top: 0,
                            right: 0,
                            marginLeft: 8,
                          },
                        ]}
                        onPress={() =>
                          openEditModal(
                            t('nutrition.caloriesLabel'),
                            editedFood.calories || 0,
                            "calories",
                            "kcal"
                          )
                        }
                      >
                        <Edit2 size={14} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={styles.divider} />
                </React.Fragment>

                <React.Fragment key="protein">
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>🥩 Proteins</Text>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Text
                        style={styles.nutritionValue}
                      >{`${editedFood.protein}g 🥩`}</Text>
                      <TouchableOpacity
                        style={[
                          styles.editButton,
                          {
                            position: "relative",
                            top: 0,
                            right: 0,
                            marginLeft: 8,
                          },
                        ]}
                        onPress={() =>
                          openEditModal(
                            t('nutrition.proteinLabel'),
                            editedFood.protein || 0,
                            "protein",
                            "g"
                          )
                        }
                      >
                        <Edit2 size={14} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={styles.divider} />
                </React.Fragment>

                <React.Fragment key="carbs">
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>🍞 Carbs</Text>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Text
                        style={styles.nutritionValue}
                      >{`${editedFood.carbohydrates}g 🍞`}</Text>
                      <TouchableOpacity
                        style={[
                          styles.editButton,
                          {
                            position: "relative",
                            top: 0,
                            right: 0,
                            marginLeft: 8,
                          },
                        ]}
                        onPress={() =>
                          openEditModal(
                            t('nutrition.carbohydratesLabel'),
                            editedFood.carbohydrates || 0,
                            "carbohydrates",
                            "g"
                          )
                        }
                      >
                        <Edit2 size={14} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={styles.divider} />
                </React.Fragment>

                <React.Fragment key="sugar">
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>🍬 Sugars</Text>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Text style={styles.nutritionValue}>{`${editedFood.sugar || 0
                        }g 🍬`}</Text>
                      <TouchableOpacity
                        style={[
                          styles.editButton,
                          {
                            position: "relative",
                            top: 0,
                            right: 0,
                            marginLeft: 8,
                          },
                        ]}
                        onPress={() =>
                          openEditModal(
                            t('nutrition.sugarLabel'),
                            editedFood.sugar || 0,
                            "sugar",
                            "g"
                          )
                        }
                      >
                        <Edit2 size={14} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={styles.divider} />
                </React.Fragment>

                <React.Fragment key="fat">
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>🥑 Fat</Text>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Text
                        style={styles.nutritionValue}
                      >{`${editedFood.fat}g 🥑`}</Text>
                      <TouchableOpacity
                        style={[
                          styles.editButton,
                          {
                            position: "relative",
                            top: 0,
                            right: 0,
                            marginLeft: 8,
                          },
                        ]}
                        onPress={() =>
                          openEditModal(
                            t('nutrition.fatLabel'),
                            editedFood.fat || 0,
                            "fat",
                            "g"
                          )
                        }
                      >
                        <Edit2 size={14} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  </View>
                </React.Fragment>
              </>
            ) : editedFood.processed_output ? (
              // Fallback to parsed data from processed_output when structured data is not available
              (() => {
                const parsedData = parseNutritionalData(
                  editedFood.processed_output
                );
                if (parsedData && parsedData.length > 0) {
                  return (
                    <>
                      {parsedData.map((item: any, index: number) => (
                        <React.Fragment key={index}>
                          <View style={styles.nutritionItem}>
                            <Text style={styles.nutritionLabel}>
                              {item.label}
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                              }}
                            >
                              <Text style={styles.nutritionValue}>
                                {item.value}
                              </Text>
                              {/* No edit buttons for parsed data as per requirement */}
                            </View>
                          </View>
                          {index < parsedData.length - 1 && (
                            <View style={styles.divider} />
                          )}
                        </React.Fragment>
                      ))}
                    </>
                  );
                } else {
                  // If parsing fails, show the default view without edit buttons
                  return (
                    <View style={styles.macroGrid}>
                      <View style={styles.macroItem}>
                        <Text style={styles.macroLabel}>{t('nutrition.caloriesLabel')}</Text>
                        <Text style={styles.macroValue}>
                          {editedFood.calories || 0}{" "}
                          <Text style={styles.macroUnit}>kcal</Text>
                        </Text>
                      </View>
                      <View style={styles.macroItem}>
                        <Text style={styles.macroLabel}>{t('nutrition.proteinLabel')}</Text>
                        <Text style={styles.macroValue}>
                          {editedFood.protein || 0}{" "}
                          <Text style={styles.macroUnit}>g</Text>
                        </Text>
                      </View>
                      <View style={styles.macroItem}>
                        <Text style={styles.macroLabel}>{t('nutrition.carbohydratesLabel')}</Text>
                        <Text style={styles.macroValue}>
                          {editedFood.carbohydrates || 0}{" "}
                          <Text style={styles.macroUnit}>g</Text>
                        </Text>
                      </View>
                      <View style={styles.macroItem}>
                        <Text style={styles.macroLabel}>{t('nutrition.fatLabel')}</Text>
                        <Text style={styles.macroValue}>
                          {editedFood.fat || 0}{" "}
                          <Text style={styles.macroUnit}>g</Text>
                        </Text>
                      </View>
                      <View style={styles.macroItem}>
                        <Text style={styles.macroLabel}>{t('nutrition.sugarLabel')}</Text>
                        <Text style={styles.macroValue}>
                          {editedFood.sugar || 0}{" "}
                          <Text style={styles.macroUnit}>g</Text>
                        </Text>
                      </View>
                    </View>
                  );
                }
              })()
            ) : (
              // Default view with edit buttons when neither structured data nor processed_output is available
              <View style={styles.macroGrid}>
                <View style={styles.macroItem}>
                  <Text style={styles.macroLabel}>Calories</Text>
                  <View
                    style={{ flexDirection: "row", alignItems: "center" }}
                  >
                    <Text style={styles.macroValue}>
                      {editedFood.calories || 0}{" "}
                      <Text style={styles.macroUnit}>kcal</Text>
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.editButton,
                        {
                          position: "relative",
                          top: 0,
                          right: 0,
                          marginLeft: 8,
                        },
                      ]}
                      onPress={() =>
                        openEditModal(
                          "Calories",
                          editedFood.calories || 0,
                          "calories",
                          "kcal"
                        )
                      }
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.macroItem}>
                  <Text style={styles.macroLabel}>Protein</Text>
                  <View
                    style={{ flexDirection: "row", alignItems: "center" }}
                  >
                    <Text style={styles.macroValue}>
                      {editedFood.protein || 0}{" "}
                      <Text style={styles.macroUnit}>g</Text>
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.editButton,
                        {
                          position: "relative",
                          top: 0,
                          right: 0,
                          marginLeft: 8,
                        },
                      ]}
                      onPress={() =>
                        openEditModal(
                          "Protein",
                          editedFood.protein || 0,
                          "protein",
                          "g"
                        )
                      }
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.macroItem}>
                  <Text style={styles.macroLabel}>Carbs</Text>
                  <View
                    style={{ flexDirection: "row", alignItems: "center" }}
                  >
                    <Text style={styles.macroValue}>
                      {editedFood.carbohydrates || 0}{" "}
                      <Text style={styles.macroUnit}>g</Text>
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.editButton,
                        {
                          position: "relative",
                          top: 0,
                          right: 0,
                          marginLeft: 8,
                        },
                      ]}
                      onPress={() =>
                        openEditModal(
                          "Carbohydrates",
                          editedFood.carbohydrates || 0,
                          "carbohydrates",
                          "g"
                        )
                      }
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.macroItem}>
                  <Text style={styles.macroLabel}>Fat</Text>
                  <View
                    style={{ flexDirection: "row", alignItems: "center" }}
                  >
                    <Text style={styles.macroValue}>
                      {editedFood.fat || 0}{" "}
                      <Text style={styles.macroUnit}>g</Text>
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.editButton,
                        {
                          position: "relative",
                          top: 0,
                          right: 0,
                          marginLeft: 8,
                        },
                      ]}
                      onPress={() =>
                        openEditModal("Fat", editedFood.fat || 0, "fat", "g")
                      }
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.macroItem}>
                  <Text style={styles.macroLabel}>Sugar</Text>
                  <View
                    style={{ flexDirection: "row", alignItems: "center" }}
                  >
                    <Text style={styles.macroValue}>
                      {editedFood.sugar || 0}{" "}
                      <Text style={styles.macroUnit}>g</Text>
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.editButton,
                        {
                          position: "relative",
                          top: 0,
                          right: 0,
                          marginLeft: 8,
                        },
                      ]}
                      onPress={() =>
                        openEditModal(
                          "Sugar",
                          editedFood.sugar || 0,
                          "sugar",
                          "g"
                        )
                      }
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}
          </View>

          {/* Components Section */}
          {editedFood.components && editedFood.components.length > 0 && (
            <View style={styles.componentsContainer}>
              {editedFood.components.map((component: any, index: number) => (
                <View
                  key={component.id || index}
                  style={[
                    styles.componentItem,
                    isComponentInEditMode(index) && styles.componentEditMode,
                  ]}
                >
                  <View style={styles.componentHeader}>
                    {/* Quantity controls moved to left side */}
                    <View
                      style={[
                        styles.quantityControls,
                        { height: 24, marginRight: 8 },
                      ]}
                    >
                      {/* minus button */}
                      <TouchableOpacity
                        style={[
                          styles.quantityButton,
                          { width: 20, height: 24, padding: 2 },
                          parseFloat(component.quantity) === 0
                            ? { opacity: 0.5 }
                            : {},
                        ]}
                        onPress={() =>
                          handleComponentQuantityChange(index, false)
                        }
                        disabled={parseFloat(component.quantity) === 0}
                      >
                        <Minus size={12} color={colors.primary} />
                      </TouchableOpacity>

                      {/* quantity */}
                      <Text
                        style={[
                          styles.quantityValue,
                          {
                            fontSize: moderateScale(theme.fontSize.xs),
                            paddingHorizontal: 4,
                            minWidth: 12,
                          },
                        ]}
                      >
                        {component.quantity}
                      </Text>

                      {/* plus */}
                      <TouchableOpacity
                        style={[
                          styles.quantityButton,
                          { width: 20, height: 24, padding: 2 },
                          // parseFloat(component.quantity) === 0
                          //   ? { opacity: 0.5 }
                          //   : {},
                        ]}
                        onPress={() =>
                          handleComponentQuantityChange(index, true)
                        }
                      >
                        <Plus size={12} color={colors.primary} />
                      </TouchableOpacity>
                    </View>

                    {component.emoji && (
                      <Text style={styles.componentEmoji}>
                        {component.emoji}
                      </Text>
                    )}
                    <Text style={styles.componentName}>{component.name}</Text>
                    <Text style={styles.componentQuantity}>
                      {component.units}
                    </Text>

                    {/* Single edit button on the right */}
                    <TouchableOpacity
                      style={styles.componentEditButton}
                      onPress={() => toggleComponentEditMode(index)}
                    >
                      <Edit2 size={14} color={colors.primary} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.macroGrid}>
                    {isComponentInEditMode(index) ? (
                      // Edit mode - show text fields
                      <>
                        <TouchableOpacity
                          style={[styles.macroItem, { width: "48%" }]}
                          onPress={() =>
                            openEditModal(
                              "Calories",
                              component.calories || 0,
                              "calories",
                              "kcal",
                              true,
                              index,
                              "calories"
                            )
                          }
                        >
                          <Text style={styles.macroLabel}>{t('nutrition.caloriesLabel')}</Text>
                          <TextInput
                            style={styles.macroInputField}
                            value={component.calories?.toString()}
                            editable={false}
                          />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.macroItem, { width: "48%" }]}
                          onPress={() =>
                            openEditModal(
                              "Protein",
                              component.protein || 0,
                              "protein",
                              "g",
                              true,
                              index,
                              "protein"
                            )
                          }
                        >
                          <Text style={styles.macroLabel}>{t('nutrition.proteinLabel')}</Text>
                          <TextInput
                            style={styles.macroInputField}
                            value={`${component.protein || 0}g`}
                            editable={false}
                          />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.macroItem, { width: "32%" }]}
                          onPress={() =>
                            openEditModal(
                              "Carbohydrates",
                              component.carbohydrates || 0,
                              "carbohydrates",
                              "g",
                              true,
                              index,
                              "carbohydrates"
                            )
                          }
                        >
                          <Text style={styles.macroLabel}>{t('nutrition.carbohydratesLabel')}</Text>
                          <TextInput
                            style={styles.macroInputField}
                            value={`${component.carbohydrates || 0}g`}
                            editable={false}
                          />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.macroItem, { width: "32%" }]}
                          onPress={() =>
                            openEditModal(
                              "Fat",
                              component.fat || 0,
                              "fat",
                              "g",
                              true,
                              index,
                              "fat"
                            )
                          }
                        >
                          <Text style={styles.macroLabel}>{t('nutrition.fatLabel')}</Text>
                          <TextInput
                            style={styles.macroInputField}
                            value={`${component.fat || 0}g`}
                            editable={false}
                          />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.macroItem, { width: "32%" }]}
                          onPress={() =>
                            openEditModal(
                              "Sugar",
                              component.sugar || 0,
                              "sugar",
                              "g",
                              true,
                              index,
                              "sugar"
                            )
                          }
                        >
                          <Text style={styles.macroLabel}>{t('nutrition.sugarLabel')}</Text>
                          <TextInput
                            style={styles.macroInputField}
                            value={`${component.sugar || 0}g`}
                            editable={false}
                          />
                        </TouchableOpacity>
                      </>
                    ) : (
                      // View mode - show regular text
                      <>
                        <View style={[styles.macroItem, { width: "48%" }]}>
                          <Text style={styles.macroLabel}>{t('nutrition.caloriesLabel')}</Text>
                          <Text style={styles.macroValue}>
                            {component.calories} kcal
                          </Text>
                        </View>

                        <View style={[styles.macroItem, { width: "48%" }]}>
                          <Text style={styles.macroLabel}>{t('nutrition.proteinLabel')}</Text>
                          <Text style={styles.macroValue}>
                            {component.protein}g
                          </Text>
                        </View>

                        <View style={[styles.macroItem, { width: "32%" }]}>
                          <Text style={styles.macroLabel}>{t('nutrition.carbohydratesLabel')}</Text>
                          <Text style={styles.macroValue}>
                            {component.carbohydrates}g
                          </Text>
                        </View>

                        <View style={[styles.macroItem, { width: "32%" }]}>
                          <Text style={styles.macroLabel}>{t('nutrition.fatLabel')}</Text>
                          <Text style={styles.macroValue}>
                            {component.fat}g
                          </Text>
                        </View>

                        <View style={[styles.macroItem, { width: "32%" }]}>
                          <Text style={styles.macroLabel}>{t('nutrition.sugarLabel')}</Text>
                          <Text style={styles.macroValue}>
                            {component.sugar || 0}g
                          </Text>
                        </View>
                      </>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
          {food.food_images.notes && (
            <View style={styles.notesSection}>
              <Text style={styles.sectionTitle}>{t('nutrition.tips')}</Text>
              <WhatsAppStyleMarkdownBubble content={food.food_images.notes} />
            </View>
          )}
          <View style={styles.macroVisualization}>
            <Text style={styles.sectionTitle}>{t('nutrition.macroBreakdown')}</Text>

            {(() => {
              // Check if we should use structured data or fallback to parsed data
              logger.info("FOOD ID", food);
              if (
                food &&
                food.food_images &&
                food.food_images.calories !== null &&
                food.food_images.calories !== undefined &&
                food.food_images.calories !== 0
              ) {
                // Use structured data
                const protein = parseFloat(editedFood.protein) || 0;
                const carbohydrates =
                  parseFloat(editedFood.carbohydrates) || 0;
                const fat = parseFloat(editedFood.fat) || 0;

                const total = protein + carbohydrates + fat;

                // Calculate percentages, default to equal distribution if total is 0
                const proteinPercent =
                  total > 0 ? (protein / total) * 100 : 33.33;
                const carbsPercent =
                  total > 0 ? (carbohydrates / total) * 100 : 33.33;
                const fatPercent = total > 0 ? (fat / total) * 100 : 33.33;

                return total > 0 ? (
                  <>
                    <View style={styles.macroBarContainer}>
                      <View
                        style={[
                          styles.macroBar,
                          styles.proteinBar,
                          { flex: proteinPercent },
                        ]}
                      />
                      <View
                        style={[
                          styles.macroBar,
                          styles.carbsBar,
                          { flex: carbsPercent },
                        ]}
                      />
                      <View
                        style={[
                          styles.macroBar,
                          styles.fatBar,
                          { flex: fatPercent },
                        ]}
                      />
                    </View>

                    <View style={styles.macroLegend}>
                      <View style={styles.legendItem}>
                        <View
                          style={[styles.legendColor, styles.proteinBar]}
                        />
                        <Text style={styles.legendText}>
                        {t('foodModal.macroLabels.protein')} ({Math.round(proteinPercent)}%)
                        </Text>
                      </View>

                      <View style={styles.legendItem}>
                        <View style={[styles.legendColor, styles.carbsBar]} />
                        <Text style={styles.legendText}>
                        {t('foodModal.macroLabels.carbs')} ({Math.round(carbsPercent)}%)
                        </Text>
                      </View>

                      <View style={styles.legendItem}>
                        <View style={[styles.legendColor, styles.fatBar]} />
                        <Text style={styles.legendText}>
                        {t('foodModal.macroLabels.fat')} ({Math.round(fatPercent)}%)
                        </Text>
                      </View>
                    </View>

                    <View 
                        style={{ position: "absolute", left: -9999, top: 0 }}
                        >
                          <View collapsable={false} ref={shareCardRef}>
                            <FoodShareCard
                              foodName={foodName}
                              foodImage={selectivelyEncodeAzureUrl(food?.url || food?.thumbnail_url)}
                              macros={{
                                calories: editedFood?.food_images?.calories || 0,
                                protein: editedFood.protein,
                                carbohydrates: editedFood.carbohydrates,
                                fat: editedFood.fat
                              }}
                              date={formattedDate}
                              time={formattedTime}
                            />
                          </View>
                      </View>
                  </>
                ) : (
                  <Text style={styles.noDataText}>
                    {t('nutrition.noMacroData')}
                  </Text>
                );
              } else if (editedFood.processed_output) {
                const parsedData = parseNutritionalData(
                  editedFood.processed_output
                );
                if (parsedData && parsedData.length > 0) {
                  // Extract protein, carbs, and fat from parsed data
                  let calories = 0;
                  let protein = 0;
                  let carbs = 0;
                  let fat = 0;

                  parsedData.forEach((item: any) => {
                    const label = item.label.toLowerCase();
                    const valueMatch = item.value.match(/(\d+(\.\d+)?)/);
                    const value = valueMatch ? parseFloat(valueMatch[0]) : 0;

                    if (label.includes("protein")) {
                      protein = value;
                    } else if (label.includes("carb")) {
                      carbs = value;
                    } else if (label.includes("fat")) {
                      fat = value;
                    } else if (label.includes("calories")) {
                      calories = value;
                    }
                  });

                  const total = protein + carbs + fat;

                  // Calculate percentages, default to equal distribution if total is 0
                  const proteinPercent =
                    total > 0 ? (protein / total) * 100 : 33.33;
                  const carbsPercent =
                    total > 0 ? (carbs / total) * 100 : 33.33;
                  const fatPercent = total > 0 ? (fat / total) * 100 : 33.33;
                  
                  return total > 0 ? (
                    <>
                      <View style={styles.macroBarContainer}>
                        <View
                          style={[
                            styles.macroBar,
                            styles.proteinBar,
                            { flex: proteinPercent },
                          ]}
                        />
                        <View
                          style={[
                            styles.macroBar,
                            styles.carbsBar,
                            { flex: carbsPercent },
                          ]}
                        />
                        <View
                          style={[
                            styles.macroBar,
                            styles.fatBar,
                            { flex: fatPercent },
                          ]}
                        />
                      </View>

                      <View style={styles.macroLegend}>
                        <View style={styles.legendItem}>
                          <View
                            style={[styles.legendColor, styles.proteinBar]}
                          />
                          <Text style={styles.legendText}>
                          {t('foodModal.macroLabels.protein')} ({Math.round(proteinPercent)}%)
                          </Text>
                        </View>

                        <View style={styles.legendItem}>
                          <View
                            style={[styles.legendColor, styles.carbsBar]}
                          />
                          <Text style={styles.legendText}>
                          {t('foodModal.macroLabels.carbs')} ({Math.round(carbsPercent)}%)
                          </Text>
                        </View>

                        <View style={styles.legendItem}>
                          <View style={[styles.legendColor, styles.fatBar]} />
                          <Text style={styles.legendText}>
                          {t('foodModal.macroLabels.fat')} ({Math.round(fatPercent)}%)
                          </Text>
                        </View>
                      </View>

                      <View 
                        style={{ position: "absolute", left: -9999, top: 0 }}
                        >
                          <View collapsable={false} ref={shareCardRef}>
                            <FoodShareCard
                              foodName={foodName}
                              foodImage={selectivelyEncodeAzureUrl(food?.url || food?.thumbnail_url)}
                              macros={{
                                calories: calories,
                                protein: protein,
                                carbohydrates: carbs,
                                fat: fat
                              }}
                              date={formattedDate}
                              time={formattedTime}
                            />
                          </View>
                      </View>
                    </>
                  ) : (
                    <Text style={styles.noDataText}>
                      {t('nutrition.noMacroData')}
                    </Text>
                  );
                } else {
                  return (
                    <Text style={styles.noDataText}>
                      {t('nutrition.noMacroData')}
                    </Text>
                  );
                }
              } else {
                // No data available
                return (
                  <Text style={styles.noDataText}>
                    {t('nutrition.noMacroData')}
                  </Text> 
                );
              }
            })()}
          </View>
          {Platform.OS === 'ios' && (
            <Text style={styles.disclaimer}>
              {t('nutrition.disclaimer')}{" "}
              <Text
                style={styles.link}
                onPress={() => Linking.openURL("https://www.meetaugust.ai/en/library/behind-august")}
              >
                {t('nutrition.disclaimerLink')}
              </Text>
              .
            </Text>
          )}
        </View>
      </View>
    </ScrollView>

    {/* Header Overlay */}
    <View style={styles.headerOverlay}>
      <View style={styles.headerContent}>
        {/* Back Button */}
        <TouchableOpacity
          style={styles.headerButtonBack}
          onPress={() => router.back()}
        >
          <ChevronDown size={26} color={colors.white} style={{ marginRight: 4, transform: [{rotate: '90deg'}]}} />
          
        </TouchableOpacity>

        {/* If the data has proper macros (i.e no need to use processed_output) */}
        {food &&
          food.food_images &&
          food.food_images.calories !== null &&
          food.food_images.calories !== undefined &&
          food.food_images.calories !== 0 && (
            (hasChanges) &&
            <TouchableOpacity
              style={[
                styles.headerButton,
                hasChanges
                  ? { backgroundColor: colors.primary }
                  : { backgroundColor: "rgba(0, 0, 0, 0.5)" },
              ]}
              onPress={handleSaveChanges}
              disabled={isSaving || !hasChanges}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={styles.headerButtonText}>{t('common.save')}</Text>
                  <Save size={16} color={colors.white} style={{ marginLeft: 6 }} />
                </View>

              )}
            </TouchableOpacity>
          )}
          {(
            (food &&
                food.food_images &&
                food.food_images.calories !== null &&
                food.food_images.calories !== undefined &&
                food.food_images.calories !== 0) 
                || 
              (parseNutritionalData(editedFood?.processed_output) && parseNutritionalData(editedFood?.processed_output)?.length > 0)
            )
          && 
          <TouchableOpacity
          style={[styles.headerButtonBack]}
          onPress={handleShareClick}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Share2 size={16} color={colors.white} />
            </View>

          </TouchableOpacity>}
      </View>
    </View>

    {/* Edit Value Modal */}
    <EditValueModal
      visible={editModalVisible}
      onClose={() => setEditModalVisible(false)}
      onSave={handleSaveEditedValue}
      initialValue={currentEditItem?.value?.toString() || ""}
      label={currentEditItem?.label || ""}
      unit={currentEditItem?.unit}
    />
   
  </>
);
}