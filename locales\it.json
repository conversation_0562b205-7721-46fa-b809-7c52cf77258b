{"common": {"error": "Errore", "yes": "Sì", "no": "No", "sometimes": "A volte", "close": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "next": "<PERSON><PERSON>", "loading": "Caricamento...", "version": "v0.0.1.7"}, "welcome": "Accedi per iniziare a parlare con August", "notFound": {"title": "Oops!", "message": "Questa schermata non esiste.", "goHome": "Torna alla schermata principale!"}, "library": {"title": "Libreria Salute"}, "specialists": {"title": "<PERSON><PERSON>", "description": "Consulta professionisti sanitari specializzati per problemi di salute più specifici. Scegli uno specialista qui sotto:", "generalPhysician": {"title": "Medico di base", "description": "Per problemi di salute generali e assistenza primaria."}, "nutritionist": {"title": "Nutrizionista", "description": "Per consigli su dieta, nutrizione e gestione del peso."}, "cardiologist": {"title": "<PERSON><PERSON><PERSON>", "description": "Per problemi cardiaci e salute cardiovascolare."}, "neurologist": {"title": "Neurologo", "description": "Per problemi al cervello, al midollo spinale e al sistema nervoso."}, "oncologist": {"title": "Oncologo", "description": "Per problemi e trattamenti relativi al cancro."}, "endocrinologist": {"title": "Endocrinologo", "description": "Per disturbi ormonali e gestione del diabete."}, "dermatologist": {"title": "Dermatologo", "description": "Per problemi di pelle, capelli e unghie."}, "psychiatrist": {"title": "Psichiatra", "description": "Per problemi di salute mentale e benessere psicologico."}}, "profile": {"title": "<PERSON>ilo", "defaultName": "Ospite", "namePlaceholder": "Inser<PERSON>ci il tuo nome", "saving": "Salvataggio...", "noPhoneNumber": "Nessun numero di telefono", "loggingOut": "Disconnessione...", "about": {"title": "Informazioni", "description": "Scopri di più su August"}, "whatsapp": {"title": "WhatsApp", "description": "<PERSON>tta con August su WhatsApp"}, "refer": {"title": "Condi<PERSON><PERSON>", "description": "Ti è piaciuto August? Condividilo con i tuoi amici"}, "deleteAccount": {"title": "Elimina account", "description": "Ci dispiace vederti andare"}, "logout": {"title": "<PERSON><PERSON><PERSON>", "description": "Torna presto. Ci mancherai"}, "shareMessage": "👋Ciao, dai un'occhiata a questa fantastica app che sto usando!\n\n\n\n➡️Sto usando August per ottenere informazioni e indicazioni sulla salute rapide e affidabili. È come avere un dottore in tasca! Dai un'occhiata qui:", "error": {"loadFailed": "Impossibile caricare i dati utente", "fetchError": "Si è verificato un errore durante il recupero dei dati utente", "updateNameFailed": "Impossibile aggiornare il nome", "updateNameError": "Si è verificato un errore durante l'aggiornamento del nome", "loadFoodData": "Impossibile caricare i dati alimentari", "logoutError": "Errore durante la disconnessione:", "shareError": "Errore durante la condivisione dei messaggi:"}}, "error": {"title": "Qualcosa è andato storto", "checkLogs": "Controlla i log del tuo dispositivo per maggiori dettagli.", "unknown": "<PERSON><PERSON><PERSON> scon<PERSON>", "unknownFile": "<PERSON> sconosciuto", "unknownLine": "Riga sconosciuta", "unknownColumn": "<PERSON><PERSON><PERSON> s<PERSON>"}, "auth": {"phone": {"selectCountry": "Seleziona <PERSON>", "searchCountries": "Cerca paesi", "validation": {"invalidPhone": "Inserisci un numero di telefono valido", "invalidDigits": "Inserisci un numero di telefono valido (7-15 cifre)"}}, "header": {"title": "Chiarezza immediata e privata sulle tue preoccupazioni di salute", "subtitle": "Guida attenta. Nessuna fretta. Nessuna confusione.", "emphasis": "Solo chiarezza."}, "greeting": "Ciao 👋", "phoneNumber": "Numero di Telefono", "requestOTP": "<PERSON><PERSON>", "otp": {"title": "Password Una <PERSON>", "verify": "Verifica OTP", "sending": "Invio...", "countdown": "Invia di nuovo OTP tra {{countdown}}s", "resend": "Invia di nuovo OTP", "sentTo": "OTP inviato a ", "whatsappSuffix": " su Whatsapp"}, "disclaimer": {"prefix": "Regis<PERSON><PERSON><PERSON>, accetti le nostre ", "continuePrefix": "<PERSON><PERSON>uan<PERSON>, accetti le nostre ", "termsOfService": "Condizioni di servizio", "and": " e ", "privacyPolicy": "Informativa sulla privacy", "whatsappConsent": ", e acconsenti a ricevere aggiornamenti e promemoria da noi tramite WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Benvenuto su August!", "buttonText": "Iniziamo"}}, "postAuth": {"step1": {"title": "Ciao!", "subtitle": "Sono August 👋", "description": "Considerami l'angolo confortevole sul tuo\ndispositivo dove esplori tutte le tue\ncuriosità sulla salute.", "subdescription": "Sentiti libero di chiedere qualsiasi cosa ti venga in mente.\n<PERSON><PERSON><PERSON> giu<PERSON>, nessun limite!", "placeholder": "Come ti devo chiamare?"}, "step2": {"title": "Ciao {{userName}},", "subtitle": "Ecco cosa posso fare:", "features": {"health": {"title": "Rispondi alle tue", "subtitle": "<PERSON><PERSON><PERSON> sulla salute"}, "nutrition": {"title": "Tieni traccia dei tuoi", "subtitle": "Macro"}, "reports": {"title": "<PERSON><PERSON><PERSON> i", "subtitle": "Report"}}}}, "pills": {"thoughtful": "Attento", "careful": "Accorto", "accurate": "Preciso"}, "features": {"symptoms": {"title": "Controlla i tuoi sintomi", "description": "Ho la nausea da una settimana. Cosa mi sta succedendo?"}, "prescriptions": {"title": "<PERSON><PERSON><PERSON> le tue prescrizioni", "description": "Carica e comprendi le prescrizioni come un medico."}, "medicine": {"title": "Con<PERSON><PERSON> i tuoi farmaci", "description": "Il Metformina per la mia PCOS interagisce con le mie pillole per l'ADHD?"}, "plans": {"title": "<PERSON><PERSON><PERSON> piani <PERSON>", "description": "Puoi darmi un piano di nutrizione e fitness per ridurre i miei livelli di HbA1c?"}}, "buttons": {"getStarted": "Inizia", "next": "<PERSON><PERSON>"}, "errors": {"nameRequired": "Inser<PERSON>ci il tuo nome"}}, "tabs": {"chat": "Cha<PERSON>", "discover": "<PERSON><PERSON><PERSON>", "nutrition": "Nutrizione", "personalize": "Personalizza"}, "chat": {"nav": {"title": "Agosto"}, "me": "Io", "augustName": "Agosto", "input": {"placeholder": "Chiedi ad Agosto...", "disclaimer": "Agosto potrebbe commettere errori. Conferma con un medico"}, "list": {"loadingMessages": "Caricamento messaggi...", "noMessages": "<PERSON><PERSON>un messaggio ancora. Inizia una conversazione!"}, "connection": {"offlineMessage": "Sembra che tu sia offline. Ricollega per inviare messaggi.", "connecting": "Connessione...", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>"}, "prompts": {"uploadReport": "<PERSON><PERSON>to", "speakInHindi": "<PERSON><PERSON><PERSON> in Hindi", "notFeelingWell": "Non mi sento bene", "whatIsMyBMI": "Qual è il mio IMC?", "nutritionAdvice": "Consigli nutrizionali", "sleepBetter": "<PERSON><PERSON><PERSON> meglio"}, "citations": {"referenceText": "Per maggiori dettagli su questa conversazione, fare riferimento a:"}, "actions": {"copiedToClipboard": "Copiato negli appunti", "copied": "Copiato"}, "share": {"introText": "👋Ciao, guarda la conversazione che ho avuto con Agosto:\n\n", "downloadText": "\n\n➡️Scarica Agosto per chattare con il tuo amico AI per la salute:\n"}}, "discover": {"nav": {"title": "<PERSON><PERSON><PERSON>"}, "categories": {"all": "<PERSON><PERSON>", "heartHealth": "Salute del cuore", "nutrition": "Nutrizione", "mentalHealth": "Salute mentale", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON>"}, "cards": {"empty": "Nessuna scheda disponibile per questa categoria"}, "sections": {"features": "Funzionalità"}, "features": {"healthLibrary": {"title": "Libreria sanitaria", "description": "Accesso a informazioni mediche affidabili, attendibili e aggiornate, completamente gratuito."}, "nutritionTracker": {"title": "<PERSON><PERSON>", "description": "Ti sei mai chiesto se potessi semplicemente caricare una foto del tuo cibo e monitorare tutti i tuoi obiettivi nutrizionali? Agosto può farlo!"}, "multilingualSupport": {"title": "Supporto multilingue", "description": "Puoi comunicare con Agosto in qualsiasi lingua ti sia più comoda! Agosto è sempre qui per ascoltarti, supportarti e risponderti ogni volta che hai bisogno.", "samplePrompt": "<PERSON><PERSON><PERSON> in Hindi"}, "labReportAnalysis": {"title": "Analisi referti di laboratorio", "description": "Quando parli con Agosto dei tuoi referti di laboratorio, ottieni estrema precisione. Agosto ha elaborato oltre 4,7 milioni di referti con un'accuratezza di estrazione dei biomarcatori del 98,4%."}}}, "nutrition": {"nav": {"title": "Nutrizione"}, "meals": {"title": "<PERSON>", "subtitle": "Tocca per vedere i macronutrienti di ogni pasto"}, "upload": {"loading": "Caricamento immagine..."}, "defaultFoodName": "Alimento", "today": "<PERSON><PERSON><PERSON>", "unknownTime": "<PERSON><PERSON> sconos<PERSON>uta", "calories": "🔥 Calorie", "proteins": "🥩 Proteine", "carbs": "🍞 Car<PERSON><PERSON><PERSON>i", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 Grassi", "caloriesLabel": "Calorie", "proteinLabel": "Proteine", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "<PERSON><PERSON>", "sugarLabel": "<PERSON><PERSON><PERSON><PERSON>", "tips": "Consigli:", "macroBreakdown": "Ripartizione Macronutrienti", "noMacroData": "<PERSON><PERSON>un dato macronutriente disponibile per questo alimento.", "disclaimer": "Solo per uso didattico. Scopri di più", "disclaimerLink": "qui", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "Qual è il tuo sesso?", "subtitle": "Questo verrà utilizzato per calibrare il tuo piano personalizzato.", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "Altro"}, "age": {"title": "Quanti anni hai?", "subtitle": "Questo verrà utilizzato per calcolare le tue esigenze giornaliere."}, "measurements": {"title": "Altezza e Peso", "subtitle": "Inserisci la tua altezza in centimetri e il peso in chilogrammi."}, "activity": {"title": "Livello di Attività", "subtitle": "Con che frequenza ti alleni?", "none": "<PERSON><PERSON><PERSON>", "moderate": "Moderato", "high": "Intenso"}, "goal": {"title": "Obiettivo di Peso", "subtitle": "Cosa vorresti o<PERSON>ere?", "increase": "Aumentare", "maintain": "Mantenere", "decrease": "<PERSON><PERSON><PERSON><PERSON>"}, "targetWeight": {"title": "Peso Obiettivo", "subtitle": "Qual è il tuo peso obiettivo in chilogrammi?"}, "setup": {"title": "Impostazione del tuo Piano", "subtitle": "Per favore aspetta mentre prepariamo il tuo piano nutrizionale."}, "review": {"title": "Revisiona il tuo Piano", "subtitle": "Rivedi e personalizza il tuo piano nutrizionale."}, "height": {"label": "Altezza (cm)"}, "weight": {"label": "Peso (kg)"}}, "error": {"updateFailed": "Impossibile aggiornare i dati nutrizionali. Riprova.", "parsingError": "Errore durante l'analisi dei dati alimentari:", "fetchReportsFailed": "Impossibile recuperare i dati dei report. Riprova.", "missingReportId": "ID report mancante"}}, "personalize": {"nav": {"title": "Personalizza"}, "button": {"saving": "Salvataggio", "review": "Revisiona", "saveNext": "Salva e Avanti"}}, "basicInfo": {"title": "Impariamo a conoscerti meglio", "subtitle": "Queste informazioni ci aiutano a personalizzare i tuoi consigli sulla salute", "age": {"question": "Quanti anni hai?", "placeholder": "Inserisci la tua età"}, "sex": {"question": "Qual è il tuo sesso?", "placeholder": "Seleziona il tuo sesso", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "Altro"}, "height": {"question": "Qual è la tua altezza? (cm)", "placeholder": "Inserisci la tua altezza"}, "weight": {"question": "Qual è il tuo peso? (kg)", "placeholder": "Inser<PERSON>ci il tuo peso"}}, "lifestyle": {"title": "Le Tue Abitudini di Vita", "subtitle": "Capire le tue abitudini quotidiane ci aiuta a fornirti consigli migliori", "diet": {"question": "Che tipo di dieta segui?", "placeholder": "Seleziona la tua dieta", "vegetarian": "Vegetariana", "nonVegetarian": "Non Vegetariana", "vegan": "Vegan", "pescatarian": "Pescetariana", "keto": "Chetogenica", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "Fai esercizio regolarmente?"}, "drinking": {"question": "Consumi alcol?"}, "smoking": {"question": "<PERSON><PERSON>?"}, "sleep": {"question": "Quante ore dormi a notte?", "value": "{{sleep}} ore"}, "hydration": {"question": "Quante tazze d'acqua bevi al giorno?", "value": "{{hydration}} tazze ({{liters}}L)"}}, "allergies": {"title": "Hai allergie?", "subtitle": "Conoscere le tue allergie ci aiuta a fornirti consigli più sicuri", "allergyIndex": "Allergia {{index}}", "name": {"question": "A cosa sei allergico?", "placeholder": "Inserisci allergia (es. <PERSON>, Polvere)"}, "severity": {"question": "Quanto è grave questa allergia?", "placeholder": "Seleziona gravità", "mild": "<PERSON><PERSON>", "moderate": "Moderata", "severe": "Grave"}, "addButton": "Aggiungi un'altra allergia", "noAllergiesButton": "Non ho allergie"}, "medications": {"title": "Farmaci e Integratori", "subtitle": "Dicci quali farmaci o integratori stai assumendo", "medicationIndex": "Farmaco {{index}}", "name": {"label": "Nome del farmaco", "placeholder": "Inserisci il nome del farmaco"}, "startDate": {"question": "Quando hai iniziato ad assumerlo?", "placeholder": "Seleziona data"}, "type": {"label": "Tipo di farmaco", "shortTerm": "Breve termine", "longTerm": "Lungo termine"}, "dose": {"label": "Dosaggio", "placeholder": "Quantità"}, "unit": {"label": "Unità"}, "frequency": {"label": "Frequenza", "placeholder": "Volte", "perDay": "al giorno", "perWeek": "a settimana", "perMonth": "al mese", "perYear": "all'anno"}, "units": {"mg": "mg", "ml": "ml", "iu": "UI", "puffs": "puff", "drops": "gocce", "tsp": "cu<PERSON><PERSON>i", "tbsp": "cucchiai", "cups": "tazze"}, "addButton": "Aggiungi un altro farmaco", "noMedicationsButton": "Non assumo farmaci", "calendar": {"title": "Seleziona data di inizio"}}, "conditions": {"title": "Condizioni Mediche", "subtitle": "Dicci quali condizioni mediche hai o hai avuto in passato", "conditionIndex": "Condizione {{index}}", "name": {"label": "Nome della condizione", "placeholder": "Inserisci condizione (es. Asma, ecc.)"}, "since": {"question": "Da quando hai questa condizione?", "placeholder": "Seleziona data"}, "current": {"question": "Ti sta attualmente causando problemi?"}, "medicated": {"question": "Stai assumendo farmaci per questo?"}, "addButton": "Aggiungi un'altra condizione", "noConditionsButton": "Non ho condizioni mediche", "calendar": {"title": "Seleziona data"}}, "reproductive": {"title": "Salute Riproduttiva", "subtitle": "Queste informazioni ci aiutano a fornire raccomandazioni di salute più personalizzate", "menstruation": {"question": "Hai mai avuto il ciclo mestruale?", "detailsTitle": "Dettagli del Ciclo Mestruale", "regularity": {"question": "Quanto è regolare il tuo ciclo?", "regular": "Regolare", "irregular": "Irregolare", "notSure": "Non Sicuro"}, "cycleLength": {"label": "Lunghezza media del ciclo (giorni)", "placeholder": "Inserisci la lunghezza del ciclo"}, "flowDays": {"label": "<PERSON><PERSON><PERSON> di flusso: {{flowDays}}", "min": "1 giorno", "max": "15 giorni"}, "padsPerDay": {"label": "Assorbenti/tamponi al giorno: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "Hai sintomi durante il periodo?", "placeholder": "Inserisci i sintomi (es. crampi, mal di testa)"}}, "childbirth": {"question": "Hai mai avuto un parto?", "detailsTitle": "Dettagli del Parto", "children": {"label": "Numero di Figli"}, "pregnancies": {"label": "Numero di Gravidanze"}, "complications": {"question": "Complicazioni durante la gravidanza o il parto?", "placeholder": "Inserisci le complicazioni (se presenti)"}}}, "review": {"title": "<PERSON><PERSON><PERSON> le tue informazioni", "subtitle": "Per favore rivedi le informazioni fornite prima di inviare", "sections": {"basicInfo": "Informazioni di Base", "lifestyle": "Stile di Vita", "allergies": "Allergie", "medications": "Farmaci e Integratori", "conditions": "Condizioni Mediche", "reproductive": "Salute Riproduttiva", "menstruationDetails": "Dettagli del Ciclo Mestruale", "childbirthDetails": "Dettagli del Parto"}, "fields": {"age": "Età:", "sex": "Sesso:", "height": "Altezza:", "weight": "Peso:", "diet": "Dieta:", "exercise": "Esercizio:", "drinking": "Alcol:", "smoking": "Fumo:", "sleep": "Sonno:", "hydration": "Idratazione:", "allergyIndex": "Allergia {{index}}:", "dose": "Dosaggio:", "frequency": "Frequenza:", "type": "Tipo:", "since": "Da:", "currentlyActive": "Attualmente Attivo:", "takingMedication": "Assunzione Farmaci:", "hasMenstruated": "Ha avuto il ciclo mestruale:", "regularity": "Regolarità:", "cycleLength": "Lunghezza del ciclo:", "flowDays": "<PERSON><PERSON><PERSON> di <PERSON>:", "padsPerDay": "Assorbenti/tamponi al giorno:", "hasChildbirth": "Ha avuto un parto:", "children": "Figli:", "pregnancies": "Gravidanze:"}, "notProvided": "Non fornito", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} ore al giorno", "hydration": "{{hydration}} bi<PERSON><PERSON> ({{liters}}L) al giorno", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} g<PERSON><PERSON>"}, "noData": {"allergies": "Nessuna allergia fornita", "medications": "<PERSON><PERSON><PERSON> farmaco fornito", "conditions": "Nessuna condizione medica fornita"}, "submitButton": "Invia Informazioni"}, "success": {"title": "Informazioni Aggiornate!", "message": "Grazie per aver fornito le tue informazioni sulla salute. Le useremo per personalizzare la tua esperienza e fornire migliori raccomandazioni.", "benefits": {"insights": "Informazioni sulla salute personalizzate", "reminders": "Migliori promemoria per i farmaci", "recommendations": "Raccomandazioni sanitarie personalizzate"}, "continueButton": "Continua al Dashboard"}, "permissions": {"microphonePermissionDenied": "Permesso microfono negato", "microphoneAccessDescription": "August richiede l'accesso al tuo microfono per registrare audio e inviare note vocali", "permissionDenied": "<PERSON><PERSON><PERSON> negato", "cameraPermissionRequired": "Abbiamo bisogno del permesso per la fotocamera per far funzionare questo!", "mediaLibraryPermissionRequired": "Abbiamo bisogno dei permessi alla libreria multimediale per far funzionare questo!"}, "voiceRecording": {"recordingTooLong": "Registrazione troppo lunga", "recordingTooLongMessage": "Le registrazioni vocali devono essere inferiori a 5 minuti. Si prega di registrare un messaggio più breve."}, "errors": {"uploadFailed": "Caricamento fallito", "voiceUploadFailed": "Impossibile caricare la registrazione vocale.", "voiceRecordingFailed": "Impossibile inviare la registrazione vocale", "failedToStopRecording": "Impossibile interrompere la registrazione", "photoUploadFailed": "Impossibile caricare la foto.", "failedToTakePhoto": "Impossibile scattare la foto", "imageUploadFailed": "Impossibile caricare l'immagine: {{fileName}}", "failedToPickImage": "Impossibile selezionare l'immagine", "documentUploadFailed": "Impossibile caricare il documento: {{fileName}}", "failedToPickDocument": "Impossibile selezionare il documento"}, "audioPlayer": {"downloadingAudio": "Download audio...", "loadingAudio": "Caricamento audio..."}, "mediaProcessing": {"processingFile": "Elaborazione del tuo file", "uploadingSecuring": "Caricamento e protezione del file...", "analyzingContent": "Analisi del contenuto del documento...", "extractingInfo": "Estrazione delle informazioni chiave...", "processingInsights": "Elaborazione delle informazioni...", "preparingResponse": "Preparazione della risposta dettagliata...", "finalizingResponse": "Finalizzazione della risposta..."}, "attachments": {"voiceMessage": "Messaggio vocale", "image": "[IMMAGINE]", "pdf": "[PDF]", "voice": "[NOTA VOCALE]"}, "pdf": {"loadingPdf": "Caricamento PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "selezionati"}, "mediaUpload": {"photoLibrary": "Libreria foto", "takePhoto": "Scatta foto", "chooseFile": "Scegli file"}, "comingSoon": {"title": "Prossimamente!", "description": " è attualmente in fase di sviluppo. Resta sintonizzato per gli aggiornamenti!", "buttonText": "Ho capito!"}, "clipboard": {"success": "Link copiato negli appunti"}, "mediaPhotos": {"emptyState": "Nessuna voce ancora."}, "foodDetail": {"defaultFoodName": "Alimento", "nutrition": {"totalCalories": "Calorie totali", "proteins": "Proteine", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "<PERSON><PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>", "fibers": "Fibre"}}, "reports": {"defaultTitle": "Elemento multimediale", "defaultFoodName": "Alimento", "defaultName": "Documento", "openButton": "Apri nel visualizzatore esterno", "biomarker": {"headerBiomarker": "Biomarcatore", "headerValue": "Valore", "headerRefRange": "Intervallo di riferimento", "headerStatus": "Stato"}, "noData": "<PERSON><PERSON>un dato di biomarcatori disponibile"}, "setup": {"title": "<PERSON><PERSON>o configurando tutto per te", "inProgress": "In corso...", "progressMessages": {"0": "Calcolo delle calorie giornaliere", "1": "Ottimizzazione della ripartizione dei macronutrienti", "2": "Creazione del piano alimentare", "3": "Calcolo del punteggio di salute", "4": "Finalizzazione della configurazione"}, "checklistItems": {"0": "Analisi dei tuoi dati sanitari", "1": "Calcolo del piano nutrizionale ottimale", "2": "Personalizzazione dei tuoi consigli", "3": "Creazione dei tuoi suggerimenti per i pasti", "4": "Finalizzazione della tua configurazione"}}, "foodEntry": {"emptyState": "Nessuna voce di cibo ancora. Scatta una foto del tuo pasto per aggiungerla!"}, "nutritionReview": {"congratulations": "Complimenti!", "subtitle": "Il tuo piano nutrizionale personalizzato è pronto", "submitButton": "Iniziamo!", "dailyTargetsTitle": "I Tuoi Obiettivi Nutrizionali Giornalieri", "macroLabels": {"calories": "Calorie", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>a", "fats": "<PERSON><PERSON>"}, "recommendations": {"title": "Come raggiungere i tuoi obiettivi:", "healthScores": "Usa i punteggi di salute per migliorare la tua routine", "trackFood": "Tieni traccia del tuo apporto alimentare in modo costante", "followCalories": "Segui il tuo fabbisogno calorico giornaliero", "balanceMacros": "Equilibra l'assunzione di carboidrati, proteine e grassi"}}, "editModal": {"titlePrefix": "Modifica", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Prossimo"}, "processing": {"stages": {"scanning": "Sto scansionando il cibo...", "identifying": "Identificazione degli ingredienti...", "extracting": "Estrazione di nutrienti...", "finalizing": "Finalizzazione dei risultati..."}, "error": {"defaultMessage": "<PERSON><PERSON><PERSON> cibo rilevato", "subtitle": "Prova da un'altra angolazione"}, "retakeButton": "Tocca per rifare la foto", "notification": "Ti avviseremo quando sarà fatto!"}, "chart": {"title": "Monitoraggio Nutrizionale nel Tempo", "selectNutrient": "Seleziona nutriente:", "emptyState": "<PERSON>essun dato nutrizionale disponibile al momento.", "dropdown": {"calories": "Calorie", "protein": "<PERSON><PERSON>a", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Grass<PERSON>", "sugars": "<PERSON><PERSON><PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON>", "defaultDate": "<PERSON><PERSON><PERSON>", "defaultTime": "<PERSON><PERSON> sconos<PERSON>uta", "saveChanges": "Salva modifiche", "error": {"title": "Errore", "message": "Impossibile aggiornare i dati nutrizionali. Riprova."}, "nutrition": {"calories": "🔥 Calorie", "proteins": "🥩 Proteine", "carbs": "🍞 Car<PERSON><PERSON><PERSON>i", "sugars": "🍬 <PERSON><PERSON><PERSON><PERSON>", "fat": "🥑 Grasso"}, "macroBreakdown": {"title": "Ripartizione dei Macronutrienti", "noData": "<PERSON><PERSON>un dato disponibile sui macronutrienti per questo alimento."}, "macroLabels": {"calories": "Calorie", "protein": "<PERSON><PERSON>a", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Grass<PERSON>", "sugar": "<PERSON><PERSON><PERSON><PERSON>"}}, "infoModal": {"title": "<PERSON><PERSON>", "edit": "Modifica", "save": "<PERSON><PERSON>", "saving": "Salvataggio...", "enterValue": "Inserisci valore", "notSet": "Non impostato", "age": "Età", "heightCm": "Altezza (cm)", "weightKg": "Peso (kg)", "targetWeight": "Peso <PERSON>", "nutritionTargets": "Obiettivi Nutrizionali", "protein": "<PERSON><PERSON>a", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON>", "gm": "gm", "editNote": "Inserisci i valori o lascia vuoto per il calcolo automatico.", "autoCalculateNote": "Le macro vengono calcolate automaticamente in base ai tuoi dati.", "validation": {"ageMin": "L'età deve essere almeno 18 anni", "ageMax": "L'età deve essere inferiore a 125", "heightMin": "L'altezza deve essere di almeno 50 cm", "heightMax": "L'altezza deve essere inferiore a 250 cm", "weightMin": "Il peso deve essere di almeno 30 kg", "weightMax": "Il peso deve essere inferiore a 500 kg", "targetWeightMin": "Il peso target deve essere di almeno 30 kg", "targetWeightMax": "Il peso target deve essere inferiore a 500 kg", "proteinMin": "Le proteine devono essere 0 o più.", "carbsMin": "I carboidrati devono essere 0 o più.", "fatsMin": "I grassi devono essere 0 o più."}}, "tracker": {"calories": "Calorie", "protein": "<PERSON><PERSON>a", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Grass<PERSON>", "excess": "<PERSON><PERSON><PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>"}, "specialistConstants": {"nutritionist": {"name": "Nutrizionista", "description": "Consigli esperti su dieta, nutrizione e sane abitudini alimentari", "featureName": "Specialista in Nutrizione"}, "cardiologist": {"name": "<PERSON><PERSON><PERSON>", "description": "Specializzato nella salute del cuore e nelle patologie cardiovascolari", "featureName": "Specialista in Cardiologia"}, "neurologist": {"name": "Neurologo", "description": "Specializzato in disturbi del cervello, del midollo spinale e del sistema nervoso", "featureName": "Specialista in Neurologia"}, "oncologist": {"name": "Oncologo", "description": "Specializzato nella diagnosi e nel trattamento del cancro", "featureName": "Specialista in Oncologia"}, "endocrinologist": {"name": "Endocrinologo", "description": "Esperto in patologie ormonali e disturbi metabolici", "featureName": "Specialista in Endocrinologia"}}, "discoverCards": {"categories": {"nutrition": "Nutrizione", "heartHealth": "Salute del cuore", "mentalHealth": "Salute mentale", "fitness": "Fitness", "wellness": "<PERSON><PERSON><PERSON>"}, "titles": {"vitaminB12Recovery": "Quanto tempo ci vuole per riprendersi da una carenza di vitamina B12?", "vitaminDeficiencyGanglion": "Quale carenza vitaminica causa le cisti del ganglio?", "vitaminDeficiencyHairFall": "Quale carenza vitaminica causa la caduta dei capelli?", "vitaminWaters": "Le acque vitaminiche fanno bene?", "cholesterolHeadaches": "Il colesterolo alto causa mal di testa?", "cholesterolEyes": "Quali sono i sintomi del colesterolo alto visibili negli occhi?", "diabetesHeadaches": "Il diabete può causare mal di testa?", "chestPainDrinking": "Perché il petto fa male dopo aver bevuto?", "stressDizziness": "Lo stress può causare vertigini?", "bulimiaFace": "Cos'è il viso da bulimia?", "kneeTwitch": "Perché mi si contrae il ginocchio?", "noseTwitching": "Perché il naso si contrae?", "piriformisVsSciatica": "Quali sono le differenze tra la sindrome del piriforme e la sciatica?", "shoulderBladePinched": "Come liberare un nervo pizzicato nella scapola?", "shoulderPinched": "Come liberare un nervo pizzicato nella spalla?", "meniscusTorn": "Come curare naturalmente una lesione del menisco?", "hydrateQuickly": "Come idratarsi rapidamente?", "periodConstipation": "È normale avere stitichezza durante il periodo?", "acneScars": "Come eliminare naturalmente le cicatrici da acne in una settimana?", "perimenopausePregnancy": "Si può rimanere incinta durante la perimenopausa?"}, "descriptions": {"vitaminB12Recovery": "Scopri la timeline di recupero per la carenza di vitamina B12 e rimedi efficaci per aumentare i tuoi livelli di energia.", "vitaminDeficiencyGanglion": "Esplora il legame tra carenze vitaminiche e lo sviluppo di cisti gangliari nel corpo.", "vitaminDeficiencyHairFall": "Impara come la mancanza di vitamine essenziali può portare alla caduta dei capelli e cosa puoi fare per prevenirla.", "vitaminWaters": "Scopri i benefici e gli eventuali svantaggi delle acque vitaminiche come parte della tua nutrizione quotidiana.", "cholesterolHeadaches": "Esamina la possibile connessione tra alti livelli di colesterolo e l'insorgenza di mal di testa.", "cholesterolEyes": "Impara come l'alto colesterolo può manifestarsi nei tuoi occhi e quali sintomi osservare.", "diabetesHeadaches": "Indaga sulla relazione tra diabete e comparsa di mal di testa nella vita quotidiana.", "chestPainDrinking": "Esplora le ragioni alla base del dolore toracico dopo il consumo di alcune bevande.", "stressDizziness": "Approfondisci come lo stress può influenzare il tuo equilibrio e il tuo benessere generale, causando vertigini.", "bulimiaFace": "Comprendi i segni fisici della bulimia, inclusi gli effetti sull'aspetto del viso.", "kneeTwitch": "Indaga sulle possibili cause del sussulto involontario del ginocchio e la sua relazione con stress o affaticamento.", "noseTwitching": "Impara le possibili ragioni del sussulto del naso e il suo legame con l'ansia o altri fattori.", "piriformisVsSciatica": "Confronta i sintomi della sindrome del piriforme e della sciatica per comprendere meglio la tua condizione.", "shoulderBladePinched": "Scopri tecniche efficaci per alleviare un nervo pizzicato nella scapola e ripristinare la mobilità.", "shoulderPinched": "Impara semplici esercizi e stretching per alleviare la compressione nervosa nella zona della spalla.", "meniscusTorn": "Esplora metodi naturali ed esercizi per sostenere la guarigione di una lesione del menisco.", "hydrateQuickly": "Scopri modi veloci ed efficaci per reidratarti e mantenere un'idratazione corporea ottimale.", "periodConstipation": "Comprendi le ragioni alla base della stitichezza durante le mestruazioni e scopri rimedi naturali.", "acneScars": "Scopri rimedi naturali e consigli per la cura della pelle per ridurre rapidamente l'aspetto delle cicatrici da acne.", "perimenopausePregnancy": "Impara la perimenopausa, le considerazioni sulla fertilità e cosa aspettarti durante questa fase della vita."}}}