import * as FileSystem from 'expo-file-system';
import logger from '../logger/logger';

const VOICE_DIR = `${FileSystem.documentDirectory}voices/`;

export async function downloadAudioToLocal(uri: string, filename: string): Promise<string | null> {
  try {
    if (uri.startsWith('file://')) {
      return uri;
    }
    // Ensure the "voices" directory exists
    const dirInfo = await FileSystem.getInfoAsync(VOICE_DIR);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(VOICE_DIR, { intermediates: true });
    }

    const fileUri = `${VOICE_DIR}${filename}`;
    const result = await FileSystem.downloadAsync(uri, fileUri);

    if (result.status === 200) {
      return result.uri;
    } else {
      return null;
    }
  } catch (error) {
    logger.error('Failed to download voice file:', error);
    return null;
  }
}


export const downloadIfRecentVoice = async (message: any): Promise<string | null> => {
  const voiceAttachment = message?.attachments?.["0"];
  if (message?.isRecent && voiceAttachment?.type === 'voice') {
    const filename = `voice_${message.id}.m4a`;
    const localPath = await downloadAudioToLocal(voiceAttachment.uri, filename);
    return localPath;
  }
  return null;
};

export async function deleteFile(fileUri: string): Promise<boolean> {
  try {
    const fileInfo = await FileSystem.getInfoAsync(fileUri);
    if (fileInfo.exists) {
      await FileSystem.deleteAsync(fileUri, { idempotent: true }); // idempotent avoids throwing if file already deleted
      return true;
    } else {
      // File doesn't exist — optionally treat this as success
      return true;
    }
  } catch (error) {
    logger.error('Failed to delete file:', error);
    return false;
  }
}

export async function deleteAllVoiceFiles(): Promise<void> {
  try {
    const dirInfo = await FileSystem.getInfoAsync(VOICE_DIR);
    if (!dirInfo.exists) return;

    const files = await FileSystem.readDirectoryAsync(VOICE_DIR);
    await Promise.all(
      files.map(file =>
        FileSystem.deleteAsync(VOICE_DIR + file, { idempotent: true })
      )
    );

    logger.info('✅ Cleaned up voice files');
  } catch (error) {
    logger.error('⚠️ Failed to delete voice files:', error);
  }
}
