import 'react-native-get-random-values';
import './i18n'; // Initialize i18n
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack, usePathname, useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { View, Platform } from "react-native";
import { ErrorBoundary } from "./error-boundary";
import { ThemeProvider } from '@/src/theme/ThemeContext';
import { GluestackUIProvider } from "@gluestack-ui/themed";
import { config } from "@/theme/config";
import * as Notifications from 'expo-notifications';
import { AppState } from 'react-native';
import { initializeClarity } from '@/utils/clarity/clarity-utils';
import { trackClarityEvent } from '@/utils/clarity/clarity-utils';
import logger from '@/utils/logger/logger';
import { BackHandler } from 'react-native';
import { useChatStore } from '@/store/chatStore';
import { usePhoneStore } from '@/store/phoneStore';
import { trackUserInteraction } from '@/utils/mixpanel/mixpanel-utils';
import AppsFlyerService from '@/utils/appsflyer/appsflyer-utils';
import { RootSiblingParent } from 'react-native-root-siblings';
import { deleteAllVoiceFiles } from '@/utils/chat/downloadAudioToLocal';
import * as NavigationBar from 'expo-navigation-bar';
import { AppEventsLogger } from 'react-native-fbsdk-next';

declare global {
  var currentScreen: string | undefined;
}

// ----------------------------------------------------------------------------
// Set up the global notification handler.
// This runs as soon as the app loads (in this root file) so that it applies everywhere.
Notifications.setNotificationHandler({
  handleNotification: async () => {
    logger.info('Notification handler - current screen:', global.currentScreen);
    // If the current route indicates the Chat screen inside the tabs (adjust as needed),
    // we can choose not to show an in-app alert.
    return {
      shouldShowAlert: false,
      shouldPlaySound: false,
      shouldSetBadge: false,
    };    
  },
});
// ----------------------------------------------------------------------------

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });
  useEffect(() => {
    if (error) {
      logger.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    try{
      AppEventsLogger.logEvent('fb_mobile_activate_app'); // logs app activation
      logger.info("Facebook ad event success")
    }catch(e){
      logger.error("Facebook ad event failure")
    }
    
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
      <GluestackUIProvider config={config}>
        <RootSiblingParent>
            <RootLayoutNav />
        </RootSiblingParent>
      </GluestackUIProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

function RootLayoutNav() {
  const pathname = usePathname();
  const router = useRouter();
  const [screenEntryTimes, setScreenEntryTimes] = useState<Record<string, Date>>({});
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);

  useEffect(() => {
    initializeClarity();
  }, []);

  useEffect(() => {
    if (Platform.OS === 'android') {
    async function setNavBarColor() {
      try {
        await NavigationBar.setBackgroundColorAsync('#fff');
      } catch (e) {
      }
    }
    setNavBarColor();
  }
  }, [pathname]);

  // Update global.currentScreen and track screen entry/exit times whenever the pathname changes.
  useEffect(() => {
    if (pathname) {
      const now = new Date();
      logger.info('Navigation changed to:', pathname);
      
      // If we have a previous screen, calculate and track the duration
      if (global.currentScreen && screenEntryTimes[global.currentScreen]) {
        const previousScreen = global.currentScreen;
        const entryTime = screenEntryTimes[previousScreen];
        const durationMs = now.getTime() - entryTime.getTime();
        const durationSec = Math.round(durationMs / 1000);
        
        
        trackClarityEvent('Screen_Session_Duration', {
          screen: previousScreen,
          durationSeconds: durationSec,
          timestamp: new Date().toISOString()
        });
      }
      
      global.currentScreen = pathname;
      setScreenEntryTimes(prev => ({
        ...prev,
        [pathname]: now
      }));
    }
  }, [pathname]);

  useEffect(() => {
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      logger.info('NOTIFICATION PAYLOAD', response.notification.request);
      logger.info('User tapped notification:', response);
      Notifications.setBadgeCountAsync(0);
      router.push('/chat');
    });
    return () => responseListener.remove();
  }, [router]);

  useEffect(() => {
    if (!sessionStartTime) {
      setSessionStartTime(new Date());
      logger.info('Initial session start time set');
    }
  }, []);  

  useEffect(() => {
    const initializeAppsFlyer = async () => {
      try {
        await AppsFlyerService.initialize();
       //logger.info('AppsFlyer initialization completed');
      } catch (error) {
        //logger.error('Failed to initialize AppsFlyer:', JSON.stringify(error));
      }
    };

    initializeAppsFlyer();

    const subscription = AppState.addEventListener('change', state => {
      logger.info('STATE IS',state)
      if (state === 'active') {
        logger.info('I AM ACTIVE')
        if (!sessionStartTime) {
          setSessionStartTime(new Date());
        }
        // App came to foreground
        trackClarityEvent('App_Foregrounded', {
          screen: global.currentScreen,
          timestamp: new Date().toISOString()
        });
        
        
      } else if (state === 'background') {
        logger.info('I AM IN THE BACKGROUND')
        deleteAllVoiceFiles();
        // App went to background
        trackClarityEvent('App_Backgrounded', {
          screen: global.currentScreen,
          timestamp: new Date().toISOString()
        });
        
        
        // If we have a current screen, track the session duration
        if (global.currentScreen && screenEntryTimes[global.currentScreen]) {
          const now = new Date();
          const entryTime = screenEntryTimes[global.currentScreen];
          const durationMs = now.getTime() - entryTime.getTime();
          const durationSec = Math.round(durationMs / 1000);
          
          trackClarityEvent('Screen_Session_Duration', {
            screen: global.currentScreen,
            durationSeconds: durationSec,
            exitReason: 'app_backgrounded',
            timestamp: new Date().toISOString()
          });
        }
        if (sessionStartTime) {
          const now = new Date();
          const totalDurationSec = Math.round((now.getTime() - sessionStartTime.getTime()) / 1000);
          logger.info('TOTAL SESSION DURATION IS', totalDurationSec);
          const phoneNumber = usePhoneStore.getState().phoneNumber;
          if (phoneNumber) {
            trackUserInteraction('SESSION DURATION', { duration: totalDurationSec})
          }
          setSessionStartTime(null);
        }
      }
      
      // Original code for badge count reset
      if (global.currentScreen && global.currentScreen.includes('chat')) {
        Notifications.setBadgeCountAsync(0);
      }
    });
    return () => subscription.remove();
  }, [screenEntryTimes]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      const selectedMessages = useChatStore.getState().selectedMessages;
      if (selectedMessages.length > 0) {
        useChatStore.getState().clearSelectedMessages();
        return true; // Prevent default back action
      }
      return false; // Allow default back action
    });

    return () => backHandler.remove();
  }, []);

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: 'fade', // Add fade transition
      }}
    >
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="auth" options={{ headerShown: false }} />
      <Stack.Screen name="library" options={{ headerShown: false }} />
      <Stack.Screen name="specialists" options={{ headerShown: false }} />
      <Stack.Screen name="splash" options={{ headerShown: false }} />
      <Stack.Screen name="nutrition-food-detail" options={{
        animation: 'none', // Override to disable fade (use native default)
      }} />
    </Stack>
  );
}
