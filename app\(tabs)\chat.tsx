import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { StyleSheet, View, KeyboardAvoidingView, Platform, Keyboard, Text, Alert } from 'react-native';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import TopNavbar from '@/components/navigation/TopNavbar';
import ChatList from '@/components/chat/ChatList';
import ChatInput from '@/components/chat/ChatInput';
import ChatSkeleton from '@/components/common/loaders/chatSkeleton';
import { ChatInputRef } from '@/types/chat';
import { colors } from '@/constants/colors';
import { useChatStore, useIsLoading, useIsWaitingForResponse, useMessages } from '@/store/chatStore';
import logger from '@/utils/logger/logger';
import { trackUserInteraction, trackOperation, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';
import { trackClarityEvent } from '@/utils/clarity/clarity-utils';
import * as Notifications from 'expo-notifications';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import { useScreenTracking } from '../currentScreen';

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.secondary[50],
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    chatContainer: {
      flex: 1,
    },
  });

const ChatScreen = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const params = useLocalSearchParams();
  const chatInputRef = useRef<ChatInputRef>(null);
  const navigation = useNavigation();
  const storeLoading = useIsLoading();
  const [initializing, setInitializing] = useState(true);
  const TAB_BAR_HEIGHT = Platform.OS === 'ios' ? 76 : 64;
  const [isWebPubSubConnected,setIsWebPubSubConnected] = useState(false) 
  
  useEffect(() => {
    // Immediately clear the badge count when the Chat page mounts.
    Notifications.setBadgeCountAsync(0);
    trackOperation('Badge Count Reset', OperationStatus.SUCCESS);
    
    // Track badge count reset in Clarity
    trackClarityEvent('Badge_Count_Reset', {
      timestamp: new Date().toISOString()
    });
  }, []);

  useScreenTracking('Chat', { screen: "Chat" });
  
  useEffect(() => {
    // Check if we need to set a prompt
    if (params.setPrompt) {
      setTimeout(() => {
        chatInputRef.current?.setInputText(params.setPrompt as string);
        
      }, 500); // Small delay to ensure component is fully mounted
    }
  }, [params]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      trackOperation('Keyboard Listener Setup', OperationStatus.INITIATED);
      
      const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
        // Hide tab bar when keyboard shows
        navigation.setOptions({
          tabBarStyle: { display: 'none' }
        });
        
      });
      
      const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
        // Show tab bar when keyboard hides
        navigation.setOptions({
          tabBarStyle: {
            display: 'flex',
            height: TAB_BAR_HEIGHT,
            paddingTop: 4,
          }
        });
        
      });

      trackOperation('Keyboard Listener Setup', OperationStatus.SUCCESS);

      return () => {
        showSubscription.remove();
        hideSubscription.remove();
        trackOperation('Keyboard Listener Cleanup', OperationStatus.SUCCESS);
      };
    }
  }, [navigation]);

  // Add a ref to track if WebPubSub is already initialized
  const webPubSubInitialized = useRef(false);

  // Initialize WebPubSub when chat page loads and clean up when unmounting
  useEffect(() => {
    logger.info('THE CHAT PAGE IS MOUNTED');
    trackUserInteraction('Chat Screen Mounted');
    trackClarityEvent('Chat_Page_Mounted', {
      platform: Platform.OS,
      timestamp: new Date().toISOString()
    });
    
    const initializeWebPubSub = async () => {
      // Skip initialization if already done
      if (webPubSubInitialized.current) {
        setInitializing(false);
        return;
      }
      
      try {
        logger.info('Chat Screen: Initializing WebPubSub');
        logger.info('Initializing WebPubSub from chat screen');
        trackOperation('WebPubSub Connection', OperationStatus.INITIATED);
        
        await useChatStore.getState().initializeWebPubSub();
        
        logger.info('Chat Screen: WebPubSub initialization complete');
        logger.info('WebPubSub initialization complete from chat screen');
        trackOperation('WebPubSub Connection', OperationStatus.SUCCESS);
        
        // Track WebPubSub connection success in Clarity
        trackClarityEvent('WebPubSub_Connection_Success', {
          timestamp: new Date().toISOString()
        });
        
        // Set initializing to false after initialization
        setInitializing(false);
        webPubSubInitialized.current = true;
        setIsWebPubSubConnected(true)
      } catch (error: any) {
        const is401 = String(error?.message || '').includes('401');
        if (!is401) {
          logger.error('Failed to initialize WebPubSub from chat screen:', JSON.stringify(error));
        }
        trackOperation('WebPubSub Connection', OperationStatus.FAILURE, { 
          error: error instanceof Error ? error.message : String(error) 
        });
        
        // Track WebPubSub connection failure in Clarity
        trackClarityEvent('WebPubSub_Connection_Failure', {
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        setInitializing(false);
        setIsWebPubSubConnected(false)
      }
    };

    logger.info('Chat Screen: Component mounted, initializing WebPubSub');
    initializeWebPubSub();
    
    return () => {
      trackUserInteraction('Chat Screen Unmounted');
      // Track screen unmount in Clarity
      trackClarityEvent('Chat_Screen_Unmounted', {
        platform: Platform.OS,
        timestamp: new Date().toISOString()
      });
      
      // Attempt to disconnect WebPubSub when unmounting
      trackOperation('WebPubSub Disconnection', OperationStatus.INITIATED);
    };
  }, []);
  
  const handleKeyboardVisibilityChange = useCallback((isVisible: boolean) => {
    if (isVisible) {
      logger.info('Keyboard is now visible, scrolling to bottom');
    }
  }, []);
  
  // Add useMemo for expensive computations
  const memoizedChatList = useMemo(() => (
      <ChatList>
        <ChatInput 
          ref={chatInputRef} 
          onKeyboardVisibilityChange={handleKeyboardVisibilityChange}
        />
      </ChatList>
  ), [handleKeyboardVisibilityChange]);

  return (
    <SafeAreaView style={styles.container} edges={[]}>
      <Stack.Screen options={{ headerShown: false }} />
      <TopNavbar title={t('chat.nav.title')} showProfile={true} />
      <KeyboardAvoidingView 
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 20 : 0}
      >
        <View style={styles.chatContainer}>
          {memoizedChatList}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   keyboardAvoidingView: {
//     flex: 1,
//   },
//   chatContainer: {
//     flex: 1,
//   },
// });

export default React.memo(ChatScreen);
