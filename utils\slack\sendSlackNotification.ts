import { getEnvironmentVariable } from "../getEnvironmentVariable";
import logger from '@/utils/logger/logger';

export async function sendSlackNotification(message: string): Promise<void> {
    try {
      const response = await fetch(
        `${getEnvironmentVariable("GATEKEEPER_URL")}/user/august/send-slack-notification`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            errorMessage: message,
          }),
        }
      );
  
      if (!response.ok) {
        throw new Error(`Failed to send Slack notification: ${response.statusText}`);
      }
    } catch (error) {
      //logger.error('Error sending Slack notification:', error);
      throw error;
    }
}