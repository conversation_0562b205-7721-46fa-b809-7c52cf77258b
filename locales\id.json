{"common": {"error": "<PERSON><PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "sometimes": "Terkadang", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "Simpan", "next": "Berikutnya", "loading": "Memuat...", "version": "v0.0.1.7"}, "welcome": "<PERSON><PERSON><PERSON> untuk mulai berbicara dengan August", "notFound": {"title": "Ups!", "message": "Layar ini tidak ada.", "goHome": "Ke layar utama!"}, "library": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "specialists": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Konsultasikan dengan profesional kesehatan spesialis untuk masalah kesehatan yang lebih spesifik. Pilih spesialis di bawah ini:", "generalPhysician": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>tuk masalah kesehatan umum dan perawatan primer."}, "nutritionist": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Untuk saran diet, nutrisi, dan manajemen berat badan."}, "cardiologist": {"title": "Kardiolog", "description": "Untuk masalah yang berhubungan dengan jantung dan kesehatan kardiovaskular."}, "neurologist": {"title": "Neurolog", "description": "<PERSON><PERSON><PERSON> ma<PERSON>ah <PERSON>, <PERSON>um tulang be<PERSON>, dan sistem saraf."}, "oncologist": {"title": "Onkolog", "description": "<PERSON>tuk masalah dan perawatan yang berhubungan dengan kanker."}, "endocrinologist": {"title": "Endokrinolog", "description": "<PERSON>tuk gangguan yang berhubungan dengan hormon dan manajemen diabetes."}, "dermatologist": {"title": "Dermatolog", "description": "Untuk kondisi kulit, rambut, dan kuku."}, "psychiatrist": {"title": "Psikiater", "description": "<PERSON><PERSON>k masalah kesehatan mental dan kesejahteraan psikologis."}}, "profile": {"title": "Profil", "defaultName": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "saving": "Menyimpan...", "noPhoneNumber": "Tidak ada nomor telepon", "loggingOut": "Sedang keluar...", "about": {"title": "Tentang", "description": "<PERSON><PERSON><PERSON> lebih lanjut tentang August"}, "whatsapp": {"title": "WhatsApp", "description": "<PERSON><PERSON><PERSON> dengan August di WhatsApp"}, "refer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Suka August? Bagikan dengan teman-teman <PERSON>a"}, "deleteAccount": {"title": "<PERSON><PERSON> akun", "description": "<PERSON><PERSON> men<PERSON>al melihat Anda pergi"}, "logout": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>pai jumpa lagi. <PERSON><PERSON> akan me<PERSON>"}, "shareMessage": "👋Hai, Lihat aplikasi keren yang telah saya gunakan!\n\n\n\n➡️Saya telah menggunakan August untuk mendapatkan informasi dan panduan kesehatan yang cepat dan andal. Ini seperti memiliki dokter di saku Anda! Lihat di sini:", "error": {"loadFailed": "Gagal memuat data pengguna", "fetchError": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengambil data pengguna", "updateNameFailed": "<PERSON><PERSON> me<PERSON> nama", "updateNameError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat memperbarui nama", "loadFoodData": "Gagal memuat data makanan", "logoutError": "Kesalahan saat keluar:", "shareError": "<PERSON><PERSON><PERSON> berbagi pesan:"}}, "error": {"title": "<PERSON><PERSON><PERSON><PERSON>", "checkLogs": "Silakan periksa log perangkat Anda untuk detail lebih lanjut.", "unknown": "Kesalahan tidak diketahui", "unknownFile": "File tidak diketahui", "unknownLine": "<PERSON>s tida<PERSON>", "unknownColumn": "<PERSON><PERSON><PERSON> tida<PERSON>"}, "auth": {"phone": {"selectCountry": "<PERSON><PERSON><PERSON>", "searchCountries": "<PERSON><PERSON> negara", "validation": {"invalidPhone": "<PERSON><PERSON>an masukkan nomor telepon yang valid", "invalidDigits": "Silakan masukkan nomor telepon yang valid (7-15 digit)"}}, "header": {"title": "Dapatkan kejelasan tentang kekhawatiran kesehatan Anda secara instan dan pribadi", "subtitle": "Bimbingan yang bi<PERSON>. Tanpa terburu-buru. <PERSON><PERSON> keb<PERSON>.", "emphasis": "<PERSON><PERSON>."}, "greeting": "Hai 👋", "phoneNumber": "Nomor Telepon", "requestOTP": "Minta OTP", "otp": {"title": "<PERSON><PERSON>", "verify": "Verifikasi OTP", "sending": "Sedang mengirim...", "countdown": "<PERSON><PERSON> dalam {{countdown}} detik", "resend": "<PERSON><PERSON> OTP", "sentTo": "OTP dikirim ke ", "whatsappSuffix": " <PERSON> What<PERSON>pp"}, "disclaimer": {"prefix": "<PERSON><PERSON>, <PERSON><PERSON> kami ", "continuePrefix": "<PERSON><PERSON>, <PERSON><PERSON> kami ", "termsOfService": "<PERSON><PERSON><PERSON> dan <PERSON>", "and": " dan ", "privacyPolicy": "<PERSON><PERSON><PERSON><PERSON>", "whatsappConsent": ", dan menyetujui untuk menerima pembaruan & pengingat dari kami melalui WhatsApp."}}, "onboarding": {"preAuth": {"welcome": {"title": "Selamat datang di August!", "buttonText": "Mari kita mulai"}}, "postAuth": {"step1": {"title": "Hai!", "subtitle": "Saya August 👋", "description": "Anggap saya sebagai sudut nyaman di perangkat Anda di mana Anda dapat menjelajahi semua rasa ingin tahu kesehatan <PERSON>.", "subdescription": "<PERSON>an ragu untuk menanyakan apa pun yang ada di pikiran <PERSON>.\n<PERSON><PERSON>, <PERSON><PERSON> batas!", "placeholder": "Apa yang harus saya panggil Anda?"}, "step2": {"title": "Hai {{userName}},", "subtitle": "<PERSON><PERSON><PERSON> yang dapat saya lakukan:", "features": {"health": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON>"}, "nutrition": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>"}}}}, "pills": {"thoughtful": "<PERSON><PERSON><PERSON><PERSON>", "careful": "Hati-hati", "accurate": "Akurat"}, "features": {"symptoms": {"title": "<PERSON><PERSON><PERSON>", "description": "Saya mual selama seminggu. Apa yang terjadi pada saya?"}, "prescriptions": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>ggah dan pahami resep seperti dokter."}, "medicine": {"title": "<PERSON><PERSON><PERSON> o<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Metformin untuk PCOS saya berinteraksi dengan pil ADHD saya?"}, "plans": {"title": "Dapatkan rencana yang dipersonalisasi", "description": "<PERSON><PERSON><PERSON><PERSON> Anda memberi saya rencana nutrisi & keb<PERSON>ran untuk mengurangi kadar HbA1c saya?"}}, "buttons": {"getStarted": "<PERSON><PERSON>", "next": "Berikutnya"}, "errors": {"nameRequired": "<PERSON><PERSON><PERSON> masukkan nama <PERSON>"}}, "tabs": {"chat": "<PERSON><PERSON>lan", "discover": "Temukan", "nutrition": "<PERSON><PERSON><PERSON><PERSON>", "personalize": "<PERSON><PERSON><PERSON>"}, "chat": {"nav": {"title": "<PERSON><PERSON><PERSON>"}, "me": "<PERSON><PERSON>", "augustName": "<PERSON><PERSON><PERSON>", "input": {"placeholder": "<PERSON><PERSON> pada Agustus...", "disclaimer": "<PERSON><PERSON><PERSON> mungkin melakukan k<PERSON>. Konfirmasikan dengan dokter"}, "list": {"loadingMessages": "Sedang memuat pesan...", "noMessages": "Belum ada pesan. <PERSON><PERSON><PERSON> per<PERSON>!"}, "connection": {"offlineMessage": "Sepertinya Anda sedang offline. Sambungkan kembali untuk mengirim pesan.", "connecting": "Sedang menyambung...", "tryAgain": "<PERSON><PERSON>"}, "prompts": {"uploadReport": "<PERSON><PERSON><PERSON>", "speakInHindi": "<PERSON><PERSON><PERSON> dalam bahasa <PERSON>", "notFeelingWell": "<PERSON>a tidak enak badan", "whatIsMyBMI": "Berapa BMI saya?", "nutritionAdvice": "Saran nut<PERSON>i", "sleepBetter": "<PERSON><PERSON><PERSON> lebih nyenyak"}, "citations": {"referenceText": "Untuk detail lebih lanjut mengenai perca<PERSON>pan ini, silakan lihat:"}, "actions": {"copiedToClipboard": "Disalin ke clipboard", "copied": "Di<PERSON>in"}, "share": {"introText": "👋Hai, lihat percakapan saya dengan Agustus:\n\n", "downloadText": "\n\n➡️Unduh A<PERSON><PERSON> untuk mengobrol dengan teman AI kesehatan <PERSON>:\n"}}, "discover": {"nav": {"title": "Temukan"}, "categories": {"all": "<PERSON><PERSON><PERSON>", "heartHealth": "<PERSON><PERSON><PERSON><PERSON>", "nutrition": "<PERSON><PERSON><PERSON><PERSON>", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "Kebugara<PERSON>", "wellness": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cards": {"empty": "Tidak ada kartu yang tersedia untuk kategori ini"}, "sections": {"features": "<PERSON><PERSON>"}, "features": {"healthLibrary": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ks<PERSON> ke informasi medis yang tepercaya, andal, dan mutakhir secara gratis."}, "nutritionTracker": {"title": "Pelacak Nutrisi", "description": "Pernah bertanya-tanya apakah Anda bisa mengunggah foto makanan Anda dan melacak semua tujuan nutrisi Anda? Agus<PERSON> dapat melakukan hal itu!"}, "multilingualSupport": {"title": "Dukungan Multibahasa", "description": "Anda dapat berkomunikasi dengan Agustus dalam bahasa apa pun yang Anda kuasai! Agustus selalu siap menden<PERSON>, men<PERSON><PERSON><PERSON>, dan menang<PERSON>pi Anda kapan pun Anda membutuhkannya.", "samplePrompt": "<PERSON><PERSON><PERSON> dalam bahasa <PERSON>"}, "labReportAnalysis": {"title": "<PERSON><PERSON><PERSON>", "description": "Saat Anda berbicara dengan Agustus tentang laporan lab <PERSON>, <PERSON><PERSON> men<PERSON> presisi yang ekstrem. Agustus telah memproses lebih dari 4,7 juta laporan dengan akurasi ekstraksi biomarker 98,4%."}}}, "nutrition": {"nav": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "meals": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Ketuk untuk melihat detail makronutrien setiap makanan"}, "upload": {"loading": "Mengunggah gambar..."}, "defaultFoodName": "<PERSON><PERSON>", "today": "<PERSON>", "unknownTime": "<PERSON><PERSON><PERSON> tidak di<PERSON>i", "calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON>", "carbs": "🍞 <PERSON><PERSON><PERSON><PERSON><PERSON>", "sugars": "🍬 Gula", "fat": "🥑 Lemak", "caloriesLabel": "<PERSON><PERSON><PERSON>", "proteinLabel": "<PERSON><PERSON>", "carbohydratesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatLabel": "Lemak", "sugarLabel": "<PERSON><PERSON>", "tips": "Tips:", "macroBreakdown": "<PERSON><PERSON><PERSON>", "noMacroData": "Tidak ada data makronutrien yang tersedia untuk item makanan ini.", "disclaimer": "Hanya untuk tujuan edukasi. Pelajari lebih lan<PERSON>t", "disclaimerLink": "di sini", "unit": {"kcal": "kcal", "g": "g"}, "form": {"gender": {"title": "<PERSON>pa jenis k<PERSON>?", "subtitle": "Ini akan digunakan untuk mengkalibrasi rencana k<PERSON>.", "male": "<PERSON><PERSON>-laki", "female": "Perempuan", "other": "<PERSON><PERSON><PERSON>"}, "age": {"title": "<PERSON><PERSON><PERSON> usia And<PERSON>?", "subtitle": "Ini akan digunakan untuk menghitung kebutuhan harian <PERSON>."}, "measurements": {"title": "Tinggi & Berat <PERSON>", "subtitle": "<PERSON><PERSON><PERSON> masukkan tinggi badan <PERSON>a dalam sentimeter dan berat badan dalam kilogram."}, "activity": {"title": "Tingkat Aktivitas", "subtitle": "<PERSON><PERSON><PERSON> sering <PERSON><PERSON> be<PERSON>?", "none": "Tidak Berolahraga", "moderate": "Sedang", "high": "Tingg<PERSON>"}, "goal": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Apa yang ingin Anda capai?", "increase": "Meningkatkan", "maintain": "Pertahankan", "decrease": "Menurunkan"}, "targetWeight": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> berat badan target Anda dalam kilogram?"}, "setup": {"title": "Menyiapkan Rencana And<PERSON>", "subtitle": "<PERSON><PERSON> tunggu sementara kami menyiapkan rencana nutrisi <PERSON>a."}, "review": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> dan sesuaikan rencana nutrisi <PERSON>."}, "height": {"label": "Tinggi (cm)"}, "weight": {"label": "Berat (kg)"}}, "error": {"updateFailed": "Gagal memperbarui data nutrisi. Silakan coba lagi.", "parsingError": "Kesalahan penguraian data makanan:", "fetchReportsFailed": "Gagal mengambil data laporan. Silakan coba lagi.", "missingReportId": "ID Laporan hilang"}}, "personalize": {"nav": {"title": "<PERSON><PERSON><PERSON>"}, "button": {"saving": "Sedang Menyimpan", "review": "Tinjau", "saveNext": "Simpan & Berikutnya"}}, "basicInfo": {"title": "Mari kita mengenal Anda lebih baik", "subtitle": "Informasi ini membantu kami mempersonalisasi rekomendasi kesehatan <PERSON>a", "age": {"question": "<PERSON><PERSON><PERSON>?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> usia <PERSON>"}, "sex": {"question": "<PERSON>pa jenis k<PERSON>?", "placeholder": "<PERSON><PERSON><PERSON> jeni<PERSON> k<PERSON>", "male": "<PERSON><PERSON>-laki", "female": "Perempuan", "other": "<PERSON><PERSON><PERSON>"}, "height": {"question": "<PERSON><PERSON><PERSON> tinggi badan <PERSON>? (cm)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tinggi badan <PERSON>"}, "weight": {"question": "<PERSON><PERSON><PERSON> berat badan <PERSON>? (kg)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> berat badan <PERSON>"}}, "lifestyle": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> keb<PERSON><PERSON> harian Anda membantu kami memberikan rekomendasi yang lebih baik.", "diet": {"question": "Jenis diet apa yang Anda ikuti?", "placeholder": "<PERSON><PERSON><PERSON> diet <PERSON>a", "vegetarian": "Vegetarian", "nonVegetarian": "Non-Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "keto": "Keto", "paleo": "<PERSON><PERSON>"}, "exercise": {"question": "<PERSON><PERSON><PERSON><PERSON> Anda berolahraga secara teratur?"}, "drinking": {"question": "<PERSON><PERSON><PERSON><PERSON>a men<PERSON>i alko<PERSON>?"}, "smoking": {"question": "<PERSON><PERSON><PERSON><PERSON>a me<PERSON>?"}, "sleep": {"question": "<PERSON><PERSON>a jam <PERSON>a tidur setiap malam?", "value": "{{sleep}} jam"}, "hydration": {"question": "Berapa cangkir air yang kamu minum setiap hari?", "value": "{{hydration}} cangkir ({{liters}}L)"}}, "allergies": {"title": "<PERSON><PERSON><PERSON><PERSON> Anda memiliki alergi?", "subtitle": "Menget<PERSON>i alergi Anda membantu kami memberikan rekomendasi yang lebih aman", "allergyIndex": "Alergi {{index}}", "name": {"question": "Anda alergi terhadap apa?", "placeholder": "Masukkan alergi (misalnya, Kacang, Debu)"}, "severity": {"question": "<PERSON><PERSON>apa parah alergi ini?", "placeholder": "<PERSON><PERSON><PERSON> tingkat keparahan", "mild": "<PERSON><PERSON>", "moderate": "Sedang", "severe": "<PERSON><PERSON>"}, "addButton": "Tambahkan Alergi Lain", "noAllergiesButton": "<PERSON>a tidak memiliki alergi."}, "medications": {"title": "Obat & Suplemen", "subtitle": "Ceritakan kepada kami tentang obat atau suplemen apa pun yang sedang Anda konsumsi.", "medicationIndex": "Obat {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama obat"}, "startDate": {"question": "<PERSON><PERSON> Anda mulai meminumnya?", "placeholder": "<PERSON><PERSON><PERSON> tanggal"}, "type": {"label": "<PERSON><PERSON>", "shortTerm": "<PERSON><PERSON>", "longTerm": "<PERSON><PERSON>"}, "dose": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "unit": {"label": "Unit"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>", "perDay": "per hari", "perWeek": "per minggu", "perMonth": "per bulan", "perYear": "per tahun"}, "units": {"mg": "mg", "ml": "ml", "iu": "iu", "puffs": "semburan", "drops": "tetes", "tsp": "sdm", "tbsp": "sdm", "cups": "cangkir"}, "addButton": "Tambahkan Obat Lain", "noMedicationsButton": "<PERSON>a tidak minum obat apa pun.", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "conditions": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Ceritakan kepada kami tentang kondisi medis apa pun yang Anda miliki atau pernah miliki di masa lalu.", "conditionIndex": "<PERSON><PERSON><PERSON> {{index}}", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> (<PERSON><PERSON><PERSON>, dll)"}, "since": {"question": "<PERSON><PERSON> kapan Anda mengalami kondisi ini?", "placeholder": "<PERSON><PERSON><PERSON> tanggal"}, "current": {"question": "Apakah ini sedang mengganggu <PERSON>?"}, "medicated": {"question": "<PERSON>paka<PERSON> Anda sedang mengonsumsi obat apa pun untuk ini?"}, "addButton": "Tambahkan Kondisi Lain", "noConditionsButton": "Saya tidak memiliki kondisi medis apa pun", "calendar": {"title": "<PERSON><PERSON><PERSON>"}}, "reproductive": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Informasi ini membantu kami memberikan rekomendasi kesehatan yang lebih personal", "menstruation": {"question": "<PERSON><PERSON><PERSON><PERSON> Anda pernah mengalami menstruasi?", "detailsTitle": "Detail Menstruasi", "regularity": {"question": "<PERSON><PERSON><PERSON> teratur siklus <PERSON>?", "regular": "<PERSON><PERSON><PERSON>", "irregular": "Tidak Teratur", "notSure": "Tidak <PERSON>"}, "cycleLength": {"label": "Panjang siklus rata-rata (hari)", "placeholder": "Ma<PERSON>kka<PERSON> panjang siklus"}, "flowDays": {"label": "<PERSON> menstruasi: {{flowDays}}", "min": "1 hari", "max": "15 hari"}, "padsPerDay": {"label": "Pembalut/tampon per hari: {{padsPerDay}}", "min": "1", "max": "15"}, "symptoms": {"question": "A<PERSON><PERSON>h ada gejala selama menstruasi?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> gejala (misalnya, kram, sakit kepala)"}}, "childbirth": {"question": "<PERSON><PERSON><PERSON>h Anda pernah melah<PERSON>an?", "detailsTitle": "Detail Persalinan", "children": {"label": "<PERSON><PERSON><PERSON>"}, "pregnancies": {"label": "<PERSON><PERSON><PERSON>"}, "complications": {"question": "A<PERSON><PERSON>h ada komplikasi selama kehamilan atau persalinan?", "placeholder": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON> (jika ada)"}}}, "review": {"title": "Tinjau Informasi Anda", "subtitle": "<PERSON><PERSON> tinjau informasi yang telah Anda berikan sebelum mengirim", "sections": {"basicInfo": "Informasi <PERSON>", "lifestyle": "<PERSON><PERSON>", "allergies": "<PERSON><PERSON><PERSON>", "medications": "Obat & Suplemen", "conditions": "<PERSON><PERSON><PERSON>", "reproductive": "<PERSON><PERSON><PERSON><PERSON>", "menstruationDetails": "Detail Menstruasi", "childbirthDetails": "Detail Persalinan"}, "fields": {"age": "Usia:", "sex": "<PERSON><PERSON>:", "height": "Tinggi:", "weight": "Berat:", "diet": "Diet:", "exercise": "Olahraga:", "drinking": "Minuman:", "smoking": "Merokok:", "sleep": "Tidur:", "hydration": "<PERSON><PERSON><PERSON>:", "allergyIndex": "Alergi {{index}}:", "dose": "Dosis:", "frequency": "Frekuensi:", "type": "<PERSON><PERSON>:", "since": "Sejak:", "currentlyActive": "Sedang Aktif:", "takingMedication": "Mengonsumsi <PERSON>:", "hasMenstruated": "<PERSON><PERSON>:", "regularity": "Keteraturan:", "cycleLength": "Panjang Siklus:", "flowDays": "<PERSON>:", "padsPerDay": "Pembalut/Tampon per Hari:", "hasChildbirth": "<PERSON><PERSON>:", "children": "Anak:", "pregnancies": "Kehamilan:"}, "notProvided": "Tidak tersedia", "units": {"cm": "{{height}} cm", "kg": "{{weight}} kg"}, "values": {"sleepHours": "{{sleep}} jam per hari", "hydration": "{{hydration}} gelas ({{liters}}L) per hari", "allergySeverity": "{{name}} ({{severity}})", "doseUnit": "{{dose}} {{unit}}", "frequencyUnit": "{{frequency}} {{unit}}", "cycleDays": "{{cycleLength}} hari"}, "noData": {"allergies": "Tidak ada alergi yang diberikan", "medications": "Tidak ada obat yang di<PERSON>ikan", "conditions": "Tidak ada kondisi medis yang diberikan"}, "submitButton": "<PERSON><PERSON>"}, "success": {"title": "Informasi Diperbarui!", "message": "<PERSON><PERSON> kasih telah memberikan informasi kesehatan Anda. Ka<PERSON> akan menggunakan ini untuk mempersonalisasi pengalaman Anda dan memberikan rekomendasi yang lebih baik.", "benefits": {"insights": "Informasi kesehatan yang dipersonalisasi", "reminders": "Pengingat pengobatan yang lebih baik", "recommendations": "Reko<PERSON><PERSON><PERSON> kesehatan yang disesuaikan"}, "continueButton": "Lanjutkan ke Dasbor"}, "permissions": {"microphonePermissionDenied": "<PERSON><PERSON> mikrofon di<PERSON>", "microphoneAccessDescription": "August membutuhkan akses ke mikrofon Anda untuk merekam audio dan mengirim catatan suara", "permissionDenied": "<PERSON><PERSON>", "cameraPermissionRequired": "Kami membutuhkan izin kamera agar ini berfungsi!", "mediaLibraryPermissionRequired": "Kami membutuhkan izin pustaka media agar ini berfungsi!"}, "voiceRecording": {"recordingTooLong": "<PERSON><PERSON><PERSON>", "recordingTooLongMessage": "<PERSON><PERSON><PERSON> suara harus kurang dari 5 menit. <PERSON><PERSON> rekam pesan yang lebih pendek."}, "errors": {"uploadFailed": "<PERSON><PERSON><PERSON>", "voiceUploadFailed": "Tidak dapat mengunggah rekaman suara.", "voiceRecordingFailed": "<PERSON><PERSON> mengirim rekaman suara", "failedToStopRecording": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "photoUploadFailed": "Tidak dapat mengunggah foto.", "failedToTakePhoto": "Gagal mengambil foto", "imageUploadFailed": "Tidak dapat mengunggah gambar: {{fileName}}", "failedToPickImage": "<PERSON><PERSON> memilih gambar", "documentUploadFailed": "Tidak dapat mengunggah dokumen: {{fileName}}", "failedToPickDocument": "<PERSON>l memilih dokumen"}, "audioPlayer": {"downloadingAudio": "Mengunduh audio...", "loadingAudio": "Memuat audio..."}, "mediaProcessing": {"processingFile": "Memproses File Anda", "uploadingSecuring": "<PERSON><PERSON><PERSON>ah dan mengam<PERSON>kan file...", "analyzingContent": "Menganalisis isi dokumen...", "extractingInfo": "Mengekstrak informasi kunci...", "processingInsights": "Memp<PERSON><PERSON> wawasan...", "preparingResponse": "Mempersiapkan respons detail...", "finalizingResponse": "Memfinalisasi respons..."}, "attachments": {"voiceMessage": "<PERSON><PERSON>", "image": "[GAMBAR]", "pdf": "[PDF]", "voice": "[CATATAN SUARA]"}, "pdf": {"loadingPdf": "Memuat PDF..."}, "dateTime": {"yesterday": "<PERSON><PERSON><PERSON>, "}, "navbar": {"defaultTitle": "august", "selectedCount": "terp<PERSON><PERSON>"}, "mediaUpload": {"photoLibrary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "takePhoto": "Ambil Foto", "chooseFile": "<PERSON><PERSON><PERSON>"}, "comingSoon": {"title": "<PERSON><PERSON><PERSON>!", "description": " sedang dalam pengembangan. Nantikan pembaruan!", "buttonText": "Baik!"}, "clipboard": {"success": "Tautan telah disalin ke clipboard"}, "mediaPhotos": {"emptyState": "Belum ada entri."}, "foodDetail": {"defaultFoodName": "<PERSON><PERSON>", "nutrition": {"totalCalories": "Total Kalori", "proteins": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugars": "<PERSON><PERSON>", "fibers": "Serat"}}, "reports": {"defaultTitle": "Item Media", "defaultFoodName": "<PERSON><PERSON>", "defaultName": "Dokumen", "openButton": "Buka di Penampil Eksternal", "biomarker": {"headerBiomarker": "Biomarker", "headerValue": "<PERSON><PERSON>", "headerRefRange": "<PERSON><PERSON><PERSON>", "headerStatus": "Status"}, "noData": "Tidak ada data biomarker yang tersedia"}, "setup": {"title": "<PERSON><PERSON>g menyi<PERSON>kan semuanya untuk <PERSON>a", "inProgress": "Sedang berlangsung...", "progressMessages": {"0": "Mengh<PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "Membuat Rencana Makan", "3": "Menghitung Skor Kesehatan", "4": "Memfinalisasi <PERSON>"}, "checklistItems": {"0": "Menganalisis data kesehatan <PERSON>a", "1": "Menghitung rencana nutrisi optimal", "2": "Memprivatisasi rekomendasi Anda", "3": "Membuat saran makanan <PERSON>", "4": "Memfinalisasi pengaturan <PERSON>"}}, "foodEntry": {"emptyState": "Belum ada entri makanan. Ambil foto makanan Anda untuk menambahkannya!"}, "nutritionReview": {"congratulations": "Selamat!", "subtitle": "<PERSON><PERSON><PERSON> nutrisi khusus Anda sudah siap", "submitButton": "Mari kita mulai!", "dailyTargetsTitle": "Target N<PERSON><PERSON><PERSON>", "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "fats": "Lemak"}, "recommendations": {"title": "Cara mencapai tujuan Anda:", "healthScores": "Gunakan skor kesehatan untuk meningkatkan rutinitas Anda", "trackFood": "<PERSON><PERSON> asupan makanan <PERSON>a secara konsisten", "followCalories": "<PERSON><PERSON><PERSON> reko<PERSON>dasi kalori harian <PERSON>", "balanceMacros": "Seimbangkan as<PERSON><PERSON>, protein, dan lemak <PERSON>"}}, "editModal": {"titlePrefix": "Sunting", "cancelButton": "<PERSON><PERSON>", "saveButton": "Berikutnya"}, "processing": {"stages": {"scanning": "<PERSON><PERSON><PERSON> ma<PERSON>...", "identifying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bahan...", "extracting": "Mengekstrak nutrisi...", "finalizing": "Mem<PERSON><PERSON><PERSON> hasil..."}, "error": {"defaultMessage": "Tidak terdeteksi makanan", "subtitle": "Coba sudut pandang yang berbeda"}, "retakeButton": "Ketuk untuk mengambil foto ulang", "notification": "<PERSON><PERSON> akan memberi tahu Anda saat selesai!"}, "chart": {"title": "Pelacakan Nutrisi Seiring Waktu", "selectNutrient": "<PERSON><PERSON><PERSON>:", "emptyState": "Data nutrisi belum tersedia.", "dropdown": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugars": "<PERSON><PERSON>"}}, "foodModal": {"defaultName": "<PERSON><PERSON><PERSON>", "defaultDate": "<PERSON> ini", "defaultTime": "<PERSON><PERSON><PERSON> tidak di<PERSON>i", "saveChanges": "<PERSON><PERSON><PERSON>", "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Gagal memperbarui data nutrisi. Silakan coba lagi."}, "nutrition": {"calories": "🔥 Kalori", "proteins": "🥩 <PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON>", "sugars": "🍬 Gula", "fat": "🥑 Lemak"}, "macroBreakdown": {"title": "<PERSON><PERSON><PERSON>", "noData": "Tidak ada data makronutrien yang tersedia untuk item makanan ini."}, "macroLabels": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "sugar": "<PERSON><PERSON>"}}, "infoModal": {"title": "Info Detail", "edit": "Sunting", "save": "Simpan", "saving": "Menyimpan...", "enterValue": "<PERSON><PERSON><PERSON><PERSON> nilai", "notSet": "Belum diatur", "age": "Usia", "heightCm": "Tinggi (cm)", "weightKg": "Berat (kg)", "targetWeight": "<PERSON><PERSON>", "nutritionTargets": "Target Gizi", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fats": "Lemak", "gm": "<PERSON><PERSON>", "editNote": "<PERSON><PERSON><PERSON><PERSON> nilai atau biarkan kosong untuk perhitungan otomatis.", "autoCalculateNote": "Makro dihitung otomatis berdasarkan data Anda.", "validation": {"ageMin": "Usia minimal 18 tahun", "ageMax": "<PERSON><PERSON> harus di bawah 125", "heightMin": "Tinggi harus minimal 50cm", "heightMax": "Tinggi badan harus di bawah 250cm", "weightMin": "Berat minimal 30kg", "weightMax": "<PERSON><PERSON> harus di bawah 500kg", "targetWeightMin": "Berat target minimal 30kg", "targetWeightMax": "Berat target harus di bawah 500 kg", "proteinMin": "Protein harus 0 atau lebih", "carbsMin": "Karbohidrat harus 0 atau lebih", "fatsMin": "Lemak harus 0 atau lebih"}}, "tracker": {"calories": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fat": "Lemak", "excess": "k<PERSON>bihan", "remaining": "sisa"}, "specialistConstants": {"nutritionist": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Konsultasi ahli tentang diet, nutrisi, dan keb<PERSON>aan makan sehat", "featureName": "<PERSON><PERSON><PERSON><PERSON>"}, "cardiologist": {"name": "Kardiolog", "description": "Spesialis kesehatan jantung dan kondisi kardiovaskular", "featureName": "<PERSON><PERSON><PERSON><PERSON>"}, "neurologist": {"name": "Neurolog", "description": "Berfokus pada gang<PERSON>n otak, <PERSON>um tulang be<PERSON>, dan sistem saraf", "featureName": "Spes<PERSON><PERSON>"}, "oncologist": {"name": "Onkolog", "description": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> kanker dan pilihan pengo<PERSON>an", "featureName": "Spesialis <PERSON>"}, "endocrinologist": {"name": "Endokrinolog", "description": "<PERSON><PERSON> dalam kondisi hormonal dan gangguan metabolisme", "featureName": "Spesialis <PERSON>ok<PERSON>logi"}}, "discoverCards": {"categories": {"nutrition": "<PERSON><PERSON><PERSON><PERSON>", "heartHealth": "<PERSON><PERSON><PERSON><PERSON>", "mentalHealth": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "Kebugara<PERSON>", "wellness": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "titles": {"vitaminB12Recovery": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>an untuk Pulih dari <PERSON>n Vitamin B12", "vitaminDeficiencyGanglion": "Kekurangan Vitamin <PERSON> yang Menyebabkan Kista Ganglion", "vitaminDeficiencyHairFall": "Kekurangan Vita<PERSON> yang Menyebabkan Rambut Rontok", "vitaminWaters": "Apakah Air Vitamin Baik untuk Anda", "cholesterolHeadaches": "<PERSON><PERSON><PERSON><PERSON>terol Tinggi Menyebabkan Sakit Kepala", "cholesterolEyes": "Apa Saja Gejala Kolesterol Tinggi yang Dapat Dilihat di Mata", "diabetesHeadaches": "Dapatkah Diabetes Menyebabkan Sakit <PERSON>", "chestPainDrinking": "Mengapa Dada Sa<PERSON>", "stressDizziness": "Dapatkah Stres Menyebabkan Pusing", "bulimiaFace": "Apa Itu <PERSON>", "kneeTwitch": "Mengapa Lutut Say<PERSON>", "noseTwitching": "Mengapa Hidung Berkedut Terjadi", "piriformisVsSciatica": "Apa Perbedaan Antara Sindrom Piriformis vs Sciatica", "shoulderBladePinched": "Cara Melepaskan Saraf <PERSON> Tulang Belikat", "shoulderPinched": "Cara Melepaskan <PERSON>", "meniscusTorn": "Cara Menyembuhkan Meniskus Robek Secara Alami", "hydrateQuickly": "Cara <PERSON> den<PERSON>", "periodConstipation": "Apakah Normal Mengalami Sembelit Saat Haid", "acneScars": "Cara Menghilangkan Bekas Jerawat Secara Alami Dalam <PERSON>nggu", "perimenopausePregnancy": "<PERSON><PERSON><PERSON><PERSON>"}, "descriptions": {"vitaminB12Recovery": "Temukan garis waktu pemulihan defisiensi vitamin B12 dan pengobatan efektif untuk meningkatkan tingkat energi Anda.", "vitaminDeficiencyGanglion": "<PERSON><PERSON><PERSON><PERSON> hubungan antara kekurangan vitamin dan perkembangan kista ganglion di dalam tubuh.", "vitaminDeficiencyHairFall": "Pelajari tentang bagaimana kurangnya vitamin penting dapat menyebabkan rambut rontok dan apa yang dapat Anda lakukan untuk mencegahnya.", "vitaminWaters": "Ungkap manfaat dan potensi kekurangan air vitamin sebagai bagian dari nutrisi harian <PERSON>.", "cholesterolHeadaches": "Periksa kemungkinan hubungan antara kadar kolesterol tinggi dan timbulnya sakit kepala.", "cholesterolEyes": "Pelajari bagaimana kolesterol tinggi dapat bermanifestasi di mata Anda dan gejala apa yang harus diwaspadai.", "diabetesHeadaches": "Selid<PERSON> hubungan antara diabetes dan kejadian sakit kepala dalam kehidupan se<PERSON>-hari.", "chestPainDrinking": "<PERSON><PERSON><PERSON><PERSON> alasan di balik nyeri dada setelah mengonsumsi minuman tertentu.", "stressDizziness": "P<PERSON>jari bagaimana stres dapat memengaruhi keseimbangan dan kesejahteraan Anda secara kese<PERSON>han, yang menyebab<PERSON> pusing.", "bulimiaFace": "<PERSON><PERSON><PERSON> tanda-tanda fisik bulimia, term<PERSON><PERSON> efeknya pada penampilan wajah.", "kneeTwitch": "Selidiki potensi penyebab di balik kedutan lutut yang tidak disengaja dan hubungannya dengan stres atau kelelahan.", "noseTwitching": "Pelajari tentang kemungkinan alasan untuk kedutan hidung dan hubungannya dengan kecemasan atau faktor lain.", "piriformisVsSciatica": "Bandingkan gejala sindrom piriformis dan sciatica untuk lebih memahami kondisi Anda.", "shoulderBladePinched": "Temukan teknik efektif untuk meredakan saraf yang terjepit di tulang belikat Anda dan mengembalikan mobilitas.", "shoulderPinched": "P<PERSON>jar<PERSON> latihan dan peregangan sederhana untuk mengurangi kompresi saraf di area bahu.", "meniscusTorn": "<PERSON><PERSON><PERSON><PERSON> metode alami dan latihan untuk mendukung penyembuhan meniskus yang robek.", "hydrateQuickly": "Cari tahu cara cepat dan efektif untuk rehidrasi dan menjaga hidrasi tubuh yang optimal.", "periodConstipation": "<PERSON><PERSON><PERSON> alasan di balik sembelit selama menstruasi dan pelajari pengobatan alami.", "acneScars": "Temukan pengobatan alami dan kiat perawatan kulit untuk mengurangi munculnya bekas jerawat dengan cepat.", "perimenopausePregnancy": "Pelajari tentang perimenopause, per<PERSON><PERSON><PERSON><PERSON> kes<PERSON>n, dan apa yang diharapkan selama tahap kehidupan ini."}}}