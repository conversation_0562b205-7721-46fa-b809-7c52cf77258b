import React, { useState, useRef, FC, forwardRef, useImperativeHandle, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, Platform, Alert, Text, ScrollView, Image, ActivityIndicator, Keyboard, Button } from 'react-native';
import logger from '@/utils/logger/logger';
import { Mic, MicOff, Plus, Send, Image as ImageIcon, FileText, Camera, X, Trash2 } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useChatStore, useIsLoading, useIsWaitingForResponse } from '@/store/chatStore';
import { useDrawerStore } from '@/store/drawerStore';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import MediaUploadDrawer from '@/components/common/drawers/MediaUploadDrawer';
import { uploadMedia } from '@/services/mediaService';
import { ChatInputRef } from '@/types/chat';
import { useAudioRecorder, AudioModule, RecordingPresets, useAudioPlayer } from 'expo-audio';
import { Audio } from 'expo-av';
import { trackUserInteraction, trackOperation, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';
import { trackClarityEvent } from '@/utils/clarity/clarity-utils';
import {
  Waveform,
  type IWaveformRef,
} from '@simform_solutions/react-native-audio-waveform';
import {
  scale,
  verticalScale,
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import * as Haptics from 'expo-haptics';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";
import AppsFlyerService from '@/utils/appsflyer/appsflyer-utils';
import { mediumHaptic } from '@/utils/haptics/haptics';
import { useTranslation } from 'react-i18next';
import { AppEventsLogger } from 'react-native-fbsdk-next';

interface ChatInputProps {
  isAtBottom?: boolean;
  onKeyboardVisibilityChange?: (isVisible: boolean) => void;
  disabled?: boolean;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      backgroundColor: "transparent",
      paddingBottom: 0,
      position: "relative",
    },
    promptsContainer: {
      maxHeight: moderateVerticalScale(30),
      marginTop: moderateVerticalScale(8),
      backgroundColor: "transparent",
    },
    promptsContent: {
      paddingHorizontal: moderateScale(10),
      backgroundColor: "transparent",
    },
    promptButton: {
      backgroundColor: "rgba(255, 255, 255, 0.7)",
      borderColor: theme.colors.gray[300],
      borderWidth: 1,
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateVerticalScale(3),
      borderRadius: moderateScale(32),
      marginRight: moderateScale(8),
    },
    loadingOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(255, 255, 255, 0.7)",
      alignItems: "center",
      justifyContent: "center",
      zIndex: 10,
    },
    promptText: {
      fontSize: moderateScale(theme.fontSize.sm),
      color: theme.colors.gray[600],
    },
    attachmentPreview: {
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateVerticalScale(4),
      flexDirection: "row",
      flexWrap: "wrap",
    },
    attachmentItem: {
      marginRight: moderateScale(8)
    },
    filePreview: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.gray[100],
      borderRadius: moderateScale(theme.radii.sm),
      padding: moderateScale(8),
      maxWidth: moderateScale(300),
    },
    fileName: {
      flex: 1,
      marginLeft: moderateScale(8),
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray[800],
    },
    imagePreview: {
      position: "relative",
      width: moderateScale(82),
      height: moderateScale(82),
      borderRadius: moderateScale(theme.radii.lg),
      borderWidth: moderateVerticalScale(1),
      borderColor: "rgba(0, 0, 0, 0.5)",
      overflow: "hidden",
    },
    previewImage: {
      width: "100%",
      height: "100%",
      borderRadius: moderateScale(theme.radii.lg),
    },
    removeAttachment: {
      padding: moderateScale(4),
    },
    removeImageButton: {
      position: "absolute",
      top: moderateVerticalScale(4),
      right: moderateScale(4),
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      borderRadius: moderateScale(theme.radii.md),
      width: moderateScale(20),
      height: moderateScale(20),
      alignItems: "center",
      justifyContent: "center",
    },
    imageName: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      color: theme.colors.secondary[50],
      fontSize: moderateScale(10),
      padding: moderateScale(4),
      textAlign: "center",
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.gray[100],
      borderRadius: moderateScale(theme.radii["2xl"]),
      paddingHorizontal: moderateScale(8),
      paddingVertical: moderateVerticalScale(4),
      marginHorizontal: moderateScale(8),
      marginTop: moderateVerticalScale(8),
    },
    iconCircle: {
      width: moderateScale(36),
      height: moderateScale(36),
      borderRadius: moderateScale(18),
      backgroundColor: theme.colors.gray[200],
      alignItems: "center",
      justifyContent: "center",
    },
    sendActive: {
      backgroundColor: theme.colors.primary.main,
    },
    disabledButton: {
      backgroundColor: theme.colors.gray[300],
    },
    attachButton: {
      padding: moderateScale(4),
    },
    input: {
      flex: 1,
      fontSize: moderateScale(theme.fontSize.sm),
      maxHeight: moderateVerticalScale(100),
      paddingHorizontal: moderateScale(8),
    },
    sendButton: {
      padding: moderateScale(4),
    },
    disclaimer: {
      textAlign: "center",
      fontSize: moderateScale(theme.fontSize.xs),
      color: theme.colors.gray.main,
      marginTop: moderateVerticalScale(4),
      marginBottom: moderateVerticalScale(4),
    },
    recordingActive: {
      backgroundColor: theme.colors.error[50],
      borderColor: theme.colors.error,
    },
    recordingIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.error[50],
      paddingHorizontal: moderateScale(12),
      paddingVertical: moderateScale(6),
      borderRadius: moderateScale(4),
      marginBottom: moderateScale(8),
    },
    recordingDot: {
      width: moderateScale(10),
      height: moderateScale(10),
      borderRadius: moderateScale(5),
      backgroundColor: theme.colors.error,
      marginRight: moderateScale(8),
    },
    recordingText: {
      color: theme.colors.error,
      flex: 1,
    },
    recordingStop: {
      color: theme.colors.error,
      fontWeight: 'bold',
    },
  });

const ChatInput = forwardRef<ChatInputRef, ChatInputProps>((props, ref) => {

  const MAX_RECORDING_DURATION = 5 * 60;
  const { t } = useTranslation();


  const { isAtBottom = true, onKeyboardVisibilityChange ,disabled = false} = props;
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const [message, setMessage] = useState('');
  const [showMediaDrawer, setShowMediaDrawer] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // Get drawer state from the store
  const { isUploadDrawerOpen, setUploadDrawerOpen } = useDrawerStore();

  const [attachments, setAttachments] = useState<Array<{ type: string, uri: string, name?: string, serverUrl?: string, signedUrl?: string, duration?: number }>>([]);
  const [isUploading, setIsUploading] = useState(false);
  //const storeLoading = useIsLoading();
  const isWaitingForResponse = useIsWaitingForResponse();
  const { sendMessageToApi, addMessage } = useChatStore();
  const inputRef = useRef(null);

  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const [isRecording, setIsRecording] = useState(false);

  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [meterings, setMeterings] = useState<number[]>([]);
  const recordingRef = useRef<Audio.Recording | null>(null);
  const meteringIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [sound, setSound] = useState();


  useEffect(() => {
    return sound
      ? () => {
        //@ts-ignore
        sound.unloadAsync();
      }
      : undefined;
  }, [sound]);

  const startRecording = async () => {
    try {
      setMeterings([]);
      setRecordingDuration(0);
  
      // Request microphone permissions
      const { status, granted } = await Audio.requestPermissionsAsync();
      if (!granted || status !== 'granted') {
        logger.info('Microphone permission denied');
        Alert.alert(
          'Microphone permission denied',
          'August requires accesses your microphone to record audio and send voice notes',
          [{ text: 'OK' }]
        );
        return;
      } 
  
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
  
      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync({
        ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
        isMeteringEnabled: true,
      });
  
      await recording.startAsync();
      recordingRef.current = recording;
      setIsRecording(true);
  
      // Timer for duration
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
  
      // Timer for metering
      meteringIntervalRef.current = setInterval(async () => {
        if (recordingRef.current) {
          const status = await recordingRef.current.getStatusAsync();
          if (status.isRecording && typeof status.metering === 'number') {
            //@ts-ignore
            setMeterings(prev => [...prev.slice(-40), status.metering]);
          }
        }
      }, 100);
  
      logger.info('Recording started successfully');
  
    } catch (error) {
      logger.error('Failed to start recording:', error);
      setIsRecording(false);
      setRecordingDuration(0);
  
      // Cleanup timers
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
      if (meteringIntervalRef.current) {
        clearInterval(meteringIntervalRef.current);
        meteringIntervalRef.current = null;
      }
    }
  };  

  const cancelRecording = async () => {
    try {
      // Stop the timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      // Stop metering polling
      if (meteringIntervalRef.current) {
        clearInterval(meteringIntervalRef.current);
        meteringIntervalRef.current = null;
      }

      logger.info('Cancelling voice recording');

      // Stop and unload the recording without saving
      if (recordingRef.current) {
        await recordingRef.current.stopAndUnloadAsync();
        recordingRef.current = null;
      }

      // Reset state
      setIsRecording(false);
      setRecordingDuration(0);
      setMeterings([]);

      // Track cancelled recording
      trackUserInteraction('Voice Recording Cancelled', {
        durationSeconds: recordingDuration
      });

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    } catch (error) {
      logger.error('Failed to cancel recording:', error);
      setIsRecording(false);
      setRecordingDuration(0);
      setMeterings([]);
    }
  };

  const sendVoiceRecording = async () => {
    try {
      // Check if recording duration is less than 5 minutes (300 seconds)
      if (recordingDuration >= 300) {
        Alert.alert(
          'Recording Too Long',
          'Voice recordings must be less than 5 minutes. Please record a shorter message.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Stop the timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      // Stop metering polling
      if (meteringIntervalRef.current) {
        clearInterval(meteringIntervalRef.current);
        meteringIntervalRef.current = null;
      }

      logger.info('Sending voice recording');
      setIsRecording(false);
      let uri: string | null = null;

      try {
        // If using expo-av Audio.Recording
        if (recordingRef.current) {
          await recordingRef.current.stopAndUnloadAsync();
          uri = recordingRef.current.getURI();
          recordingRef.current = null;
        } else {
          logger.info('No recording instance found, recorder may not have started correctly');
        }
      } catch (stopError) {
        logger.error('Error stopping recorder:', stopError);
        // Still try to access the URI
        if (recordingRef.current) {
          uri = recordingRef.current.getURI();
        }
      }

      // Get recording URI
      if (!uri) {
        throw new Error('No recording URI available');
      }

      // Track successful recording
      trackOperation('Voice Recording', OperationStatus.SUCCESS, {
        durationSeconds: recordingDuration
      });

      // Track in Clarity
      trackClarityEvent('Voice_Recording_Completed', {
        durationSeconds: recordingDuration,
        timestamp: new Date().toISOString()
      });

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Create unique filename
      const fileName = `Voice_${new Date().toISOString().replace(/[:.]/g, '-')}.m4a`;

      setIsUploading(true);

      try {
        trackOperation('Voice Upload', OperationStatus.INITIATED, {
          fileName,
          durationSeconds: recordingDuration
        });

        // Upload the file to the server using the same uploadMedia function
        const uploadResponse = await uploadMedia(uri);
        logger.info('Voice upload response:', uploadResponse);

        // Extract the file URL from the response
        let serverUrl = null;
        if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
          serverUrl = uploadResponse.files['0'].fileURL;
          logger.info('Extracted server URL for voice:', serverUrl);
          trackOperation('Voice Upload', OperationStatus.SUCCESS, {
            fileName,
            durationSeconds: recordingDuration,
            hasServerUrl: !!serverUrl
          });
        }

        // Create voice attachment object
        const voiceAttachment = {
          type: 'voice' as 'voice',
          uri: uri,
          name: fileName,
          serverUrl: serverUrl,
          duration: recordingDuration
        };

        // Send the voice message immediately
        logger.info('Sending voice message to API via store');
        await sendMessageToApi('', [voiceAttachment]);

        logger.info('Voice message sent successfully');

        // Track successful send
        trackOperation('Voice Message Send', OperationStatus.SUCCESS, {
          durationSeconds: recordingDuration,
          fileName: fileName
        });

        trackClarityEvent('Voice_Message_Sent', {
          durationSeconds: recordingDuration,
          timestamp: new Date().toISOString()
        });

      } catch (uploadError) {
        logger.error('Voice upload failed:', uploadError);
        trackOperation('Voice Upload', OperationStatus.FAILURE, {
          error: uploadError instanceof Error ? uploadError.message : String(uploadError)
        });
        Alert.alert('Upload Failed', 'Could not upload the voice recording.');
      } finally {
        setIsUploading(false);
      }

      // Clear waveform bars and reset duration
      setMeterings([]);
      setRecordingDuration(0);

    } catch (error) {
      logger.error('Failed to send voice recording:', error);
      trackOperation('Voice Recording', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
      Alert.alert('Error', 'Failed to send voice recording');
    } finally {
      setIsRecording(false);
      setMeterings([]);
      setRecordingDuration(0);
    }
  };

  const stopRecording = async () => {
    try {
      // Stop the timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      // Stop metering polling
      if (meteringIntervalRef.current) {
        clearInterval(meteringIntervalRef.current);
        meteringIntervalRef.current = null;
      }

      logger.info('Stopping voice recording');
      setIsRecording(false);
      let uri: string | null = null;

      try {
        // If using expo-av Audio.Recording
        if (recordingRef.current) {
          await recordingRef.current.stopAndUnloadAsync();
          uri = recordingRef.current.getURI();
          recordingRef.current = null;
        } else {
          logger.info('No recording instance found, recorder may not have started correctly');
        }
      } catch (stopError) {
        logger.error('Error stopping recorder:', stopError);
        // Still try to access the URI
        if (recordingRef.current) {
          uri = recordingRef.current.getURI();
        }
      }

      // Get recording URI
      if (!uri) {
        throw new Error('No recording URI available');
      }

      // Track successful recording
      trackOperation('Voice Recording', OperationStatus.SUCCESS, {
        durationSeconds: recordingDuration
      });

      // Track in Clarity
      trackClarityEvent('Voice_Recording_Completed', {
        durationSeconds: recordingDuration,
        timestamp: new Date().toISOString()
      });

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Create unique filename
      const fileName = `Voice_${new Date().toISOString().replace(/[:.]/g, '-')}.m4a`;

      setIsUploading(true);

      try {
        trackOperation('Voice Upload', OperationStatus.INITIATED, {
          fileName,
          durationSeconds: recordingDuration
        });

        // Upload the file to the server using the same uploadMedia function
        const uploadResponse = await uploadMedia(uri);
        logger.info('Voice upload response:', uploadResponse);

        // Extract the file URL from the response
        let serverUrl = null;
        if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
          serverUrl = uploadResponse.files['0'].fileURL;
          logger.info('Extracted server URL for voice:', serverUrl);
          trackOperation('Voice Upload', OperationStatus.SUCCESS, {
            fileName,
            durationSeconds: recordingDuration,
            hasServerUrl: !!serverUrl
          });
        }

        // Add voice recording to attachments
        setAttachments([...attachments, {
          type: 'voice',
          uri: uri,
          name: fileName,
          serverUrl: serverUrl,
          duration: recordingDuration
        }]);

      } catch (uploadError) {
        logger.error('Voice upload failed:', uploadError);
        trackOperation('Voice Upload', OperationStatus.FAILURE, {
          error: uploadError instanceof Error ? uploadError.message : String(uploadError)
        });
        Alert.alert('Upload Failed', 'Could not upload the voice recording.');
      } finally {
        setIsUploading(false);
      }

      // Clear waveform bars
      setMeterings([]);

    } catch (error) {
      logger.error('Failed to stop recording:', error);
      trackOperation('Voice Recording', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
      Alert.alert('Error', 'Failed to stop recording');
    } finally {
      setIsRecording(false);
      setMeterings([]);
    }
  };

  useEffect(() => {
    return () => {
      if (meteringIntervalRef.current) clearInterval(meteringIntervalRef.current);
      if (recordingTimerRef.current) clearInterval(recordingTimerRef.current);
      if (recordingRef.current) recordingRef.current.stopAndUnloadAsync();
    };
  }, []);

  // keyboard event listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
        if (onKeyboardVisibilityChange) {
          onKeyboardVisibilityChange(true);
        }

      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
        if (onKeyboardVisibilityChange) {
          onKeyboardVisibilityChange(false);
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }

      if (isRecording) {
        audioRecorder.stop().catch(error => {
          logger.error('Error cleaning up recording:', error);
        });
      }
    };
  }, [onKeyboardVisibilityChange]);


  // Expose methods to parent components
  useImperativeHandle(ref, () => ({
    setInputText: (text: string) => {
      setMessage(text);
    },
    openMediaDrawer: () => {
      // This method is kept for backward compatibility
      // but we'll use the drawer store for actual control
      setShowMediaDrawer(true);
    }
  }));

  // Watch for changes to the drawer store state
  useEffect(() => {
    if (isUploadDrawerOpen) {
      // Open the drawer when the store state is true
      setShowMediaDrawer(true);
      // Reset the store state immediately
      setUploadDrawerOpen(false);
    }
  }, [isUploadDrawerOpen, setUploadDrawerOpen]);

  const handleSend = useCallback(async () => {
    if (message.trim() || attachments.length > 0) {
      if(Platform.OS === 'ios'){
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
         } else {
            mediumHaptic()
         }
      logger.info('User initiated send message', { 
        hasText: !!message.trim(), 
        attachmentsCount: attachments.length 
      });

      // Track message send attempt
      trackUserInteraction('Message Send', {
        hasText: !!message.trim(),
        attachmentsCount: attachments.length,
        attachmentTypes: attachments.map(att => att.type)
      });

      // Track in Clarity
      trackClarityEvent('Message_Send_Attempt', {
        hasText: !!message.trim(),
        attachmentsCount: attachments.length,
        attachmentTypes: attachments.map(att => att.type).join(','),
        timestamp: new Date().toISOString()
      });

      try {
        // Convert attachments to the correct type
        const typedAttachments = attachments.length > 0
          ? attachments.map(att => {
            logger.debug('Processing attachment', {
              type: att.type,
              name: att.name,
              hasServerUrl: !!att.serverUrl,
              hasSignedUrl: !!att.signedUrl
            });
            return {
              type: att.type as 'image' | 'file' | 'pdf' | 'voice',
              uri: att.uri,
              name: att.name,
              serverUrl: att.serverUrl,
              signedUrl: att.signedUrl // Include signedUrl for PDF previews
            };
          })
          : undefined;

        logger.info('Sending message to API via store');
        // Send message to API
        // Clear input immediately for responsive UI
        setMessage('');
        setAttachments([]);
        // Fire and forget, but still handle errors
        sendMessageToApi(
          message.trim(),
          typedAttachments
        );

        logger.info('Message sent successfully, clearing input');
        // Track successful message send
        trackOperation('Message Send', OperationStatus.SUCCESS, {
          messageLength: message.trim().length,
          attachmentsCount: attachments.length,
          attachmentTypes: attachments.map(att => att.type)
        });

        // Clear input
        setMessage('');
        setAttachments([]);
      } catch (error) {
        logger.error('Error sending message from UI:', error);
        // Track failed message send
        trackOperation('Message Send', OperationStatus.FAILURE, {
          error: error instanceof Error ? error.message : String(error),
          messageLength: message.trim().length,
          attachmentsCount: attachments.length
        });

        Alert.alert(t('common.error'), 'Failed to send message. Please try again.');
      }
    }
  }, [message, attachments, sendMessageToApi]);

  const handleAttachment = useCallback(() => {
    trackUserInteraction('Media Drawer Open');

    // Track in Clarity
    trackClarityEvent('Media_Drawer_Opened', {
      timestamp: new Date().toISOString()
    });

    setShowMediaDrawer(true);
  }, []);

  const handleTakePhoto = useCallback(async () => {
    trackOperation('Camera Capture', OperationStatus.INITIATED);
    try {
      setIsUploading(true);
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        trackOperation('Camera Permission', OperationStatus.FAILURE, {
          reason: 'Permission denied'
        });
        Alert.alert('Permission Denied', 'We need camera permissions to make this work!');
        setIsUploading(false);
        return;
      }

      trackOperation('Camera Permission', OperationStatus.SUCCESS);
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });
      setShowMediaDrawer(false);
      
      if (!result.canceled) {
        trackOperation('Photo Capture', OperationStatus.SUCCESS);

        // Track in Clarity
        trackClarityEvent('Photo_Captured', {
          source: 'camera',
          timestamp: new Date().toISOString()
        });

        const fileUri = result.assets[0].uri;
        const fileName = `Photo ${new Date().toLocaleTimeString()}`;

        try {
          trackOperation('Photo Upload', OperationStatus.INITIATED, {
            fileName
          });
          // Upload the file to the server
          const uploadResponse = await uploadMedia(fileUri);
          logger.info('Photo upload response:', uploadResponse);

          // Extract the file URL from the response
          let serverUrl = null;
          if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
            serverUrl = uploadResponse.files['0'].fileURL;
            logger.info('Extracted server URL:', serverUrl);
            trackOperation('Photo Upload', OperationStatus.SUCCESS, {
              fileSize: result.assets[0].fileSize,
              hasServerUrl: !!serverUrl
            });
          }

          setAttachments([...attachments, {
            type: 'image',
            uri: fileUri,
            name: fileName,
            serverUrl: serverUrl // Add the server URL to the attachment
          }]);
          // Close the drawer after successful photo capture
          //setShowMediaDrawer(false);
        } catch (uploadError) {
          logger.error('Photo upload failed:', uploadError);
          trackOperation('Photo Upload', OperationStatus.FAILURE, {
            error: uploadError instanceof Error ? uploadError.message : String(uploadError)
          });
          Alert.alert('Upload Failed', 'Could not upload the photo.');
        }
      } else {
        trackOperation('Photo Capture', OperationStatus.FAILURE, {
          reason: 'user_cancelled'
        });
      }
    } catch (error) {
      trackOperation('Camera Capture', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
      Alert.alert('Error', 'Failed to take photo');
    } finally {
      setIsUploading(false);
    }
  }, [attachments]);

  const handlePickImage = useCallback(async () => {
    trackOperation('Image Selection', OperationStatus.INITIATED);
    try {
      setIsUploading(true);
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        trackOperation('Image Permission', OperationStatus.FAILURE, {
          reason: 'Permission denied'
        });
        Alert.alert('Permission Denied', 'We need media library permissions to make this work!');
        setIsUploading(false);
        return;
      }

      trackOperation('Image Permission', OperationStatus.SUCCESS);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: true,
      });
      setShowMediaDrawer(false);
        
      if (!result.canceled) {
        trackOperation('Image Selection', OperationStatus.SUCCESS, {
          count: result.assets.length
        });

        // Track in Clarity
        trackClarityEvent('Images_Selected', {
          source: 'gallery',
          count: result.assets.length,
          timestamp: new Date().toISOString()
        });

        // Process all selected images
        const newAttachments = [];

        for (const asset of result.assets) {
          // Get file URI and name
          const fileUri = asset.uri;
          const uriParts = fileUri.split('/');
          const fileName = uriParts[uriParts.length - 1];

          try {
            trackOperation('Image Upload', OperationStatus.INITIATED, {
              fileName
            });
            // Upload the file to the server
            const uploadResponse = await uploadMedia(fileUri);
            logger.info('Image upload response:', uploadResponse);

            // Extract the file URL from the response
            let serverUrl = null;
            if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
              setShowMediaDrawer(false);
              serverUrl = uploadResponse.files['0'].fileURL;
              logger.info('Extracted server URL:', serverUrl);
              trackOperation('Image Upload', OperationStatus.SUCCESS, {
                fileName,
                fileSize: asset.fileSize,
                hasServerUrl: !!serverUrl
              });
            }

            newAttachments.push({
              type: 'image',
              uri: fileUri,
              name: fileName,
              serverUrl: serverUrl // Add the server URL to the attachment
            });
          } catch (uploadError) {
            logger.error('Image upload failed:', uploadError);
            trackOperation('Image Upload', OperationStatus.FAILURE, {
              fileName,
              error: uploadError instanceof Error ? uploadError.message : String(uploadError)
            });
            Alert.alert('Upload Failed', `Could not upload the image: ${fileName}`);
          }
        }

        // Add all new attachments to the existing ones
        setAttachments([...attachments, ...newAttachments]);
        // Close the drawer after successful image selection
        // setShowMediaDrawer(false);
      } else {
        trackOperation('Image Selection', OperationStatus.FAILURE, {
          reason: 'user_cancelled'
        });
      }
    } catch (error) {
      trackOperation('Image Selection', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
      Alert.alert('Error', 'Failed to pick image');
    } finally {
      setIsUploading(false);
    }
  }, [attachments]);

  const handlePickDocument = useCallback(async () => {
    trackOperation('Document Selection', OperationStatus.INITIATED);
    try {
      setIsUploading(true);

      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
        copyToCacheDirectory: true,
        multiple: true
      });
      setShowMediaDrawer(false);
      
      if (result.assets && result.assets.length > 0) {
        trackOperation('Document Selection', OperationStatus.SUCCESS, {
          count: result.assets.length
        });

        // Track in Clarity
        trackClarityEvent('Documents_Selected', {
          count: result.assets.length,
          type: 'pdf',
          timestamp: new Date().toISOString()
        });

        // Process all selected documents
        const newAttachments = [];

        for (const asset of result.assets) {
          const fileUri = asset.uri;
          const fileName = asset.name || 'document.pdf';

          try {
            trackOperation('Document Upload', OperationStatus.INITIATED, {
              fileName
            });
            // Upload the file to the server
            const uploadResponse = await uploadMedia(fileUri);
            //setShowMediaDrawer(false);
            
            // Extract the file URL and signed URL from the response
            let serverUrl = null;
            let signedUrl = null;
            if (uploadResponse.success && uploadResponse.files && uploadResponse.files['0']) {
              setShowMediaDrawer(false);
              serverUrl = uploadResponse.files['0'].fileURL;
              signedUrl = uploadResponse.files['0'].signedURL;
              logger.info('Extracted server URL:', serverUrl);
              logger.info('Extracted signed URL:', signedUrl);
              trackOperation('Document Upload', OperationStatus.SUCCESS, {
                fileName,
                fileSize: asset.size,
                hasServerUrl: !!serverUrl,
                hasSignedUrl: !!signedUrl
              });
            }
            //setShowMediaDrawer(false);
            newAttachments.push({ 
              type: 'pdf', // Changed from 'file' to 'pdf' to match the expected type in chatStore.ts
              uri: fileUri,
              name: fileName,
              serverUrl: serverUrl, // Add the server URL to the attachment
              signedUrl: signedUrl // Add the signed URL for preview
            });
          } catch (uploadError) {
            logger.error('Document upload failed:', uploadError);
            trackOperation('Document Upload', OperationStatus.FAILURE, {
              fileName,
              error: uploadError instanceof Error ? uploadError.message : String(uploadError)
            });
            Alert.alert('Upload Failed', `Could not upload the document: ${fileName}`);
          }
        }

        // Add all new attachments to the existing ones
        setAttachments([...attachments, ...newAttachments]);
        // Close the drawer after successful document selection
        //setShowMediaDrawer(false);
      } else {
        trackOperation('Document Selection', OperationStatus.FAILURE, {
          reason: 'user_cancelled'
        });
      }
    } catch (error) {
      trackOperation('Document Selection', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
      Alert.alert('Error', 'Failed to pick document');
      logger.error('Document picker error:', error);
    } finally {
      setIsUploading(false);
    }
  }, [attachments]);

  const handleVoice = useCallback(() => {
    trackUserInteraction('Voice Button Press');
    logger.info('Voice button pressed');

    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, attachments, recordingDuration]);

  //@ts-ignore
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleRemoveAttachment = useCallback((index: number) => {
    const removedAttachment = attachments[index];
    trackUserInteraction('Attachment Remove', {
      type: removedAttachment.type,
      name: removedAttachment.name
    });

    // Track in Clarity
    trackClarityEvent('Attachment_Removed', {
      attachmentType: removedAttachment.type,
      attachmentName: removedAttachment.name,
      timestamp: new Date().toISOString()
    });

    const newAttachments = [...attachments];
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  }, [attachments]);

  const handlePromptClick = useCallback((promptText: string) => {
    trackUserInteraction('Prompt Click', { promptText });

    // Track in Clarity
    trackClarityEvent('Prompt_Selected', {
      promptText,
      timestamp: new Date().toISOString()
    });

    if (promptText === t('chat.prompts.uploadReport')) {
      logger.info('Triggering media drawer due to upload report prompt');
      setShowMediaDrawer(true);
    } else {
      setMessage(promptText);
    }
  }, [t]);

  const prompts = useMemo(() => [
    t('chat.prompts.uploadReport'),
    t('chat.prompts.speakInHindi'),
    t('chat.prompts.notFeelingWell'),
    t('chat.prompts.whatIsMyBMI'),
    t('chat.prompts.nutritionAdvice'),
    t('chat.prompts.sleepBetter')
  ], [t]);

  return (
    <View style={styles.container}>
      {/* Loading indicator */}
      {isUploading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      )}

      {/* Prompt Carousel - Only show if no attachments and at bottom of chat */}
      {attachments.length === 0 && !isKeyboardVisible && !disabled && !isRecording && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.promptsContainer}
          contentContainerStyle={styles.promptsContent}
        >
          {prompts.map((prompt, index) => (
            <TouchableOpacity
              key={index}
              style={styles.promptButton}
              onPress={() => handlePromptClick(prompt)}
            >
              <Text style={styles.promptText}>{prompt}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
      {/* Attachment Preview */}
      {attachments.length > 0 && (
        <View style={styles.attachmentPreview}>
          {attachments.map((attachment, index) => (
            <View key={index} style={styles.attachmentItem}>
              {attachment.type === 'file' || attachment.type === 'pdf' ? (
                <View style={styles.filePreview}>
                  <FileText size={24} color={colors.primary} />
                  <Text style={{ marginLeft: moderateScale(4) }}>{attachment.name}</Text>
                  <TouchableOpacity
                    style={styles.removeAttachment}
                    onPress={() => handleRemoveAttachment(index)}
                    disabled={disabled}
                  >
                    <X size={16} color={colors.gray[500]} />
                  </TouchableOpacity>
                </View>
              ) : attachment.type === 'image' ? (
                <View style={styles.imagePreview}>
                  <Image source={{ uri: attachment.uri }} style={styles.previewImage} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => handleRemoveAttachment(index)}
                    disabled={disabled}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <X size={16} color={colors.white} />
                  </TouchableOpacity>
                </View>
              ) : attachment.type === 'voice' ? (
                <View style={styles.filePreview}>
                  <Mic size={24} color={colors.primary} />
                  <Text style={{ marginLeft: moderateScale(4) }}>
                    {attachment.name} {attachment.duration !== undefined && `(${formatDuration(attachment.duration)})`}
                  </Text>
                  <TouchableOpacity
                    style={styles.removeAttachment}
                    onPress={() => handleRemoveAttachment(index)}
                  >
                    <X size={16} color={colors.gray[500]} />
                  </TouchableOpacity>
                </View>
              ) : null}
            </View>
          ))}
        </View>
      )}

      {/* Voice Recording Waveform */}
      {isRecording && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            height: 50,
            borderRadius: 8,
            overflow: 'hidden',
            paddingHorizontal: 16,
          }}
        >
          {/* Recording indicator */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginRight: 12,
            flex: 0
          }}>
            <View style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              backgroundColor: 'red',
              marginRight: 6,
            }} />
            <Text style={{
              fontSize: 12,
              color: colors.gray[600],
              minWidth: 40
            }}>
              {formatDuration(recordingDuration)}
            </Text>
          </View>

          {/* Waveform bars */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1, // Takes remaining space
            height: 40,
            paddingHorizontal: 4,
            justifyContent: 'flex-start'
            
          }}>
            {meterings.map((db, i) => {
              const rawNorm = (db + 160) / 160;
              const threshold = 0.7;
              const norm = rawNorm < threshold ? 0 : (rawNorm - threshold) / (1 - threshold);
              const barHeight = Math.max(2, 1 + 30 * Math.pow(norm, 2)); // Min 2px, max ~31px
              return (
                <View
                  key={i}
                  style={{
                    width: 2,
                    height: barHeight,
                    backgroundColor: 'red',
                    marginHorizontal: 0.5,
                    borderRadius: 1,
                    alignSelf: 'center',
                  }}
                />
              );
            })}
          </View>
        </View>
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={handleAttachment}
          disabled={isUploading || isRecording || disabled}
        >
          <View style={[styles.iconCircle]}>
            <Plus
              size={20}
              color={(isUploading || isRecording || disabled) ? colors.gray[400] : colors.primary}
            />
          </View>
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder={t('chat.input.placeholder')}
          placeholderTextColor={colors.gray[400]}
          value={message}
          onChangeText={setMessage}
          multiline
          editable={!isUploading && !isRecording && !disabled}
        />

        {/* Recording Controls - Show when recording */}
        {isRecording ? (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {/* Cancel/Dustbin Button */}
            <TouchableOpacity
              style={[styles.sendButton, { marginRight: 8 }]}
              onPress={cancelRecording}
              disabled={isUploading}
            >
              <View style={[
                styles.iconCircle,
                { backgroundColor: colors.gray[200] },
                isUploading && styles.disabledButton
              ]}>
                <Trash2
                  size={18}
                  color={isUploading ? colors.gray[400] :theme.colors.error}
                />
              </View>
            </TouchableOpacity>

            {/* Send Button */}
            <TouchableOpacity
              style={styles.sendButton}
              onPress={sendVoiceRecording}
              disabled={isUploading}
            >
              <View style={[
                styles.iconCircle,
                styles.sendActive,
                isUploading && styles.disabledButton
              ]}>
                {isUploading ? (
                  <ActivityIndicator
                    size="small"
                    color={theme.colors.secondary[50]}
                  />
                ) : (
                  <Send
                    size={18}
                    color={theme.colors.secondary[50]}
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
        ) : (
          /* Normal Send/Mic Button - Show when not recording */
          <TouchableOpacity
            style={styles.sendButton}
            onPress={message.trim() || attachments.length > 0 ? handleSend : startRecording}
            disabled={isUploading}
          >
            <View style={[
              styles.iconCircle,
              message.trim() || attachments.length > 0 ? styles.sendActive : {},
              (isUploading) && styles.disabledButton
            ]}>
              {message.trim() || attachments.length > 0 ? (
                <Send
                  size={18}
                  color={(isUploading) ? theme.colors.gray[400] : theme.colors.secondary[50]}
                />
              ) : (
                <Mic
                  size={18}
                  color={(isUploading) ? theme.colors.gray[400] : theme.colors.primary.main}
                />
              )}
            </View>
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.disclaimer}>{t('chat.input.disclaimer')}</Text>

      {/* <Button title="Play Sound" onPress={playSound} />*/}
      <MediaUploadDrawer
        visible={showMediaDrawer}
        onClose={() => setShowMediaDrawer(false)}
        onTakePhoto={handleTakePhoto}
        onChooseImage={handlePickImage}
        onChooseFile={handlePickDocument}
      />
    </View>
  );
});

// const styles = StyleSheet.create({
//   container: {
//     backgroundColor: 'transparent',
//     paddingBottom: 0,
//     position: 'relative',
//   },
//   promptsContainer: {
//     maxHeight: moderateVerticalScale(30),
//     marginTop: moderateVerticalScale(8),
//     backgroundColor: 'transparent',
//   },
//   promptsContent: {
//     paddingHorizontal: moderateScale(10),
//     backgroundColor: 'transparent',
//   },
//   promptButton: {
//     backgroundColor: 'rgba(255, 255, 255, 0.7)',
//     borderColor: colors.gray[300],
//     borderWidth: 1,
//     paddingHorizontal: moderateScale(16),
//     paddingVertical: moderateVerticalScale(5),
//     borderRadius: moderateScale(32),
//     marginRight: moderateScale(8),
//   },
//   loadingOverlay: {
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: 'rgba(255, 255, 255, 0.7)',
//     alignItems: 'center',
//     justifyContent: 'center',
//     zIndex: 10,
//   },
//   promptText: {
//     fontSize: moderateScale(14),
//     color: colors.gray[600],
//   },
//   attachmentPreview: {
//     paddingHorizontal: moderateScale(16),
//     marginBottom: moderateVerticalScale(8),
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//   },
//   attachmentItem: {
//     marginRight: moderateScale(8),
//     marginBottom: moderateVerticalScale(8),
//   },
//   filePreview: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: colors.gray[100],
//     borderRadius: moderateScale(8),
//     padding: moderateScale(8),
//     maxWidth: moderateScale(200),
//   },
//   fileName: {
//     flex: 1,
//     marginLeft: moderateScale(8),
//     fontSize: moderateScale(14),
//     color: colors.gray[800],
//   },
//   imagePreview: {
//     position: 'relative',
//     width: moderateScale(100),
//     height: moderateScale(100),
//     borderRadius: moderateScale(8),
//     overflow: 'hidden',
//   },
//   previewImage: {
//     width: '100%',
//     height: '100%',
//   },
//   removeAttachment: {
//     padding: moderateScale(4),
//   },
//   removeImageButton: {
//     position: 'absolute',
//     top: moderateVerticalScale(4),
//     right: moderateScale(4),
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     borderRadius: moderateScale(12),
//     width: moderateScale(24),
//     height: moderateScale(24),
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   imageName: {
//     position: 'absolute',
//     bottom: 0,
//     left: 0,
//     right: 0,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     color: colors.white,
//     fontSize: moderateScale(10),
//     padding: moderateScale(4),
//     textAlign: 'center',
//   },
//   inputContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: colors.gray[100],
//     borderRadius: moderateScale(24),
//     paddingHorizontal: moderateScale(8),
//     paddingVertical: moderateVerticalScale(4),
//     marginHorizontal: moderateScale(8),
//     marginTop: moderateVerticalScale(8),
//   },
//   iconCircle: {
//     width: moderateScale(36),
//     height: moderateScale(36),
//     borderRadius: moderateScale(18),
//     backgroundColor: colors.gray[200],
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   sendActive: {
//     backgroundColor: colors.primary,
//   },
//   disabledButton: {
//     backgroundColor: colors.gray[300],
//   },
//   attachButton: {
//     padding: moderateScale(4),
//   },
//   input: {
//     flex: 1,
//     fontSize: moderateScale(15),
//     maxHeight: moderateVerticalScale(100),
//     paddingHorizontal: moderateScale(8),
//   },
//   sendButton: {
//     padding: moderateScale(4),
//   },
//   disclaimer: {
//     textAlign: 'center',
//     fontSize: moderateScale(12),
//     color: colors.gray[500],
//     marginTop: moderateVerticalScale(4),
//     marginBottom: moderateVerticalScale(4),
//   },
// });

// Apply React.memo to the component
const MemoizedChatInput = React.memo(ChatInput);

export default MemoizedChatInput;