import { usePhoneStore } from '@/store/phoneStore';
import logger from '@/utils/logger/logger';

export const envVars = {
  GATEKEEPER_URL: "https://api.getbeyondhealth.com",
  TENANT: "august",
  MIXPANEL_TOKEN: "984b1fdbebe1b1d2321838c27c71f5b8",
  PROJECT_ID: "de365926-a5da-43d5-94a1-a3b9a3ff8cbf",
  CLARITY_PROJECT_ID: "r40cr3xluz",
} as const;

const isUSPhoneNumber = (phone: string | null): boolean => {
  return !!phone && phone.startsWith('+1') && phone.length === 12;
};

export const getEnvironmentVariable = (name: keyof typeof envVars, phone?: string): string => {
  if (name === "GATEKEEPER_URL") {
    const phoneNumber = phone || usePhoneStore.getState().phoneNumber;
    if (isUSPhoneNumber(phoneNumber)) {
      return "https://gatekeeper-prod-us.getbeyondhealth.com";
    }
  }
  const value = envVars[name];
  
  if (!value) {
    throw new Error(`Environment variable ${name} is not defined`);
  }
  return value;
};
