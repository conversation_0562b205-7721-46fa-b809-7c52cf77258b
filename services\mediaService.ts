import axiosInstance from './axiosInstance';
import * as FileSystem from 'expo-file-system';
import logger from '@/utils/logger/logger';
import { Platform } from 'react-native';
import { getEnvironmentVariable } from '@/utils/getEnvironmentVariable';
import { trackOperation, trackUserInteraction, OperationStatus } from '@/utils/mixpanel/mixpanel-utils';

/**
 * Uploads a media file to the server
 * @param fileUri The URI of the file to upload (from cache directory)
 * @returns The server response
 */
export const uploadMedia = async (fileUri: string): Promise<any> => {
  // Declare variables outside try block so they're accessible in catch block
  const uriParts = fileUri.split('/');
  const fileName = uriParts[uriParts.length - 1];
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  let mimeType = 'application/octet-stream'; // Default
  
  try {
    logger.info(`Preparing to upload media from: ${fileUri}`);
    
    // Track user interaction for initiating a media upload
    trackUserInteraction('Media Upload', {
      fileType: fileExtension,
      fileName: fileName.substring(0, 20) 
    });
    
    // Track the operation start
    trackOperation('Media Upload', OperationStatus.INITIATED, {
      fileUri: fileUri.substring(fileUri.lastIndexOf('/') + 1),
      fileExtension
    });
    
    // Create form data
    const formData = new FormData();
    
    // Get file info to determine mime type
    let fileInfo;
    try {
      fileInfo = await FileSystem.getInfoAsync(fileUri);
      logger.info('File info:', fileInfo);
      
      trackOperation('File Info Retrieval', OperationStatus.SUCCESS, {
        size: fileInfo.exists && 'size' in fileInfo ? fileInfo.size : undefined,
        exists: fileInfo.exists,
        isDirectory: fileInfo.isDirectory
      });
    } catch (error) {
      logger.error('Error getting file info:', error);
      
      trackOperation('File Info Retrieval', OperationStatus.FAILURE, {
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    // Determine mime type based on file extension
    if (fileExtension === 'jpg' || fileExtension === 'jpeg') {
      mimeType = 'image/jpeg';
    } else if (fileExtension === 'png') {
      mimeType = 'image/png';
    } else if (fileExtension === 'pdf') {
      mimeType = 'application/pdf';
    } else if (fileExtension === 'mp4') {
      mimeType = 'video/mp4';
    }
    
    // Append file to form data
    formData.append('files', {
      uri: Platform.OS === 'ios' ? fileUri.replace('file://', '') : fileUri,
      name: fileName,
      type: mimeType,
    } as any);
    
    logger.info('Uploading media to server', { fileName, mimeType });
    logger.info('THIS IS THE FORM DATA', formData);
    
    // Make the request
    trackOperation('Media Upload API Call', OperationStatus.INITIATED, {
      fileName,
      mimeType,
      fileSize: fileInfo?.exists && 'size' in fileInfo ? fileInfo.size : undefined
    });
    
    const response = await axiosInstance.post(
      `${getEnvironmentVariable("GATEKEEPER_URL")}/user/${getEnvironmentVariable("TENANT")}/upload-media`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    logger.info('Media upload response:', response.data);
    
    trackOperation('Media Upload API Call', OperationStatus.SUCCESS, {
      fileName,
      mimeType,
      status: response.status,
      hasFileURL: !!response.data?.files?.['0']?.fileURL,
      hasSignedURL: !!response.data?.files?.['0']?.signedURL
    });
    
    // Track overall operation success
    trackOperation('Media Upload', OperationStatus.SUCCESS, {
      fileType: fileExtension,
      fileSize: fileInfo?.exists && 'size' in fileInfo ? fileInfo.size : undefined
    });
    
    return response.data;
  } catch (error) {
    if ((error as any)?.response?.status !== 401) {
      logger.error('Error uploading media:', error);
    }
    
    // Track API call failure
    trackOperation('Media Upload API Call', OperationStatus.FAILURE, {
      fileName,
      mimeType: mimeType || 'unknown',
      error: error instanceof Error ? error.message : String(error)
    });
    
    // Track overall operation failure
    trackOperation('Media Upload', OperationStatus.FAILURE, {
      fileType: fileExtension,
      error: error instanceof Error ? error.message : String(error)
    });
    
    throw error;
  }
};