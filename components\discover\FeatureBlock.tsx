import React, { useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import {
  moderateScale,
  moderateVerticalScale,
} from 'react-native-size-matters';
import { useTheme } from "@/src/theme/ThemeContext";
import { Theme } from "@/src/theme/types";

interface FeatureBlockProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  backgroundColor?: string;
  actions?: React.ReactNode[];
  onPress?: () => void;
}

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      borderRadius: moderateScale(theme.radii.md),
      marginBottom: moderateVerticalScale(16),
      marginTop: moderateVerticalScale(4),
      backgroundColor: theme.colors.secondary[50],
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: moderateVerticalScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: moderateScale(4),
      elevation: 2,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: moderateScale(12),
      borderTopLeftRadius: moderateScale(theme.radii.lg),
      borderTopRightRadius: moderateScale(theme.radii.lg),
    },
    titleContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    title: {
      fontSize: moderateScale(theme.fontSize.sm),
      fontWeight: "600",
      marginLeft: moderateScale(10),
      color: theme.colors.gray[800],
    },
    actions: {
      flexDirection: "row",
    },
    content: {
      padding: moderateScale(14),
      paddingTop: moderateVerticalScale(12),
    },
    description: {
      fontSize: moderateScale(theme.fontSize.sm),
      lineHeight: moderateVerticalScale(20),
      color: theme.colors.gray[700],
    },
  });

const FeatureBlock: React.FC<FeatureBlockProps> = ({
  title,
  description,
  icon,
  backgroundColor = colors.white,
  actions,
  onPress,
}) => {
    const { theme } = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <View style={[styles.header, { backgroundColor }]}>
        <View style={styles.titleContainer}>
          {icon}
          <Text style={styles.title}>{title}</Text>
        </View>
        {actions && (
          <View style={styles.actions}>
            {actions.map((action, index) => (
              <React.Fragment key={index}>{action}</React.Fragment>
            ))}
          </View>
        )}
      </View>
      <View style={styles.content}>
        <Text style={styles.description}>{description}</Text>
      </View>
    </TouchableOpacity>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     borderRadius: moderateScale(16),
//     marginBottom: moderateVerticalScale(16),
//     marginTop: moderateVerticalScale(4),
//     backgroundColor: colors.white,
//     shadowColor: colors.black,
//     shadowOffset: { width: 0, height: moderateVerticalScale(2) },
//     shadowOpacity: 0.1,
//     shadowRadius: moderateScale(4),
//     elevation: 2,
//   },
//   header: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     padding: moderateScale(12),
//     borderTopLeftRadius: moderateScale(16),
//     borderTopRightRadius: moderateScale(16),
//   },
//   titleContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   title: {
//     fontSize: moderateScale(14),
//     fontWeight: '600',
//     marginLeft: moderateScale(10),
//     color: colors.gray[800],
//   },
//   actions: {
//     flexDirection: 'row',
//   },
//   content: {
//     padding: moderateScale(14),
//     paddingTop: moderateVerticalScale(12),
//   },
//   description: {
//     fontSize: moderateScale(14),
//     lineHeight: moderateVerticalScale(20),
//     color: colors.gray[700],
//   },
// });

export default React.memo(FeatureBlock);
