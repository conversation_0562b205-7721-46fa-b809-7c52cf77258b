import * as SecureStore from 'expo-secure-store';
import logger from '@/utils/logger/logger';

const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

/**
 * Secure Storage Service
 * 
 * This implementation uses SecureStore for storing sensitive data like authentication tokens.
 * SecureStore provides better security than AsyncStorage by storing data in a keychain (iOS)
 * or keystore (Android).
 */
export const secureStorage = {
  /**
   * Store the access token
   * @param token The access token to store
   */
  setAccessToken: async (token: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(ACCESS_TOKEN_KEY, token);
    } catch (error) {
      logger.error('Error storing access token:', error);
      throw error;
    }
  },
  
  /**
   * Retrieve the access token
   * @returns The stored access token or null if not found
   */
  getAccessToken: async (): Promise<string | null> => {
    try {
      return await SecureStore.getItemAsync(ACCESS_TOKEN_KEY);
    } catch (error) {
      logger.error('Error retrieving access token:', error);
      return null;
    }
  },
  
  /**
   * Store the refresh token
   * @param token The refresh token to store
   */
  setRefreshToken: async (token: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(REFRESH_TOKEN_KEY, token);
    } catch (error) {
      logger.error('Error storing refresh token:', error);
      throw error;
    }
  },
  
  /**
   * Retrieve the refresh token
   * @returns The stored refresh token or null if not found
   */
  getRefreshToken: async (): Promise<string | null> => {
    try {
      return await SecureStore.getItemAsync(REFRESH_TOKEN_KEY);
    } catch (error) {
      logger.error('Error retrieving refresh token:', error);
      return null;
    }
  },
  
  
  /**
   * Remove all authentication tokens
   */
  removeTokens: async (): Promise<void> => {
    try {
      await SecureStore.deleteItemAsync(ACCESS_TOKEN_KEY);
      await SecureStore.deleteItemAsync(REFRESH_TOKEN_KEY);
    } catch (error) {
      logger.error('Error removing tokens:', error);
      throw error;
    }
  },

  /**
   * Migrate token from AsyncStorage to SecureStore if it exists
   * This function is kept for API compatibility but does nothing
   * as requested by the user
   */
  migrateTokenFromAsyncStorage: async (): Promise<void> => {
    // Migration functionality removed as requested
    return;
  }
};
